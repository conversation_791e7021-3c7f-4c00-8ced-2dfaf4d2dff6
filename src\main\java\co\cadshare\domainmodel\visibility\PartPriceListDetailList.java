package co.cadshare.domainmodel.visibility;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;
import java.util.List;

@XmlRootElement(name = "List")
@XmlAccessorType(XmlAccessType.FIELD)
public class PartPriceListDetailList {

    @XmlElement(name = "PartPriceListDetail")
    private List<PartPriceListDetail> partPriceListDetailList;

    public List<PartPriceListDetail> getPartPriceListDetailList() {
        return partPriceListDetailList;
    }

    public void setPartPriceListDetailList(List<PartPriceListDetail> partPriceListDetailList) {
        this.partPriceListDetailList = partPriceListDetailList;
    }
}
