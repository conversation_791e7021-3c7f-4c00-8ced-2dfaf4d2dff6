package co.cadshare.publications.adapters.api.web.manufacturers.publicationsCoverImage;

import co.cadshare.publications.core.CoverImage;
import co.cadshare.publications.adapters.api.web.manufacturers.publications.CoverImageDto;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

@Mapper
public interface CoverImageMapper {

    public final CoverImageMapper Instance = Mappers.getMapper(CoverImageMapper.class);

    @Mapping(source = "imageUrl", target = "locationUrl")
    CoverImage dtoToCoverImage(CoverImageDto dto);

}
