package co.cadshare.controller;


import co.cadshare.orders.core.Order;
import co.cadshare.shared.core.user.User;
import co.cadshare.request.OrderWithExternalData;
import co.cadshare.request.OrderWithWareHouseId;
import co.cadshare.response.ApiResponseEntity;
import co.cadshare.services.GlobalPaymentService;
import groovy.util.logging.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

@Controller
@RequestMapping("/payment")
@Slf4j
public class GlobalPaymentController {

    private final Logger log = LoggerFactory.getLogger(getClass());
    @Autowired
    GlobalPaymentService globalPaymentService;

    @PostMapping(value = "/hpp-config")
    public HttpEntity<String> getHPPConfig(
            @AuthenticationPrincipal User currentUser,
            @RequestBody OrderWithWareHouseId orderWithWareHouseId,
            @RequestParam(value = "userId",required = false) Integer userId) {
        try {
            Order order = orderWithWareHouseId.getOrder();
            if(order.getPrice() <= 0 ){
                return new ResponseEntity<>("Order price must be greater than zero.",HttpStatus.BAD_REQUEST);
            }

            log.info("API: getHPPConfig, ACCESS: User [{}], order [{}]", currentUser.accessDetails(), order);
            int userIdForOrder = userId == 0 ? currentUser.getUserId() : userId;
            String hppResponse = globalPaymentService.getHPPConfig(orderWithWareHouseId, userIdForOrder);
            if(hppResponse.isEmpty())
                return new ResponseEntity<>("Can not get HPP payment config.",HttpStatus.BAD_REQUEST);

            return new ResponseEntity<>(hppResponse, HttpStatus.OK);
        } catch (IllegalArgumentException e){
            return new ResponseEntity<>(e.getMessage(),HttpStatus.BAD_REQUEST);
        } catch (Exception e) {
            return new ResponseEntity<>(e.getMessage(),HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @PreAuthorize("(hasRole('ROLE_MANUFACTURER_SUB_ENTITY_DEALER') or hasRole('ROLE_MANUFACTURER_SUB_ENTITY_CUSTOMER') or hasRole('ROLE_MANUFACTURER_SUB_ENTITY_DEALER_PLUS')) " +
            " || (hasRole('ROLE_MANUFACTURER') && @manufacturerSubEntityService.getManufacturerSubEntity(@usersService.findByUserId(#userId).getManufacturerSubEntityId()).manufacturerId == #currentUser.getManufacturerId())" +
            " || (hasRole('ROLE_MANUFACTURER_SUB_ENTITY_DEALER_PLUS') and @manufacturerSubEntityService.getManufacturerSubEntity(@usersService.findByUserId(#userId).getManufacturerSubEntityId()).parentSubEntityId == #currentUser.manufacturerSubEntityId())")
    @PostMapping(value = "/create-order")
    public HttpEntity<Object> createOrderAfterSuccessPayment(
        @AuthenticationPrincipal User currentUser,
        @RequestHeader(value = "Site-Url",required = false) String siteUrl,
        @RequestBody OrderWithExternalData orderWithGlobalPaymentAndRateShipmentInfo,
        @RequestParam Integer userId
    ) {
        log.info("ACCESS: User [{}], createOrderAfterSuccessPayment, orderWithGlobalPaymentAndRateShipmentInfo [{}]", currentUser.accessDetails(), orderWithGlobalPaymentAndRateShipmentInfo);

        Order order = orderWithGlobalPaymentAndRateShipmentInfo.getOrder();
        String globalPaymentResponse = orderWithGlobalPaymentAndRateShipmentInfo.getGlobalPaymentResponse();
        String rateId = orderWithGlobalPaymentAndRateShipmentInfo.getRateId();
        if (order == null || globalPaymentResponse == null || rateId == null)
            return new ResponseEntity<>("Input data invalid.",HttpStatus.BAD_REQUEST);

        int userIdForOrder = userId == 0 ? currentUser.getUserId() : userId;
        try {
            ApiResponseEntity<Order> result = globalPaymentService.createOrderAfterSuccessPayment(orderWithGlobalPaymentAndRateShipmentInfo, siteUrl, userIdForOrder);
            return new ResponseEntity<>(result, HttpStatus.OK);
        }
        catch (IllegalArgumentException e){
            return new ResponseEntity<>(e.getMessage(),HttpStatus.BAD_REQUEST);
        }
        catch (Exception e){
            return new ResponseEntity<>(e.getMessage(),HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
}
