package co.cadshare.addresses.adapters.database;

import co.cadshare.addresses.core.ExternalAddress;
import co.cadshare.addresses.core.User;
import co.cadshare.addresses.core.UserAddressMap;
import co.cadshare.shared.adapters.database.addresses.AddressEntity;
import co.cadshare.shared.adapters.database.addresses.BaseAddressEntity;
import co.cadshare.shared.adapters.database.addresses.ExternalAddressEntity;
import co.cadshare.utils.CycleAvoidingMappingContext;
import org.mapstruct.Context;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper(uses = ExternalAddressEntityMapper.class)
public interface UserAddressMapEntityMapper {

    UserAddressMapEntityMapper Instance = Mappers.getMapper(UserAddressMapEntityMapper.class);

    @Mapping(source="address", target="address", qualifiedByName="mapAddressEntityToCore")
    @Mapping(source="user", target="user", qualifiedByName="mapUserEntityToCore")
    UserAddressMap entityToCore(UserAddressMapEntity entity, @Context CycleAvoidingMappingContext context);

	List<UserAddressMap> entitiesToCores(List<UserAddressMapEntity> entities, @Context CycleAvoidingMappingContext context);


    @Mapping(source="address", target="address", qualifiedByName="mapAddressCoreToEntity")
    @Mapping(source="user", target="user", qualifiedByName="mapUserCoreToEntity")
    UserAddressMapEntity coreToEntity(UserAddressMap core, @Context CycleAvoidingMappingContext context);

    List<UserAddressMapEntity> coresToEntities(List<UserAddressMap> cores, @Context CycleAvoidingMappingContext context);

    @Named("mapAddressCoreToEntity")
    public static ExternalAddressEntity mapAddressCoreToEntity(ExternalAddress source, @Context CycleAvoidingMappingContext context) {
        return ExternalAddressEntityMapper.Instance.coreToEntity(source, context);
    }

    @Named("mapUserCoreToEntity")
    public static AddressesUserEntity mapUserCoreToEntity(User source, @Context CycleAvoidingMappingContext context) {
        return AddressesUserEntityMapper.Instance.coreToEntity(source, context);
    }

    @Named("mapAddressEntityToCore")
    public static ExternalAddress mapAddressEntityToCore(BaseAddressEntity source, @Context CycleAvoidingMappingContext context) {
        if(source.getClass().equals(ExternalAddressEntity.class))
            return ExternalAddressEntityMapper.Instance.entityToCore((ExternalAddressEntity) source, context);
        else
            return InternalAddressEntityMapper.Instance.entityToCore((AddressEntity) source, context);
    }

    @Named("mapUserEntityToCore")
    public static User mapUserEntityToCore(AddressesUserEntity source, @Context CycleAvoidingMappingContext context) {
        return AddressesUserEntityMapper.Instance.entityToCore(source, context);
    }
}
