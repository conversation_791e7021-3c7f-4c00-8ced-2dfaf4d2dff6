package co.cadshare.modelMgt.publications.adapters.api.web;

import lombok.Data;

import java.util.List;

@Data
public class PostPutPublicationRequestDto {

    private String name;
	private String serialNumber;
	private int publicationCategoryId;
	private List<PostPutPublicationViewableDto> viewables;
	private int coverImageId;
	private int featuredViewableImageId;
	private List<Integer> techDocs;
	private List<Integer> videos;
	private List<Integer> kits;
	private List<Integer> customers;

}
