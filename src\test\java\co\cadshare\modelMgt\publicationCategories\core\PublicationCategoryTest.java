package co.cadshare.modelMgt.publicationCategories.core;

import co.cadshare.modelMgt.publicationCategories.core.PublicationCategory;
import org.junit.Before;
import org.junit.Test;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;

public class PublicationCategoryTest {

    private PublicationCategory out;

    @Before
    public void Before() {
        this.out = new PublicationCategory();
    }

    @Test
    public void CheckPublicationCategoryIsNotNullTest() {
        assertTrue(out != null);
    }
}