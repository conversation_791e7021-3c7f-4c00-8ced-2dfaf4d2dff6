[{"Namespace": "aws:autoscaling:scheduledaction", "ResourceName": "DailyUpScaler", "OptionName": "StartTime", "Value": "START_TIME"}, {"Namespace": "aws:autoscaling:scheduledaction", "ResourceName": "DailyUpScaler", "OptionName": "EndTime", "Value": "END_TIME"}, {"Namespace": "aws:autoscaling:scheduledaction", "ResourceName": "DailyUpScaler", "OptionName": "MinSize", "Value": "1"}, {"Namespace": "aws:autoscaling:scheduledaction", "ResourceName": "DailyUpScaler", "OptionName": "MaxSize", "Value": "1"}, {"Namespace": "aws:autoscaling:scheduledaction", "ResourceName": "DailyUpScaler", "OptionName": "DesiredCapacity", "Value": "1"}, {"Namespace": "aws:autoscaling:scheduledaction", "ResourceName": "DailyDownScaler", "OptionName": "StartTime", "Value": "START_TIME"}, {"Namespace": "aws:autoscaling:scheduledaction", "ResourceName": "DailyDownScaler", "OptionName": "EndTime", "Value": "END_TIME"}, {"Namespace": "aws:autoscaling:scheduledaction", "ResourceName": "DailyDownScaler", "OptionName": "MinSize", "Value": "0"}, {"Namespace": "aws:autoscaling:scheduledaction", "ResourceName": "DailyDownScaler", "OptionName": "MaxSize", "Value": "0"}, {"Namespace": "aws:autoscaling:scheduledaction", "ResourceName": "DailyDownScaler", "OptionName": "DesiredCapacity", "Value": "0"}]