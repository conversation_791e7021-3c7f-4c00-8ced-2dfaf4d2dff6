package co.cadshare.publications.boundary;

import co.cadshare.shared.core.user.User;
import co.cadshare.publications.core.Publication;
import co.cadshare.shared.boundary.CommandPort;
import org.springframework.beans.factory.annotation.Autowired;

public class DeletePublicationService {

    private final CommandPort<Publication, Integer> publicationCommandPort;

    @Autowired
    public DeletePublicationService(CommandPort<Publication, Integer> publicationCommandPort) {
        this.publicationCommandPort = publicationCommandPort;
    }

    public void delete(User user, Publication publication) throws Exception {

        //...

        this.publicationCommandPort.delete(user, publication);
    }
}
