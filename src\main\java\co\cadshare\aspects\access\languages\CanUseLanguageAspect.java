package co.cadshare.aspects.access.languages;

import co.cadshare.aspects.BaseAspect;
import co.cadshare.shared.core.Language;
import co.cadshare.exceptions.ForbiddenException;
import co.cadshare.utils.ObjectUtilsExtension;
import lombok.experimental.ExtensionMethod;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.stereotype.Component;

@Aspect
@Component
@ExtensionMethod(ObjectUtilsExtension.class)
public class CanUseLanguageAspect extends BaseAspect {

    @Pointcut("@annotation(CanUseLanguage)")
    public void serviceLogPointcut(){}

    @Before("serviceLogPointcut()")
    public void checkAbacPermissions(JoinPoint joinPoint) throws ForbiddenException {
        configureInterception(joinPoint);
        Language language = (Language)getArgumentByType(Language.class);
        if (language.isNotNull())
            if (!user.hasLanguage(language))
                throw new ForbiddenException("User does not have permission to use language provided.");

    }
}
