package co.cadshare.addresses.adapters.ext.erp.vis;

import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlRootElement;
import lombok.Data;

import java.util.List;

@Data
@JacksonXmlRootElement(localName = "GetSHIP_TO_ADDRESSSResult")
public class GetShipToAddressListResponse {
    @JacksonXmlProperty(localName = "ENTITY_CODE")
    private String entityCode;
    @JacksonXmlProperty(localName = "TP_NAME_X")
    private String tradingPartnerName;
    @JacksonXmlProperty(localName = "ErrorMsg")
    private String ErrorMsg;
    @JacksonXmlProperty(localName = "ErrorC")
    private String ErrorC;
    @JacksonXmlProperty(localName = "List")
    private List<AddressDetail> addressDetails;

}
