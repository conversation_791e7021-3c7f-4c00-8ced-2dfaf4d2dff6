package co.cadshare.publications.boundary;

import co.cadshare.publications.core.Publication;
import co.cadshare.shared.boundary.GetService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class GetPublicationService  extends GetService<Publication, Integer> {

    private PublicationQueryPort complexQueryPort;

    @Autowired
    public GetPublicationService(PublicationQueryPort queryPort){
        super(queryPort);
        this.complexQueryPort = queryPort;
    }

    public List<Publication> getSome(int manufacturerId){
        return this.complexQueryPort.getPublicationsForManufacturer(manufacturerId);
    }
}
