package co.cadshare.addresses.adapters.database;

import co.cadshare.addresses.core.ExternalCustomer ;
import co.cadshare.addresses.core.ExternalPurchaser;
import co.cadshare.addresses.core.User;
import co.cadshare.utils.CycleAvoidingMappingContext;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper(uses = { ExternalAddressEntityMapper.class, AddressesUserEntityMapper.class})
public interface AddressesCustomerEntityMapper {

    AddressesCustomerEntityMapper Instance = Mappers.getMapper(AddressesCustomerEntityMapper.class);

    //CoreToEntity
    
    @Mapping(source="users", target="users", qualifiedByName="userCoreToEntity")
    AddressesCustomerEntity externalCustomerCoreToEntity(ExternalCustomer dealer);
    
    List<AddressesCustomerEntity> externalPurchaserCoresToCustomerEntities(List<ExternalPurchaser> dealers);

    
    //Entity To Core
    
    @Mapping(source="users", target="users", qualifiedByName="userEntityToCore")
    ExternalCustomer  entityToExternalPurchaserCore(AddressesCustomerEntity dealer);

    List<ExternalCustomer > entitiesToExternalPurchaserCores(List<AddressesCustomerEntity> dealers);
    
    
    //Named   

    @Named("userEntityToCore")
    public static User userEntityToCore(AddressesUserEntity entity) {
        return AddressesUserEntityMapper.Instance.entityToCore(entity, new CycleAvoidingMappingContext());
    }

    @Named("userCoreToEntity")
    public static AddressesUserEntity userCoreToEntity(User core) {
        return AddressesUserEntityMapper.Instance.coreToEntity(core, new CycleAvoidingMappingContext());
    }

}
