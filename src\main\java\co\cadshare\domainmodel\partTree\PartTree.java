package co.cadshare.domainmodel.partTree;

import java.util.List;

import lombok.Data;

@Data
public class PartTree {

  private int partId;
  private int modelId;
  private String fileName;
  private String partDescription;
  private String partNumber;
  private Integer objectId;
  private Integer parentObjectId;
  private List<PartTree> childParts;
  
  private boolean nonModeledPart;
  private boolean linkedPart;
  private boolean masterPartFound;
  private boolean masterPartDescriptionFound;
  private String alternatePartNumber;
  private boolean sparePart;
  private boolean criticalSparePart;
  private boolean sellablePart;


  public PartTree() {
    sparePart = true;
    criticalSparePart = true;
    sellablePart = false;
  }
}
