package co.cadshare.publications.adapters.api.web.manufacturers.publications;

import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class GetPublicationResponseDto {
    private Integer publicationId;
    private String publicationName;
    private String coverImage;
    private List<PublicationCustomerDto> customers;
    private List<Integer> serialNumbers;
    private List<SerialNumberRangeDto> serialNumberRanges;

    private List<PublicationViewableDto> viewables;
    private List<PublicationKitDto> kits;
    private List<PublicationTechDocDto> techDocs;
    private List<PublicationVideoDto> videos;
    private Date lastPublishedDateTime;
    private String publicationStatus;
}
