package co.cadshare.modelMgt.publications.adapters.database;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertTrue;

import co.cadshare.modelMgt.publications.core.Customer;
import co.cadshare.modelMgt.publications.core.Dealer;
import co.cadshare.modelMgt.publications.core.DealerPlus;
import co.cadshare.modelMgt.shared.core.Purchaser;
import org.junit.Before;
import org.junit.Test;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;

public class PublicationsPurchaserEntityMapperTest {

    private PublicationsPurchaserEntityMapper mapper;
    private Purchaser dealer;
	private Purchaser dealerPlus;
	private Purchaser customer;

	@Before
	public void setUp() {
		mapper = PublicationsPurchaserEntityMapper.Instance;

		dealer = new Dealer();
		dealer.setId(1);
		dealer.setName("Test Dealer");

		dealerPlus = new DealerPlus();
		dealerPlus.setId(1);
		dealerPlus.setName("Test DealerPlus");

		customer = new Customer();
		customer.setId(1);
		customer.setName("Test Customer");
	}

	@Test
	public void testDealerCoreToEntity() {
		PublicationsPurchaserEntity entity = mapper.purchaserCoreToEntity(dealer);
		assertEquals(dealer.getId(), entity.getId());
		assertEquals(dealer.getName(), entity.getName());
		assertTrue(entity instanceof PublicationsDealerEntity);
	}

	@Test
	public void testDealerPlusCoreToEntity() {
		PublicationsPurchaserEntity entity = mapper.purchaserCoreToEntity(dealerPlus);
		assertEquals(dealerPlus.getId(), entity.getId());
		assertEquals(dealerPlus.getName(), entity.getName());
		assertTrue(entity instanceof PublicationsDealerPlusEntity);
	}

	@Test
	public void testCustomerCoreToEntity() {
		PublicationsPurchaserEntity entity = mapper.purchaserCoreToEntity(customer);
		assertEquals(customer.getId(), entity.getId());
		assertEquals(customer.getName(), entity.getName());
		assertTrue(entity instanceof PublicationsCustomerEntity);
	}

	@Test
	public void testPurchaserCoresToPurchaserEntities() {
		List<Purchaser> purchasers = new ArrayList<>();
		purchasers.add(dealer);
		purchasers.add(dealerPlus);
		purchasers.add(customer);
		List<PublicationsPurchaserEntity> entities = new ArrayList<>(mapper.purchaserCoresToEntities(purchasers));
		assertEquals(purchasers.size(), entities.size());


		assertTrue(entities.get(0) instanceof PublicationsDealerEntity);
		assertTrue(entities.get(1) instanceof PublicationsDealerPlusEntity);
		assertTrue(entities.get(2) instanceof PublicationsCustomerEntity);
	}
}
