{"info": {"_postman_id": "64a10499-35f6-4b0c-92c2-b641fc990270", "name": "LOCALHOST TESTING", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "300252"}, "item": [{"name": "Dealer Plus Tests", "item": [{"name": "Orders", "item": [{"name": "http://localhost:8080/manufacturersubentity/22/machines?language=EN", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{access-token}} ", "type": "text"}], "url": {"raw": "http://localhost:8080/dealerplus/orders?status=SUBMITTED&status=PREPARING&language=EN", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["dealerplus", "orders"], "query": [{"key": "status", "value": "SUBMITTED"}, {"key": "status", "value": "PREPARING"}, {"key": "language", "value": "EN"}]}}, "response": []}, {"name": "Order Counts Tabs", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{access-token}} ", "type": "text"}], "url": {"raw": "http://localhost:8080/dealerplus/orders/counts?language=EN", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["dealerplus", "orders", "counts"], "query": [{"key": "language", "value": "EN"}]}}, "response": []}]}, {"name": "Customer", "item": [{"name": "Create User", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{access-token}} ", "type": "text"}, {"key": "Site-Url", "value": "localhost1244", "type": "text"}], "body": {"mode": "raw", "raw": "{\"emailAddress\":\"<EMAIL>\",\"firstName\":\"<PERSON>\",\"lastName\":\"<PERSON>\",\"userType\":\"MANUFACTURER_SUB_ENTITY_CUSTOMER\",\"manufacturerSubEntityId\":\"407\",\"userPermissions\":[\"Order\",\"PublishedProducts\"]}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:8080/dealerplus/user", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["dealerplus", "user"]}}, "response": []}, {"name": "Users for subentity", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{access-token}} ", "type": "text"}], "url": {"raw": "http://localhost:8080/dealerplus/user/manufacturerSubEntity/407", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["dealerplus", "user", "manufacturerSubEntity", "407"]}}, "response": []}]}, {"name": "Parts", "item": [{"name": "Get Masterpart by ID", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{access-token}} ", "type": "text"}], "url": {"raw": "http://localhost:8080/dealerplus/masterPart/361820/", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["dealerplus", "master<PERSON>art", "361820", ""]}}, "response": []}]}]}, {"name": "1. <PERSON><PERSON>", "event": [{"listen": "test", "script": {"exec": ["var responseData = JSON.parse(responseBody);", "postman.setEnvironmentVariable(\"access-token\", responseData.access_token);"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "sec-ch-ua", "value": "\" Not A;Brand\";v=\"99\", \"Chromium\";v=\"98\", \"Google Chrome\";v=\"98\""}, {"key": "Accept", "value": "application/json, text/plain, */*"}, {"key": "Content-Type", "value": "application/x-www-form-urlencoded"}, {"key": "Authorization", "value": "Basic Y2xpZW50YXBwOnNlY3JldA=="}, {"key": "sec-ch-ua-mobile", "value": "?0"}, {"key": "User-Agent", "value": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/98.0.4758.102 Safari/537.36"}, {"key": "sec-ch-ua-platform", "value": "\"macOS\""}, {"key": "host", "value": "localhost"}], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "grant_type", "value": "password"}, {"key": "password", "value": "{{password}}"}, {"key": "username", "value": "40"}]}, "url": {"raw": "http://localhost:8080/oauth/token", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["o<PERSON>h", "token"]}}, "response": []}]}