// log in

# @name cadshareAuthResponse
POST {{CADSHARE_URL}}/oauth/token
Content-Type: application/x-www-form-urlencoded
Authorization: {{BASIC_AUTH_BASE64_ENCODED}}

username={{DEALER_USER_ID}}
&password={{STANDARD_PWD}}
&grant_type=password


###

// create an order and then duplicate

@cadshareToken = Bearer {{cadshareAuthResponse.response.body.access_token}}

# @name cadshareCreateOrderResponse
POST {{CADSHARE_URL}}/order?language=EN&userId=206
Content-Type: application/json
Authorization: {{cadshareToken}}
Site-Url: {{CADSHARE_URL}}

< ./create-order-payload.json


###

@orderId = {{cadshareCreateOrderResponse.response.body.$}}

POST {{CADSHARE_URL}}/order/{{orderId}}/duplicate?language=EN&userId=206
Content-Type: application/json
Authorization: {{cadshareToken}}
Site-Url: {{CADSHARE_URL}}