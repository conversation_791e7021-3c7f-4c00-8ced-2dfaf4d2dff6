package co.cadshare.models.core.processor;

import co.cadshare.models.core.MetadataObjectExtended;
import co.cadshare.models.core.processor.fileproperties.SparePartPropertyProcessor;
import org.junit.Before;
import org.junit.Test;

import java.util.Collections;
import java.util.LinkedHashMap;

import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;

public class SparePartPropertyProcessorTest {

    private SparePartPropertyProcessor out;
    private LinkedHashMap properties;
    private MetadataObjectExtended objectExtended;

    @Before
    public void Before() {
        out = new SparePartPropertyProcessor();
        out.setSynonyms(Collections.singletonList("DB_SPARE_PART_CHAR"));
        setProperties();
        objectExtended = new MetadataObjectExtended();
    }

    @Test
    public void isNotSellableButIsSparePart() {

        out.setProperties(properties, objectExtended);

        assertFalse(objectExtended.isSellablePart());
        assertTrue(objectExtended.isSparePart());
    }

    private void setProperties() {
        properties = new LinkedHashMap<String, String>();
        properties.put("Original System", "NX");
        properties.put("CAD_PARTNAME", "16005-0011353-01");
    }
}
