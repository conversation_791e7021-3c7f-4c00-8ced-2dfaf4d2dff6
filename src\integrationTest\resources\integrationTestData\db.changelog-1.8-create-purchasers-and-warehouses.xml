<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                   http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.0.xsd
">

    <!-- Caterpillar -->
    <changeSet author="AndyB" id="1.8-integration-test-data-create-warehouses-1">
        <sql>
            INSERT INTO public.warehouse(
            id, manufacturerid, addressid, name, externalid, phonenumber, warehouse_x)
            VALUES (1, 1, 1, 'Caterpillar Warehouse', 1, '332323232', '01');
        </sql>
    </changeSet>


    <changeSet author="AndyB" id="1.8-integration-test-data-create-purchasers-1">
        <sql stripComments="true">
            INSERT INTO public.manufacturersubentity (manufacturersubentityid, name, manufacturerid, parentsubentityid, createddate, createdbyuserid, manufacturersubentitytypeid, modifieddate, modifiedbyuserid, closeddate, defaultdiscount, pricelistidentifierid, warehouseid, viscustomercode)
            VALUES(
            1,	                        --manufacturersubentityid,
            'Caterpillar Dealer 1', 	--name,
            1, 	                        --manufacturerid,
            NULL, 	                    --parentsubentityid,
            '2018-10-26 14:23:14.014', 	--createddate,
            1,   	                    --createdbyuserid,
            1, 	                        --manufacturersubentitytypeid,
            '2024-07-18 07:37:58.287', 	--modifieddate,
            1, 	                        --modifiedbyuserid,
            NULL, 	                    --closeddate,
            30, 	                    --defaultdiscount,
            NULL, 	                    --pricelistidentifierid,
            NULL, 	                    --warehouseid,
            NULL);	                    --viscustomercode
        </sql>
    </changeSet>

    <changeSet author="AndyB" id="1.8-integration-test-data-create-purchasers-13">
        <sql stripComments="true">
            INSERT INTO public.manufacturersubentity (manufacturersubentityid, name, manufacturerid, parentsubentityid, createddate, createdbyuserid, manufacturersubentitytypeid, modifieddate, modifiedbyuserid, closeddate, defaultdiscount, pricelistidentifierid, warehouseid, viscustomercode)
            VALUES(
            13,	                        --manufacturersubentityid,
            'Caterpillar Dealer-2', 	--name,
            1, 	                        --manufacturerid,
            NULL, 	                    --parentsubentityid,
            '2018-10-26 14:23:14.014', 	--createddate,
            1,   	                    --createdbyuserid,
            1, 	                        --manufacturersubentitytypeid,
            '2024-07-18 07:37:58.287', 	--modifieddate,
            1, 	                        --modifiedbyuserid,
            NULL, 	                    --closeddate,
            30, 	                    --defaultdiscount,
            NULL, 	                    --pricelistidentifierid,
            NULL, 	                    --warehouseid,
            NULL);	                    --viscustomercode
        </sql>
    </changeSet>

    <changeSet author="AndyB" id="1.8-integration-test-data-create-purchasers-2">
        <sql stripComments="true">
            INSERT INTO public.manufacturersubentity (manufacturersubentityid, name, manufacturerid, parentsubentityid, createddate, createdbyuserid, manufacturersubentitytypeid, modifieddate, modifiedbyuserid, closeddate, defaultdiscount, pricelistidentifierid, warehouseid, viscustomercode)
            VALUES(
            2,	                        --manufacturersubentityid,
            'Caterpillar DealerPlus 1', --name,
            1, 	                        --manufacturerid,
            NULL, 	                    --parentsubentityid,
            '2018-10-26 14:23:14.014', 	--createddate,
            1,   	                    --createdbyuserid,
            4, 	                        --manufacturersubentitytypeid,
            '2024-07-18 07:37:58.287', 	--modifieddate,
            1, 	                        --modifiedbyuserid,
            NULL, 	                    --closeddate,
            30, 	                    --defaultdiscount,
            NULL, 	                    --pricelistidentifierid,
            NULL, 	                    --warehouseid,
            NULL);	                    --viscustomercode
        </sql>
    </changeSet>

    <changeSet author="AndyB" id="1.8-integration-test-data-create-purchasers-14">
        <sql stripComments="true">
            INSERT INTO public.manufacturersubentity (manufacturersubentityid, name, manufacturerid, parentsubentityid, createddate, createdbyuserid, manufacturersubentitytypeid, modifieddate, modifiedbyuserid, closeddate, defaultdiscount, pricelistidentifierid, warehouseid, viscustomercode)
            VALUES(
            14,	                        --manufacturersubentityid,
            'Caterpillar DealerPlus-2', --name,
            1, 	                        --manufacturerid,
            NULL, 	                    --parentsubentityid,
            '2018-10-26 14:23:14.014', 	--createddate,
            1,   	                    --createdbyuserid,
            4, 	                        --manufacturersubentitytypeid,
            '2024-07-18 07:37:58.287', 	--modifieddate,
            1, 	                        --modifiedbyuserid,
            NULL, 	                    --closeddate,
            30, 	                    --defaultdiscount,
            NULL, 	                    --pricelistidentifierid,
            NULL, 	                    --warehouseid,
            NULL);	                    --viscustomercode
        </sql>
    </changeSet>

    <changeSet author="AndyB" id="1.8-integration-test-data-create-purchasers-5">
        <sql stripComments="true">
            INSERT INTO public.manufacturersubentity (manufacturersubentityid, name, manufacturerid, parentsubentityid, createddate, createdbyuserid, manufacturersubentitytypeid, modifieddate, modifiedbyuserid, closeddate, defaultdiscount, pricelistidentifierid, warehouseid, viscustomercode)
            VALUES(
            5,	                                    --manufacturersubentityid,
            'Caterpillar Customer for DealerPlus 1',  --name,
            1, 	                                    --manufacturerid,
            2, 	                                    --parentsubentityid,
            '2018-10-26 14:23:14.014', 	            --createddate,
            1,   	                                --createdbyuserid,
            4, 	                                    --manufacturersubentitytypeid,
            '2024-07-18 07:37:58.287', 	            --modifieddate,
            1, 	                                    --modifiedbyuserid,
            NULL, 	                                --closeddate,
            30, 	                                --defaultdiscount,
            NULL, 	                                --pricelistidentifierid,
            NULL, 	                                --warehouseid,
            NULL);	                                --viscustomercode
        </sql>
    </changeSet>

    <changeSet author="AndyB" id="1.8-integration-test-data-create-purchasers-15">
        <sql stripComments="true">
            INSERT INTO public.manufacturersubentity (manufacturersubentityid, name, manufacturerid, parentsubentityid, createddate, createdbyuserid, manufacturersubentitytypeid, modifieddate, modifiedbyuserid, closeddate, defaultdiscount, pricelistidentifierid, warehouseid, viscustomercode)
            VALUES(
            15,	                                    --manufacturersubentityid,
            'Caterpillar Customer for DealerPlus-2',  --name,
            1, 	                                    --manufacturerid,
            14, 	                                --parentsubentityid,
            '2018-10-26 14:23:14.014', 	            --createddate,
            1,   	                                --createdbyuserid,
            4, 	                                    --manufacturersubentitytypeid,
            '2024-07-18 07:37:58.287', 	            --modifieddate,
            1, 	                                    --modifiedbyuserid,
            NULL, 	                                --closeddate,
            30, 	                                --defaultdiscount,
            NULL, 	                                --pricelistidentifierid,
            NULL, 	                                --warehouseid,
            NULL);	                                --viscustomercode
        </sql>
    </changeSet>


    <!-- JCB -->
    <changeSet author="AndyB" id="1.8-integration-test-data-create-warehouses-2">
        <sql>
            INSERT INTO public.warehouse(
            id, manufacturerid, addressid, name, externalid, phonenumber, warehouse_x)
            VALUES (2, 2, 2, 'JCB Warehouse', 2, 332323232, '01');
        </sql>
    </changeSet>

    <changeSet author="AndyB" id="1.8-integration-test-data-create-purchasers-3">
        <sql stripComments="true">
            INSERT INTO public.manufacturersubentity (manufacturersubentityid, name, manufacturerid, parentsubentityid, createddate, createdbyuserid, manufacturersubentitytypeid, modifieddate, modifiedbyuserid, closeddate, defaultdiscount, pricelistidentifierid, warehouseid, viscustomercode)
            VALUES(
            3,	                        --manufacturersubentityid,
            'JCB Dealer 1', 	        --name,
            2, 	                        --manufacturerid,
            NULL, 	                    --parentsubentityid,
            '2018-10-26 14:23:14.014', 	--createddate,
            1,   	                    --createdbyuserid,
            1, 	                        --manufacturersubentitytypeid,
            '2024-07-18 07:37:58.287', 	--modifieddate,
            1, 	                        --modifiedbyuserid,
            NULL, 	                    --closeddate,
            30, 	                    --defaultdiscount,
            NULL, 	                    --pricelistidentifierid,
            NULL, 	                    --warehouseid,
            NULL);	                    --viscustomercode
        </sql>
    </changeSet>

    <changeSet author="AndyB" id="1.8-integration-test-data-create-purchasers-6">
        <sql stripComments="true">
            INSERT INTO public.manufacturersubentity (manufacturersubentityid, name, manufacturerid, parentsubentityid, createddate, createdbyuserid, manufacturersubentitytypeid, modifieddate, modifiedbyuserid, closeddate, defaultdiscount, pricelistidentifierid, warehouseid, viscustomercode)
            VALUES(
            6,	                        --manufacturersubentityid,
            'JCB DealerPlus 1', --name,
            2, 	                        --manufacturerid,
            NULL, 	                    --parentsubentityid,
            '2018-10-26 14:23:14.014', 	--createddate,
            1,   	                    --createdbyuserid,
            4, 	                        --manufacturersubentitytypeid,
            '2024-07-18 07:37:58.287', 	--modifieddate,
            1, 	                        --modifiedbyuserid,
            NULL, 	                    --closeddate,
            30, 	                    --defaultdiscount,
            NULL, 	                    --pricelistidentifierid,
            NULL, 	                    --warehouseid,
            NULL);	                    --viscustomercode
        </sql>
    </changeSet>

    <changeSet author="AndyB" id="1.8-integration-test-data-create-purchasers-7">
        <sql stripComments="true">
            INSERT INTO public.manufacturersubentity (manufacturersubentityid, name, manufacturerid, parentsubentityid, createddate, createdbyuserid, manufacturersubentitytypeid, modifieddate, modifiedbyuserid, closeddate, defaultdiscount, pricelistidentifierid, warehouseid, viscustomercode)
            VALUES(
            7,	                                    --manufacturersubentityid,
            'JCB Customer for DealerPlus 1',        --name,
            2, 	                                    --manufacturerid,
            6, 	                                    --parentsubentityid,
            '2018-10-26 14:23:14.014', 	            --createddate,
            1,   	                                --createdbyuserid,
            4, 	                                    --manufacturersubentitytypeid,
            '2024-07-18 07:37:58.287', 	            --modifieddate,
            1, 	                                    --modifiedbyuserid,
            NULL, 	                                --closeddate,
            30, 	                                --defaultdiscount,
            NULL, 	                                --pricelistidentifierid,
            NULL, 	                                --warehouseid,
            NULL);	                                --viscustomercode
        </sql>
    </changeSet>


    <!-- Liebherr -->
    <changeSet author="AndyB" id="1.8-integration-test-data-create-warehouses-3">
        <sql>
            INSERT INTO public.warehouse(
            id, manufacturerid, addressid, name, externalid, phonenumber, warehouse_x)
            VALUES (3, 3, 1, 'Liebherr Warehouse', 3, '332323232', '01');
        </sql>
    </changeSet>

    <changeSet author="AndyB" id="1.8-integration-test-data-create-purchasers-4">
        <sql stripComments="true">
            INSERT INTO public.manufacturersubentity (manufacturersubentityid, name, manufacturerid, parentsubentityid, createddate, createdbyuserid, manufacturersubentitytypeid, modifieddate, modifiedbyuserid, closeddate, defaultdiscount, pricelistidentifierid, warehouseid, viscustomercode)
            VALUES(
            4,	                        --manufacturersubentityid,
            'Liebherr Dealer 1', 	    --name,
            3, 	                        --manufacturerid,
            NULL, 	                    --parentsubentityid,
            '2018-10-26 14:23:14.014', 	--createddate,
            1,   	                    --createdbyuserid,
            1, 	                        --manufacturersubentitytypeid,
            '2024-07-18 07:37:58.287', 	--modifieddate,
            1, 	                        --modifiedbyuserid,
            NULL, 	                    --closeddate,
            30, 	                    --defaultdiscount,
            NULL, 	                    --pricelistidentifierid,
            3, 	                        --warehouseid,
            NULL);	                    --viscustomercode
        </sql>
    </changeSet>

    <changeSet author="AndyB" id="1.8-integration-test-data-create-purchasers-8">
        <sql stripComments="true">
            INSERT INTO public.manufacturersubentity (manufacturersubentityid, name, manufacturerid, parentsubentityid, createddate, createdbyuserid, manufacturersubentitytypeid, modifieddate, modifiedbyuserid, closeddate, defaultdiscount, pricelistidentifierid, warehouseid, viscustomercode)
            VALUES(
            8,	                        --manufacturersubentityid,
            'Liebherr DealerPlus 1',    --name,
            3, 	                        --manufacturerid,
            NULL, 	                    --parentsubentityid,
            '2018-10-26 14:23:14.014', 	--createddate,
            1,   	                    --createdbyuserid,
            4, 	                        --manufacturersubentitytypeid,
            '2024-07-18 07:37:58.287', 	--modifieddate,
            1, 	                        --modifiedbyuserid,
            NULL, 	                    --closeddate,
            30, 	                    --defaultdiscount,
            NULL, 	                    --pricelistidentifierid,
            3, 	                        --warehouseid,
            NULL);	                    --viscustomercode
        </sql>
    </changeSet>

    <changeSet author="AndyB" id="1.8-integration-test-data-create-purchasers-9">
        <sql stripComments="true">
            INSERT INTO public.manufacturersubentity (manufacturersubentityid, name, manufacturerid, parentsubentityid, createddate, createdbyuserid, manufacturersubentitytypeid, modifieddate, modifiedbyuserid, closeddate, defaultdiscount, pricelistidentifierid, warehouseid, viscustomercode)
            VALUES(
            9,	                                    --manufacturersubentityid,
            'Liebherr Customer for DealerPlus 1',   --name,
            3, 	                                    --manufacturerid,
            8, 	                                    --parentsubentityid,
            '2018-10-26 14:23:14.014', 	            --createddate,
            1,   	                                --createdbyuserid,
            4, 	                                    --manufacturersubentitytypeid,
            '2024-07-18 07:37:58.287', 	            --modifieddate,
            1, 	                                    --modifiedbyuserid,
            NULL, 	                                --closeddate,
            30, 	                                --defaultdiscount,
            NULL, 	                                --pricelistidentifierid,
            3, 	                                    --warehouseid,
            NULL);	                                --viscustomercode
        </sql>
    </changeSet>


    <!-- Terex -->
    <changeSet author="AndyB" id="1.8-integration-test-data-create-warehouses-4">
        <sql>
            INSERT INTO public.warehouse(
            id, manufacturerid, addressid, name, externalid, phonenumber, warehouse_x)
            VALUES (4, 4, 2, 'Terex Warehouse', 4, '332323232', '01');
        </sql>
    </changeSet>

    <changeSet author="AndyB" id="1.8-integration-test-data-create-purchasers-10">
        <sql stripComments="true">
            INSERT INTO public.manufacturersubentity (manufacturersubentityid, name, manufacturerid, parentsubentityid, createddate, createdbyuserid, manufacturersubentitytypeid, modifieddate, modifiedbyuserid, closeddate, defaultdiscount, pricelistidentifierid, warehouseid, viscustomercode)
            VALUES(
            10,	                                    --manufacturersubentityid,
            'Terex Dealer 1',                       --name,
            4, 	                                    --manufacturerid,
            NULL,                                   --parentsubentityid,
            '2018-10-26 14:23:14.014', 	            --createddate,
            1,   	                                --createdbyuserid,
            1, 	                                    --manufacturersubentitytypeid,
            '2024-07-18 07:37:58.287', 	            --modifieddate,
            1, 	                                    --modifiedbyuserid,
            NULL, 	                                --closeddate,
            30, 	                                --defaultdiscount,
            NULL, 	                                --pricelistidentifierid,
            4, 	                                    --warehouseid,
            NULL);	                                --viscustomercode
        </sql>
    </changeSet>

    <changeSet author="AndyB" id="1.8-integration-test-data-create-purchasers-11">
        <sql stripComments="true">
            INSERT INTO public.manufacturersubentity (manufacturersubentityid, name, manufacturerid, parentsubentityid, createddate, createdbyuserid, manufacturersubentitytypeid, modifieddate, modifiedbyuserid, closeddate, defaultdiscount, pricelistidentifierid, warehouseid, viscustomercode)
            VALUES(
            11,	                        --manufacturersubentityid,
            'Terex DealerPlus 1',       --name,
            4, 	                        --manufacturerid,
            NULL, 	                    --parentsubentityid,
            '2018-10-26 14:23:14.014', 	--createddate,
            1,   	                    --createdbyuserid,
            4, 	                        --manufacturersubentitytypeid,
            '2024-07-18 07:37:58.287', 	--modifieddate,
            1, 	                        --modifiedbyuserid,
            NULL, 	                    --closeddate,
            30, 	                    --defaultdiscount,
            NULL, 	                    --pricelistidentifierid,
            4,   	                    --warehouseid,
            NULL);	                    --viscustomercode
        </sql>
    </changeSet>

    <changeSet author="AndyB" id="1.8-integration-test-data-create-purchasers-12">
        <sql stripComments="true">
            INSERT INTO public.manufacturersubentity (manufacturersubentityid, name, manufacturerid, parentsubentityid, createddate, createdbyuserid, manufacturersubentitytypeid, modifieddate, modifiedbyuserid, closeddate, defaultdiscount, pricelistidentifierid, warehouseid, viscustomercode)
            VALUES(
            12,	                                    --manufacturersubentityid,
            'Terex Customer for DealerPlus 1',      --name,
            4, 	                                    --manufacturerid,
            11, 	                                --parentsubentityid,
            '2018-10-26 14:23:14.014', 	            --createddate,
            1,   	                                --createdbyuserid,
            4, 	                                    --manufacturersubentitytypeid,
            '2024-07-18 07:37:58.287', 	            --modifieddate,
            1, 	                                    --modifiedbyuserid,
            NULL, 	                                --closeddate,
            30, 	                                --defaultdiscount,
            NULL, 	                                --pricelistidentifierid,
            4,   	                                --warehouseid,
            NULL);	                                --viscustomercode
        </sql>
    </changeSet>


</databaseChangeLog>
