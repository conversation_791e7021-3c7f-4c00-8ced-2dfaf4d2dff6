package co.cadshare.glue;

import co.cadshare.masterKits.adapters.api.web.PostMasterKitsSearchResponseDto;
import co.cadshare.masterParts.adapters.api.web.PostPurchaserSearchMasterPartsDto;
import co.cadshare.masterParts.boundary.MasterPartSearchResult;
import co.cadshare.modelMgt.publicationCategories.adapters.api.web.GetPublicationCategoryListItemResponseDto;
import co.cadshare.modelMgt.publicationCategories.adapters.api.web.GetPublicationCategoryListResponseDto;
import co.cadshare.modelMgt.publicationCategories.adapters.api.web.PostPublicationCategoriesDto;
import co.cadshare.modelMgt.publications.adapters.api.web.*;
import co.cadshare.response.CustomerManual;
import co.cadshare.shared.core.manufacturer.subentity.ManufacturerSubEntity;
import co.cadshare.shared.core.user.User;
import io.cucumber.spring.ScenarioScope;
import io.restassured.response.Response;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.NotImplementedException;
import org.springframework.stereotype.Component;

import javax.persistence.EntityManager;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import static org.junit.Assert.*;

@Component
@ScenarioScope
@EqualsAndHashCode(callSuper = true)
public class DealerPlusUserIT extends UserIT {

	private Integer purchaserId;

	public DealerPlusUserIT(String port, String clientId, String secret, EntityManager em) {
		super(port, clientId, secret, em);
	}

	@Override
	public void logIn(String emailAddress) {
		user = logInUsingEmailAddress(emailAddress);
		purchaserId = user.getManufacturerSubEntityId();
	}

	@Override
	public User getUser(String userId) {
		String sql = String.format("/dealerplus/user/%s", userId);
		return getResource(sql, User.class);
	}

	@Override
	public void notAuthorisedToGetUser(String userId) {
		String sql = String.format("/dealerplus/user/%s", userId);
		getForbidden(sql);
	}

	@Override
	public PostMasterKitsSearchResponseDto searchForMasterKitByNumber(String identifiedPartNumber){
		return searchForMasterKitByNumber("purchasers", identifiedPartNumber, "EN", purchaserId);
	}

	@Override
	public List<ManufacturerSubEntity> getPurchasersList() {
		return getResourceList(String.format("/dealerplus/dealer/%s/manufacturersubentities", purchaserId), ManufacturerSubEntity.class);
	}


	public MasterPartSearchResult searchForMasterPartByNumber(String identifiedPartNumber) {
		return searchForMasterPartByNumber(identifiedPartNumber, "EN");
	}

	public MasterPartSearchResult searchForMasterPartByDescriptionUsingLanguage(String identifiedPartDescription, String languageCode) {
		String sql = String.format("/manufacturer-sub-entities/%s/master-parts/search?language=%s", purchaserId, languageCode);
		PostPurchaserSearchMasterPartsDto request = new PostPurchaserSearchMasterPartsDto();
		request.setPartDescription(identifiedPartDescription);
		return actionResource(sql, request, MasterPartSearchResult.class);
	}

	@Override
	public GetPublicationCategoryListResponseDto getPublicationCategoriesList() {
		return getResource(String.format("/purchasers/%s/publication-categories", purchaserId), GetPublicationCategoryListResponseDto.class);
	}

	@Override
	public void assignPublicationsToDealer(String publicationNames, String dealerName) {
		String url = String.format("/purchasers/%s/publications/assign-to-purchaser", purchaserId);
		assignPublicationsToCustomer(url, publicationNames, dealerName);
	}

	@Override
	public GetPublicationResponseDto getPublicationFromName(String publicationName) {
		int publicationId = getPublicationIdFromName(publicationName);
		String url = String.format("/purchasers/%s/publications/%s?language=EN", purchaserId, publicationId);
		return getResource(url, GetPublicationResponseDto.class);
	}

	@Override
	protected int getPublicationIdFromName(String publicationName) {
		List<GetPublicationListItemResponseDto> publications = getPublications();
		assertTrue(publications != null && !publications.isEmpty());
		Optional<GetPublicationListItemResponseDto> publicationExists = publications
				.stream()
				.filter(p -> p.getName().equals(publicationName))
				.findFirst();
		assertTrue(publicationExists.isPresent());
		return publicationExists.get().getId();
	}

	private List<GetPublicationListItemResponseDto> getPublications() {
		String url = String.format("/purchasers/%s/publications", purchaserId);
		GetPublicationsListResponseDto response = getResource(url, GetPublicationsListResponseDto.class);
		return response.getPublications();
	}

	private MasterPartSearchResult searchForMasterPartByNumber(String identifiedPartNumber, String languageCode){
		String sql = String.format("/manufacturer-sub-entities/%s/master-parts/search?language=%s", purchaserId, languageCode);
		PostPurchaserSearchMasterPartsDto request = new PostPurchaserSearchMasterPartsDto();
		request.setPartNumber(identifiedPartNumber);
		return actionResource(sql, request, MasterPartSearchResult.class);
	}

	private void assignPublicationsToCustomer(String url, String publicationNames, String dealerName) {
		List<ManufacturerSubEntity> purchasers = getPurchasersList();
		Optional<ManufacturerSubEntity> customer = purchasers.stream().filter(p -> p.getName().equals(dealerName)).findFirst();
		assertTrue(customer.isPresent());

		//get publication Ids based on names
		List<Integer> publicationIds = new ArrayList<Integer>();
		String[] publicationNamesArray = publicationNames.split(",");
		for (String pn : publicationNamesArray) {
			GetPublicationResponseDto publication = getPublicationFromName(pn);
			publicationIds.add(publication.getId());
		}

		PostAssignPublicationsDto dto = new PostAssignPublicationsDto();
		dto.setPublications(publicationIds);
		dto.setPurchaserId(customer.get().getManufacturerSubEntityId());

		//post
		Response response = postResource(url, dto);
		assertNotNull(response);
		assertEquals(200, response.getStatusCode());
	}

}
