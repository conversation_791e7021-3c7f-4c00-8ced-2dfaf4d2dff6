package co.cadshare.glue;

import co.cadshare.masterKits.adapters.api.web.PostMasterKitsSearchResponseDto;
import co.cadshare.masterParts.adapters.api.web.PostPurchaserSearchMasterPartsDto;
import co.cadshare.masterParts.boundary.MasterPartSearchResult;
import co.cadshare.shared.core.user.User;
import io.cucumber.spring.ScenarioScope;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.NotImplementedException;
import org.springframework.stereotype.Component;

import javax.persistence.EntityManager;

@Component
@ScenarioScope
@EqualsAndHashCode(callSuper = true)
public class DealerPlusUserIT extends UserIT {

	private Integer purchaserId;

	public DealerPlusUserIT(String port, String clientId, String secret, EntityManager em) {
		super(port, clientId, secret, em);
	}

	@Override
	public void logIn(String emailAddress) {
		user = logInUsingEmailAddress(emailAddress);
		purchaserId = user.getManufacturerSubEntityId();
	}

	@Override
	public User getUser(String userId) {
		String sql = String.format("/dealerplus/user/%s", userId);
		return getResource(sql, User.class);
	}

	@Override
	public void notAuthorisedToGetUser(String userId) {
		String sql = String.format("/dealerplus/user/%s", userId);
		getForbidden(sql);
	}

	@Override
	public PostMasterKitsSearchResponseDto searchForMasterKitByNumber(String identifiedPartNumber){
		return searchForMasterKitByNumber("purchasers", identifiedPartNumber, "EN", purchaserId);
	}

	public MasterPartSearchResult searchForMasterPartByNumber(String identifiedPartNumber) {
		return searchForMasterPartByNumber(identifiedPartNumber, "EN");
	}

	public MasterPartSearchResult searchForMasterPartByDescriptionUsingLanguage(String identifiedPartDescription, String languageCode) {
		String sql = String.format("/manufacturer-sub-entities/%s/master-parts/search?language=%s", purchaserId, languageCode);
		PostPurchaserSearchMasterPartsDto request = new PostPurchaserSearchMasterPartsDto();
		request.setPartDescription(identifiedPartDescription);
		return actionResource(sql, request, MasterPartSearchResult.class);
	}

	private MasterPartSearchResult searchForMasterPartByNumber(String identifiedPartNumber, String languageCode){
		String sql = String.format("/manufacturer-sub-entities/%s/master-parts/search?language=%s", purchaserId, languageCode);
		PostPurchaserSearchMasterPartsDto request = new PostPurchaserSearchMasterPartsDto();
		request.setPartNumber(identifiedPartNumber);
		return actionResource(sql, request, MasterPartSearchResult.class);
	}

}
