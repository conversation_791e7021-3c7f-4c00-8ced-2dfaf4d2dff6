package co.cadshare.models.boundary;

import co.cadshare.models.core.Model;
import co.cadshare.shared.boundary.GetService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.util.List;

@Service
public class GetModelService extends GetService<Model, Integer> {

    private ModelQueryPort complexQueryPort;

    @Autowired
    public GetModelService(ModelQueryPort queryPort) {
        super(queryPort);
        this.complexQueryPort = queryPort;
    }

    public List<Model> getModelsForProduct(int id){
        return this.complexQueryPort.getModelsListForProduct(id);
    }

}
