package co.cadshare.addresses.adapters.database;

import co.cadshare.addresses.adapters.ext.erp.vis.ExternalAddressMapper;
import co.cadshare.addresses.boundary.AddressCommandPort;
import co.cadshare.addresses.boundary.AddressQueryPort;
import co.cadshare.addresses.boundary.ManufacturerQueryPort;
import co.cadshare.addresses.core.ExternalContact;
import co.cadshare.addresses.core.Manufacturer;
import co.cadshare.addresses.core.Address;
import co.cadshare.addresses.core.ExternalAddress;
import co.cadshare.addresses.core.ContactName;
import co.cadshare.exceptions.NotFoundException;
import co.cadshare.orders.adapters.ext.erp.vis.ExternalAddressContactKeys;
import co.cadshare.shared.adapters.database.addresses.ExternalAddressEntity;
import co.cadshare.utils.AuditUtilsExtension;
import co.cadshare.utils.CycleAvoidingMappingContext;
import co.cadshare.utils.ObjectUtilsExtension;
import lombok.experimental.ExtensionMethod;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.persistence.EntityNotFoundException;
import javax.transaction.Transactional;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Service
@Transactional
@ExtensionMethod({AuditUtilsExtension.class, ObjectUtilsExtension.class})
public class AddressDataGateway implements ManufacturerQueryPort, AddressQueryPort, AddressCommandPort {

    private final String SAVE_ERROR_MSG = "An error occurred while %s Address id [%s], externalRefId [%s] | Cause: %s";
    private final AddressDao addressDao;
    private final ExternalAddressRepo repo;
    private final AddressesManufacturerComplexQueryRepo manufacturerQueryRepo;
	private final ExternalContactRepo contactRepo;
	private final ContactNameDao contactNameDao;
	private final ExternalAddressRepo externalAddressRepo;
    private final Logger logger = LoggerFactory.getLogger(getClass());

    @Autowired
    public AddressDataGateway(AddressDao addressDao, ExternalAddressRepo repo,
                              AddressesManufacturerComplexQueryRepo manufacturerQueryRepo,
                              ExternalContactRepo contactRepo,
                              ContactNameDao contactNameDao,
                              ExternalAddressRepo externalAddressRepo) {
        this.addressDao = addressDao;
        this.repo = repo;
        this.manufacturerQueryRepo = manufacturerQueryRepo;
	    this.contactRepo = contactRepo;
	    this.contactNameDao = contactNameDao;
	    this.externalAddressRepo = externalAddressRepo;
    }


    @Override
    public void createAddress(ExternalAddress address) {
        try {
            ExternalAddressEntity entity = ExternalAddressEntityMapper.Instance.coreToEntity(address, new CycleAvoidingMappingContext());
            entity.addCreatedByAuditEntry();
            entity.getUserAddressMaps().forEach(map -> map.addCreatedByAuditEntry());
			entity.getContactAddressMaps().forEach(cam -> {
				cam.addCreatedByAuditEntry();
			//	cam.getContact().getUserContactMaps().forEach(ucm -> ucm.addCreatedByAuditEntry());
			});
	        repo.save(entity);
        }
       catch (Exception e) {
                logger.error(String.format(SAVE_ERROR_MSG, "inserting",
                        address.getId(), address.getExternalRefId(), e.getMessage()));
            }
    }

    @Override
    public void updateAddress(ExternalAddress address) {
        try {
            ExternalAddressEntity entity = ExternalAddressEntityMapper.Instance.coreToEntity(address, new CycleAvoidingMappingContext());
            entity.addModifiedByAuditEntry();
            entity.getUserAddressMaps().forEach(map-> {
                if(map.getId().isNull())
                    map.addCreatedByAuditEntry();
                else
                    map.addModifiedByAuditEntry();
            });
			entity.getContactAddressMaps().forEach(map-> {
				if(map.getId().isNull()) {
					map.addCreatedByAuditEntry();
					/*
					map.getContact().getUserContactMaps().forEach(ucm -> {
						if(ucm.getId().isNull())
							ucm.addCreatedByAuditEntry();
						else
							ucm.addModifiedByAuditEntry();
					});

					 */
				} else {
		            map.addModifiedByAuditEntry();
		            /*
					map.getContact().getUserContactMaps().forEach(ucm -> {
			            if(ucm.getId().isNull())
				            ucm.addCreatedByAuditEntry();
			            else
				            ucm.addModifiedByAuditEntry();
		            });

		             */
	            }
			});
            repo.save(entity);
        }
        catch (Exception e) {
            logger.error(String.format(SAVE_ERROR_MSG, "updating",
                    address.getId(), address.getExternalRefId(), e.getMessage()));
        }
    }

	@Override
	public ExternalContact createContact(ExternalContact contact) {
		try {
			AddressesContactEntity entity = AddressesContactEntityMapper.Instance.coreToEntity(contact);
			contactRepo.save(entity);
			return AddressesContactEntityMapper.Instance.entityToExternalCore(entity);
		}
		catch (Exception e) {
			logger.error(String.format(SAVE_ERROR_MSG, "inserting",
					contact.getId(), contact.getExternalRefId(), e.getMessage()));
		}
		return contact;
	}

	@Override
	public void updateContact(ExternalContact contact) {

	}

	@Override
    public List<Manufacturer> getManufacturersWithExternalAddresses() {
        List<AddressesManufacturerEntity> entities = manufacturerQueryRepo.getManufacturersWithExternalAddresses();
        List<Manufacturer> cores = AddressesManufacturerEntityMapper.Instance.entitiesToCores(entities);
        return cores;
    }

	@Override
	public ExternalAddressContactKeys getExternalAddressContactKeys(int addressId, int contactId) {
		ExternalAddressEntity address = repo.getOne(addressId);
		ExternalAddressContactKeys keys = new ExternalAddressContactKeys();
		keys.setExternalAddressId(address.getExternalRefId());
		Optional<AddressContactMapEntity> map = address.getContactAddressMaps().stream()
				.filter(cam -> cam.getContact().getId() == contactId)
				.findFirst();
		if(map.isPresent()) {
			keys.setExternalAddressContactCompositeId(map.get().getCompositeExternalRefId());
			keys.setExternalContactId(map.get().getContact().getExternalRefId());
			return keys;
		} else
			throw new NotFoundException("Address & Contact combination not found");
	}

    @Override
    public Address getAddressById(int addressId) {
        return addressDao.getAddressById(addressId);
    }

    @Override
    public List<Address> getAddressesForUser(int userId) {
        return addressDao.getAddressesForUser(userId);
    }

    @Override
    public Address getAddressForWareHouse(int warehouseId) {
        return addressDao.getAddressForWareHouse(warehouseId);
    }

    @Override
    public Integer createUserAddress(int userId, Address address) {
        return addressDao.createUserAddress(userId, address);
    }

    @Override
    public int updateUserAddress(int userId, Address address) {
        return addressDao.updateUserAddress(userId, address);
    }

    @Override
    public Boolean deleteUserAddress(int userId, int addressId) {
        return addressDao.deleteUserAddress(userId, addressId);
    }

    @Override
    public Integer createManufacturerAddress(int manufacturerId, Address address) {
        return addressDao.createManufacturerAddress(manufacturerId, address);
    }

	@Override
	public List<ContactName> getAllNamesForUser(int userId) {
		return contactNameDao.getNamesForUser(userId);
	}

	@Override
	public List<ContactName> getAllNamesForAddressForUser(int addressId, int userId) {
		try {
			ExternalAddressEntity address = externalAddressRepo.getOne(addressId);
			boolean hasUser = address.getUserAddressMaps().stream()
					.anyMatch(map -> map.getUser().getId() == userId);
			if (!hasUser)
				return Collections.emptyList();

			List<AddressContactMapEntity> addressContactMaps = address.getContactAddressMaps();
			List<AddressesContactEntity> contactEntities = addressContactMaps.stream()
					.filter(cam -> !cam.isDeleted())
					.flatMap(cam -> {
						if (cam.getContact() != null) {
							return Stream.of(cam.getContact());
						} else {
							return Stream.empty();
						}
					})
					.collect(Collectors.toList());

			return AddressesContactEntityMapper.Instance.entitiesToCores(contactEntities);
		} catch (EntityNotFoundException e) {
			return new ArrayList<>();
		}
	}

}
