package co.cadshare.modelMgt.publications.adapters.database;

import com.blazebit.persistence.view.EntityView;
import com.blazebit.persistence.view.IdMapping;
import com.blazebit.persistence.view.Mapping;

import java.sql.Timestamp;
import java.util.List;

@EntityView(PublicationsModelEntity.class)
public interface PublicationsViewableEntityView {

    @IdMapping("modelId")
    int getId();

    @Mapping("modelName")
    String getName();

    @Mapping(value = "product.range.assignedPurchasers")
    List<PublicationsPurchaserEntityView> getPurchasersAssignedToRange();

    @Mapping("isSetupComplete")
    boolean isSetupComplete();

	@Mapping("retries")
	int getRetries();

	@Mapping("createdByUserId")
	Integer getCreatedByUserId();

	@Mapping("modifiedByUserId")
	Integer getModifiedByUserId();

	@Mapping("createdDate")
	Timestamp getCreatedDate();

	@Mapping("modifiedDate")
	Timestamp getModifiedDate();

    @Mapping(value="product")
    PublicationsProductEntityView getProduct();
}
