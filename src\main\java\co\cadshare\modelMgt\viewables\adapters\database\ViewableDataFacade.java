package co.cadshare.modelMgt.viewables.adapters.database;

import co.cadshare.exceptions.NoCadshareDataFoundException;
import co.cadshare.modelMgt.publications.adapters.database.PublicationsViewableEntityView;
import co.cadshare.modelMgt.shared.boundary.ViewableQueryPort;
import co.cadshare.modelMgt.shared.core.Viewable;
import org.springframework.stereotype.Service;

import javax.persistence.NoResultException;
import java.util.Collections;
import java.util.List;

@Service
public class ViewableDataFacade implements ViewableQueryPort {

	private final ViewableQueryRepo viewableQueryRepo;

	public ViewableDataFacade(ViewableQueryRepo viewableQueryRepo) {
		this.viewableQueryRepo = viewableQueryRepo;
	}

	@Override
	public Viewable getViewable(int viewableId) throws NoCadshareDataFoundException {
		try {
			PublicationsViewableEntityView viewableEntity = this.viewableQueryRepo.getById(viewableId);
			return ViewableEntityViewMapper.Instance.entityToCore(viewableEntity);
		} catch (NoResultException e) {
			throw new NoCadshareDataFoundException("Viewable not found");
		}
	}

	@Override
	public Viewable get(Integer id) {
		return null;
	}

	@Override
	public List<Viewable> getList() {
		return Collections.emptyList();
	}

	@Override
	public List<Viewable> getListForManufacturer(int manufacturerId) {
		List<PublicationsViewableEntityView> viewableEntities = this.viewableQueryRepo.getAllViewablesForManufacturer(manufacturerId);
		return ViewableEntityViewMapper.Instance.entitiesToCores(viewableEntities);
	}

	@Override
	public List<Viewable> searchViewables(int manufacturerId, String searchTerm) {
		List<PublicationsViewableEntityView> viewableEntities = this.viewableQueryRepo.searchViewables(manufacturerId, searchTerm);
		return ViewableEntityViewMapper.Instance.entitiesToCores(viewableEntities);
	}

	@Override
	public List<Viewable> filterViewablesByProduct(int manufacturerId, int productId) {
		List<PublicationsViewableEntityView> viewableEntities = this.viewableQueryRepo.filterViewablesByProduct(manufacturerId, productId);
		return ViewableEntityViewMapper.Instance.entitiesToCores(viewableEntities);
	}
}
