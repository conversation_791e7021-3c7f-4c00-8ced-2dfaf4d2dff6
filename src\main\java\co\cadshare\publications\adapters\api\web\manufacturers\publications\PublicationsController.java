package co.cadshare.publications.adapters.api.web.manufacturers.publications;

import co.cadshare.publications.boundary.PublishPublicationService;
import co.cadshare.shared.core.user.User;
import co.cadshare.publications.boundary.CreatePublicationService;
import co.cadshare.publications.boundary.GetPublicationService;
import co.cadshare.publications.core.Publication;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.*;

import java.time.Instant;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import static org.bouncycastle.pqc.math.linearalgebra.IntegerFunctions.isPrime;


@RestController
@RequestMapping("/manufacturers/{manufacturer-id}/publications")
public class PublicationsController {
    private final CreatePublicationService createPublicationService;
    private final GetPublicationService getPublicationService;
    private final PublishPublicationService publishPublicationService;

    @Autowired
    public PublicationsController(CreatePublicationService createPublicationService,
                                  GetPublicationService getPublicationService,
                                  PublishPublicationService publishPublicationService){
        this.createPublicationService = createPublicationService;
        this.getPublicationService = getPublicationService;
        this.publishPublicationService = publishPublicationService;
    }

    @PostMapping(path = "/create-and-publish", consumes = "application/json", produces = "application/json")
    @PreAuthorize("hasRole('ROLE_MANUFACTURER')")
    @ResponseStatus(HttpStatus.OK)
    @Operation(summary = "Create and unpublish a Publication")
    public ResponseEntity<PostPublicationResponseDto> createAndPublishPublication(@PathVariable("manufacturer-id") int manufacturerId,
                                                                        @AuthenticationPrincipal User currentUser,
                                                                        @RequestBody PostCreateAndPublishRequestDto createAndPublish) throws Exception {

        Integer createdId = this.publishPublicationService.createAndAutoPublishPublication(currentUser, createAndPublish.getViewableId());
        return new ResponseEntity<>(new PostPublicationResponseDto(createdId), HttpStatus.OK);
    }

    @PostMapping(consumes = "application/json", produces = "application/json")
    @PreAuthorize("hasRole('ROLE_MANUFACTURER')")
    @ResponseStatus(HttpStatus.OK)
    @Operation(summary = "Create an unpublished Publication with a name")
    public ResponseEntity<PostPublicationResponseDto> createPublication(@PathVariable("manufacturer-id") int manufacturerId,
                                                                        @AuthenticationPrincipal User currentUser,
                                                                        @RequestBody PostPublicationRequestDto postPublication) {
        //TODO: factor manufacturerId  currentUser into publication (either object or command object?)
        Integer createdId = this.createPublicationService.create(currentUser, PublicationMapper.Instance.postRequestDtoToPublication(postPublication));
        return new ResponseEntity<>(new PostPublicationResponseDto(createdId), HttpStatus.OK);
    }

    @PutMapping(path = "/{publication-id}", consumes = "application/json")
    @PreAuthorize("hasRole('ROLE_MANUFACTURER')")
    @ResponseStatus(HttpStatus.OK)
    @Operation(summary = "Maintain the details of a Publication")
    public ResponseEntity updatePublication(@PathVariable("manufacturer-id") int manufacturerId,
                                         @PathVariable("publication-id") int publicationId,
                                         @AuthenticationPrincipal User currentUser,
                                         @RequestBody PutPublicationRequestDto putPublication) {
        return new ResponseEntity<>(HttpStatus.OK);
    }

    @DeleteMapping(path = "/{publication-id}")
    @PreAuthorize("hasRole('ROLE_MANUFACTURER')")
    @ResponseStatus(HttpStatus.OK)
    @Operation(summary = "Delete a Publication")
    public ResponseEntity deletePublication(@PathVariable("manufacturer-id") int manufacturerId,
                                            @PathVariable("publication-id") int publicationId,
                                            @AuthenticationPrincipal User currentUser) {
        return new ResponseEntity<>(HttpStatus.OK);
    }

    @GetMapping(path = "/{publication-id}", produces = "application/json")
    //@PreAuthorize("hasRole('ROLE_MANUFACTURER')")
    @ResponseStatus(HttpStatus.OK)
    @Operation(summary = "Get the details of a specific Publication")
    public ResponseEntity<GetPublicationResponseDto> getPublication(@PathVariable("manufacturer-id") int manufacturerId,
                                            @PathVariable("publication-id") int publicationId){//,
                                            //@AuthenticationPrincipal User currentUser) {

        //Publication publication = this.getPublicationService.get(publicationId);
        //GetPublicationResponseDto getResponseDto = publicationMapper.publicationToGetResponseDto(publication);
        GetPublicationResponseDto getResponseDto = new GetPublicationResponseDto() {{
            setPublicationId(publicationId);
            setPublicationName("Sample Publication");
            setPublicationStatus("Published");
            setCoverImage("https://media.istockphoto.com/id/887465766/photo/road-construction-machinery-on-the-construction-of-highway.jpg?s=612x612&w=0&k=20&c=75DpFIj4gUJ7gPodIGAcKkpS2JScsoeQLGcGRxFRgLg=");
            List<Integer> serialNumbers = new ArrayList<>();
            for (Integer i = 0; i < publicationId; i++) {
                serialNumbers.add(i);
            }
            setSerialNumbers(serialNumbers);
            List<SerialNumberRangeDto> serialNumberRanges = new ArrayList<>();
            for (Integer i = 1; i < publicationId  + 1; i++) {
                Integer finalI = i;
                SerialNumberRangeDto serialNumberRange = new SerialNumberRangeDto() {{
                    setStartSerialNumber(finalI + 5);
                    setEndSerialNumber(finalI + 10);
                }};
                serialNumberRanges.add(serialNumberRange);
            }
            setSerialNumberRanges(serialNumberRanges);
            List<PublicationCustomerDto> customers = new ArrayList<>();
            for (Integer i = 1; i < publicationId  + 1; i++) {
                Integer finalI = i;
                PublicationCustomerDto customer = new PublicationCustomerDto() {{
                    setId(finalI);
                    setName("Sample Customer Name".concat(finalI.toString()));
                    setSelectedForPublication(isPrime(finalI));
                }};
                customers.add(customer);
            }
            setCustomers(customers);
            List<PublicationViewableDto> viewables = new ArrayList<>();
            for (Integer i = 1; i < publicationId  + 1; i++) {
                Integer finalI = i;
                PublicationViewableDto viewable = new PublicationViewableDto() {{
                    setId(finalI);
                    setName("Sample Viewable Name".concat(finalI.toString()));
                    setSelectedForPublication(isPrime(finalI));
                    setRange("Sample Range Name ".concat(finalI.toString()));
                    setProduct("Sample Product Name ".concat(finalI.toString()));
                }};
                viewables.add(viewable);
            }
            setViewables(viewables);
            List<PublicationKitDto> kits = new ArrayList<>();
            for (Integer i = 1; i < publicationId  + 1; i++) {
                Integer finalI = i;
                PublicationKitDto kit = new PublicationKitDto() {{
                    setId(finalI);
                    setName("Sample Kit Name".concat(finalI.toString()));
                    setSelectedForPublication(isPrime(finalI));
                }};
                kits.add(kit);
            }
            setKits(kits);
            List<PublicationTechDocDto> techDocs = new ArrayList<>();
            for (Integer i = 1; i < publicationId  + 1; i++) {
                Integer finalI = i;
                PublicationTechDocDto item = new PublicationTechDocDto() {{
                    setId(finalI);
                    setName("Sample Tech Doc Name".concat(finalI.toString()));
                    setSelectedForPublication(isPrime(finalI));
                }};
                techDocs.add(item);
            }
            setTechDocs(techDocs);
            List<PublicationVideoDto> videos = new ArrayList<>();
            for (Integer i = 1; i < publicationId  + 1; i++) {
                Integer finalI = i;
                PublicationVideoDto item = new PublicationVideoDto() {{
                    setId(finalI);
                    setName("Sample Video Name".concat(finalI.toString()));
                    setSelectedForPublication(isPrime(finalI));
                }};
                videos.add(item);
            }
            setVideos(videos);
            setLastPublishedDateTime(Date.from( Instant.now()));
        }};

        return new ResponseEntity<>(getResponseDto, HttpStatus.OK);
    }

    @GetMapping(produces = "application/json")
    @PreAuthorize("hasRole('ROLE_MANUFACTURER')")
    @ResponseStatus(HttpStatus.OK)
    @Operation(summary = "Get the summary details of a list of Publications")
    public ResponseEntity<GetPublicationsListResponseDto> getPublications(@PathVariable("manufacturer-id") int manufacturerId,
                                                                          @AuthenticationPrincipal User currentUser) {
        List<Publication> publications = this.getPublicationService.getSome(manufacturerId);
        List<PublicationListItemDto> publicationDtos = PublicationMapper.Instance.publicationsToPublicationListItemDtos(publications);
        GetPublicationsListResponseDto response = new GetPublicationsListResponseDto() {{
            setPublications(publicationDtos);
        }};
        return new ResponseEntity<>(response, HttpStatus.OK);
    }
}
