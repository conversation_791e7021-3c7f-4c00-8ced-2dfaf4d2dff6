package co.cadshare.glue;

import co.cadshare.domainmodel.part.Part;
import co.cadshare.domainmodel.part.PartViewerDetails;
import co.cadshare.masterKits.adapters.api.web.GetMasterKitListItemResponseDto;
import co.cadshare.masterKits.adapters.api.web.GetMasterKitListResponseDto;
import co.cadshare.masterKits.adapters.api.web.PostMasterKitsSearchResponseDto;
import co.cadshare.masterParts.adapters.api.web.PostManufacturerSearchMasterPartsDto;
import co.cadshare.masterParts.adapters.api.web.PostSupersedeRequestDto;
import co.cadshare.masterParts.adapters.api.web.SupersessionHistoryDto;
import co.cadshare.masterParts.boundary.MasterPartSearchResult;
import co.cadshare.masterParts.core.MasterPartDetails;
import co.cadshare.media.adapters.api.web.GetImageResponseDto;
import co.cadshare.modelMgt.models.core.Model;
import co.cadshare.modelMgt.products.adapters.api.GetProductListResponseDto;
import co.cadshare.modelMgt.products.adapters.api.web.GetManufacturerProductListResponseDto;
import co.cadshare.modelMgt.publicationCategories.adapters.api.web.*;
import co.cadshare.modelMgt.publications.adapters.api.web.*;
import co.cadshare.modelMgt.ranges.adapters.api.web.GetProductRangeListResponseDto;
import co.cadshare.modelMgt.ranges.adapters.api.web.ProductRangeListItemDto;
import co.cadshare.modelMgt.viewables.adapters.api.web.*;
import co.cadshare.orders.adapters.api.web.GetOrderListResponseDto;
import co.cadshare.shared.core.manufacturer.subentity.ManufacturerSubEntity;
import co.cadshare.users.adapters.api.web.manufacturer.GetManufacturerUserListResponseDto;
import co.cadshare.utils.ObjectUtilsExtension;
import io.cucumber.spring.ScenarioScope;
import io.restassured.response.Response;
import lombok.EqualsAndHashCode;
import lombok.experimental.ExtensionMethod;
import org.springframework.stereotype.Component;

import javax.persistence.EntityManager;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import static org.junit.Assert.*;


@Component
@ScenarioScope
@EqualsAndHashCode(callSuper = true)
@ExtensionMethod(ObjectUtilsExtension.class)
public class ManufacturerUserIT extends UserIT {

	public ManufacturerUserIT(String port, String clientId, String secret, EntityManager em) {
		super(port, clientId, secret, em);
	}

	@Override
    public void logIn(String emailAddress) {
        user = logInUsingEmailAddress(emailAddress);
    }

	@Override
	public MasterPartSearchResult exactSearchForMasterPartByNumber(String identifiedPartNumber) {
		PostManufacturerSearchMasterPartsDto request = new PostManufacturerSearchMasterPartsDto();
		request.setPartNumber(identifiedPartNumber);
		request.setExactMatch(true);
		return searchForMasterPartByNumber(request, "EN");
	}

    @Override
    public MasterPartSearchResult searchForMasterPartByNumber(String identifiedPartNumber) {
	    PostManufacturerSearchMasterPartsDto request = new PostManufacturerSearchMasterPartsDto();
	    request.setPartNumber(identifiedPartNumber);
		return searchForMasterPartByNumber(request, "EN");
    }

	@Override
	public MasterPartSearchResult searchForMasterPartByNumberUsingLanguage(String identifiedPartNumber, String languageCode) {
		PostManufacturerSearchMasterPartsDto request = new PostManufacturerSearchMasterPartsDto();
		request.setPartNumber(identifiedPartNumber);
		return searchForMasterPartByNumber(request, languageCode);
	}

	@Override
	public MasterPartSearchResult searchForMasterPartByDescriptionUsingLanguage(String identifiedPartDesc, String languageCode) {
		String sql = String.format("/manufacturers/%s/master-parts/search?language=%s", manufacturerId, languageCode);
		PostManufacturerSearchMasterPartsDto request = new PostManufacturerSearchMasterPartsDto();
		request.setPartDescription(identifiedPartDesc);
		return actionResource(sql, request, MasterPartSearchResult.class);
	}

    @Override
    public int searchForMasterKit(String identifiedPartNumber) {
        String sql = String.format("/manufacturers/%s/master-part-kits?language=EN", manufacturerId);
        GetMasterKitListResponseDto responseDto = getResource(sql, GetMasterKitListResponseDto.class);
        Optional<GetMasterKitListItemResponseDto> masterKit = responseDto.getMasterKits().stream().filter(p -> p.getPartNumber().equals(identifiedPartNumber)).findFirst();
        return masterKit.map(GetMasterKitListItemResponseDto::getId).orElse(0);
    }

	@Override
	public PostMasterKitsSearchResponseDto searchForMasterKitByNumber(String identifiedPartNumber){
		return searchForMasterKitByNumber("manufacturers", identifiedPartNumber, "EN", manufacturerId);
	}

	@Override
	public PostMasterKitsSearchResponseDto searchForMasterKitByDescriptionUsingLanguage(String masterKitDescSearch, String languageCode) {
		return searchForMasterKitByDesc("manufacturers", masterKitDescSearch, languageCode, manufacturerId);
	}

	@Override
    public void updatePurchaser(String newName, String purchaserId) {
        ManufacturerSubEntity updatedPurchaser = UserIT.buildPurchaser(newName, ManufacturerSubEntity.ManufacturerSubEntityType.DEALER);
        updatedPurchaser.setManufacturerSubEntityId(Integer.parseInt(purchaserId));
        putResource("/manufacturersubentity", updatedPurchaser);
    }

	@Override
    public void deletePurchaser(String purchaserId) {
        deleteResource("/manufacturersubentity/".concat(purchaserId));
    }

	@Override
    public List<ManufacturerSubEntity> getPurchasersList() {
        return getResourceList(String.format("/manufacturer/%s/manufacturersubentities", manufacturerId), ManufacturerSubEntity.class);
    }

	@Override
    public ManufacturerSubEntity getPurchaser(String purchaserId) {
        return getResource(String.format("/manufacturersubentity/%s", purchaserId), ManufacturerSubEntity.class);
    }

	@Override
    public String createPurchaser(String name, ManufacturerSubEntity.ManufacturerSubEntityType type) {
        ManufacturerSubEntity purchaser = buildPurchaser(name, type);
        Response response = postResource("/manufacturersubentity", purchaser);
        return response.asString();
    }

	@Override
	public GetPublicationCategoryListResponseDto getPublicationCategoriesList() {
		return getResource(String.format("/manufacturers/%s/publication-categories", manufacturerId), GetPublicationCategoryListResponseDto.class);
	}

	@Override
	public void verifyPublicationCategoryExists(String name) {
		GetPublicationCategoryListResponseDto publicationCategories = getPublicationCategoriesList();
		boolean found = publicationCategories.getPublicationCategories().stream()
				.anyMatch(pc -> pc.getName().equals(name));
		assertTrue(found);
	}

	@Override
	public String createPublicationCategory(String name) {
		PostPublicationCategoryRequestDto publicationCategory = new PostPublicationCategoryRequestDto();
		publicationCategory.setName(name);
		Response response = postResource(String.format("/manufacturers/%s/publication-categories", manufacturerId), publicationCategory);
		return response.asString();
	}

	@Override
	public GetPublicationCategoryResponseDto getPublicationCategory(String publicationCategoryId) {
		return getResource(String.format("/manufacturers/%s/publication-categories/%s", manufacturerId, publicationCategoryId), GetPublicationCategoryResponseDto.class);
	}

	@Override
	public GetPublicationCategoryResponseDto searchForPublicationCategoryByName(String name) {
		GetPublicationCategoryListResponseDto publicationCategories = getPublicationCategoriesList();
		GetPublicationCategoryListItemResponseDto publicationCategory = publicationCategories.getPublicationCategories().stream()
				.filter(pc -> pc.getName().equals(name))
				.findFirst()
				.orElse(null);
		if(publicationCategory.isNull())
			return null;
		return getPublicationCategory(publicationCategory.getId().toString());
	}

	@Override
	public void updatePublicationCategory(String newName, String publicationCategoryId) {
		PutPublicationCategoryRequestDto publicationCategory = new PutPublicationCategoryRequestDto();
		publicationCategory.setName(newName);
		putResource(String.format("/manufacturers/%s/publication-categories/%s", manufacturerId, publicationCategoryId), publicationCategory);
	}

	@Override
	public void deletePublicationCategory(String publicationCategoryId) {
		deleteResource(String.format("/manufacturers/%s/publication-categories/%s", manufacturerId, publicationCategoryId));
	}

	@Override
	public void cantDeletePublicationCategory(String publicationCategoryId) {
		deleteResourceWithHttpErrorCode(String.format("/manufacturers/%s/publication-categories/%s", manufacturerId, publicationCategoryId), 422);
	}


	@Override
    public GetOrderListResponseDto getEnquiries() {
        String url = String.format("/manufacturers/%s/orders", manufacturerId);
        return getResource(url, GetOrderListResponseDto.class);
    }

	@Override
    public SupersessionHistoryDto getSupersessionHistoryForPart(String supersessionMasterPart) {
        MasterPartSearchResult supersededMasterPart = searchForMasterPartByNumber(supersessionMasterPart);
        String sql = String.format("manufacturers/%s/master-parts/%s/supersession-history",
                manufacturerId, supersededMasterPart.getMasterParts().get(0).getMasterPartId());
        return getResource(sql, SupersessionHistoryDto.class);
    }

	@Override
    public void doNotGetSupersessionHistoryForPart(String supersessionMasterPart) {
        MasterPartSearchResult supersededMasterPart = searchForMasterPartByNumber(supersessionMasterPart);
        String sql = String.format("manufacturers/%s/master-parts/%s/supersession-history",
                manufacturerId, supersededMasterPart.getMasterParts().get(0).getMasterPartId());
        getNotFound(sql);
    }

	@Override
    public Response supersedeMasterPart(String supersededMasterPartNumber, String supersessionMasterPartNumber) {
        return supersededMasterPart(supersededMasterPartNumber, supersessionMasterPartNumber, 200);
    }

	@Override
    public Response splitSupersessionHistory(String masterPartNumber) {
        MasterPartSearchResult supersededMasterPart = searchForMasterPartByNumber(masterPartNumber);
        String url = String.format("/manufacturers/%s/master-parts/%s/supersession-split",
                manufacturerId,
                supersededMasterPart.getMasterParts().get(0).getMasterPartId());
        return actionResource(url);
    }

	@Override
    public Response reviseSupersessionHistory(String masterPartNumber) {
        MasterPartSearchResult supersededMasterPart = searchForMasterPartByNumber(masterPartNumber);
        String url = String.format("/manufacturers/%s/master-parts/%s/supersession-remove-part",
                manufacturerId,
                supersededMasterPart.getMasterParts().get(0).getMasterPartId());
        return actionResource(url);
    }

	@Override
    public Response attemptRevisionSupersessionHistory(String masterPartNumber, int expectedErrorCode) {
        MasterPartSearchResult supersededMasterPart = searchForMasterPartByNumber(masterPartNumber);
        String url = String.format("/manufacturers/%s/master-parts/%s/supersession-remove-part",
                manufacturerId,
                supersededMasterPart.getMasterParts().get(0).getMasterPartId());
        return actionResourceWithHttpErrorCode(url, expectedErrorCode);
    }

    public PartViewerDetails getPartViewerDetails(MasterPartDetails masterPart) {
        Part part = getPart(masterPart.getId());
        String sql = String.format("/model/%s/part/%s/viewerDetails?userType=%s",
                 part.getModelId(), part.getObjectId(), "manufacturer");
        return getResource(sql, PartViewerDetails.class);
    }

    @Override
    public PartViewerDetails getPartViewerDetailsOnBehalfOf(MasterPartDetails masterPart, int userId) {
        return getPartViewerDetailsOnBehalfOf(masterPart, userId, "manufacturer");
    }

    @Override
    public void failToGetPartViewerDetailsOnBehalfOf(MasterPartDetails masterPart, int userId) {
        failToGetPartViewerDetailsOnBehalfOf(masterPart, userId, "manufacturer");
    }

	@Override
    public void failToSupersedeMasterPart(String supersededMasterPartNumber, String supersessionMasterPartNumber) {
        supersededMasterPart(supersededMasterPartNumber, supersessionMasterPartNumber, 400);
    }

	@Override
	public int getManufacturerId() {
		return manufacturerId;
	}

	@Override
	protected int getPublicationIdFromName(String publicationName) {
		GetPublicationsListResponseDto publications = getPublications();
		assertTrue(publications != null && !publications.getPublications().isEmpty());
		Optional<GetPublicationListItemResponseDto> publicationExists = publications
				.getPublications().stream()
				.filter(p -> p.getName().equals(publicationName))
				.findFirst();
		assertTrue(publicationExists.isPresent());
		return publicationExists.get().getId();
	}

	@Override
	public GetPublicationResponseDto getPublicationFromName(String publicationName) {
		int publicationId = getPublicationIdFromName(publicationName);
		String url = String.format("/manufacturers/%s/publications/%s?language=EN", manufacturerId, publicationId);
		return getResource(url, GetPublicationResponseDto.class);
	}

	@Override
	public GetProductRangeListResponseDto getProductRanges() {
		String url = String.format("/manufacturers/%s/product-ranges", manufacturerId);
		return getResource(url, GetProductRangeListResponseDto.class);
	}

	@Override
	public GetProductListResponseDto getProductsForRange(String productRangeName) {
		GetProductRangeListResponseDto productRanges = getProductRanges();
		Optional<ProductRangeListItemDto> productRange = productRanges.getProductRanges().stream()
				.filter(pr -> pr.getName().equals(productRangeName))
				.findFirst();
		assertTrue(productRange.isPresent());
		int productRangeId = productRange.get().getId();
		String url = String.format("/manufacturers/%s/product-ranges/%s/products", manufacturerId, productRangeId);
		return getResource(url, GetProductListResponseDto.class);
	}

	@Override
	public GetManufacturerProductListResponseDto getAllProducts() {
		String url = String.format("/manufacturers/%s/products", manufacturerId);
		return getResource(url, GetManufacturerProductListResponseDto.class);
	}

	@Override
	public GetViewableListResponseDto getAllViewables() {
		String url = String.format("/manufacturers/%s/viewables", manufacturerId);
		return getResource(url, GetViewableListResponseDto.class);
	}

	@Override
	public PostSearchViewablesResponseDto searchViewablesByName(String viewableName) {
		String url = String.format("/manufacturers/%s/viewables/search", manufacturerId);
		PostSearchViewablesRequestDto request = new PostSearchViewablesRequestDto();
		request.setSearchParameter(viewableName);
		return actionResource(url, request, PostSearchViewablesResponseDto.class);
	}

	@Override
	public PostFilterViewablesResponseDto filterViewablesByProduct(int productId) {
		String url = String.format("/manufacturers/%s/viewables/filter", manufacturerId);
		PostFilterViewablesRequestDto request = new PostFilterViewablesRequestDto();
		request.setFilterProductId(productId);
		return actionResource(url, request, PostFilterViewablesResponseDto.class);
	}

	@Override
	public GetImageResponseDto getImage(int imageId) {
		String url = String.format("/manufacturers/%s/images/%s", manufacturerId, imageId);
		return getResource(url, GetImageResponseDto.class);
	}

	public GetPublicationsListResponseDto getPublicationsForManufacturer() {
		String url = String.format("/manufacturers/%s/publications", manufacturerId);
		return getResource(url, GetPublicationsListResponseDto.class);
	}

	private GetPublicationsListResponseDto getPublications() {
		String url = String.format("/manufacturers/%s/publications", manufacturerId);
		return getResource(url, GetPublicationsListResponseDto.class);
	}

    public Model getModel(String modelName) {
        String sql = String.format("SELECT modelid FROM model WHERE modelname = '%s'", modelName);
        int modelId = (int) entityManager.createNativeQuery(sql).getSingleResult();
        String url = String.format("/model/%s", modelId);
        Model model = getResource(url, Model.class);
        assertNotNull(model);
        return model;
    }

    public void autoPublishViewable(Model model, String viewableName) {
        assertEquals(viewableName, model.getModelName());
        String url = String.format("/manufacturers/%s/publications/create-and-publish", manufacturerId);
        PostCreateAndPublishRequestDto request = new PostCreateAndPublishRequestDto();
        request.setViewableId(model.getModelId());
        Response response = postResource(url, request);
        PostPublicationResponseDto dto = response.body().as(PostPublicationResponseDto.class);
        assertTrue(dto.getPublicationId() > 0);
    }

	@Override
    public void verifyPublicationExists(String publicationName) {
		Optional<GetPublicationListItemResponseDto> publication = getPublicationList(publicationName);
		assertTrue(publication.isPresent());
    }

	@Override
	public void verifyPublicationDoesntExist(String publicationName) {
		Optional<GetPublicationListItemResponseDto> publication = getPublicationList(publicationName);
		assertFalse(publication.isPresent());
	}

	private Optional<GetPublicationListItemResponseDto> getPublicationList(String publicationName) {
		String url = String.format("/manufacturers/%s/publications", manufacturerId);
		GetPublicationsListResponseDto response = getResource(url, GetPublicationsListResponseDto.class);
		assertFalse(response.getPublications().isEmpty());
		Optional<GetPublicationListItemResponseDto> publication = response.getPublications()
		        .stream()
		        .filter(p -> p.getName().equals(publicationName))
		        .findFirst();
		return publication;
	}

	@Override
	public GetPublicationListItemResponseDto getPublicationFromListByName(String publicationName) {
		Optional<GetPublicationListItemResponseDto> publication = getPublicationList(publicationName);
		assertTrue(publication.isPresent());
		return publication.get();
	}

	@Override
	public void createPublication(PostPublicationRequestDto request) {
		String url = String.format("/manufacturers/%s/publications", manufacturerId);
		Response response = postResource(url, request);
		PostPublicationResponseDto publicationResponseDto = response.body().as(PostPublicationResponseDto.class);
		assertNotNull(publicationResponseDto);
		assertTrue(publicationResponseDto.getPublicationId() > 0);
	}

	@Override
	public void updatePublication(PutPublicationRequestDto request, int publicationId) {
		String url = String.format("/manufacturers/%s/publications/%s", manufacturerId, publicationId);
		Response response = putResource(url, request);
		assertNotNull(response);
		assertEquals(200, response.getStatusCode());
	}

	@Override
	public void deletePublication(String publicationId) {
		String url = String.format("/manufacturers/%s/publications/%s", manufacturerId, publicationId);
		Response response = deleteResource(url);
		assertNotNull(response);
		assertEquals(200, response.getStatusCode());
	}

	@Override
	public void publishPublication(String publicationId) {
		String url = String.format("/manufacturers/%s/publications/%s/publish", manufacturerId, publicationId);
		Response response = actionResource(url);
		assertNotNull(response);
		assertEquals(200, response.getStatusCode());
	}

	@Override
	public void unpublishPublication(String publicationId) {
		String url = String.format("/manufacturers/%s/publications/%s/unpublish", manufacturerId, publicationId);
		Response response = actionResource(url);
		assertNotNull(response);
		assertEquals(200, response.getStatusCode());
	}

	@Override
	public void assignPublicationCategoriesToDealer(String publicationCategoriesArray, String dealerName) {
		String url = String.format("/manufacturers/%s/publication-categories/assign-to-purchaser", manufacturerId);
		assignPublicationCategoriesToDealer(url, publicationCategoriesArray, dealerName);
	}

	@Override
	public void assignPublicationsToDealer(String publicationNames, String dealerName) {
		String url = String.format("/manufacturers/%s/publications/assign-to-purchaser", manufacturerId);
		assignPublicationsToDealer(url, publicationNames, dealerName);
	}

	private void assignPublicationCategoriesToDealer(String url, String publicationCategoriesArray, String dealerName) {

		//get dealer based on dealerName
		List<ManufacturerSubEntity> purchasers = getPurchasersList();
		ManufacturerSubEntity dealer = purchasers.stream().filter(p -> p.getName().equals(dealerName)).findFirst().get();

		//get publicationCategory Ids based on names
		GetPublicationCategoryListResponseDto publicationCategoriesList = getPublicationCategoriesList();
		List<Integer> publicationCategories = publicationCategoriesList.getPublicationCategories().stream()
				.filter(pc -> Arrays.asList(publicationCategoriesArray.split(",")).contains(pc.getName()))
				.map(GetPublicationCategoryListItemResponseDto::getId).collect(Collectors.toList());

		//build dto
		PostPublicationCategoriesDto dto = new PostPublicationCategoriesDto();
		dto.setPurchaserId(dealer.getManufacturerSubEntityId());
		dto.setPublicationCategoryIds(publicationCategories);

		//post
		Response response = postResource(url, dto);
		assertNotNull(response);
		assertEquals(200, response.getStatusCode());
	}

	private void assignPublicationsToDealer(String url, String publicationNames, String dealerName) {
		List<ManufacturerSubEntity> purchasers = getPurchasersList();
		Optional<ManufacturerSubEntity> dealer = purchasers.stream().filter(p -> p.getName().equals(dealerName)).findFirst();
		assertTrue(dealer.isPresent());

		//get publication Ids based on names
		List<Integer> publicationIds = new ArrayList<Integer>();
		String[] publicationNamesArray = publicationNames.split(",");
		for (String pn : publicationNamesArray) {
			GetPublicationResponseDto publication = getPublicationFromName(pn);
			publicationIds.add(publication.getId());
		}

		PostAssignPublicationsDto dto = new PostAssignPublicationsDto();
		dto.setPublications(publicationIds);
		dto.setPurchaserId(dealer.get().getManufacturerSubEntityId());

		//post
		Response response = postResource(url, dto);
		assertNotNull(response);
		assertEquals(200, response.getStatusCode());
	}

	private Response supersededMasterPart(String supersededMasterPartNumber, String supersessionMasterPartNumber, int httpErrorCode) {
		MasterPartSearchResult supersededMasterPart = searchForMasterPartByNumber(supersededMasterPartNumber);
		String url = String.format("/manufacturers/%s/master-parts/%s/supersede",
				manufacturerId,
				supersededMasterPart.getMasterParts().get(0).getMasterPartId());
		MasterPartSearchResult supersedingMasterPart = searchForMasterPartByNumber(supersessionMasterPartNumber);
		PostSupersedeRequestDto body = new PostSupersedeRequestDto();
		body.setSupersedingMasterPartId(supersedingMasterPart.getMasterParts().get(0).getMasterPartId());
		return actionResourceWithHttpErrorCode(url, body, httpErrorCode);
	}

	private MasterPartSearchResult searchForMasterPartByNumber(PostManufacturerSearchMasterPartsDto request, String languageCode) {
		String sql = String.format("/manufacturers/%s/master-parts/search?language=%s", manufacturerId, languageCode);
		return actionResource(sql, request, MasterPartSearchResult.class);
	}

	private GetManufacturerUserListResponseDto getAllUsers() {
		String sql = String.format("/manufacturers/%s/users", manufacturerId);
		return getResource(sql, GetManufacturerUserListResponseDto.class);
	}

}
