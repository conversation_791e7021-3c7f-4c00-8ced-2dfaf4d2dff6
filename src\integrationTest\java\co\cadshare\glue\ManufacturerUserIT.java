package co.cadshare.glue;

import co.cadshare.domainmodel.part.Part;
import co.cadshare.domainmodel.part.PartViewerDetails;
import co.cadshare.masterKits.adapters.api.web.GetMasterKitListItemResponseDto;
import co.cadshare.masterKits.adapters.api.web.GetMasterKitListResponseDto;
import co.cadshare.masterKits.adapters.api.web.PostMasterKitsSearchResponseDto;
import co.cadshare.masterParts.adapters.api.web.PostManufacturerSearchMasterPartsDto;
import co.cadshare.masterParts.adapters.api.web.PostPurchaserSearchMasterPartsDto;
import co.cadshare.masterParts.adapters.api.web.PostSupersedeRequestDto;
import co.cadshare.masterParts.adapters.api.web.SupersessionHistoryDto;
import co.cadshare.masterParts.boundary.MasterPartSearchResult;
import co.cadshare.masterParts.core.MasterPartDetails;
import co.cadshare.models.core.Model;
import co.cadshare.orders.adapters.api.web.GetOrderListResponseDto;
import co.cadshare.publications.adapters.api.web.manufacturers.publications.GetPublicationsListResponseDto;
import co.cadshare.publications.adapters.api.web.manufacturers.publications.PostCreateAndPublishRequestDto;
import co.cadshare.publications.adapters.api.web.manufacturers.publications.PostPublicationResponseDto;
import co.cadshare.publications.adapters.api.web.manufacturers.publications.PublicationListItemDto;
import co.cadshare.shared.core.manufacturer.subentity.ManufacturerSubEntity;
import io.cucumber.spring.ScenarioScope;
import io.restassured.response.Response;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.NotImplementedException;
import org.springframework.stereotype.Component;

import javax.persistence.EntityManager;
import java.util.List;
import java.util.Optional;

import static org.junit.Assert.*;


@Component
@ScenarioScope
@EqualsAndHashCode(callSuper = true)
public class ManufacturerUserIT extends UserIT {

	public ManufacturerUserIT(String port, String clientId, String secret, EntityManager em) {
		super(port, clientId, secret, em);
	}

	@Override
    public void logIn(String emailAddress) {
        user = logInUsingEmailAddress(emailAddress);
    }

	@Override
	public MasterPartSearchResult exactSearchForMasterPartByNumber(String identifiedPartNumber) {
		PostManufacturerSearchMasterPartsDto request = new PostManufacturerSearchMasterPartsDto();
		request.setPartNumber(identifiedPartNumber);
		request.setExactMatch(true);
		return searchForMasterPartByNumber(request, "EN");
	}

    @Override
    public MasterPartSearchResult searchForMasterPartByNumber(String identifiedPartNumber) {
	    PostManufacturerSearchMasterPartsDto request = new PostManufacturerSearchMasterPartsDto();
	    request.setPartNumber(identifiedPartNumber);
		return searchForMasterPartByNumber(request, "EN");
    }

	@Override
	public MasterPartSearchResult searchForMasterPartByNumberUsingLanguage(String identifiedPartNumber, String languageCode) {
		PostManufacturerSearchMasterPartsDto request = new PostManufacturerSearchMasterPartsDto();
		request.setPartNumber(identifiedPartNumber);
		return searchForMasterPartByNumber(request, languageCode);
	}

	@Override
	public MasterPartSearchResult searchForMasterPartByDescriptionUsingLanguage(String identifiedPartDesc, String languageCode) {
		String sql = String.format("/manufacturers/%s/master-parts/search?language=%s", manufacturerId, languageCode);
		PostManufacturerSearchMasterPartsDto request = new PostManufacturerSearchMasterPartsDto();
		request.setPartDescription(identifiedPartDesc);
		return actionResource(sql, request, MasterPartSearchResult.class);
	}

    @Override
    public int searchForMasterKit(String identifiedPartNumber) {
        String sql = String.format("/manufacturers/%s/master-part-kits?language=EN", manufacturerId);
        GetMasterKitListResponseDto responseDto = getResource(sql, GetMasterKitListResponseDto.class);
        Optional<GetMasterKitListItemResponseDto> masterKit = responseDto.getMasterKits().stream().filter(p -> p.getPartNumber().equals(identifiedPartNumber)).findFirst();
        return masterKit.map(GetMasterKitListItemResponseDto::getId).orElse(0);
    }

	@Override
	public PostMasterKitsSearchResponseDto searchForMasterKitByNumber(String identifiedPartNumber){
		return searchForMasterKitByNumber("manufacturers", identifiedPartNumber, "EN", manufacturerId);
	}

	@Override
	public PostMasterKitsSearchResponseDto searchForMasterKitByDescriptionUsingLanguage(String masterKitDescSearch, String languageCode) {
		return searchForMasterKitByDesc("manufacturers", masterKitDescSearch, languageCode, manufacturerId);
	}

	@Override
    public void updatePurchaser(String newName, String purchaserId) {
        ManufacturerSubEntity updatedPurchaser = UserIT.buildPurchaser(newName, ManufacturerSubEntity.ManufacturerSubEntityType.DEALER);
        updatedPurchaser.setManufacturerSubEntityId(Integer.parseInt(purchaserId));
        putResource("/manufacturersubentity", updatedPurchaser);
    }

	@Override
    public void deletePurchaser(String purchaserId) {
        deleteResource("/manufacturersubentity/".concat(purchaserId));
    }

	@Override
    public List<ManufacturerSubEntity> getPurchasersList() {
        return getResourceList(String.format("/manufacturer/%s/manufacturersubentities", manufacturerId), ManufacturerSubEntity.class);
    }

	@Override
    public ManufacturerSubEntity getPurchaser(String purchaserId) {
        return getResource(String.format("/manufacturersubentity/%s", purchaserId), ManufacturerSubEntity.class);
    }

	@Override
    public String createPurchaser(String name, ManufacturerSubEntity.ManufacturerSubEntityType type) {
        ManufacturerSubEntity purchaser = buildPurchaser(name, type);
        Response response = postResource("/manufacturersubentity", purchaser);
        return response.asString();
    }

	@Override
    public GetOrderListResponseDto getEnquiries() {
        String url = String.format("/manufacturers/%s/orders", manufacturerId);
        return getResource(url, GetOrderListResponseDto.class);
    }

	@Override
    public SupersessionHistoryDto getSupersessionHistoryForPart(String supersessionMasterPart) {
        MasterPartSearchResult supersededMasterPart = searchForMasterPartByNumber(supersessionMasterPart);
        String sql = String.format("manufacturers/%s/master-parts/%s/supersession-history",
                manufacturerId, supersededMasterPart.getMasterParts().get(0).getMasterPartId());
        return getResource(sql, SupersessionHistoryDto.class);
    }

	@Override
    public void doNotGetSupersessionHistoryForPart(String supersessionMasterPart) {
        MasterPartSearchResult supersededMasterPart = searchForMasterPartByNumber(supersessionMasterPart);
        String sql = String.format("manufacturers/%s/master-parts/%s/supersession-history",
                manufacturerId, supersededMasterPart.getMasterParts().get(0).getMasterPartId());
        getNotFound(sql);
    }

	@Override
    public Response supersedeMasterPart(String supersededMasterPartNumber, String supersessionMasterPartNumber) {
        return supersededMasterPart(supersededMasterPartNumber, supersessionMasterPartNumber, 200);
    }

	@Override
    public Response splitSupersessionHistory(String masterPartNumber) {
        MasterPartSearchResult supersededMasterPart = searchForMasterPartByNumber(masterPartNumber);
        String url = String.format("/manufacturers/%s/master-parts/%s/supersession-split",
                manufacturerId,
                supersededMasterPart.getMasterParts().get(0).getMasterPartId());
        return actionResource(url);
    }

	@Override
    public Response reviseSupersessionHistory(String masterPartNumber) {
        MasterPartSearchResult supersededMasterPart = searchForMasterPartByNumber(masterPartNumber);
        String url = String.format("/manufacturers/%s/master-parts/%s/supersession-remove-part",
                manufacturerId,
                supersededMasterPart.getMasterParts().get(0).getMasterPartId());
        return actionResource(url);
    }

	@Override
    public Response attemptRevisionSupersessionHistory(String masterPartNumber, int expectedErrorCode) {
        MasterPartSearchResult supersededMasterPart = searchForMasterPartByNumber(masterPartNumber);
        String url = String.format("/manufacturers/%s/master-parts/%s/supersession-remove-part",
                manufacturerId,
                supersededMasterPart.getMasterParts().get(0).getMasterPartId());
        return actionResourceWithHttpErrorCode(url, expectedErrorCode);
    }

    public PartViewerDetails getPartViewerDetails(MasterPartDetails masterPart) {
        Part part = getPart(masterPart.getId());
        String sql = String.format("/model/%s/part/%s/viewerDetails?userType=%s",
                 part.getModelId(), part.getObjectId(), "manufacturer");
        return getResource(sql, PartViewerDetails.class);
    }

    @Override
    public PartViewerDetails getPartViewerDetailsOnBehalfOf(MasterPartDetails masterPart, int userId) {
        return getPartViewerDetailsOnBehalfOf(masterPart, userId, "manufacturer");
    }

    @Override
    public void failToGetPartViewerDetailsOnBehalfOf(MasterPartDetails masterPart, int userId) {
        failToGetPartViewerDetailsOnBehalfOf(masterPart, userId, "manufacturer");
    }

	@Override
    public void failToSupersedeMasterPart(String supersededMasterPartNumber, String supersessionMasterPartNumber) {
        supersededMasterPart(supersededMasterPartNumber, supersessionMasterPartNumber, 400);
    }

	@Override
	public int getManufacturerId() {
		return manufacturerId;
	}

	@Override
	protected int getPublicationIdFromName(String publicationName) {
		GetPublicationsListResponseDto publications = getPublications();
		assertTrue(publications != null && !publications.getPublications().isEmpty());
		Optional<PublicationListItemDto> publicationExists = publications
				.getPublications().stream()
				.filter(p -> p.getName().equals(publicationName))
				.findFirst();
		assertTrue(publicationExists.isPresent());
		return publicationExists.get().getId();
	}

	private GetPublicationsListResponseDto getPublications() {
		String url = String.format("/manufacturers/%s/publications", manufacturerId);
		return getResource(url, GetPublicationsListResponseDto.class);
	}

    public Model getModel(String modelName) {
        String sql = String.format("SELECT modelid FROM model WHERE modelname = '%s'", modelName);
        int modelId = (int) entityManager.createNativeQuery(sql).getSingleResult();
        String url = String.format("/model/%s", modelId);
        Model model = getResource(url, Model.class);
        assertNotNull(model);
        return model;
    }

    public void autoPublishViewable(Model model, String viewableName) {
        assertEquals(viewableName, model.getModelName());
        String url = String.format("/manufacturers/%s/publications/create-and-publish", manufacturerId);
        PostCreateAndPublishRequestDto request = new PostCreateAndPublishRequestDto();
        request.setViewableId(model.getModelId());
        Response response = postResource(url, request);
        PostPublicationResponseDto dto = response.body().as(PostPublicationResponseDto.class);
        assertTrue(dto.getPublicationId() > 0);
    }

    public void verifyPublicationExists(String publicationName) {
        String url = String.format("/manufacturers/%s/publications", manufacturerId);
        GetPublicationsListResponseDto response = getResource(url, GetPublicationsListResponseDto.class);
        assertFalse(response.getPublications().isEmpty());
        Optional<PublicationListItemDto> publication = response.getPublications()
                .stream()
                .filter(p -> p.getName().equals(publicationName))
                .findFirst();
        assertTrue(publication.isPresent());
    }

	private Response supersededMasterPart(String supersededMasterPartNumber, String supersessionMasterPartNumber, int httpErrorCode) {
		MasterPartSearchResult supersededMasterPart = searchForMasterPartByNumber(supersededMasterPartNumber);
		String url = String.format("/manufacturers/%s/master-parts/%s/supersede",
				manufacturerId,
				supersededMasterPart.getMasterParts().get(0).getMasterPartId());
		MasterPartSearchResult supersedingMasterPart = searchForMasterPartByNumber(supersessionMasterPartNumber);
		PostSupersedeRequestDto body = new PostSupersedeRequestDto();
		body.setSupersedingMasterPartId(supersedingMasterPart.getMasterParts().get(0).getMasterPartId());
		return actionResourceWithHttpErrorCode(url, body, httpErrorCode);
	}

	private MasterPartSearchResult searchForMasterPartByNumber(PostManufacturerSearchMasterPartsDto request, String languageCode) {
		String sql = String.format("/manufacturers/%s/master-parts/search?language=%s", manufacturerId, languageCode);
		return actionResource(sql, request, MasterPartSearchResult.class);
	}

}
