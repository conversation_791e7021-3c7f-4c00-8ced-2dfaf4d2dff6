package co.cadshare.services;

import static junit.framework.TestCase.fail;
import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import co.cadshare.modelMgt.models.boundary.ModelViewableService;
import co.cadshare.shared.core.manufacturer.Manufacturer;
import co.cadshare.modelMgt.models.core.model.viewable.Viewable;
import co.cadshare.modelMgt.models.core.model.viewable.StateDetail;
import co.cadshare.shared.core.manufacturer.ManufacturerSettings;
import co.cadshare.exceptions.NotFoundException;
import co.cadshare.shared.adapters.database.manufacturer.ManufacturerDao;
import co.cadshare.persistence.ModelDataDao;
import com.flextrade.jfixture.annotations.Fixture;
import com.flextrade.jfixture.rules.FixtureRule;
import java.util.List;
import org.junit.Before;
import org.junit.Rule;
import org.junit.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.dao.EmptyResultDataAccessException;

public class ModelViewableServiceTest {

    private ModelViewableService viewableService;

    @Mock
    private ModelDataDao modelDataDao;
    @Mock
    private ManufacturerDao manufacturerDao;
    @Mock
    private StateDetailService stateDetailService;
    @Mock
    private ModelUpdateService modelUpdateService;

    @Fixture
    private Viewable viewable;
    @Fixture
    private List<StateDetail> stateDetails;

    @Rule
    public FixtureRule fr = FixtureRule.initFixtures();

    @Before
    public void setup() {
        MockitoAnnotations.initMocks(this);
        viewableService = new ModelViewableService(modelDataDao, manufacturerDao, stateDetailService, modelUpdateService);
    }

    @Test
    public void getStatesReturnsPopulatedModelStateWithSnapshotStateDetails() {
        int testModelId = 99;
        int testModelStateId = 11;

        viewable.setId(testModelStateId);
        stateDetails.forEach(stateDetail -> stateDetail.setId(testModelStateId));
        when(modelDataDao.getCoreViewableByModelId(testModelId)).thenReturn(viewable);
        when(stateDetailService.getStateDetailsForViewable(testModelStateId)).thenReturn(stateDetails);
        Viewable result = viewableService.getStatesForModel(testModelId);

        viewable.setStateDetails(stateDetails);

        verify(stateDetailService).getStateDetailsForViewable(testModelStateId);
        verify(modelDataDao).getCoreViewableByModelId(testModelId);
        assertEquals(viewable, result);
    }

    @Test(expected = NotFoundException.class)
    public void getStatesThrowsNotFoundWhenModelStateNotFoundForSnapshots() {
        int testModelId = 99;

        when(modelDataDao.getCoreViewableByModelId(testModelId))
            .thenThrow(new NotFoundException("Not found", new EmptyResultDataAccessException("No records found.", 1)));

        viewableService.getStatesForModel(testModelId);
        fail("Exception should have been thrown by now.");
    }


    @Test
    public void createStatesUpdatesModelUpdate() {
        Manufacturer manufacturer = new Manufacturer();
        ManufacturerSettings settings = new ManufacturerSettings();
        settings.setEdgingEnabledDefault(true);
        manufacturer.setManufacturerSettings(settings);

        when(manufacturerDao.getManufacturerForModel(99)).thenReturn(manufacturer);
        viewableService.createViewable(viewable);
        verify(modelUpdateService).update(viewable.getModelId());
    }
}