package co.cadshare.modelMgt.publications.adapters.database;

import co.cadshare.modelMgt.products.core.Product;
import org.junit.Test;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;

public class PublicationsProductEntityMapperTest {


	@Test
	public void testEntityToCoreMapping() {
		PublicationsRangeEntity rangeEntity = new PublicationsRangeEntity();
		rangeEntity.setId(1);
		rangeEntity.setName("Range Name");
		PublicationsProductEntity productEntity = new PublicationsProductEntity();
		productEntity.setId(2);
		productEntity.setName("Product Name");
		productEntity.setRange(rangeEntity);

		Product product = PublicationsProductEntityMapper.Instance.entityToCore(productEntity);

		assertNotNull(product);
		assertEquals(productEntity.getId(), product.getId());
		assertEquals("Product Name", product.getName());
		assertEquals(rangeEntity.getId(), product.getRangeId());
		assertEquals(rangeEntity.getName(), product.getRangeName());
	}
}
