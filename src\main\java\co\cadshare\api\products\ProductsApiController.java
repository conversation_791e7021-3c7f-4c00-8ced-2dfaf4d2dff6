package co.cadshare.api.products;

import co.cadshare.shared.core.user.User;
import co.cadshare.response.CustomerManual;
import co.cadshare.publications.boundary.ManualService;
import java.util.List;
import java.util.stream.Collectors;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("api/products")
public class ProductsApiController {

    private ManualService manualService;

    public ProductsApiController(ManualService manualService) {
        this.manualService = manualService;
    }

    @GetMapping
    public List<Product> getAllProductsForMse(@AuthenticationPrincipal User currentUser) {
        int manufacturerSubEntityId = currentUser.getManufacturerSubEntityId();
        List<CustomerManual> manualList = manualService.getManualsForManufacturerSubEntity(manufacturerSubEntityId);

        return manualList.stream()
            .map(customerManual -> Product.builder()
                .name(customerManual.getManualName())
                .imageUrl(customerManual.getThumbnailUrl())
                .build())
            .collect(Collectors.toList());
    }
}
