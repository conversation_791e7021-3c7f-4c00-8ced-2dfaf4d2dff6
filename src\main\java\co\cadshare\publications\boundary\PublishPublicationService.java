package co.cadshare.publications.boundary;

import co.cadshare.exceptions.UnprocessableEntityException;

import co.cadshare.publications.core.Manual;
import co.cadshare.publications.core.Viewable;
import co.cadshare.shared.core.user.User;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
public class PublishPublicationService {

    private ManualCommandPort manualCommand;
    private final ViewableQueryPort viewableQuery;
    private final ProductCommandPort productCommand;

    @Autowired
    public PublishPublicationService(ManualCommandPort manualCommand,
                                     ViewableQueryPort modelQuery, ProductCommandPort productCommand) {
        this.manualCommand = manualCommand;
        this.viewableQuery = modelQuery;
        this.productCommand = productCommand;
    }

    @Transactional
    public int createAndAutoPublishPublication(User user, int viewableId) throws Exception {
        Viewable viewable = viewableQuery.getViewable(viewableId);
        if(viewable.isReadyForPublication(user)) {
            viewable.ensureProductHasThumbnail();
            productCommand.updateProduct(user, viewable.getProduct());
            Manual manual = Manual.buildForImmediatePublication(viewable, user);
            int manualId = manualCommand.createManual(manual);
            if (manual.shouldBeAssignedToPurchasers())
                manualCommand.assignManualToManufacturerSubEntities(manualId,
                        manual.getPurchasersAssignedToRanges());
            return manualId;
        } else
            throw new UnprocessableEntityException("Viewable not ready for publication");
    }
}
