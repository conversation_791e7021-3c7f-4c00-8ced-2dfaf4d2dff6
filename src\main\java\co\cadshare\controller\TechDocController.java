/*
 * Copyright 2016 Bell.
 */
package co.cadshare.controller;

import co.cadshare.modelMgt.publications.core.TechDoc;
import co.cadshare.shared.core.user.User;
import co.cadshare.modelMgt.publications.boundary.TechDocService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import java.sql.Timestamp;
import java.util.Calendar;
import java.util.List;

@Slf4j
@Controller
@RequestMapping("/techDoc")
public class TechDocController {

  private TechDocService techDocService;

  public TechDocController(TechDocService techDocService) {
    this.techDocService = techDocService;
  }

  @GetMapping
  public HttpEntity<List<TechDoc>> getTechDocByManufacturerId(@AuthenticationPrincipal User currentUser) {
    log.info("ACCESS: User [{}], getTechDocByManufacturerId", currentUser.accessDetails());

    List<TechDoc> techDocs = techDocService.getTechDocByManufacturerId(currentUser.getManufacturerId());
    return new ResponseEntity<>(techDocs, HttpStatus.OK);
  }


  @PostMapping(consumes = "application/json")
  public HttpEntity<Integer> createTechDoc(@AuthenticationPrincipal User currentUser, @RequestBody TechDoc techDoc) throws Exception {
    log.info("ACCESS: User [{}], createTechDoc", currentUser.accessDetails());

    techDoc.setManufacturerId(currentUser.getManufacturerId());
    techDoc.setCreatedByUserId(currentUser.getUserId());
    Timestamp now = new Timestamp(Calendar.getInstance().getTime().getTime());
    techDoc.setCreatedDate(now);
    int techDocId = techDocService.createTechDoc(techDoc);

    return new ResponseEntity<>(techDocId, HttpStatus.OK);
  }

  @PutMapping(value = "/{techDocId}")
  public HttpEntity<Boolean> updateTechDoc(@AuthenticationPrincipal User currentUser, @PathVariable int techDocId, @RequestBody TechDoc techDoc) {
    log.info("ACCESS: User [{}], updateTechDoc, techDocId [{}]", currentUser.accessDetails(), techDocId);
    Boolean updated = false;
    TechDoc existingTechDoc = techDocService.getTechDocById(techDocId);

    if (existingTechDoc != null) {
      //Check user manufacturer has access to that tech doc
      if (existingTechDoc.getManufacturerId() == currentUser.getManufacturerId()) {
        techDoc.setId(techDocId);
        techDoc.setManufacturerId(existingTechDoc.getManufacturerId());
        updated = techDocService.updateTechDoc(techDoc);
      } else {
        return new ResponseEntity<>(HttpStatus.FORBIDDEN);
      }
    }
    return new ResponseEntity<>(updated, HttpStatus.OK);
  }

  @DeleteMapping(value = "/{techDocId}")
  public HttpEntity<Boolean>deleteTechDoc(@AuthenticationPrincipal User currentUser, @PathVariable int techDocId) {
    log.info("ACCESS: User [{}], deleteTechDoc, techDocId [{}]", currentUser.accessDetails(), techDocId);
    Boolean deleted = false;
    TechDoc existingTechDoc = techDocService.getTechDocById(techDocId);

    if (existingTechDoc != null) {
      //Check user manufacturer has access to that tech doc
      if (existingTechDoc.getManufacturerId() == currentUser.getManufacturerId()) {
        deleted = techDocService.deleteTechDoc(techDocId);
      } else {
        return new ResponseEntity<>(HttpStatus.FORBIDDEN);
      }
    }
    return new ResponseEntity<>(deleted, HttpStatus.OK);
  }
}
