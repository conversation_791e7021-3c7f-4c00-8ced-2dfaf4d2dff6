package co.cadshare.publications.adapters.database;

import com.blazebit.persistence.CriteriaBuilder;
import com.blazebit.persistence.CriteriaBuilderFactory;
import com.blazebit.persistence.view.EntityViewManager;
import com.blazebit.persistence.view.EntityViewSetting;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import javax.persistence.EntityManager;
import java.util.List;

@Repository
public class PublicationsViewableQueryRepo {

    private final EntityManager entityManager;
    private final EntityViewManager entityViewManager;
    private final CriteriaBuilderFactory factory;

    @Autowired
    public PublicationsViewableQueryRepo(EntityManager entityManager,
                                         EntityViewManager entityViewManager,
                                         CriteriaBuilderFactory factory) {
        this.entityManager = entityManager;
        this.entityViewManager = entityViewManager;
        this.factory = factory;
    }

    public PublicationsViewableEntityView getById(Integer id) {

        CriteriaBuilder<PublicationsModelEntity> criteriaBuilder =
                factory.create(entityManager, PublicationsModelEntity.class)
                        .where("modelId")
                        .eq(id);

        PublicationsViewableEntityView viewables =
                entityViewManager.applySetting(EntityViewSetting.create(PublicationsViewableEntityView.class), criteriaBuilder)
                        .getSingleResult();

        return viewables;
    }

}
