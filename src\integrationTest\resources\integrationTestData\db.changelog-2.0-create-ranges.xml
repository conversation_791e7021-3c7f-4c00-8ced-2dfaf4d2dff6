<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                   http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.0.xsd
">

    <changeSet author="AndyB" id="2.0-integration-test-data-create-ranges-1">
        <sql stripComments="true">
            INSERT INTO public.range (rangeid, manufacturerid, name, description, createddate, createdbyuserid, modifiedbyuserid, modifieddate, deleted)
            VALUES (
            1, 	                        --rangeid,
            1, 	                        --manufacturerid,
            'Caterpillar Range 1', 	    --name,
            '', 	                    --description,
            '2017-06-02 10:13:31.364', 	--createddate,
            NULL, 	                    --createdbyuserid,
            NULL, 	                    --modifiedbyuserid,
            NULL, 	                    --modifieddate,
            false);	                    --deleted
        </sql>
        <sql stripComments="true">
            INSERT INTO public.manufacturersubentityrangemap(rangeid, manufacturersubentityid, createddate, createdbyuserid, archived)
            VALUES (
            1,                          --rangeid
            1,                          --manufacturersubentityid,
            '2017-06-02 10:13:31.364', 	--createddate,
            1, 	                        --createdbyuserid
            false);	                    --archived
        </sql>
    </changeSet>

    <changeSet author="AndyB" id="2.0-integration-test-data-create-ranges-2">
        <sql stripComments="true">
            INSERT INTO public.range (rangeid, manufacturerid, name, description, createddate, createdbyuserid, modifiedbyuserid, modifieddate, deleted)
            VALUES (
            2, 	                        --rangeid,
            2, 	                        --manufacturerid,
            'JCB Range 1', 	            --name,
            '', 	                    --description,
            '2017-06-02 10:13:31.364', 	--createddate,
            NULL, 	                    --createdbyuserid,
            NULL, 	                    --modifiedbyuserid,
            NULL, 	                    --modifieddate,
            false);	                    --deleted
        </sql>
        <sql stripComments="true">
            INSERT INTO public.manufacturersubentityrangemap(rangeid, manufacturersubentityid, createddate, createdbyuserid, archived)
            VALUES (
            2,                          --rangeid
            3,                          --manufacturersubentityid,
            '2017-06-02 10:13:31.364', 	--createddate,
            1, 	                        --createdbyuserid
            false);	                    --archived
        </sql>
    </changeSet>

    <changeSet author="AndyB" id="2.0-integration-test-data-create-ranges-3">
        <sql stripComments="true">
            INSERT INTO public.range (rangeid, manufacturerid, name, description, createddate, createdbyuserid, modifiedbyuserid, modifieddate, deleted)
            VALUES (
            3, 	                        --rangeid,
            3, 	                        --manufacturerid,
            'Liebherr Range 1', 	    --name,
            '', 	                    --description,
            '2017-06-02 10:13:31.364', 	--createddate,
            NULL, 	                    --createdbyuserid,
            NULL, 	                    --modifiedbyuserid,
            NULL, 	                    --modifieddate,
            false);	                    --deleted
        </sql>
        <sql stripComments="true">
            INSERT INTO public.manufacturersubentityrangemap(rangeid, manufacturersubentityid, createddate, createdbyuserid, archived)
            VALUES (
            3,                          --rangeid
            4,                          --manufacturersubentityid,
            '2017-06-02 10:13:31.364', 	--createddate,
            1, 	                        --createdbyuserid
            false);	                    --archived
        </sql>
    </changeSet>

    <changeSet author="AndyB" id="2.0-integration-test-data-create-ranges-4">
        <sql stripComments="true">
            INSERT INTO public.range (rangeid, manufacturerid, name, description, createddate, createdbyuserid, modifiedbyuserid, modifieddate, deleted)
            VALUES (
            4, 	                        --rangeid,
            4, 	                        --manufacturerid,
            'Terex Range 1', 	        --name,
            '', 	                    --description,
            '2017-06-02 10:13:31.364', 	--createddate,
            NULL, 	                    --createdbyuserid,
            NULL, 	                    --modifiedbyuserid,
            NULL, 	                    --modifieddate,
            false);	                    --deleted
        </sql>
        <sql stripComments="true">
            INSERT INTO public.manufacturersubentityrangemap(rangeid, manufacturersubentityid, createddate, createdbyuserid, archived)
            VALUES (
            4,                          --rangeid
            10,                         --manufacturersubentityid,
            '2017-06-02 10:13:31.364', 	--createddate,
            1, 	                        --createdbyuserid
            false);	                    --archived
        </sql>
    </changeSet>
</databaseChangeLog>
