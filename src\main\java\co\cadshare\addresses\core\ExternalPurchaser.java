package co.cadshare.addresses.core;

import co.cadshare.exceptions.ExternalDataException;
import co.cadshare.shared.core.purchaser.Purchaser;
import co.cadshare.utils.ObjectUtilsExtension;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.ExtensionMethod;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;
import java.util.stream.Collectors;

@EqualsAndHashCode(callSuper = true)
@Data
@ExtensionMethod(ObjectUtilsExtension.class)
public class ExternalPurchaser extends Purchaser {

    private final Logger logger = LoggerFactory.getLogger(getClass());

    private String externalRefIdentifier;
    private List<User> users;
    public ExternalPurchaser() {
        this.users = new ArrayList<>();
    }

    public void addUser(User user) {
        users.add(user);
    }

    public List<ExternalAddress> deleteAllExistingExternalAddresses() {
        getUsers().forEach(user -> user.getExternalAddresses().forEach(ExternalAddress::delete));
	    return getAllExistingAddressesForAllUsers();
    }

	public List<ExternalAddress> upsertAllAddresses(List<ExternalAddress> externalAddresses, List<ExternalContact> savedContacts) {
		List<ExternalAddress> deletedAddresses = deleteAllExistingExternalAddresses();

		for (ExternalAddress address : externalAddresses) {
			List<ExternalAddressContact> addressContactsWithSavedContacts = new ArrayList<>();
			for (ExternalAddressContact addressContact : address.getContactMaps()) {
				Optional<ExternalContact> matchedContact = savedContacts.stream()
						.filter(savedContact -> savedContact.getExternalRefId().equals(addressContact.getContact().getExternalRefId()))
						.findFirst();
				if(matchedContact.isPresent()) {
					ExternalContact contact = matchedContact.get();
					addressContact.setContact(contact);
					contact.getAddressContacts()
							.removeIf(ac -> ac.getContact().getExternalRefId().equals(addressContact.getContact().getExternalRefId()));
					addressContactsWithSavedContacts.add(addressContact);
				}
			}
			address.setContactMaps(addressContactsWithSavedContacts);
		}
		List<ExternalAddress> upsertAddresses = upsertIncomingAddresses(externalAddresses);
		return overwriteDeletedAddresses(deletedAddresses, upsertAddresses);
	}

    private List<ExternalAddress> upsertIncomingAddresses(List<ExternalAddress> addresses) {
        List<ExternalAddress> syncedAddresses = new ArrayList<>();
        if (addresses.isNotNull())
            for (ExternalAddress address : addresses)
                if(address.isValid())
                    syncedAddresses.add(upsertSingleAddress(address));
        return syncedAddresses;
    }

    private ExternalAddress upsertSingleAddress(ExternalAddress address) throws ExternalDataException {

		ExternalAddress syncedAddress = address;
        Optional<ExternalAddress> alreadyHaveAddress = searchForAddressAcrossAllUsers(address);
        if (alreadyHaveAddress.isPresent()) {
            ExternalAddress existingAddress = alreadyHaveAddress.get();
            existingAddress.refresh(address);
            syncedAddress = existingAddress;
        } else {
	        syncedAddress.getContactMaps().forEach(cm -> cm.setAddress(address));
        }
		shareAddressWithAllUsers(syncedAddress);
        return syncedAddress;
    }

	private Optional<ExternalAddress> searchForAddressAcrossAllUsers(ExternalAddress address) {
        return users
                .stream()
                .flatMap(user -> user.getExternalAddresses()
                        .stream()
                        .filter(searchAddr -> searchAddr.matchesExternally(address)))
                .findAny();
    }

    private void shareAddressWithAllUsers(ExternalAddress address) {

        for (User user : users) {
            if (user.doesNotHaveAddressMapped(address)) {
                UserAddressMap addressMap = new UserAddressMap() {{
                    setUser(user);
                    setAddress(address);
                }};
                address.shareWithUser(addressMap);
            }
        }
    }

	private List<ExternalAddress> overwriteDeletedAddresses(List<ExternalAddress> deletedAddresses,
	                                                        List<ExternalAddress> syncedAddresses) {
		Map<String, ExternalAddress> map = new HashMap<>();
		for (ExternalAddress deletedAddress : deletedAddresses)
			map.put(deletedAddress.getExternalRefId(), deletedAddress);
		for (ExternalAddress syncedAddress : syncedAddresses)
			map.put(syncedAddress.getExternalRefId(), syncedAddress);
		return new ArrayList<>(map.values());
	}

	public List<ExternalContact> upsertContacts(List<ExternalAddress> incomingAddresses) {
		List<ExternalContact> uniqueIncomingContacts = buildUniqueContactList(incomingAddresses);
		List<ExternalAddress> existingAddresses = getAllExistingAddressesForAllUsers();
		List<ExternalContact> uniqueExistingContacts = buildUniqueContactList(existingAddresses);

		List<ExternalContact> mergedContacts = new ArrayList<>();
		for (ExternalContact uniqueIncomingContact : uniqueIncomingContacts) {
			if(uniqueIncomingContact.isValid()) {
				Optional<ExternalContact> existingContact = uniqueExistingContacts.stream()
						.filter(contact -> contact.getExternalRefId().equals(uniqueIncomingContact.getExternalRefId()))
						.findAny();
				if (existingContact.isPresent()) {
					ExternalContact existingContactUpdated = existingContact.get();
					existingContactUpdated.updateContactDetails(uniqueIncomingContact);
					mergedContacts.add(existingContactUpdated);
				} else {
					mergedContacts.add(uniqueIncomingContact);
				}
			}
		}
		return mergedContacts;
	}

	private List<ExternalAddress> getAllExistingAddressesForAllUsers() {
		return getUsers().stream()
				.flatMap(user -> user.getExternalAddresses()
						.stream())
				.collect(Collectors.toList());
	}

	private static ArrayList<ExternalContact> buildUniqueContactList(List<ExternalAddress> externalAddresses) {
		List<ExternalAddressContact> allExternalAddressContacts = externalAddresses.stream()
				.flatMap(address -> address.getContactMaps().stream())
				.collect(Collectors.toList());
		List<ExternalContact> allExternalContacts = allExternalAddressContacts.stream()
				.map(ExternalAddressContact::getContact)
				.collect(Collectors.toList());

		return new ArrayList<>(allExternalContacts.stream()
				.collect(Collectors.toCollection(() -> new TreeSet<>(
						Comparator.comparing(ExternalContact::getExternalRefId)))));
	}
}
