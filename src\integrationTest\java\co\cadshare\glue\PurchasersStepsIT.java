package co.cadshare.glue;

import co.cadshare.shared.core.manufacturer.subentity.ManufacturerSubEntity;
import co.cadshare.utils.ObjectUtilsExtension;
import io.cucumber.java.en.And;
import io.cucumber.java.en.Given;
import io.cucumber.java.en.Then;
import io.cucumber.java.en.When;
import lombok.experimental.ExtensionMethod;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

import static io.restassured.RestAssured.given;
import static org.junit.Assert.*;

@ExtensionMethod(ObjectUtilsExtension.class)
public class PurchasersStepsIT extends BasicStepsIT {

    private String purchaserId;
	private final UserIT loggedInUser;

	@Autowired
	public PurchasersStepsIT(AuthenticationIT auth) {
		loggedInUser = auth.getLoggedInUser();
	}

    @Given("no Purchaser with name {string} exists")
    public void noPurchaserWithNameExists(String name) {
        List<ManufacturerSubEntity> purchasers = loggedInUser.getPurchasersList();
        assertFalse(purchasers.stream().anyMatch(purchaser -> purchaser.getName().equals(name)));
    }

    @Given("a Purchaser with name {string} exists")
    public void aPurchaserWithNameExists(String name) {
        purchaserId = loggedInUser.createPurchaser(name, ManufacturerSubEntity.ManufacturerSubEntityType.DEALER);
        ManufacturerSubEntity existingDealer = loggedInUser.getPurchaser(purchaserId);
        assertEquals(name, existingDealer.getName());
    }

    @When("I create a Dealer named {string}")
    public void iCreateADealerNamed(String name) {
        purchaserId = loggedInUser.createPurchaser(name, ManufacturerSubEntity.ManufacturerSubEntityType.DEALER);
    }

    @When("I create a Customer named {string}")
    public void iCreateACustomerNamed(String name) {
        purchaserId = loggedInUser.createPurchaser(name, ManufacturerSubEntity.ManufacturerSubEntityType.CUSTOMER);
    }

    @When("I change the name of the Purchaser to {string}")
    public void iChangeTheNameOfThePurchaserTo(String newName) {
	    loggedInUser.updatePurchaser(newName, purchaserId);
    }


    @When("I delete a Purchaser named {string}")
    public void iDeleteAPurchaserNamed(String name) {
	    loggedInUser.deletePurchaser(purchaserId);
    }

    @And("the Purchaser with name {string} should no longer exist")
    public void thePurchaserWithNameShouldNoLongerExist(String name) {
        List<ManufacturerSubEntity> purchasers = loggedInUser.getPurchasersList();
        assertFalse(purchasers.stream().anyMatch(purchaser -> purchaser.getName().equals(name)));
    }

    @Then("a Purchaser with name {string} should now exist")
    public void aPurchaserWithThatNameShouldNowExist(String name) {
        ManufacturerSubEntity createdPurchaser = loggedInUser.getPurchaser(purchaserId);
        assertEquals(name, createdPurchaser.getName());
    }




}