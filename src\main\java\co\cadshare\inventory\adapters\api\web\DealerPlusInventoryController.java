package co.cadshare.inventory.adapters.api.web;

import co.cadshare.inventory.core.InventoryFile;
import co.cadshare.shared.core.user.User;
import co.cadshare.inventory.boundary.DealerPlusInventoryService;
import com.google.gson.JsonObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;

@RestController
@PreAuthorize("hasRole('ROLE_MANUFACTURER_SUB_ENTITY_DEALER_PLUS')")
@RequestMapping("/dealerplus/inventory")
@Slf4j
public class DealerPlusInventoryController {

    @Autowired
    private DealerPlusInventoryService inventoryService;

    @Autowired
    private InventoryFileProcessor csvProcessor;

    @PostMapping(value = "/price")
    public ResponseEntity<String> inventoryManagementPriceFileUpload(@AuthenticationPrincipal User user, @RequestParam("file") MultipartFile file) {
        log.info("Dealerplus - Received inventory price file [{}] for processing for dealer subentity id [{}]", file.getName(), user.getManufacturerSubEntityId());
        // We will do some quick validation and then hand back to the client. In the background
        // we'll process the file and commit to database
        try {
            InventoryFile inventoryFile = csvProcessor.convert(file);
            inventoryService.savePrice(user, inventoryFile);
        } catch (InventoryFile.UnparseableInventoryFileException | IOException e) {
            JsonObject response = new JsonObject();
            response.addProperty("message", "Error processing file: " + e.getMessage());
            return new ResponseEntity<>(response.toString(), HttpStatus.BAD_REQUEST);
        }
        return new ResponseEntity<>( HttpStatus.ACCEPTED);
    }

    @GetMapping(value = "/headers", produces = MediaType.APPLICATION_OCTET_STREAM_VALUE)
    public ResponseEntity masterPartFileTemplate(@AuthenticationPrincipal User currentUser) {
        log.info("Masterparts inventory management template for dealer subentity id [{}]", currentUser.getManufacturerSubEntityId());
        String inventoryCSV = null;
        try {
            inventoryCSV = inventoryService.downloadMasterPartInventoryTemplate();
        } catch (Exception e) {
            JsonObject response = new JsonObject();
            response.addProperty("message", "Error processing request: " + e.getMessage());
            return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
        }
        return new ResponseEntity<>(inventoryCSV, HttpStatus.OK);
    }

    @GetMapping
    public ResponseEntity inventoryManagementDownload(@AuthenticationPrincipal User currentUser) {
        log.info("Masterparts data download request for dealer subentity id [{}]", currentUser.getManufacturerSubEntityId());
        String masterPartCSV = null;
        try {
            inventoryService.downloadMasterPartInventory(currentUser.getManufacturerSubEntityId());
        } catch (Exception e) {
            JsonObject response = new JsonObject();
            response.addProperty("message", "Error processing file: " + e.getMessage());
            return new ResponseEntity<>(response.toString(), HttpStatus.BAD_REQUEST);
        }
        return new ResponseEntity<>(masterPartCSV, HttpStatus.OK);
    }
}
