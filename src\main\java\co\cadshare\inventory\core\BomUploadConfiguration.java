package co.cadshare.inventory.core;

import co.cadshare.utils.ObjectExtension;
import lombok.Data;
import lombok.experimental.ExtensionMethod;

import java.io.IOException;

@Data
@ExtensionMethod(ObjectExtension.class)
public class BomUploadConfiguration {

  private final Integer nameColumn;
  private final Integer objectIdColumn;
  private final Integer descriptionColumn;
  private final boolean updateDescription;
  private final Integer numberColumn;
  private final boolean updateNumber;

  public BomUploadConfiguration(Integer nameColumn,
                                Integer objectIdColumn,
                                Integer descriptionColumn,
                                Integer numberColumn,
                                boolean updateDescription,
                                boolean updateNumber) {
    this.nameColumn = nameColumn;
    this.objectIdColumn = objectIdColumn;
    this.descriptionColumn = descriptionColumn;
    this.numberColumn = numberColumn;
    this.updateDescription = updateDescription;
    this.updateNumber = updateNumber;
  }


  public void validate() throws IOException {

    if ((nameColumn.isNull() || objectIdColumn.isNull()) ||
            (numberColumn.isNull() && descriptionColumn.isNull())) {
      throw new IOException("No Filename Column, ObjectId Column or Column(s) to update selected.");
    }
  }

  public Integer getNameColumn() {
    return (nameColumn.isNotNull() && nameColumn >= 0) ? nameColumn : null;
  }

  public Integer getObjectIdColumn() {
    return (objectIdColumn.isNotNull() && objectIdColumn >= 0) ? objectIdColumn : null;
  }

  public Integer getDescriptionColumn() {
    return (descriptionColumn.isNotNull() && descriptionColumn >= 0) ? descriptionColumn : null;
  }

  public Integer getNumberColumn() {
    return (numberColumn.isNotNull() && numberColumn >= 0) ? numberColumn : null;
  }
}
