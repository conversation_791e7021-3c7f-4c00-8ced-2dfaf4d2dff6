/*
 * Copyright 2016 Bell.
 */
package co.cadshare.masterParts.adapters.database;

import co.cadshare.shared.core.Language;
import co.cadshare.models.core.Model;
import co.cadshare.masterParts.core.MasterPart;
import co.cadshare.masterParts.core.extensions.kit.Kit;
import co.cadshare.masterParts.core.extensions.kit.KitPart;
import co.cadshare.masterParts.core.extensions.nonModelled.NonModelled;
import co.cadshare.masterParts.core.extensions.nonModelled.NonModelledPart;
import co.cadshare.masterParts.core.extensions.optionsSet.OptionsSet;
import co.cadshare.masterParts.core.extensions.optionsSet.OptionsSetPart;
import co.cadshare.masterParts.core.extensions.partModelLink.PartModelLink;
import co.cadshare.masterParts.core.extensions.partModelLink.LegacySupersededModelLink;
import co.cadshare.domainmodel.techDoc.TechDoc;
import lombok.extern.slf4j.Slf4j;
import org.springframework.dao.EmptyResultDataAccessException;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.namedparam.MapSqlParameterSource;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.jdbc.support.GeneratedKeyHolder;
import org.springframework.jdbc.support.KeyHolder;
import org.springframework.stereotype.Repository;

import java.util.*;

@Slf4j
@Repository
public class MasterPartExtensionsDao {

  private NamedParameterJdbcTemplate namedParamJdbcTemplate;
  private MasterPartDao masterPartDao;

  public MasterPartExtensionsDao(NamedParameterJdbcTemplate namedParamJdbcTemplate, MasterPartDao masterPartDao) {
    this.namedParamJdbcTemplate = namedParamJdbcTemplate;
    this.masterPartDao = masterPartDao;
  }

  private static final String LINKED_PARTS_EXIST = "SELECT COUNT(*) FROM masterpart_linkmodel_map WHERE masterpartid = :masterPartId";

  public boolean hasLinkedParts(int masterPartId) {
    Map<String, Object> parameters = new HashMap<String, Object>();
    parameters.put("masterPartId", masterPartId);

    // Check if user has a record for reading the latest comment in the thread.
    Integer linkedCount = (Integer) namedParamJdbcTemplate.queryForObject(LINKED_PARTS_EXIST, parameters, Integer.class);
    return linkedCount != 0;
  }

  private static final String SUPERSEDED_PARTS_EXIST = "SELECT COUNT(*) FROM masterpart_superseded_map WHERE masterpartid = :masterPartId";

  public boolean hasLegacySupersededPart(int masterPartId) {
    Map<String, Object> parameters = new HashMap<String, Object>();
    parameters.put("masterPartId", masterPartId);

    // Check if user has a record for reading the latest comment in the thread.
    Integer supersedeCount = (Integer) namedParamJdbcTemplate.queryForObject(SUPERSEDED_PARTS_EXIST, parameters, Integer.class);
    return supersedeCount != 0;
  }

  private static final String KITS_EXIST = "SELECT COUNT(*) " +
          "FROM masterpart_kit_map mkm, " +
          "mp_kit mk " +
          "WHERE mkm.masterpartid = :masterPartId " +
          "and mkm.kitid = mk.id " +
          "AND mk.deleted = false";

  public boolean hasKitParts(int masterPartId) {
    Map<String, Object> parameters = new HashMap<String, Object>();
    parameters.put("masterPartId", masterPartId);

    Integer kitCount = (Integer) namedParamJdbcTemplate.queryForObject(KITS_EXIST, parameters, Integer.class);
    return kitCount != 0;
  }

  private static final String OPTIONS_SETS_EXIST = "SELECT COUNT(*) FROM mp_optionset WHERE masterpartid = :masterPartId";
  public boolean hasOptionSet(int masterPartId) {
    Map<String, Object> parameters = new HashMap<String, Object>();
    parameters.put("masterPartId", masterPartId);

    Integer optionsCount = (Integer) namedParamJdbcTemplate.queryForObject(OPTIONS_SETS_EXIST, parameters, Integer.class);
    return optionsCount != 0;

  }private static final String LINKED_TECH_DOC_EXIST = "SELECT COUNT(*) FROM masterpart_techdoc_map WHERE masterpartid = :masterPartId";
  public boolean hasLinkedTechDocs(int masterPartId) {
    Map<String, Object> parameters = new HashMap<String, Object>();
    parameters.put("masterPartId", masterPartId);

    Integer linkedCount = (Integer) namedParamJdbcTemplate.queryForObject(LINKED_TECH_DOC_EXIST, parameters, Integer.class);
    return linkedCount != 0;
  }

  private static final String NONMODELLED_EXIST = "SELECT COUNT(*) FROM masterpart_nonmodelled_map WHERE masterpartid = :masterPartId";
  public boolean hasNonModelledPart(int masterPartId) {
    Map<String, Object> parameters = new HashMap<String, Object>();
    parameters.put("masterPartId", masterPartId);

    // Check if user has a record for reading the latest comment in the thread.
    Integer nonModelledCount = (Integer) namedParamJdbcTemplate.queryForObject(NONMODELLED_EXIST, parameters, Integer.class);
    return nonModelledCount != 0;
  }

  private static final String OPTIONS_SETS_FOR_MASTERPART_ID = "SELECT * FROM mp_optionset WHERE masterpartid = :masterpartId";

  private final static String GET_MASTERPARTS_FOR_OPTION_SET = "SELECT masterpartid FROM masterpart_optionset_map " +
          "WHERE optionsetid = :optionsetId ";

  public List<OptionsSet> getOptionsSetsForMasterPartId(int masterpartId, Integer languageIdForCode, Language defaultLanguage) {
    Map<String, Object> parameters = new HashMap<String, Object>();
    parameters.put("masterpartId", masterpartId);

    // Check if user has a record for reading the latest comment in the thread.
    List<OptionsSet> optionSets =  namedParamJdbcTemplate.query(OPTIONS_SETS_FOR_MASTERPART_ID, parameters, new BeanPropertyRowMapper<>(OptionsSet.class));

    for (OptionsSet option : optionSets) {
      Map<String, Object> parameters2 = new HashMap<String, Object>();
      parameters2.put("optionsetId", option.getId());

      List<Integer> masterPartIds = namedParamJdbcTemplate.queryForList(GET_MASTERPARTS_FOR_OPTION_SET, parameters2, Integer.class);
      List<OptionsSetPart> options = new ArrayList<>();

      for (Integer mpId : masterPartIds) {
        MasterPart masterPart = masterPartDao.getMasterPartForMasterPartId(mpId, languageIdForCode);
        OptionsSetPart part = new OptionsSetPart();
        part.setMasterPartId(mpId);
        part.setPartNumber(masterPart.getPartNumber());
        part.setStock(masterPart.getStock());
        part.setPrice(masterPart.getPrice());
        if (masterPart.getDescription() != null) {
          part.setDescription(masterPart.getDescription());
        } else if (languageIdForCode != defaultLanguage.getLanguageId()){
          part.setDescription(masterPartDao.getPartTranslationForMasterPartId(mpId, defaultLanguage.getLanguageId()));
        }

        options.add(part);
      }
      option.setOptionsSet(options);
    }
    return optionSets;
  }

  private static final String OPTIONS_SET_BY__ID = "SELECT * FROM mp_optionset WHERE id = :optionsetId";

  public OptionsSet getOptionsSetsForOptionSetId(int optionSetId, Integer languageIdForCode, Language defaultLanguage) {
    Map<String, Object> parameters = new HashMap<String, Object>();
    parameters.put("optionsetId", optionSetId);

    // Check if user has a record for reading the latest comment in the thread.
    OptionsSet option =  namedParamJdbcTemplate.queryForObject(OPTIONS_SET_BY__ID, parameters, new BeanPropertyRowMapper<>(OptionsSet.class));
    List<Integer> masterPartIds = namedParamJdbcTemplate.queryForList(GET_MASTERPARTS_FOR_OPTION_SET, parameters, Integer.class);

    List<OptionsSetPart> options = new ArrayList<>();
    for (Integer mpId : masterPartIds) {
      MasterPart masterPart = masterPartDao.getMasterPartForMasterPartId(mpId, languageIdForCode);
      OptionsSetPart part = new OptionsSetPart();
      part.setMasterPartId(mpId);
      part.setPartNumber(masterPart.getPartNumber());
      if (masterPart.getDescription() != null) {
        part.setDescription(masterPart.getDescription());
      } else if (languageIdForCode != defaultLanguage.getLanguageId()){
        part.setDescription(masterPartDao.getPartTranslationForMasterPartId(mpId, defaultLanguage.getLanguageId()));
      }
      options.add(part);
    }
    option.setOptionsSet(options);

    return option;
  }

  private final static String CREATE_OPTIONSET = "INSERT INTO mp_optionset (masterpartid, description) "
          + "VALUES( :masterpartId, :description)";

  public int createOptionSet(OptionsSet optionsSet) {
    KeyHolder keyHolder = new GeneratedKeyHolder();

    MapSqlParameterSource parameters = new MapSqlParameterSource();
    parameters.addValue("masterpartId", optionsSet.getMasterPartId());
    parameters.addValue("description", optionsSet.getDescription());

    int result = namedParamJdbcTemplate.update(CREATE_OPTIONSET, parameters, keyHolder, new String[] { "id" });
    return keyHolder.getKey().intValue();
  }

  private final static String UPDATE_OPTION_SET = "UPDATE mp_optionset SET description = :description WHERE id = :id";

  public void updateOptionSet(OptionsSet optionsSet) {

    MapSqlParameterSource parameters = new MapSqlParameterSource();
    parameters.addValue("id", optionsSet.getId());
    parameters.addValue("description", optionsSet.getDescription());

    namedParamJdbcTemplate.update(UPDATE_OPTION_SET, parameters);
  }

  private final static String CREATE_OPTIONSET_MAP = "INSERT INTO masterpart_optionset_map (optionsetid, masterpartid) "
          + "VALUES( :optionsetId, :masterpartId)";


  public boolean linkOptionSet(int optionSetId, int masterpartId) {
    KeyHolder keyHolder = new GeneratedKeyHolder();

    Map<String, Object> parameters = new HashMap<String, Object>();
    parameters.put("optionsetId", optionSetId);
    parameters.put("masterpartId", masterpartId);

    int result = namedParamJdbcTemplate.update(CREATE_OPTIONSET_MAP, parameters);
    return result > 0;
  }


  private static final String DELETE_OPTION_SET_MAP = "DELETE FROM masterpart_optionset_map WHERE optionsetid = :optionsetId";
  private static final String DELETE_OPTION_SET = "DELETE FROM mp_optionset WHERE id = :optionsetId";
  public boolean deleteOptionSet(int optionsetId) {
    Map<String, Integer> namedParameters = new HashMap<String, Integer>();
    namedParameters.put("optionsetId", optionsetId);

    deleteOptionSetMap(optionsetId);

    namedParamJdbcTemplate.update(DELETE_OPTION_SET, namedParameters);

    log.info("Deleted Master Part Option Set with ID [{}]", optionsetId);
    return true;
  }

  public boolean deleteOptionSetMap(int optionsetId) {
    Map<String, Integer> namedParameters = new HashMap<String, Integer>();
    namedParameters.put("optionsetId", optionsetId);

    namedParamJdbcTemplate.update(DELETE_OPTION_SET_MAP, namedParameters);

    log.info("Deleted Master Part Option Set with ID [{}]", optionsetId);
    return true;
  }

  private static final String DELETE_OPTION_SET_MAP_FOR_MASTER_PART = "DELETE FROM masterpart_optionset_map WHERE masterpartid = :masterPartId";

  public boolean deleteOptionSetMapByMasterPartId(int masterPartId) {
    Map<String, Integer> namedParameters = new HashMap<String, Integer>();
    namedParameters.put("masterPartId", masterPartId);

    namedParamJdbcTemplate.update(DELETE_OPTION_SET_MAP_FOR_MASTER_PART, namedParameters);

    log.info("Deleted Master Part Option Sets for Master Part [{}]", masterPartId);
    return true;
  }


  private static final String KIT_IDS_FOR_MASTERPART_ID = "SELECT kitid FROM masterpart_kit_map WHERE masterpartid = :masterpartId";

  private static final String KIT_FOR_KIT_ID = "SELECT * FROM mp_kit WHERE id = :kitId LIMIT 1";


  public List<Kit> getKitsForMasterPartId(int masterpartId, Integer languageId, Language defaultLanguage) {
    Map<String, Object> parameters = new HashMap<String, Object>();
    parameters.put("masterpartId", masterpartId);

    List<Kit> kitList = new ArrayList<>();
    // Check if user has a record for reading the latest comment in the thread.
    List<Integer> kitIds = namedParamJdbcTemplate.queryForList(KIT_IDS_FOR_MASTERPART_ID, parameters, Integer.class);

    for (Integer kitId : kitIds) {

      Map<String, Object> parameters2 = new HashMap<String, Object>();
      parameters2.put("kitId", kitId);

      Kit kit = namedParamJdbcTemplate.queryForObject(KIT_FOR_KIT_ID, parameters2, new BeanPropertyRowMapper<>(Kit.class));
      kit.setParts(getKitPartsForKitId(kitId, languageId, defaultLanguage));

      kitList.add(kit);
    }
    return kitList;
  }

  private final static String GET_MASTERPART_KITS_FOR_KIT_ID = "SELECT * FROM masterpart_kit_map " +
          "WHERE kitid = :kitId ";

  private List<KitPart> getKitPartsForKitId(int kitId, Integer languageIdForCode, Language defaultLanguage) {
    Map<String, Object> parameters = new HashMap<String, Object>();
    parameters.put("kitId", kitId);

    // Check if user has a record for reading the latest comment in the thread.
    List<KitPart> kitPart = namedParamJdbcTemplate.query(GET_MASTERPART_KITS_FOR_KIT_ID, parameters, new BeanPropertyRowMapper<>(KitPart.class));

    for (KitPart kitMap : kitPart) {
      MasterPart masterPart = masterPartDao.getMasterPartForMasterPartId(kitMap.getMasterPartId(), languageIdForCode, defaultLanguage);
      kitMap.setPartNumber(masterPart.getPartNumber());
      kitMap.setDescription(masterPart.getDescription());
      kitMap.setStock(masterPart.getStock());
      kitMap.setPrice(masterPart.getPrice());
      kitMap.setType(masterPart.getType());
    }
    return kitPart;
  }

  public Double getStockForKitPartWithWarehouseId(int masterPartId, int warehouseId) {
    return masterPartDao.getMasterPartStockById(masterPartId, warehouseId);
  }

  public Kit getKitForKitId(int kitId, Integer languageIdForCode, Language defaultLanguage) {
    Map<String, Object> parameters2 = new HashMap<String, Object>();
    parameters2.put("kitId", kitId);

    Kit kit = namedParamJdbcTemplate.queryForObject(KIT_FOR_KIT_ID, parameters2, new BeanPropertyRowMapper<>(Kit.class));
    if (languageIdForCode != null && defaultLanguage != null) {
      kit.setParts(getKitPartsForKitId(kitId, languageIdForCode, defaultLanguage));
    }
    return kit;
  }

  private final static String CREATE_KIT = "INSERT INTO mp_kit (title, description) "
          + "VALUES( :title, :description)";

  public int createKit(Kit kit) {
    KeyHolder keyHolder = new GeneratedKeyHolder();

    MapSqlParameterSource parameters = new MapSqlParameterSource();
    parameters.addValue("title", kit.getTitle());
    parameters.addValue("description", kit.getDescription());

    int result = namedParamJdbcTemplate.update(CREATE_KIT, parameters, keyHolder, new String[] { "id" });
    return keyHolder.getKey().intValue();
  }

  private final static String CREATE_KIT_MAP = "INSERT INTO masterpart_kit_map (kitid, masterpartid, quantity) "
          + "VALUES( :kitId, :masterpartId, :quantity)";

  public boolean linkKitParts(int kitId, KitPart kitPart) {
    KeyHolder keyHolder = new GeneratedKeyHolder();

    Map<String, Object> parameters = new HashMap<String, Object>();
    parameters.put("kitId", kitId);
    parameters.put("masterpartId", kitPart.getMasterPartId());
    parameters.put("quantity", kitPart.getQuantity());

    int result = namedParamJdbcTemplate.update(CREATE_KIT_MAP, parameters);
    return result > 0;
  }

  private final static String UPDATE_KIT_DETAILS = "UPDATE mp_kit SET  title = :title, description = :description "
          + "WHERE id = :kitId";

  public boolean updateKit(Kit kit) {
    Map<String, Object> parameters = new HashMap<String, Object>();
    parameters.put("kitId", kit.getId());
    parameters.put("title", kit.getTitle());
    parameters.put("description", kit.getDescription());

    int result = namedParamJdbcTemplate.update(UPDATE_KIT_DETAILS, parameters);
    return result > 0;
  }


  private static final String DELETE_KIT_MAP = "DELETE FROM masterpart_kit_map WHERE kitid = :kitId";
  private static final String DELETE_KIT = "DELETE FROM mp_kit WHERE id = :kitId";

  public void deleteKit(int kitId) {
    Map<String, Integer> namedParameters = new HashMap<String, Integer>();
    namedParameters.put("kitId", kitId);

    deleteKitMap(kitId);

    namedParamJdbcTemplate.update(DELETE_KIT, namedParameters);

    log.info("Deleted Master Part Kit with ID [{}]", kitId);
  }

  public void deleteKitMap(int kitId) {
    Map<String, Integer> namedParameters = new HashMap<String, Integer>();
    namedParameters.put("kitId", kitId);
    namedParamJdbcTemplate.update(DELETE_KIT_MAP, namedParameters);
  }

  private static final String DELETE_MASTER_PART_FROM_KIT_MAP = "DELETE FROM masterpart_kit_map WHERE kitid = :kitId AND masterpartid = :masterPartId";
  public void deleteMasterPartFromKit(int kitId, int masterPartId) {
    Map<String, Integer> namedParameters = new HashMap<String, Integer>();
    namedParameters.put("kitId", kitId);
    namedParameters.put("masterPartId", masterPartId);
    namedParamJdbcTemplate.update(DELETE_MASTER_PART_FROM_KIT_MAP, namedParameters);
  }

  private static final String DELETE_KIT_MAP_FOR_MASTER_PART = "DELETE FROM masterpart_kit_map WHERE masterpartid = :masterPartId";

  public boolean deleteKitMapByMasterPartId(int masterPartId) {
    Map<String, Integer> namedParameters = new HashMap<String, Integer>();
    namedParameters.put("masterPartId", masterPartId);

    namedParamJdbcTemplate.update(DELETE_KIT_MAP_FOR_MASTER_PART, namedParameters);

    log.info("Deleted Master Part Kit maps for Master Part [{}]", masterPartId);
    return true;
  }

  private final static String GET_PARTS_LINK_FOR_PARTID = "SELECT mplm.*,  mp.partnumber, m.autodeskurn AS urn, m.modelName, m.is2d, " +
          "mac.machineid, mac.name AS machineName, r.rangeid, r.name AS rangeName " +
          "FROM masterpart_linkmodel_map mplm " +
          "INNER JOIN model m ON mplm.modelid = m.modelid INNER JOIN masterpart mp ON mplm.masterpartid = mp.id " +
          "INNER JOIN machine mac ON m.machineid = mac.machineid INNER JOIN range r ON mac.rangeid = r.rangeid " +
          "WHERE mplm.masterpartid = :masterpartId ";

  public PartModelLink getLinkedPartForMasterPart(int masterpartId) {
    Map<String, Object> parameters = new HashMap<String, Object>();
    parameters.put("masterpartId", masterpartId);

    PartModelLink partLink = new PartModelLink();
    try {
      partLink = namedParamJdbcTemplate.queryForObject(GET_PARTS_LINK_FOR_PARTID, parameters, new BeanPropertyRowMapper<>(PartModelLink.class));
    } catch (EmptyResultDataAccessException e) {
      partLink = null;
    }
    return partLink;
  }

  private final static String CREATE_LINK_MAP = "INSERT INTO masterpart_linkmodel_map (masterpartid, modelid) "
          + "VALUES(:masterpartId, :modelId)";

  public boolean createLinkedPart(PartModelLink modelLink) {
    Map<String, Object> parameters = new HashMap<String, Object>();
    parameters.put("masterpartId", modelLink.getMasterPartId());
    parameters.put("modelId", modelLink.getModelId());

    int result = namedParamJdbcTemplate.update(CREATE_LINK_MAP, parameters);
    return result > 0;
  }

  private final static String UPDATE_LINK_MAP = "UPDATE masterpart_linkmodel_map SET  modelid = :modelId "
          + "WHERE masterpartid = :masterpartId";

  public boolean updateLinkedPart(PartModelLink modelLink) {
    Map<String, Object> parameters = new HashMap<String, Object>();
    parameters.put("masterpartId", modelLink.getMasterPartId());
    parameters.put("modelId", modelLink.getModelId());

    int result = namedParamJdbcTemplate.update(UPDATE_LINK_MAP, parameters);
    return result > 0;
  }

  private static final String DELETE_LINKED_PART = "DELETE FROM masterpart_linkmodel_map WHERE masterpartid = :masterPartId";

  public void deleteLinkedPartForPartId(int masterPartId) {
    Map<String, Integer> namedParameters = new HashMap<String, Integer>();
    namedParameters.put("masterPartId", masterPartId);

    namedParamJdbcTemplate.update(DELETE_LINKED_PART, namedParameters);

    log.info("Deleted Master Part Link for Part with ID [{}]", masterPartId);
  }

  private final static String GET_MODELS_CONTAINING_MASTERPART = "SELECT DISTINCT(mod.modelid), mod.*, mac.name AS machinename FROM masterpart mp " +
          "INNER JOIN part p ON mp.partnumber = p.partnumber " +
          "INNER JOIN model mod ON p.modelid = mod.modelid " +
          "INNER JOIN machine mac ON mod.machineid = mac.machineid " +
          "INNER JOIN range r ON mac.rangeid = r.rangeid " +
          "WHERE mp.id = :masterpartId " +
          "AND r.manufacturerid = mp.manufacturerid " +
          "AND mod.archived = FALSE " +
          "AND mac.archived = FALSE ";


  public List<Model> getModelsForMasterPart(int masterpartId) {
    Map<String, Object> parameters = new HashMap<String, Object>();
    parameters.put("masterpartId", masterpartId);

    // Check if user has a record for reading the latest comment in the thread.
    List<Model> models =  namedParamJdbcTemplate.query(GET_MODELS_CONTAINING_MASTERPART, parameters, new BeanPropertyRowMapper<>(Model.class));
    return models;
  }

  private final static String GET_MODELS_CONTAINING_MASTERPART_FOR_DEALER_ENTITY = "SELECT DISTINCT(mod.modelid), mod.*, mac.name AS machinename FROM masterpart mp " +
          "INNER JOIN part p ON mp.partnumber = p.partnumber " +
          "INNER JOIN model mod ON p.modelid = mod.modelid " +
          "INNER JOIN machine mac ON mod.machineid = mac.machineid " +
          "INNER JOIN range r ON mac.rangeid = r.rangeid " +
          "INNER JOIN manualmodelmap mmm ON mod.modelid = mmm.modelid " +
          "INNER JOIN v_subentitymanuals vsem ON mmm.manualid = vsem.manualid " +
          "WHERE mp.id = :masterpartId " +
          "AND r.manufacturerid = mp.manufacturerid " +
          "AND vsem.manufacturersubentityid = :dealerEntityId";
  public List<Model> getModelsForDealerMasterPart(int dealerEntityId, int masterpartId) {
    Map<String, Object> parameters = new HashMap<String, Object>();
    parameters.put("masterpartId", masterpartId);
    parameters.put("dealerEntityId", dealerEntityId);

    // Check if user has a record for reading the latest comment in the thread.
    List<Model> models =  namedParamJdbcTemplate.query(GET_MODELS_CONTAINING_MASTERPART_FOR_DEALER_ENTITY, parameters, new BeanPropertyRowMapper<>(Model.class));
    return models;
  }

  private static final String GET_NONMODELLED_FOR_MASTERPART_ID = "SELECT nonmodelledmasterpartid FROM masterpart_nonmodelled_map WHERE masterpartid = :masterPartId";
  public NonModelled getNonModelledForMasterPartId(int masterPartId, Integer languageIdForCode, Language defaultLanguage) {

    NonModelled nonModelled = new NonModelled();
    nonModelled.setMasterPartId(masterPartId);

      Map<String, Object> parameters = new HashMap<String, Object>();
      parameters.put("masterPartId", masterPartId);

      List<Integer> masterPartIds = namedParamJdbcTemplate.queryForList(GET_NONMODELLED_FOR_MASTERPART_ID, parameters, Integer.class);

      List<NonModelledPart> nonModelledParts = new ArrayList<>();
      for (Integer mpId : masterPartIds) {
        MasterPart masterPart = masterPartDao.getMasterPartForMasterPartId(mpId, languageIdForCode);
        NonModelledPart part = new NonModelledPart();
        part.setMasterPartId(mpId);
        part.setPartNumber(masterPart.getPartNumber());

        part.setStock(masterPart.getStock());
        part.setPrice(masterPart.getPrice());
        if (masterPart.getDescription() != null) {
          part.setDescription(masterPart.getDescription());
        } else if (languageIdForCode != defaultLanguage.getLanguageId()){
          part.setDescription(masterPartDao.getPartTranslationForMasterPartId(mpId, defaultLanguage.getLanguageId()));
        }
        nonModelledParts.add(part);
      }
      nonModelled.setNonModelledPart(nonModelledParts);

      return nonModelled;
    }

  private final static String CREATE_NONMODELLED_MP_MAP = "INSERT INTO masterpart_nonmodelled_map (masterpartid, nonmodelledmasterpartid) "
          + "VALUES(:masterpartId, :nonModelledMasterPartId)";

  public boolean createNonModelled(NonModelled nonModelled) {
    int created = 0;
    //Master part being linked from
    int masterPartId = nonModelled.getMasterPartId();
    for (NonModelledPart part : nonModelled.getNonModelledPart()) {
      Map<String, Object> parameters = new HashMap<String, Object>();

      parameters.put("masterpartId", masterPartId);
      //Master part being linked too as non modelled
      parameters.put("nonModelledMasterPartId", part.getMasterPartId());

      int result = namedParamJdbcTemplate.update(CREATE_NONMODELLED_MP_MAP, parameters);
      created = created + result;
    }
    return created > 0;
  }

  private static final String DELETE_NONMODELLED_MP = "DELETE FROM masterpart_nonmodelled_map WHERE masterpartid = :masterPartId";

  public void deleteNonModelled(int masterPartId) {
    Map<String, Integer> namedParameters = new HashMap<String, Integer>();
    namedParameters.put("masterPartId", masterPartId);

    namedParamJdbcTemplate.update(DELETE_NONMODELLED_MP, namedParameters);

    log.info("Deleted Master Part NonModelled for Part with ID [{}]", masterPartId);
  }

  private final static String GET_SUPERSEDED_FOR_MODELID = "SELECT mpsm.*, mp.partnumber, m.autodeskurn AS urn, m.modelName, m.is2d, m.translatetype, " +
          "mac.machineid, mac.name AS machineName, r.rangeid, r.name AS rangeName, p.objectid " +
          "FROM masterpart_superseded_map mpsm " +
          "INNER JOIN model m ON mpsm.modelid = m.modelid INNER JOIN masterpart mp ON mpsm.masterpartid = mp.id " +
          "INNER JOIN machine mac ON m.machineid = mac.machineid INNER JOIN range r ON mac.rangeid = r.rangeid " +
          "INNER JOIN part p ON p.partnumber = mp.partnumber " +
          "WHERE p.modelid = :modelId ";

  public List<LegacySupersededModelLink> getLegacySupersededMasterPartsForModel(Integer modelId) {
  Map<String, Object> parameters = new HashMap<String, Object>();
    parameters.put("modelId", modelId);

    List<LegacySupersededModelLink> supersededLink = new ArrayList<>();
    try {
      supersededLink = namedParamJdbcTemplate.query(GET_SUPERSEDED_FOR_MODELID, parameters, new BeanPropertyRowMapper<>(LegacySupersededModelLink.class));
    } catch (EmptyResultDataAccessException e) {
      supersededLink = null;
    }
    return supersededLink;
  }


  private final static String GET_SUPERSEDED_FOR_PARTID = "SELECT mpsm.*,  mp.partnumber, m.autodeskurn AS urn, m.modelName, m.is2d, " +
          "mac.machineid, mac.name AS machineName, r.rangeid, r.name AS rangeName " +
          "FROM masterpart_superseded_map mpsm " +
          "INNER JOIN model m ON mpsm.modelid = m.modelid INNER JOIN masterpart mp ON mpsm.masterpartid = mp.id " +
          "INNER JOIN machine mac ON m.machineid = mac.machineid INNER JOIN range r ON mac.rangeid = r.rangeid " +
          "WHERE mpsm.masterpartid = :masterpartId ";

  public LegacySupersededModelLink getLegacySupersededPartForMasterPart(int masterpartId) {
    Map<String, Object> parameters = new HashMap<String, Object>();
    parameters.put("masterpartId", masterpartId);

    LegacySupersededModelLink supersededLink = new LegacySupersededModelLink();
    try {
      supersededLink = namedParamJdbcTemplate.queryForObject(GET_SUPERSEDED_FOR_PARTID, parameters, new BeanPropertyRowMapper<>(LegacySupersededModelLink.class));
    } catch (EmptyResultDataAccessException e) {
      supersededLink = null;
    }
    return supersededLink;
  }


  private final static String CREATE_SUPERSEDED_MAP = "INSERT INTO masterpart_superseded_map (masterpartid, modelid) "
          + "VALUES(:masterpartId, :modelId)";

  public Boolean createLegacySuperseded(LegacySupersededModelLink modelLink) {
    Map<String, Object> parameters = new HashMap<String, Object>();
    parameters.put("masterpartId", modelLink.getMasterPartId());
    parameters.put("modelId", modelLink.getModelId());

    int result = namedParamJdbcTemplate.update(CREATE_SUPERSEDED_MAP, parameters);
    return result > 0;
  }


  private final static String UPDATE_SUPERSEDED_MAP = "UPDATE masterpart_superseded_map SET  modelid = :modelId "
          + "WHERE masterpartid = :masterpartId";

  public boolean updateLegacySuperseded(LegacySupersededModelLink modelLink) {
    Map<String, Object> parameters = new HashMap<String, Object>();
    parameters.put("masterpartId", modelLink.getMasterPartId());
    parameters.put("modelId", modelLink.getModelId());

    int result = namedParamJdbcTemplate.update(UPDATE_SUPERSEDED_MAP, parameters);
    return result > 0;
  }

  private static final String DELETE_SUPERSEDED_PART = "DELETE FROM masterpart_superseded_map WHERE masterpartid = :masterPartId";

  public void deleteLegacySupersededForPartId(int masterPartId) {
    Map<String, Integer> namedParameters = new HashMap<String, Integer>();
    namedParameters.put("masterPartId", masterPartId);

    namedParamJdbcTemplate.update(DELETE_SUPERSEDED_PART, namedParameters);

    log.info("Deleted Superseded Link for Part with ID [{}]", masterPartId);
  }


  private final static String GET_KITS_FOR_MODELID = "SELECT DISTINCT(mk.id), mk.* FROM mp_kit mk " +
          "INNER JOIN masterpart_kit_map mkm ON mkm.kitid = mk.id " +
          "INNER JOIN masterpart mp ON mp.id = mkm.masterpartid " +
          "INNER JOIN part p ON mp.partnumber = p.partnumber " +
          "WHERE p.modelid = :modelId " +
          "AND mp.manufacturerid = :manufacturerId " +
          "GROUP BY mk.id, mp.partnumber ";

  public List<Kit> getKitsForModelId(int modelId, int manufacturerId, Integer languageId, Language defaultLanguage) {
    Map<String, Object> parameters = new HashMap<String, Object>();
    parameters.put("modelId", modelId);
    parameters.put("manufacturerId", manufacturerId);

    List<Kit> kits = new ArrayList<>();
    try {
      kits = namedParamJdbcTemplate.query(GET_KITS_FOR_MODELID, parameters, new BeanPropertyRowMapper<>(Kit.class));

      //Fetch part part for kits to be returned
      for (Kit kit : kits) {
        List<KitPart> parts = getKitPartsForKitId(kit.getId(), languageId, defaultLanguage);
        kit.setParts(parts);
      }
    } catch (EmptyResultDataAccessException e) {
      kits = null;
    }
    return kits;
  }

   private final static String GET_KITS_FOR_MANUFACTURER_ID = "SELECT DISTINCT(mk.id), mk.* FROM mp_kit mk " +
          "INNER JOIN masterpart_kit_map mkm ON mkm.kitid = mk.id " +
          "INNER JOIN masterpart mp ON mp.id = mkm.masterpartid " +
          "WHERE mp.manufacturerid = :manufacturerId ";

  public List<Kit> getKitsForManufacturerId(int manufacturerId, Integer languageId, Language defaultLanguage) {
    List<Kit> kits = new ArrayList<>();
    try {
      kits = getKitsDetailsByManufacturerId(manufacturerId);

      //Fetch part part for kits to be returned
      for (Kit kit : kits) {
        List<KitPart> parts = getKitPartsForKitId(kit.getId(), languageId, defaultLanguage);
        kit.setParts(parts);
      }
    } catch (EmptyResultDataAccessException e) {
      kits = null;
    }
    return kits;
  }

  public List<Kit> getKitsDetailsByManufacturerId(Integer manufacturerId) {
    Map<String, Object> parameters = new HashMap<String, Object>();
    parameters.put("manufacturerId", manufacturerId);

    List<Kit> kits = new ArrayList<>();
    try {
      kits = namedParamJdbcTemplate.query(GET_KITS_FOR_MANUFACTURER_ID, parameters, new BeanPropertyRowMapper<>(Kit.class));
    } catch (EmptyResultDataAccessException e) {
      kits = null;
    }
    return kits;
  }

  private final static String GET_LINKED_TECH_DOCS_FOR_MASTERPART_ID = "SELECT td.*, mtm.page AS linkedpage FROM technical_document td " +
          "INNER JOIN masterpart_techdoc_map mtm ON mtm.techdocid = td.id " +
          "WHERE mtm.masterpartid = :masterpartId ";

    public List<TechDoc> getLinkedTechDocsForPart(int masterpartId) {
      Map<String, Object> parameters = new HashMap<String, Object>();
      parameters.put("masterpartId", masterpartId);

      List<TechDoc> techDocs = new ArrayList<>();
      try {
        techDocs = namedParamJdbcTemplate.query(GET_LINKED_TECH_DOCS_FOR_MASTERPART_ID, parameters, new BeanPropertyRowMapper<>(TechDoc.class));

        if (techDocs != null) {
          for (TechDoc techDoc : techDocs) {
            techDoc.setUrl(buildPageLinkUrl(techDoc));
          }
        }
      } catch (EmptyResultDataAccessException e) {
        techDocs = null;
      }
      return techDocs;
    }

  private String buildPageLinkUrl(TechDoc techDoc) {
    String linkedFileUrl = techDoc.getUrl();
      if (techDoc.getLinkedPage() != null && techDoc.getLinkedPage() > 0) {
         linkedFileUrl = techDoc.getUrl().concat("#page=").concat(String.valueOf(techDoc.getLinkedPage()));
      }
      return linkedFileUrl;
  }

  private static final String DELETE_LINKED_TECH_DOCS = "DELETE FROM masterpart_techdoc_map WHERE masterpartid = :masterPartId";

  public void deleteLinkedTechDocs(int masterPartId) {
    Map<String, Integer> namedParameters = new HashMap<String, Integer>();
    namedParameters.put("masterPartId", masterPartId);

    namedParamJdbcTemplate.update(DELETE_LINKED_TECH_DOCS, namedParameters);

    log.info("Deleted Linked TechDocs for Part with ID [{}]", masterPartId);
  }

  private static final String DELETE_TECH_DOC_LINKS_BY_ID = "DELETE FROM masterpart_techdoc_map WHERE techdocid = :techDocId";

  public void deleteLinkedTechDocById(int techDocId) {
    Map<String, Integer> namedParameters = new HashMap<String, Integer>();
    namedParameters.put("techDocId", techDocId);

    namedParamJdbcTemplate.update(DELETE_TECH_DOC_LINKS_BY_ID, namedParameters);

    log.info("Deleted TechDocs Links for TechDoc with ID [{}]", techDocId);
  }

  private final static String CREATE_LINKED_TECH_DOCS_MAP = "INSERT INTO masterpart_techdoc_map (masterpartid, techdocid, page) "
          + "VALUES(:masterpartId, :techDocId, :linkedPage)";

  public boolean createLinkedTechDocs(int masterpartId, List<TechDoc> linkedTechDocs) {

    Map<String, Object> parameters = new HashMap<String, Object>();
    int added = 0;
    for (TechDoc techDoc : linkedTechDocs) {
      if (techDoc.getId() != null && techDoc.getId() > 0) {
        parameters.put("masterpartId", masterpartId);
        parameters.put("techDocId", techDoc.getId());
        parameters.put("linkedPage", techDoc.getLinkedPage());

        int result = namedParamJdbcTemplate.update(CREATE_LINKED_TECH_DOCS_MAP, parameters);
        added = added + result;
      }
    }

    return added > 0;
  }
}
