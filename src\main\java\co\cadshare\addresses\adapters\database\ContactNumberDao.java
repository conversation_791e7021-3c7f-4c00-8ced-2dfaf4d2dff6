package co.cadshare.addresses.adapters.database;

import co.cadshare.addresses.core.ContactNumber;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.namedparam.BeanPropertySqlParameterSource;
import org.springframework.jdbc.core.namedparam.MapSqlParameterSource;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.jdbc.support.GeneratedKeyHolder;
import org.springframework.jdbc.support.KeyHolder;
import org.springframework.stereotype.Repository;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Repository
public class ContactNumberDao {

  @Autowired
  private NamedParameterJdbcTemplate namedParamJdbcTemplate;

  private final static String SELECT_NUMBER_BY_ID = "SELECT * FROM contactnumber WHERE id=:numberId";

  public ContactNumber getContactNumberById(int numberId) {

    Map<String, Object> parameters = new HashMap<String, Object>();
    parameters.put("numberId", numberId);

    ContactNumber number = (ContactNumber) namedParamJdbcTemplate.queryForObject(SELECT_NUMBER_BY_ID, parameters, new BeanPropertyRowMapper<>(ContactNumber.class));
    return number;
  }

  private final static String GET_NUMBERS_FOR_USER_ID = "SELECT n.* FROM contactnumber n INNER JOIN user_contactnumber_map unm ON unm.contactnumberid = n.id WHERE unm.userid = :userId ";

  public List<ContactNumber> getNumbersForUser(int userId) {
    Map<String, Object> parameters = new HashMap<String, Object>();
    parameters.put("userId", userId);

    List<ContactNumber> numberList = (List<ContactNumber>) namedParamJdbcTemplate.query(GET_NUMBERS_FOR_USER_ID, parameters, new BeanPropertyRowMapper<ContactNumber>(ContactNumber.class));

    return numberList;
  }

  private static final String CREATE_NUMBER = "INSERT INTO contactnumber (contactnumber) "
      + "VALUES(:contactNumber)";

  private static final String CREATE_USER_NUMBER_MAP = "INSERT INTO user_contactnumber_map (contactnumberid, userid) "
      + "VALUES(:contactNumberId, :userId)";

  public Integer createContactNumber(int userId, ContactNumber number) {

    BeanPropertySqlParameterSource namedParameters = new BeanPropertySqlParameterSource(number);
    KeyHolder keyHolder = new GeneratedKeyHolder();

    namedParamJdbcTemplate.update(CREATE_NUMBER, namedParameters, keyHolder, new String[] { "id" });
    int numberId = keyHolder.getKey().intValue();

    MapSqlParameterSource mapperParameters = new MapSqlParameterSource();
    mapperParameters.addValue("contactNumberId", numberId);
    mapperParameters.addValue("userId", userId);

    namedParamJdbcTemplate.update(CREATE_USER_NUMBER_MAP, mapperParameters);

    return numberId;
  }
  
  public int updateContactNumber(int userId, ContactNumber number) {
    deleteContactNumber(userId, number.getId());
    int addressId = createContactNumber(userId, number);
    return addressId;
  }

  private static final String DELETE_NUMBER= "DELETE FROM contactnumber where id = :numberId";
  private static final String DELETE_USER_NUMBER_MAP = "DELETE FROM user_contactnumber_map where contactnumberid = :numberId AND userid = :userId";

  public Boolean deleteContactNumber(int userId, int numberId) {
    Map namedParameters = new HashMap();
    namedParameters.put("numberId", numberId);
    namedParameters.put("userId", userId);

    namedParamJdbcTemplate.update(DELETE_USER_NUMBER_MAP, namedParameters);
    namedParamJdbcTemplate.update(DELETE_NUMBER, namedParameters);

    return true;
  }

}
