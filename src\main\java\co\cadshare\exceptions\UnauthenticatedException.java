package co.cadshare.exceptions;

import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ResponseStatus;

@ResponseStatus(value = HttpStatus.UNAUTHORIZED)
public class UnauthenticatedException extends RuntimeException {
	public UnauthenticatedException(String message) {
		super(message);
	}

	public UnauthenticatedException(String message, Throwable throwable) {
		super(message, throwable);
	}

}
