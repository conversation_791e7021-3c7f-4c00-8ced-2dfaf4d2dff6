package co.cadshare.modelMgt.models.core;

import com.autodesk.client.ApiResponse;
import com.autodesk.client.model.Metadata;
import com.autodesk.client.model.MetadataCollection;
import lombok.Getter;

import java.util.LinkedHashMap;
import java.util.List;

public class MetadataWrapper {
    @Getter
    private Metadata metadata;
    private int statusCode;

    private Model model;

    public MetadataWrapper(ApiResponse<Metadata> metadata, Model model) {
        this.metadata = metadata.getData();
        this.statusCode = metadata.getStatusCode();
        this.model = model;
    }

    public boolean isParseable() throws Exception {
        if (statusCode == 200) {
            return true;
        } else if (statusCode == 202) {
            return false;
        } else {
            throw new Exception("Unknown status code " + statusCode + " returned whilst trying to determine property status for urn: " + model.getAutodeskUrn());
        }
    }

    public String getGuid() throws Exception {
        if (metadata != null && metadata.getData() != null && metadata.getData().getMetadata() != null && metadata.getData().getMetadata().size() > 0
                && metadata.getData().getMetadata().get(0) != null && metadata.getData().getMetadata().get(0).getGuid() != null) {
            return metadata.getData().getMetadata().get(0).getGuid();
        } else {
            throw new Exception("Exception thrown trying to get guid for " + model.getAutodeskUrn());
        }
    }

    public boolean isGuidProvidedByMetadata() {
        return metadata != null &&
                metadata.getData() != null &&
                metadata.getData() != null &&
                metadata.getData().getMetadata() != null &&
                !metadata.getData().getMetadata().isEmpty() &&
                metadata.getData().getMetadata().get(0) != null &&
                metadata.getData().getMetadata().get(0).getGuid() != null;
    }

    public List<MetadataCollection> getMetadataCollectionList() {
        return getMetadata().getData().getCollection();
    }

    public LinkedHashMap getFilePropertiesForObject(int objectId) {
        MetadataCollection metadataCollection = getMetadataCollectionList().stream()
                .filter(collection -> collection.getObjectid().equals(objectId))
                .findFirst()
                .get();
        LinkedHashMap properties = (LinkedHashMap) metadataCollection.getProperties();
        return (LinkedHashMap)properties.get("File Properties");
    }


}
