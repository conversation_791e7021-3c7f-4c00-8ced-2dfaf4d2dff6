package co.cadshare.aspects.logging;

import co.cadshare.aspects.BaseAspect;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.*;
import org.springframework.stereotype.Component;

@Component
@Aspect
public class ServiceLoggingAspect extends BaseAspect {

    @Pointcut("@annotation(Log)")
    public void serviceLogPointcut(){}


    @Before("serviceLogPointcut()")
    public void logAllMethodCallsBeforeAdvice(JoinPoint joinPoint){
        configureInterception(joinPoint);
        logger.info("before: {}  |  object: {}  |  user: {}", methodName, args[1], user.accessDetails());
    }

    @AfterReturning("serviceLogPointcut()")
    public void logAllMethodCallsAfterAdvice(JoinPoint joinPoint){
        configureInterception(joinPoint);
        logger.info("after: {}  |  object: {}  |  user: {}", methodName, args[1], user.accessDetails());
    }

    @AfterThrowing(value = "serviceLogPointcut()", throwing = "ex")
    public void logAllMethodCallsAfterException(JoinPoint joinPoint, Exception ex) throws Throwable {
        configureInterception(joinPoint);
        logger.info("during: {}  |  object: {}  |  user: {}  |  thrown: {}", methodName, args[1], user.accessDetails(), ex.getMessage());
    }
}
