/*
 * Copyright 2016 Bell.
 */
package co.cadshare.masterParts.adapters.api.web;

import co.cadshare.masterParts.boundary.MasterPartSupersessionService;
import co.cadshare.masterParts.core.SupersessionHistoryItem;
import co.cadshare.shared.core.user.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Slf4j
@Controller
@PreAuthorize("hasRole('ROLE_MANUFACTURER')")
@RequestMapping("/manufacturers/{manufacturer-id}/master-parts")
public class ManufacturerMasterPartController {

    private final MasterPartSupersessionService supersessionService;

    public ManufacturerMasterPartController(MasterPartSupersessionService supersessionService) {
        this.supersessionService = supersessionService;
    }

    @PostMapping(value = "/{master-part-id}/supersede")
    public HttpEntity<Void> supersedePart(@AuthenticationPrincipal User currentUser,
                                             @PathVariable("manufacturer-id") int manufacturerId,
                                             @PathVariable("master-part-id") int masterPartId,
                                             @RequestBody PostSupersedeRequestDto requestDto) throws Exception {

        log("supersede", currentUser, manufacturerId, masterPartId);
        supersessionService.supersedePart(currentUser, masterPartId, requestDto.getSupersedingMasterPartId());
        return new ResponseEntity<>(HttpStatus.OK);
    }

    @PostMapping(value = "/{master-part-id}/supersession-remove-part")
    public HttpEntity<Void> removePartFromSupersession(@AuthenticationPrincipal User currentUser,
                                                       @PathVariable("manufacturer-id") int manufacturerId,
                                                       @PathVariable("master-part-id") int masterPartId) throws Exception {

        log("supersession-remove-part", currentUser, manufacturerId, masterPartId);
        supersessionService.removePartFromSupersession(currentUser, masterPartId);
        return new ResponseEntity<>(HttpStatus.OK);
    }

    @PostMapping(value = "/{master-part-id}/supersession-split")
    public HttpEntity<Void> splitPartFromSupersession(@AuthenticationPrincipal User currentUser,
                                                      @PathVariable("manufacturer-id") int manufacturerId,
                                                      @PathVariable("master-part-id") int masterPartId) throws Exception {

        log("supersession-split", currentUser, manufacturerId, masterPartId);
        supersessionService.splitSupersession(currentUser, masterPartId);
        return new ResponseEntity<>(HttpStatus.OK);
    }

    @GetMapping(value = "/{master-part-id}/supersession-history")
    public HttpEntity<SupersessionHistoryDto> getMasterPartSupersessionHistory(@AuthenticationPrincipal User currentUser,
                                                                               @PathVariable("manufacturer-id") int manufacturerId,
                                                                               @PathVariable("master-part-id") int masterPartId) throws Exception {

        log("getMasterPartSupersessionHistory", currentUser, manufacturerId, masterPartId);
        List<SupersessionHistoryItem> historyItems = supersessionService.getMasterPartSupersessionHistoryForManufacturer(masterPartId, currentUser);
        List<SupersessionHistoryItemDto> history = SupersessionHistoryItemMapper.Instance.coresToDtos(historyItems);
        SupersessionHistoryDto supersessionHistoryDto = new SupersessionHistoryDto();
        supersessionHistoryDto.setSupersessionHistory(history);
        return new ResponseEntity<>(supersessionHistoryDto, HttpStatus.OK);
    }

    private static void log(String action, User currentUser, int manufacturerId, int masterPartId) {
        log.info("ACCESS: User [{}], [{}}, manufacturerId [{}], partId [{}]",
                currentUser.accessDetails(), action, manufacturerId, masterPartId);
    }
}
