Feature: Search MasterParts

  Scenario Outline: Manufacturer Search MasterParts by PartN<PERSON>ber
    Given I am a Manufacturer with email address <emailAddress>
    And I use <priceLists> and <warehouses>
    When I search MasterParts for part number by <searchTerm>
    Then I am presented with a list of <count> MasterParts that match part number on <searchTerm>
    Examples:
      | emailAddress                 | priceLists | warehouses | searchTerm   | count |
      | <EMAIL> | false      | false      | Cat-part-7   | 1     |
      | <EMAIL> | false      | false      | Cat-part     | 51    |
      | <EMAIL>         | true       | false      | Jcb-part-201 | 1     |
      | <EMAIL>    | false      | true       | Lbr-part-202 | 1     |
      | <EMAIL>       | true       | true       | Trx-part-203 | 1     |

  Scenario Outline: Manufacturer Search MasterParts by PartDescription
    Given I am a Manufacturer with <NAME_EMAIL>
    When I search MasterParts for part description by <searchTerm> using <languageCode>
    Then I am presented with a list of <count> MasterParts that match part description on <searchTerm>
    Examples:
      | searchTerm                  | languageCode | count |
      | Cat-part-3 desc in English  | EN           | 1     |
      | Cat-part-3 desc en Français | FR           | 1     |
      | Cat-part-3 desc in English  | FR           | 0     |

  Scenario Outline: Manufacturer Exact Match Search MasterParts by PartNumber
    Given I am a Manufacturer with <NAME_EMAIL>
    When I <exactMatch> search MasterParts for part number by <searchTerm>
    Then I am presented with a list of <count> MasterParts that match part number on <searchTerm>
    Examples:
      | searchTerm | count | exactMatch |
      | Cat-part-1 | 18    | false      |
      | Cat-part-1 | 1     | true       |

  Scenario Outline: Dealer Search MasterParts by PartNumber
    Given I am a Dealer with email address <emailAddress>
    And my Manufacturer uses <priceLists> and <warehouses>
    When I search MasterParts for part number by <searchTerm>
    Then I am presented with a list of <count> MasterParts that match part number on <searchTerm>
    Examples:
      | emailAddress           | priceLists | warehouses | searchTerm   | count |
      | <EMAIL> | false      | false      | Cat-part-7   | 1     |
      | <EMAIL> | false      | false      | Cat-part     | 51    |
      | <EMAIL>         | true       | false      | Jcb-part-201 | 1     |
      | <EMAIL>    | false      | true       | Lbr-part-202 | 1     |
      | <EMAIL>       | true       | true       | Trx-part-203 | 1     |

  Scenario Outline: Dealer Search MasterParts by PartDescription
    Given I am a Dealer with email address <emailAddress>
    And my Manufacturer uses <priceLists> and <warehouses>
    When I search MasterParts for part description by <searchTerm> using <languageCode>
    Then I am presented with a list of <count> MasterParts that match part description on <searchTerm>
    Examples:
      | emailAddress           | priceLists | warehouses | searchTerm                  | languageCode | count |
      | <EMAIL> | false      | false      | Cat-part-3 desc in English  | EN           | 1     |
      | <EMAIL> | false      | false      | Cat-part-3 desc en Français | FR           | 1     |
      | <EMAIL> | false      | false      | Cat-part-3 desc in English  | FR           | 0     |

  Scenario Outline: Dealer Exact Match Search MasterParts by PartNumber
    Given I am a Dealer with <NAME_EMAIL>
    When I <exactMatch> search MasterParts for part number by <searchTerm>
    Then I am presented with a list of <count> MasterParts that match part number on <searchTerm>
    Examples:
      | searchTerm | count | exactMatch |
      | Cat-part-1 | 18    | false      |
      | Cat-part-1 | 1     | true       |


  Scenario Outline: DealerPlus Search MasterParts by PartNumber
    Given I am a DealerPlus with <NAME_EMAIL>
    When I search MasterParts for part number by <searchTerm>
    Then I am presented with a list of <count> MasterParts that match part number on <searchTerm>
    Examples:
      | searchTerm | count |
      | Cat-part-7 | 1     |
      | Cat-part   | 51    |

  Scenario Outline: DealerPlus Search MasterParts by PartDescription
    Given I am a Dealer with email address <emailAddress>
    And my Manufacturer uses <priceLists> and <warehouses>
    When I search MasterParts for part description by <searchTerm> using <languageCode>
    Then I am presented with a list of <count> MasterParts that match part description on <searchTerm>
    Examples:
      | emailAddress                       | priceLists | warehouses | searchTerm                   | languageCode | count |
      | <EMAIL> | false      | false      | Cat-part-3 desc in English   | EN           | 1     |
      | <EMAIL> | false      | false      | Cat-part-3 desc en Français  | FR           | 1     |
      | <EMAIL> | false      | false      | Cat-part-3 desc in English   | FR           | 0     |
      | <EMAIL>         | true       | false      | Jcb-part-201 desc in English | EN           | 1     |
      | <EMAIL>    | false      | true       | Lbr-part-202 desc in English | EN           | 1     |
      | <EMAIL>       | true       | true       | Trx-part-203 desc in English | EN           | 1     |