package co.cadshare.services;

import co.cadshare.masterParts.adapters.database.MasterPartExtensionsDao;
import co.cadshare.publications.boundary.ManualQueryPort;
import co.cadshare.shared.adapters.aws.s3.S3StorageClient;
import co.cadshare.domainmodel.techDoc.TechDoc;
import co.cadshare.persistence.*;
import co.cadshare.publications.boundary.ManualService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.dao.EmptyResultDataAccessException;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Slf4j
@Service
public class TechDocService {

    private TechDocDao techDocDao;
    private ManualService manualService;
    private ManualQueryPort manualDao;
    private MasterPartExtensionsDao masterPartExtensionsDao;
    private S3StorageClient s3StorageClient;


    public TechDocService(TechDocDao techDocDao, ManualService manualService, ManualQueryPort manualDao, MasterPartExtensionsDao masterPartExtensionsDao, S3StorageClient s3StorageClient) {
        this.techDocDao = techDocDao;
        this.manualService = manualService;
        this.manualDao = manualDao;
        this.masterPartExtensionsDao = masterPartExtensionsDao;
        this.s3StorageClient = s3StorageClient;
    }

    public List<TechDoc> getTechDocByManufacturerId(int manufacturerId) {
        List<TechDoc> techDocs = techDocDao.getTechDocByManufacturerId(manufacturerId);
        return techDocs;
    }

    public TechDoc getTechDocById(int techDocId) {
        return techDocDao.getTechDocById(techDocId);
    }

    public int createTechDoc(TechDoc techDoc) {
        if (techDoc.getUrl().contains("/manufacturer/" + techDoc.getManufacturerId())) {
            return techDocDao.createTechDoc(techDoc);
        } else {
            throw new IllegalArgumentException("Invalid tech doc URL");
        }
    }

    public boolean updateTechDoc(TechDoc techDoc) {
        if (techDoc.getUrl().contains("/manufacturer/" + techDoc.getManufacturerId())) {
            TechDoc existingTechDoc = getTechDocById(techDoc.getId());
            techDocDao.updateTechDoc(techDoc);

            boolean deleted = s3StorageClient.deleteS3FileHelper(existingTechDoc.getUrl(), techDoc.getUrl());
            return true;
        } else {
            throw new IllegalArgumentException("Invalid tech doc URL");
        }
    }

    public Boolean deleteTechDoc(int techDocId) {

        TechDoc existingTechDoc = getTechDocById(techDocId);
        manualService.deleteTechDocFromManualTechDocMap(techDocId);
        masterPartExtensionsDao.deleteLinkedTechDocById(techDocId);
        boolean deleted = techDocDao.deleteTechDoc(techDocId);
        if (deleted) {
            s3StorageClient.deleteS3File(existingTechDoc.getUrl());
        }
        return deleted;
    }

    public List<TechDoc> getTechDocByManualId(int manualId) {
        List<Integer> techDocsIds = manualDao.getTechDocIdsByManualId(manualId);

        List<TechDoc> techDocs = new ArrayList<>();
        for (Integer id : techDocsIds) {
            try {
                techDocs.add(techDocDao.getTechDocById(id));
            } catch (EmptyResultDataAccessException e) {
                log.error("Tech doc with id [{}] not found", id);
            }
        }
        return techDocs;
    }
}
