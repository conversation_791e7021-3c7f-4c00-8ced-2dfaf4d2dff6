package co.cadshare.modelMgt.shared.core;

import co.cadshare.modelMgt.publications.core.Customer;
import co.cadshare.modelMgt.publications.core.Dealer;
import co.cadshare.modelMgt.publications.core.DealerPlus;
import lombok.Data;

@Data
public class Purchaser {
    private Integer id;
    private String name;

	public boolean isDealer() {
		return this instanceof Dealer;
	}

	public boolean isDealerPlus() {
		return this instanceof DealerPlus;
	}

	public boolean isCustomer() {
		return this instanceof Customer;
	}
}
