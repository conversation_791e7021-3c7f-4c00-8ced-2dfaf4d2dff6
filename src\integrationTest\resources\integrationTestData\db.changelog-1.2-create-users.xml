<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                   http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.0.xsd
">
<!-- caterpillar users -->
    <changeSet author="AndyB" id="1.2-integration-test-data-create-users-1">
        <sql>
            INSERT INTO public.users(
            userid, password, firstname, lastname,
            createddate, createdbyuserid, modifieddate, modifiedbyuserid, emailaddress, usertype, userstatus)
            VALUES (1, '$2a$12$ZIoTcKSeo/WbngLxb7AtfeVMMR4aBMG./2Pke6TFjczCKsz7uwT3i', 'Caterpillar', 'Manufacturer',
            NOW(), 1, NOW(), 1, '<EMAIL>', 1, 1);
        </sql>
    </changeSet>
    <changeSet author="AndyB" id="1.2-integration-test-data-create-users-2">
        <sql>
            INSERT INTO public.users(
            userid, password, firstname, lastname,
            createddate, createdbyuserid, modifieddate, modifiedbyuserid, emailaddress, usertype, userstatus)
            VALUES (2, '$2a$12$ZIoTcKSeo/WbngLxb7AtfeVMMR4aBMG./2Pke6TFjczCKsz7uwT3i', 'Caterpillar', 'Dealer',
            NOW(), 1, NOW(), 1, '<EMAIL>', 3, 1);
        </sql>
    </changeSet>
    <changeSet author="AndyB" id="1.2-integration-test-data-create-users-2-2">
        <sql>
            INSERT INTO public.users(
            userid, password, firstname, lastname,
            createddate, createdbyuserid, modifieddate, modifiedbyuserid, emailaddress, usertype, userstatus)
            VALUES (17, '$2a$12$ZIoTcKSeo/WbngLxb7AtfeVMMR4aBMG./2Pke6TFjczCKsz7uwT3i', 'Caterpillar', 'Dealer-2',
            NOW(), 1, NOW(), 1, '<EMAIL>', 3, 1);
        </sql>
    </changeSet>
    <changeSet author="AndyB" id="1.2-integration-test-data-create-users-3">
        <sql>
            INSERT INTO public.users(
            userid, password, firstname, lastname,
            createddate, createdbyuserid, modifieddate, modifiedbyuserid, emailaddress, usertype, userstatus)
            VALUES (3, '$2a$12$ZIoTcKSeo/WbngLxb7AtfeVMMR4aBMG./2Pke6TFjczCKsz7uwT3i', 'Caterpillar', 'Dealer Plus',
            NOW(), 1, NOW(), 1, '<EMAIL>', 7, 1);
        </sql>
    </changeSet>
    <changeSet author="AndyB" id="1.2-integration-test-data-create-users-7">
        <sql>
            INSERT INTO public.users(
            userid, password, firstname, lastname,
            createddate, createdbyuserid, modifieddate, modifiedbyuserid, emailaddress, usertype, userstatus)
            VALUES (7, '$2a$12$ZIoTcKSeo/WbngLxb7AtfeVMMR4aBMG./2Pke6TFjczCKsz7uwT3i', 'Caterpillar', 'Dealer Plus Customer',
            NOW(), 1, NOW(), 1, '<EMAIL>', 4, 1);
        </sql>
    </changeSet>
    <changeSet author="AndyB" id="1.2-integration-test-data-create-users-3-2">
        <sql>
            INSERT INTO public.users(
            userid, password, firstname, lastname,
            createddate, createdbyuserid, modifieddate, modifiedbyuserid, emailaddress, usertype, userstatus)
            VALUES (18, '$2a$12$ZIoTcKSeo/WbngLxb7AtfeVMMR4aBMG./2Pke6TFjczCKsz7uwT3i', 'Caterpillar', 'Dealer Plus-2',
            NOW(), 1, NOW(), 1, '<EMAIL>', 7, 1);
        </sql>
    </changeSet>
    <changeSet author="AndyB" id="1.2-integration-test-data-create-users-7-2">
        <sql>
            INSERT INTO public.users(
            userid, password, firstname, lastname,
            createddate, createdbyuserid, modifieddate, modifiedbyuserid, emailaddress, usertype, userstatus)
            VALUES (19, '$2a$12$ZIoTcKSeo/WbngLxb7AtfeVMMR4aBMG./2Pke6TFjczCKsz7uwT3i', 'Caterpillar', 'Dealer Plus Customer-2',
            NOW(), 1, NOW(), 1, '<EMAIL>', 4, 1);
        </sql>
    </changeSet>

<!-- jcb users -->
    <changeSet author="AndyB" id="1.2-integration-test-data-create-users-4">
        <sql>
            INSERT INTO public.users(
            userid, password, firstname, lastname,
            createddate, createdbyuserid, modifieddate, modifiedbyuserid, emailaddress, usertype, userstatus)
            VALUES (4, '$2a$12$ZIoTcKSeo/WbngLxb7AtfeVMMR4aBMG./2Pke6TFjczCKsz7uwT3i', 'Jcb', 'Manufacturer',
            NOW(), 1, NOW(), 1, '<EMAIL>', 1, 1);
        </sql>
    </changeSet>
    <changeSet author="AndyB" id="1.2-integration-test-data-create-users-5">
        <sql>
            INSERT INTO public.users(
            userid, password, firstname, lastname,
            createddate, createdbyuserid, modifieddate, modifiedbyuserid, emailaddress, usertype, userstatus)
            VALUES (5, '$2a$12$ZIoTcKSeo/WbngLxb7AtfeVMMR4aBMG./2Pke6TFjczCKsz7uwT3i', 'Jcb', 'Dealer',
            NOW(), 1, NOW(), 1, '<EMAIL>', 3, 1);
        </sql>
    </changeSet>
    <changeSet author="AndyB" id="1.2-integration-test-data-create-users-6">
        <sql>
            INSERT INTO public.users(
            userid, password, firstname, lastname,
            createddate, createdbyuserid, modifieddate, modifiedbyuserid, emailaddress, usertype, userstatus)
            VALUES (6, '$2a$12$ZIoTcKSeo/WbngLxb7AtfeVMMR4aBMG./2Pke6TFjczCKsz7uwT3i', 'Jcb', 'Dealer Plus',
            NOW(), 1, NOW(), 1, '<EMAIL>', 7, 1);
        </sql>
    </changeSet>

    <changeSet author="AndyB" id="1.2-integration-test-data-create-users-8">
        <sql>
            INSERT INTO public.users(
            userid, password, firstname, lastname,
            createddate, createdbyuserid, modifieddate, modifiedbyuserid, emailaddress, usertype, userstatus)
            VALUES (8, '$2a$12$ZIoTcKSeo/WbngLxb7AtfeVMMR4aBMG./2Pke6TFjczCKsz7uwT3i', 'Jcb', 'Dealer Plus',
            NOW(), 1, NOW(), 1, '<EMAIL>', 4, 1);
        </sql>
    </changeSet>

    <!-- liebherr users -->
    <changeSet author="AndyB" id="1.2-integration-test-data-create-users-9">
        <sql>
            INSERT INTO public.users(
            userid, password, firstname, lastname,
            createddate, createdbyuserid, modifieddate, modifiedbyuserid, emailaddress, usertype, userstatus)
            VALUES (9, '$2a$12$ZIoTcKSeo/WbngLxb7AtfeVMMR4aBMG./2Pke6TFjczCKsz7uwT3i', 'Liebherr', 'Manufacturer',
            NOW(), 1, NOW(), 1, '<EMAIL>', 1, 1);
        </sql>
    </changeSet>
    <changeSet author="AndyB" id="1.2-integration-test-data-create-users-10">
        <sql>
            INSERT INTO public.users(
            userid, password, firstname, lastname,
            createddate, createdbyuserid, modifieddate, modifiedbyuserid, emailaddress, usertype, userstatus)
            VALUES (10, '$2a$12$ZIoTcKSeo/WbngLxb7AtfeVMMR4aBMG./2Pke6TFjczCKsz7uwT3i', 'Liebherr', 'Dealer',
            NOW(), 1, NOW(), 1, '<EMAIL>', 3, 1);
        </sql>
    </changeSet>
    <changeSet author="AndyB" id="1.2-integration-test-data-create-users-11">
        <sql>
            INSERT INTO public.users(
            userid, password, firstname, lastname,
            createddate, createdbyuserid, modifieddate, modifiedbyuserid, emailaddress, usertype, userstatus)
            VALUES (11, '$2a$12$ZIoTcKSeo/WbngLxb7AtfeVMMR4aBMG./2Pke6TFjczCKsz7uwT3i', 'Liebherr', 'Dealer Plus',
            NOW(), 1, NOW(), 1, '<EMAIL>', 7, 1);
        </sql>
    </changeSet>
    <changeSet author="AndyB" id="1.2-integration-test-data-create-users-12">
        <sql>
            INSERT INTO public.users(
            userid, password, firstname, lastname,
            createddate, createdbyuserid, modifieddate, modifiedbyuserid, emailaddress, usertype, userstatus)
            VALUES (12, '$2a$12$ZIoTcKSeo/WbngLxb7AtfeVMMR4aBMG./2Pke6TFjczCKsz7uwT3i', 'Liebherr', 'Dealer Plus Customer',
            NOW(), 1, NOW(), 1, '<EMAIL>', 4, 1);
        </sql>
    </changeSet>

    <!-- terex users -->
    <changeSet author="AndyB" id="1.2-integration-test-data-create-users-13">
        <sql>
            INSERT INTO public.users(
            userid, password, firstname, lastname,
            createddate, createdbyuserid, modifieddate, modifiedbyuserid, emailaddress, usertype, userstatus)
            VALUES (13, '$2a$12$ZIoTcKSeo/WbngLxb7AtfeVMMR4aBMG./2Pke6TFjczCKsz7uwT3i', 'Terex', 'Manufacturer',
            NOW(), 1, NOW(), 1, '<EMAIL>', 1, 1);
        </sql>
    </changeSet>
    <changeSet author="AndyB" id="1.2-integration-test-data-create-users-14">
        <sql>
            INSERT INTO public.users(
            userid, password, firstname, lastname,
            createddate, createdbyuserid, modifieddate, modifiedbyuserid, emailaddress, usertype, userstatus)
            VALUES (14, '$2a$12$ZIoTcKSeo/WbngLxb7AtfeVMMR4aBMG./2Pke6TFjczCKsz7uwT3i', 'Terex', 'Dealer',
            NOW(), 1, NOW(), 1, '<EMAIL>', 3, 1);
        </sql>
    </changeSet>
    <changeSet author="AndyB" id="1.2-integration-test-data-create-users-15">
        <sql>
            INSERT INTO public.users(
            userid, password, firstname, lastname,
            createddate, createdbyuserid, modifieddate, modifiedbyuserid, emailaddress, usertype, userstatus)
            VALUES (15, '$2a$12$ZIoTcKSeo/WbngLxb7AtfeVMMR4aBMG./2Pke6TFjczCKsz7uwT3i', 'Terex', 'Dealer Plus',
            NOW(), 1, NOW(), 1, '<EMAIL>', 7, 1);
        </sql>
    </changeSet>
    <changeSet author="AndyB" id="1.2-integration-test-data-create-users-16">
        <sql>
            INSERT INTO public.users(
            userid, password, firstname, lastname,
            createddate, createdbyuserid, modifieddate, modifiedbyuserid, emailaddress, usertype, userstatus)
            VALUES (16, '$2a$12$ZIoTcKSeo/WbngLxb7AtfeVMMR4aBMG./2Pke6TFjczCKsz7uwT3i', 'Terex', 'Dealer Plus Customer',
            NOW(), 1, NOW(), 1, '<EMAIL>', 4, 1);
        </sql>
    </changeSet>


</databaseChangeLog>
