package co.cadshare.models.core.processor;

import co.cadshare.models.core.MetadataObjectExtended;
import co.cadshare.models.core.processor.fileproperties.NexatSparePartPropertyProcessor;
import org.junit.Before;
import org.junit.Test;

import java.util.Collections;
import java.util.LinkedHashMap;

import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;

public class NexatSparePartPropertyProcessorTest {
    
    private NexatSparePartPropertyProcessor out;
    private LinkedHashMap<String, String> properties;
    private MetadataObjectExtended objectExtended;
    
    @Before
    public void Before() {
        out = new NexatSparePartPropertyProcessor();
        out.setSynonyms(Collections.singletonList("DB_SPARE_PART_CHAR"));
        setProperties();
        objectExtended = new MetadataObjectExtended();
    }

    @Test
    public void isSparePartForValueA() {
        properties.put("DB_SPARE_PART_CHAR", "A");
        out.setProperties(properties, objectExtended);

        assertTrue(objectExtended.isSparePart());
    }

    @Test
    public void isSparePartForValueB() {
        properties.put("DB_SPARE_PART_CHAR", "B");
        out.setProperties(properties, objectExtended);

        assertTrue(objectExtended.isSparePart());
    }

    @Test
    public void isSparePartForValueC() {
        properties.put("DB_SPARE_PART_CHAR", "C");
        out.setProperties(properties, objectExtended);

        assertTrue(objectExtended.isSparePart());
    }

    @Test
    public void isSparePartForValueD() {
        properties.put("DB_SPARE_PART_CHAR", "D");
        out.setProperties(properties, objectExtended);

        assertTrue(objectExtended.isSparePart());
    }

    @Test
    public void isSparePartForValueZ() {
        properties.put("DB_SPARE_PART_CHAR", "Z");
        out.setProperties(properties, objectExtended);

        assertTrue(objectExtended.isSparePart());
    }

    @Test
    public void isNotSparePartForAlternativeValue() {
        properties.put("DB_SPARE_PART_CHAR", "X");
        out.setProperties(properties, objectExtended);

        assertFalse(objectExtended.isSparePart());
    }

    @Test
    public void isNotSparePartForNullValue() {

        out.setProperties(properties, objectExtended);

        assertFalse(objectExtended.isSparePart());
    }

    private void setProperties() {
        properties = new LinkedHashMap<String, String>();
        properties.put("Original System", "NX");
        properties.put("CAD_PARTNAME", "16005-0011353-01");
    }
}
