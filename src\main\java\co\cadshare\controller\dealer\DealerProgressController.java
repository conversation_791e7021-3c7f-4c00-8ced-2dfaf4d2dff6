package co.cadshare.controller.dealer;

import co.cadshare.shared.core.manufacturer.ManufacturerProgress;
import co.cadshare.shared.core.manufacturer.ManufacturerProgressResult;
import co.cadshare.shared.core.user.User;
import co.cadshare.services.ManufacturerProgressService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@Slf4j
@Controller
@PreAuthorize("hasRole('ROLE_MANUFACTURER_SUB_ENTITY_DEALER_PLUS')")
@RequestMapping("/dealerplus/progress")
public class DealerProgressController {

  @Autowired
  private ManufacturerProgressService manufacturerProgressService;

  @PreAuthorize("#currentUser.manufacturerSubEntityId == #manufacturerSubEntityId")
  @RequestMapping(value = "/{manufacturerSubEntityId}", method = RequestMethod.GET)
  public HttpEntity<List<ManufacturerProgressResult>> getDealerPlusProgressTasksByManufacturerId(@AuthenticationPrincipal User currentUser,
                                                                                 @PathVariable int manufacturerSubEntityId,
                                                                                 @RequestParam(value = "process", required = false) List<ManufacturerProgress.Process> process,
                                                                                 @RequestParam(value = "machineId", required = false) Integer machineId,
                                                                                 @RequestParam(value = "modelId", required = false) Integer modelId) throws Exception {

    log.info("ACCESS: User [{}],  dealerplus - getDealerPlusProgressTasksByManufacturerId, manufacturer [{}]", currentUser, manufacturerSubEntityId);
    List<ManufacturerProgressResult> progress = manufacturerProgressService.getDealerManufacturerProgressTasksByManufacturerId(manufacturerSubEntityId, process, machineId, modelId);

    return new ResponseEntity<>(progress, HttpStatus.OK);
  }
}
