package co.cadshare.masterParts.adapters.database;

import co.cadshare.masterParts.boundary.MasterPartSearchRequest;
import co.cadshare.masterParts.core.SupersededMasterPart;
import co.cadshare.masterParts.core.SupersessionHistoryItem;
import co.cadshare.shared.core.manufacturer.subentity.ManufacturerSubEntity;
import co.cadshare.shared.core.user.User;
import co.cadshare.masterParts.boundary.MasterPartCommandPort;
import co.cadshare.masterParts.boundary.MasterPartQueryPort;
import co.cadshare.masterParts.core.MasterPart;
import org.apache.commons.lang3.NotImplementedException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service
public class MasterPartDataFacade implements MasterPartQueryPort, MasterPartCommandPort {

    private final MpMasterPartEntityRepo masterPartRepo;
    private final MasterPartComplexQueryRepo masterPartComplexQueryRepo;
    private final MasterPartSupersessionQueryRepo masterPartSupersessionQueryRepo;
    private final MasterPartDao masterPartDao;

    @Autowired
    public MasterPartDataFacade(MpMasterPartEntityRepo masterPartRepo,
                                MasterPartComplexQueryRepo masterPartComplexQueryRepo,
                                MasterPartSupersessionQueryRepo supersessionQueryRepo,
                                MasterPartDao masterPartDao) {
        this.masterPartRepo = masterPartRepo;
        this.masterPartComplexQueryRepo = masterPartComplexQueryRepo;
        this.masterPartSupersessionQueryRepo = supersessionQueryRepo;
        this.masterPartDao = masterPartDao;
    }

    @Override
    public Integer create(User user, MasterPart masterPart) {
        MpMasterPartEntity masterPartEntity = MpMasterPartEntityMapper.Instance.coreToEntity(masterPart);
        MpMasterPartEntity savedEntity = this.masterPartRepo.save(masterPartEntity);
        return savedEntity.getMasterPartId();
    }

    @Override
    public void update(User user, MasterPart masterPart) throws Exception {
        MpMasterPartEntity masterPartEntity = MpMasterPartEntityMapper.Instance.coreToEntity(masterPart);
        this.masterPartRepo.save(masterPartEntity);
this.masterPartRepo.flush();
    }

    @Override
    public void delete(User user, MasterPart masterPart) throws Exception {
        throw new NotImplementedException("You cannot currently delete a Master Part.");
    }

    @Override
    public MasterPart get(Integer id) {
        MpMasterPartEntity entity = this.masterPartRepo.getOne(id);
        return MpMasterPartEntityMapper.Instance.entityToCore(entity);
    }

    @Override
    public List<MasterPart> getList() {
        return new ArrayList<>();
    }

    @Override
    public List<SupersessionHistoryItem> getSupersessionHistory(User user, String partNumber) {

        List<SupersededMasterPartEntityCte> supersededParts =
                masterPartSupersessionQueryRepo.getSupersedingMasterParts(partNumber, user.getManufacturerId());
        List<MasterPartSupersessionEntityView> masterPartEntities =
                masterPartComplexQueryRepo.getMasterPartsForSupersessionHistory(supersededParts, user.getManufacturerId());
        // Match the order of masterPartEntities with supersededParts
        List<MasterPartSupersessionEntityView> orderedMasterPartEntities = masterPartEntities.stream()
                .sorted(Comparator.comparingInt(mp -> supersededParts.indexOf(
                        supersededParts.stream()
                                .filter(sp -> sp.getMasterPartId().equals(mp.getId()))
                                .findFirst()
                                .orElse(null)
                )))
                .collect(Collectors.toList());
        List<SupersessionHistoryItem> historyItems = MasterPartEntityViewMapper.Instance.entityToSupersessionHistoryItems(orderedMasterPartEntities);
        for (int i = 0; i < historyItems.size(); i++)
            historyItems.get(i).setSupersessionIndex(i);
        return historyItems;
    }

    @Override
    public List<SupersededMasterPart> getDerivedSupersessionMasterParts(User user, String partNumber) {
        SupersededMasterPartEntityCte maxSupersedingMasterPart =
                masterPartSupersessionQueryRepo.getMaxSupersedingMasterPart(partNumber, user.getManufacturerId());
        List<SupersededMasterPartEntityCte> entities =
                masterPartSupersessionQueryRepo.getSupersedingMasterParts(maxSupersedingMasterPart.getMaxSupersessionPartNumber(), user.getManufacturerId());
        return SupersededMasterPartCteMapper.Instance.entitiesToCores(entities);
    }

    @Override
    public List<MasterPart> getMasterPartsForSupersession(User user, List<SupersededMasterPart> supersededMasterParts) {
        List<Integer> ids = supersededMasterParts.stream()
                .map(SupersededMasterPart::getId)
                .collect(Collectors.toList());
        List<MasterPartEntityView> entities = masterPartComplexQueryRepo.getMasterPartsForSupersession(ids, user.getManufacturerId());
        return MasterPartEntityViewMapper.Instance.entitiesToMasterParts(entities);
    }

    @Override
    public List<MasterPart> getSupersessionSplitMasterParts(User user, List<SupersessionHistoryItem> supersededMasterParts) {
        List<Integer> ids = supersededMasterParts.stream()
                .map(SupersessionHistoryItem::getMasterPartId)
                .collect(Collectors.toList());
        List<MasterPartEntityView> entities = masterPartComplexQueryRepo.getMasterPartsForSupersession(ids, user.getManufacturerId());
        return MasterPartEntityViewMapper.Instance.entitiesToMasterParts(entities);
    }

    @Override
    public List<MasterPart> searchMasterPartsForManufacturer(MasterPartSearchRequest request, int manufacturerId) {
        return masterPartDao.searchMasterPartsForManufacturer(request, manufacturerId);
    }

    @Override
    public List<MasterPart> searchMasterPartsForPurchaser(MasterPartSearchRequest request,
                                                          int manufacturerId,
                                                          ManufacturerSubEntity purchaser,
                                                          boolean priceListEnabled,
                                                          Integer priceListIdentifierId,
                                                          boolean warehousesEnabled,
                                                          Integer warehouseId) {

        return masterPartDao.searchMasterPartsForPurchaser(request,
                manufacturerId,
				purchaser,
                priceListEnabled,
                priceListIdentifierId,
                warehousesEnabled,
                warehouseId);
    }

	@Override
	public List<MasterPart> searchMasterPartsForDealerPlusPrice(MasterPartSearchRequest request, ManufacturerSubEntity purchaser) {
		return masterPartDao.searchMasterPartsForDealerPlusPrice(request, purchaser);
	}

	@Override
    public MasterPart getMasterPartByPartNumber(String partNumber, int manufacturerId, Integer languageId) {
        return masterPartDao.getMasterPartByPartNumber(partNumber, manufacturerId, languageId);
    }

    private void addSupersessions(int manufacturerId, List<MasterPart> masterParts) {
        List<String> masterPartNumbers = masterParts.stream().map(MasterPart::getPartNumber).collect(Collectors.toList());
        List<SupersededMasterPartEntityCte> supersededParts = masterPartSupersessionQueryRepo.getMaxSupersedingMasterParts(masterPartNumbers, manufacturerId);
        if(!supersededParts.isEmpty()) {
            masterParts.forEach(masterPart -> {
                Optional<SupersededMasterPartEntityCte> matchingSupersededPart = supersededParts.stream()
                        .filter(supersededPart ->
                            supersededPart.getPartNumber().equals(masterPart.getPartNumber())
                                && supersededPart.getSupersessionPartNumber() != null)
                        .findFirst();
                matchingSupersededPart.ifPresent(supersededMasterPartEntityCte -> {
                        masterPart.setSupersessionPartNumber(supersededMasterPartEntityCte.getMaxSupersessionPartNumber());
                    }
                );
            });
        }
    }
}
