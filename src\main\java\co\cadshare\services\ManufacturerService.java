package co.cadshare.services;

import co.cadshare.masterParts.boundary.MasterPartKitService;
import co.cadshare.modelMgt.models.adapters.database.ModelDao;
import co.cadshare.modelMgt.models.boundary.ModelQueryPort;
import co.cadshare.modelMgt.publications.boundary.TechDocService;
import co.cadshare.modelMgt.publications.boundary.VideoService;
import co.cadshare.orders.boundary.OrderQueryPort;
import co.cadshare.shared.boundary.S3StoragePort;
import co.cadshare.shared.core.Language;
import co.cadshare.domainmodel.machine.Machine;
import co.cadshare.modelMgt.publications.core.Manual;
import co.cadshare.shared.adapters.database.manufacturer.ManufacturerConfigDao;
import co.cadshare.shared.adapters.database.manufacturer.ManufacturerDao;
import co.cadshare.shared.core.manufacturer.AdditionalEmail;
import co.cadshare.shared.core.manufacturer.Manufacturer;
import co.cadshare.shared.core.manufacturer.ManufacturerConfig;
import co.cadshare.shared.core.manufacturer.ManufacturerDetails;
import co.cadshare.shared.core.manufacturer.subentity.ManufacturerSubEntity;
import co.cadshare.modelMgt.models.core.Model;
import co.cadshare.domainmodel.priceListIdentifier.PriceListIdentifier;
import co.cadshare.domainmodel.range.Range;
import co.cadshare.domainmodel.serialnumber.SerialNumber;
import co.cadshare.modelMgt.publications.core.TechDoc;
import co.cadshare.shared.core.user.User;
import co.cadshare.shared.core.manufacturer.ManufacturerSettings;
import co.cadshare.modelMgt.publications.core.Video;
import co.cadshare.masterParts.adapters.database.MasterPartExtensionsDao;
import co.cadshare.masterParts.core.extensions.kit.Kit;
import co.cadshare.orders.core.Order;
import co.cadshare.orders.core.OrderStatus;
import co.cadshare.orders.core.OrderUnreadCounts;
import co.cadshare.persistence.*;
import co.cadshare.modelMgt.ranges.adapters.database.RangeDao;
import co.cadshare.modelMgt.publications.boundary.ManualQueryPort;
import co.cadshare.response.CustomerModel;
import co.cadshare.users.adapters.database.UserDetailsDao;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.List;

@Service
public class ManufacturerService {

  private final Logger log = LoggerFactory.getLogger(getClass());

  @Autowired
  private ManufacturerDao manufacturerDao;

  @Autowired
  private ManufacturerConfigDao manufacturerConfigDao;

  @Autowired
  private ManufacturerSubEntityDao manufacturerSubEntityDao;

  @Autowired
  private RangeDao rangeDao;

  @Autowired
  private MachineDao machineDao;

  @Autowired
  private OrderQueryPort orderQuery;

  @Autowired
  private ModelDao modelDao;

  @Autowired
  private ManualQueryPort manualDao;

  @Autowired
  private UserDetailsDao userDetailsDao;

  @Autowired
  private MasterPartExtensionsDao masterPartExtensionsDao;

  @Autowired
  private TechDocService techDocService;

  @Autowired
  private VideoService videoService;

  @Autowired
  private MasterPartKitService masterPartKitService;

  @Autowired
  S3StoragePort s3StorageClient;

  @Autowired
  private ModelQueryPort modelQuery;

  public Integer createManufacturer(Manufacturer manufacturer) throws Exception {

    java.sql.Timestamp now = new java.sql.Timestamp(Calendar.getInstance().getTime().getTime());
    manufacturer.setCreatedDate(now);

    return manufacturerDao.createManufacturer(manufacturer);
  }

  public List<Range> getRangesForManufacturer(int manufacturerId) {

    return rangeDao.getRangesForManufacturer(manufacturerId);
  }

  public List<Machine> getMachinesForManufacturer(int manufacturerId) {

    return machineDao.getMachinesForManufacturer(manufacturerId);
  }

  public List<SerialNumber> getDetailedManualsForManufacturer(int manufacturerId, Boolean published) {

    List<Manual> manualList = manualDao.getManualsForManufacturer(manufacturerId, published,"manualid", 9999);
    List<SerialNumber> serialList = new ArrayList<>();
    for (Manual manual : manualList)
      serialList.add(buildSerialNumber(manual));
    return serialList;
  }

  public SerialNumber getDetailedManualForManufacturer(int manufacturerId, int manualId, Boolean published) {
    Manual manual = manualDao.getManual(manualId);
    return buildSerialNumber(manual);
  }

  private SerialNumber buildSerialNumber(Manual manual) {
    SerialNumber serialNumber = new SerialNumber();
    serialNumber.setManualId(manual.getManualId());
    serialNumber.setManualName(manual.getManualName());
    serialNumber.setManualStatus(manual.getStatus().toString());
    serialNumber.setSerialNumber(manual.getSerialNumber());
    serialNumber.setCreatedDate(manual.getCreatedDate());
    List<Integer> subEntityIds = manualDao.getSubEntityIdsForManual(serialNumber.getManualId());
    List<String> subEntityNames = manualDao.getSubEntityNamesForManual(serialNumber.getManualId());
    serialNumber.setManufacturerSubEntityIds(subEntityIds);
    serialNumber.setManufacturerSubEntityNames(subEntityNames);
    serialNumber.setUseViewableImage(manual.isUseViewableImage());
    List<CustomerModel> modelList = modelDao.getModelsByManualId(serialNumber.getManualId());

    List<Integer> modelIds = new ArrayList<>();
    List<String> modelNames = new ArrayList<>();
    Integer modelId = null;
    for (CustomerModel model : modelList) {
      modelIds.add(model.getModelId());
      modelNames.add(model.getModelName());

      modelId = model.getModelId();
    }
    serialNumber.setModelId(modelIds);
    serialNumber.setModelName(modelNames);

    serialNumber.setFeaturedModelId(manual.getFeaturedModelId());
    serialNumber.setFeaturedModelUrl(manual.getFeaturedModelUrl());

      if (modelId != null) {
        Model model = modelQuery.get(modelId);
        serialNumber.setMachineId(model.getMachineId());
        Range range = rangeDao.getRangeForMachineId(model.getMachineId());
        serialNumber.setRangeId(range.getRangeId());
      }
      List<TechDoc> techDocs = techDocService.getTechDocByManualId(serialNumber.getManualId());

    List<Integer> techDocIds = new ArrayList<>();
    List<String> techDocNames = new ArrayList<>();
    for (TechDoc techDoc : techDocs) {
      techDocIds.add(techDoc.getId());
      techDocNames.add(techDoc.getName());
    }
    serialNumber.setTechDocId(techDocIds);
    serialNumber.setTechDocName(techDocNames);


    List<Video> videos = videoService.getVideoByManualId(serialNumber.getManualId());

    List<Integer> videoIds = new ArrayList<>();
    List<String> videoNames = new ArrayList<>();
    for (Video video : videos) {
      videoIds.add(video.getId());
      videoNames.add(video.getName());
    }
    serialNumber.setVideoId(videoIds);
    serialNumber.setVideoName(videoNames);


    List<Kit> kits = masterPartKitService.getKitsByManualId(serialNumber.getManualId(), null, null, null);

    List<Integer> kitIds = new ArrayList<>();
    List<String> kitTitles = new ArrayList<>();
    for (Kit kit : kits) {
      kitIds.add(kit.getId());
      kitTitles.add(kit.getTitle() + " - " + kit.getDescription());
    }
    serialNumber.setKitId(kitIds);
    serialNumber.setKitTitle(kitTitles);
    return serialNumber;
  }

  public List<ManufacturerSubEntity> getManufacturerSubEntitiesForManufacturer(int manufacturerId,
      ManufacturerSubEntity.ManufacturerSubEntityType subEntityType) {
    return manufacturerSubEntityDao.getManufacturerSubEntitiesForManufacturer(manufacturerId, subEntityType);
  }

  public List<Order> getOrdersForManufacturer(int manufacturerId, List<OrderStatus> status,
      Timestamp requestedDeliveryDateBefore, int userId) {
    return orderQuery.getOrdersForManufacturer(manufacturerId, status, requestedDeliveryDateBefore, userId);
  }

  public Manufacturer getManufacturer(int manufacturerId) {
    return manufacturerDao.getManufacturer(manufacturerId);
  }

  public ManufacturerDetails getManufacturerDetails(int manufacturerId) {
    Manufacturer manufacturer = manufacturerDao.getManufacturer(manufacturerId);

    ManufacturerDetails details = new ManufacturerDetails();
    details.setManufacturerId(manufacturer.getManufacturerId());
    details.setEmailSignature(manufacturer.getEmailSignature());
    details.setLogoUrl(manufacturer.getLogoUrl());
    details.setSupportEmail(manufacturer.getSupportEmail());
    details.setPhone(manufacturer.getPhone());
    details.setModifiedDate(manufacturer.getModifiedDate());
    details.setModifiedByUserId(manufacturer.getModifiedByUserId());

    // Set manufacturer settings fields
    ManufacturerSettings settings = manufacturer.getManufacturerSettings();
    if (settings != null) {
      details.setViewerColour(settings.getViewerColour());
      details.setEdgingEnabledDefault(settings.isEdgingEnabledDefault());
      details.setContactUsPageEnabled(settings.isContactUsPageEnabled());
    }

    // Load additional emails
    details.setAdditionalEmails(manufacturerDao.getAdditionalEmailsForManufacturer(manufacturerId));

    return details;
  }

  public List<Manufacturer> getAllManufacturers() {
    return manufacturerDao.getAllManufacturers();
  }

  public Integer getManufacturerIdForCustomerUserId(int userId) {
    return manufacturerDao.getManufacturerIdForCustomerUserId(userId);
  }

  public List<Manual> getManualsForManufacturer(int manufacturerId, String sortBy, int resultSize) {
    return manualDao.getManualsForManufacturer(manufacturerId, null, sortBy, resultSize);
  }

  public OrderUnreadCounts getOrderCountsForManufacturer(int manufacturerId) {
    return orderQuery.getOrderCountsForManufacturer(manufacturerId);
  }

  public List<User> getUsersForManufacturer(int manufacturerId) {
    return userDetailsDao.getUsersForManufacturer(manufacturerId);
  }

  public boolean updateManufacturerDetails(int manufacturerId, ManufacturerDetails manufacturerDetails, int userId) {

    //Check if logo updated to clean up S3 Bucket -  retrieve here before update for passing to helper for comparison and deletion
    Manufacturer existingManufacturer = manufacturerDao.getManufacturer(manufacturerId);

    //Continue with update
    java.sql.Timestamp now = new java.sql.Timestamp(Calendar.getInstance().getTime().getTime());

    manufacturerDetails.setManufacturerId(manufacturerId);
    manufacturerDetails.setModifiedByUserId(userId);
    manufacturerDetails.setModifiedDate(now);
    boolean updated = manufacturerDao.updateManufacturer(manufacturerDetails);

    if (updated) {
      ManufacturerSettings settings = manufacturerDao.getManufacturerSettingsById(manufacturerId);
      settings.setViewerColour(manufacturerDetails.getViewerColour());
      settings.setEdgingEnabledDefault(manufacturerDetails.isEdgingEnabledDefault());
      settings.setContactUsPageEnabled(manufacturerDetails.isContactUsPageEnabled());

      updateManufacturerSettings(settings);

      // Save additional emails
      manufacturerDao.saveAdditionalEmailsForManufacturer(manufacturerId, manufacturerDetails.getAdditionalEmails());

      //If details update complete - Tidy up logo in s3 bucket
      boolean deleted = s3StorageClient.deleteS3FileHelper(existingManufacturer.getLogoUrl(), manufacturerDetails.getLogoUrl());
    }
    return updated;
  }

  public Manufacturer getManufacturerByDomain(String subdomain) {
    return manufacturerDao.getManufacturerByDomain(subdomain);
  }

  public List<Kit> getMasterPartKitsByManufacturerId(int manufacturerId, Integer languageIdForCode, Language defaultLanguage) {
    return masterPartExtensionsDao.getKitsForManufacturerId(manufacturerId, languageIdForCode, defaultLanguage);
  }

  public ManufacturerSettings getManufacturerSettings(int manufacturerId) {
    return manufacturerDao.getManufacturerSettingsById(manufacturerId);
  }

  public boolean updateManufacturerSettings(ManufacturerSettings settings) {
    return manufacturerDao.updateManufacturerSettings(settings);
  }

  public ManufacturerConfig getManufacturerConfigById(int manufacturerId) {
    return manufacturerConfigDao.getManufacturerConfigById(manufacturerId);
  }

  public List<PriceListIdentifier> getPriceListIdentifiersForManufacturer(int manufacturerId) {
    return manufacturerDao.getPriceListIdentifiersForManufacturer(manufacturerId);
  }
}
