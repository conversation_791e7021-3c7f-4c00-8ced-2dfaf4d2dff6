package co.cadshare.ranges.boundary;

import co.cadshare.aspects.logging.ServiceLoggingAspect;
import co.cadshare.shared.core.user.User;
import co.cadshare.models.boundary.UserQueryPort;
import co.cadshare.ranges.core.Range;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.MockitoAnnotations;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import static org.mockito.Mockito.*;

@EnableAspectJAutoProxy
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(classes = {UpdateRangeService.class, ServiceLoggingAspect.class})
public class UpdateRangeServiceTest {

    @MockBean
    private RangeCommandPort commandPort;
    @MockBean
    private RangeQueryPort queryPort;
    @MockBean
    private UserQueryPort userQueryPort;
    @Autowired
    private UpdateRangeService out;

    private User user;
    private Range range;
    private Range errorRange = new Range();

    @Before
    public void Before() {
        MockitoAnnotations.initMocks(this);
        range = buildRange();
        user = new User() {{
            setUserId(1);
            setFirstName("firstName");
            setLastName("lastName");
        }};
    }

    @Test
    public void UpdateRangeSuccess() throws Exception {
        when(userQueryPort.getApiUserForManufacturer(1)).thenReturn(user);
        doNothing().when(commandPort).update(user, range);
        out.update(range, 1);
    }

    @Test(expected = RuntimeException.class)
    public void UpdateRangeFailureException() throws Exception {
        when(userQueryPort.getApiUserForManufacturer(1)).thenReturn(user);
        doThrow(new RuntimeException("terrible")).when(commandPort).update(user, errorRange);
        out.update(errorRange, 1);
    }

    private Range buildRange() {
        Range range = new Range();
        return range;
    }
}
