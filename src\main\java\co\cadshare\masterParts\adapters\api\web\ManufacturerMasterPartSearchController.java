package co.cadshare.masterParts.adapters.api.web;

import co.cadshare.aspects.access.languages.CanUseLanguage;
import co.cadshare.shared.core.Language;
import co.cadshare.shared.core.user.User;
import co.cadshare.shared.core.manufacturer.ManufacturerSettings;
import co.cadshare.masterParts.boundary.SearchMasterPartService;
import co.cadshare.masterParts.boundary.MasterPartSearchRequest;
import co.cadshare.masterParts.boundary.MasterPartSearchResult;
import co.cadshare.services.ManufacturerService;
import co.cadshare.users.boundary.UsersService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("manufacturers/{manufacturer-id}/master-parts")
public class ManufacturerMasterPartSearchController {

  private final SearchMasterPartService searchMasterPartService;
  private final ManufacturerService manufacturerService;
  private final UsersService userService;

  @Autowired
  public ManufacturerMasterPartSearchController(SearchMasterPartService searchMasterPartService,
                                                ManufacturerService manufacturerService,
                                                UsersService userService) {

    this.searchMasterPartService = searchMasterPartService;
    this.manufacturerService = manufacturerService;
    this.userService = userService;
  }

  private static final Logger logger = LoggerFactory.getLogger(ManufacturerMasterPartSearchController.class);

  @PreAuthorize("hasRole('ROLE_MANUFACTURER') and hasRole('Parts')")
  @PostMapping ("/search")
  @CanUseLanguage
  public ResponseEntity<MasterPartSearchResult> searchMasterPartsForManufacturer(@AuthenticationPrincipal User currentUser,
                                                @PathVariable("manufacturer-id") int manufacturerId,
                                                @RequestBody PostManufacturerSearchMasterPartsDto searchMasterPartsDto,
                                                @RequestParam(value = "language") Language language) {

    try {
      if (searchMasterPartsDto.isSearchStringEmptyOrNull()) {
        logger.error("Part search failed no PartNumber or Description to search");
        return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
      }
      ManufacturerSettings settings = manufacturerService.getManufacturerSettings(manufacturerId);
      User user = userService.findByUserId(currentUser.getUserId());
      if (user.isNotManufacturerUser() && settings.isCustomerSearchManualsOnly()) {
        return new ResponseEntity<>(HttpStatus.FORBIDDEN);
      }
    } catch (Exception ex) {
      return new ResponseEntity<>(HttpStatus.CONFLICT);
    }

    MasterPartSearchRequest searchRequest = new MasterPartSearchRequest();
    searchRequest.setPartNumber(searchMasterPartsDto.getPartNumber());
    searchRequest.setDescription(searchMasterPartsDto.getPartDescription());
    searchRequest.setLanguage(currentUser.findLanguage(language));
    searchRequest.setPage(1);
    searchRequest.setSize(100);
	searchRequest.setExactMatch(searchMasterPartsDto.isExactMatch());

    MasterPartSearchResult resultPage = this.searchMasterPartService.findForManufacturer(currentUser, searchRequest, manufacturerId);
    return new ResponseEntity<>(resultPage, HttpStatus.OK);
  }

}
