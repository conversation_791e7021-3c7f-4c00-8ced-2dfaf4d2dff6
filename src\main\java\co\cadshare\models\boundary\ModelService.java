package co.cadshare.models.boundary;

import co.cadshare.shared.core.Language;
import co.cadshare.domainmodel.part.Part;
import co.cadshare.domainmodel.part.PartViewerDetails;
import co.cadshare.models.core.ModelManifestWrapper;
import co.cadshare.shared.core.user.User;
import co.cadshare.masterParts.adapters.database.MasterPartDao;
import co.cadshare.masterParts.adapters.database.MasterPartExtensionsDao;
import co.cadshare.masterParts.core.MasterPart;
import co.cadshare.masterParts.core.extensions.kit.Kit;
import co.cadshare.masterParts.core.extensions.kit.KitPart;
import co.cadshare.masterParts.core.extensions.optionsSet.OptionsSet;
import co.cadshare.masterParts.core.extensions.partModelLink.PartModelLink;
import co.cadshare.models.adapters.database.ModelDao;
import co.cadshare.models.core.Model;
import co.cadshare.publications.boundary.ManualService;
import co.cadshare.publications.core.Manual;
import co.cadshare.response.CustomerModel;
import co.cadshare.response.ModelTranslationWarnings;
import co.cadshare.services.*;
import co.cadshare.utils.ObjectExtension;
import com.autodesk.client.model.Message;
import lombok.experimental.ExtensionMethod;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.List;

/**
 * TODO(dallanmc) Description of class.
 */
@Slf4j
@Service
@ExtensionMethod(ObjectExtension.class)
public class ModelService {

    private final ModelDao modelDao;
    private final ManualService manualService;
    private final ModelUpdateService modelUpdateService;
    private final NonModeledPartService nonModeledPartService;
    private final PartModelLinkService partLinkService;
    private final KitService kitService;
    private final OptionsSetService optionsSetService;
    private final PartService partService;
    private final MasterPartDao masterPartDao;
    private final MasterPartExtensionsDao masterPartExtensionsDao;
    private final AutodeskPort autodesk;
    private final ModelQueryPort modelQuery;
    private final ModelCommandPort modelCommand;

    @Autowired
    public ModelService(ModelDao modelDao, ManualService manualService,
                        ModelUpdateService modelUpdateService, NonModeledPartService nonModeledPartService, PartModelLinkService partLinkService, KitService kitService, OptionsSetService optionsSetService,
                        PartService partService, MasterPartDao masterPartDao, MasterPartExtensionsDao masterPartExtensionsDao,
                        ModelQueryPort modelQuery, ModelCommandPort modelCommand, AutodeskPort autodesk) {

        this.modelDao = modelDao;
        this.manualService = manualService;
        this.modelUpdateService = modelUpdateService;
        this.nonModeledPartService = nonModeledPartService;
        this.partLinkService = partLinkService;
        this.kitService = kitService;
        this.optionsSetService = optionsSetService;
        this.partService = partService;
        this.masterPartDao = masterPartDao;
        this.masterPartExtensionsDao = masterPartExtensionsDao;
        this.autodesk = autodesk;
        this.modelQuery = modelQuery;
        this.modelCommand = modelCommand;
    }

    public Model getModel(int modelId) {
        return modelQuery.get(modelId);
    }

    public void deleteModel(User user, int modelId) throws Exception {
        List<Manual> manualList = manualService.getManualsForModel(modelId);

        // Tidy up manuals with no viewables remaining
        for (Manual manual : manualList) {
            deleteManualsWithNoViewablesRemaining(manual);
        }

        // If there are other viewables in manual, the viewable being deleted
        // should be removed but the manual must be left available to the users
        // with other viewables still active.
        manualService.deleteModelFromManualModelMap(modelId);
        Model model = modelQuery.get(modelId);
        modelCommand.delete(user, model);
    }

    private void deleteManualsWithNoViewablesRemaining(Manual manual) {
        // Check if model is only one a manual, if it is delete
        List<CustomerModel> modelList = modelDao.getModelsByManualId(manual.getManualId());

        if (modelList.size() == 1) {
            manualService.deleteManual(manual.getManualId());
        }
    }

    public void updateModel(Model model) {
        Timestamp now = new java.sql.Timestamp(Calendar.getInstance().getTime().getTime());
        model.setModifiedDate(now);

        modelDao.updateModelName(model);

        modelUpdateService.update(model.getModelId());
    }

    public List<CustomerModel> getModelsByManualId(int manualId) {
        return modelDao.getModelsByManualId(manualId);
    }

    public ModelTranslationWarnings getTranslationErrors(int modelId) {
        Model model = modelQuery.get(modelId);
        ModelTranslationWarnings warnings = new ModelTranslationWarnings();
        warnings.setModelId(modelId);
        try {
            ModelManifestWrapper manifest = autodesk.getManifest(model);

            if (manifest.hasWarnings()) {
                List<String> emptyWarnings = new ArrayList<>();
                List<String> missingWarnings = new ArrayList<>();
                List<String> extraWarnings = new ArrayList<>();

                String warningMessage = "";
                for (Message message : manifest.getWarnings()) {
                    // ATF-1023 = Missing
                    // ATF-1026 = Empty
                    if (message.getCode().equalsIgnoreCase("ATF-1023")) {
                        warningMessage = message.getMessage().get(0).replace("{0}", message.getMessage().get(1));
                        missingWarnings.add(warningMessage);
                    } else if (message.getCode().equalsIgnoreCase("ATF-1026")) {
                        warningMessage = message.getMessage().get(0).replace("{0}", message.getMessage().get(1));
                        emptyWarnings.add(warningMessage);
                    } else {
                        int warningCount = message.getMessage().size();
                        if (warningCount > 1) {
                            warningMessage = message.getMessage().get(0).replace("{0}", message.getMessage().get(1));
                            warningMessage = message.getCode().concat(" - " + warningMessage);
                        } else {
                            warningMessage = message.getMessage().get(0);
                            warningMessage = message.getCode().concat(" - " + warningMessage);
                        }
                        extraWarnings.add(warningMessage);
                    }
                }
                warnings.setEmptyWarnings(emptyWarnings);
                warnings.setMissingWarnings(missingWarnings);
                warnings.setExtraWarnings(extraWarnings);
            }


        } catch (Exception e) {
            log.error("Exception caught in getTranslationErrors() for modelId " + modelId, e);
        }
        return warnings;
    }

    public int getPartCountForModel(int modelId) {
        return modelDao.getPartCountForModel(modelId);
    }

    public PartViewerDetails getModelPartViewerDetails(int modelId, Part part, Integer languageId, Language defaultLanguage, int manufacturerId) {
        PartViewerDetails partViewerDetails = new PartViewerDetails();

        partViewerDetails.setPart(part);

        if (part.getObjectId() != null) {
            List<Part> nonModelledPartList = nonModeledPartService.getPartsForAssociatedDbIdAndModelId(part.getObjectId(), modelId, languageId, defaultLanguage, manufacturerId);
            if (nonModelledPartList != null && nonModelledPartList.size() > 0) {
                partViewerDetails.setNonModelledPart(nonModelledPartList);
                partViewerDetails.setNonModelled(true);
                partViewerDetails.setNonModelledId(nonModeledPartService.findNonModeledPartIdForAssociatedDbIdAndModelId(part.getObjectId(), modelId));
            }
        }
        PartModelLink partLink = partLinkService.getActivePartLink(part.getPartId());
        if (partLink != null) {
            partViewerDetails.setLinkedModel(partLink);
            partViewerDetails.setLinked(true);
        }

        boolean inKit = kitService.isPartInKit(part.getPartId());
        partViewerDetails.setInKit(inKit);

        if (inKit) {
            partViewerDetails.setKits(kitService.getKitsByModelId(modelId));
        }

        List<OptionsSet> optionsSets = optionsSetService.getOptionsSetByPartId(part.getPartId(), languageId, defaultLanguage);
        partViewerDetails.setContainsOptionsSet(!optionsSets.isEmpty());

        partViewerDetails.setOptionsSet(optionsSets);
        return partViewerDetails;
    }


    public PartViewerDetails getSoftcopyPartDetails(int modelId, int objectId, Integer languageId, int manufacturerId) {
        PartViewerDetails partViewerDetails = new PartViewerDetails();
        Part part = partService.getPartSoftcopyForModel(modelId, objectId, languageId, manufacturerId);
        partViewerDetails.setPart(part);

        MasterPart masterPart = masterPartDao.getMasterPartByPartNumber(part.getPartNumber(), manufacturerId, null);

        boolean mpHasKit = false;

        if (masterPart != null) {
            mpHasKit = masterPartExtensionsDao.hasKitParts(masterPart.getMasterPartId());
        }

        if (part != null) {
            if (mpHasKit) {
                List<Kit> mpKits = masterPartExtensionsDao.getKitsForMasterPartId(masterPart.getMasterPartId(), languageId, null);
                List<Kit> kits = new ArrayList<>();
                for (Kit mpKit : mpKits) {
                    int totalParts = 0;
                    Kit kit = new Kit();
                    kit.setId(mpKit.getId());
                    kit.setMasterPartKitId(mpKit.getId());
                    kit.setTitle(mpKit.getTitle());
                    kit.setDescription(mpKit.getDescription());

                    List<KitPart> parts = new ArrayList<>();

                    for (KitPart kitPart : mpKit.getParts()) {
                        KitPart kPart = new KitPart();

                        kPart.setMasterPartId(kitPart.getMasterPartId());
                        kPart.setPartNumber(kitPart.getPartNumber());
                        kPart.setDescription(kitPart.getDescription());
                        kPart.setQuantity(kitPart.getQuantity());

                        totalParts = totalParts + kitPart.getQuantity();

                        parts.add(kPart);
                    }
                    kit.setParts(parts);
                    kit.setTotalPartsQuantity(totalParts);

                    kits.add(kit);

                }
                if (kits != null && kits.size() > 0) {
                    partViewerDetails.setInKit(true);
                } else {
                    partViewerDetails.setInKit(false);
                }
                partViewerDetails.setKits(kits);
            } else {
                List<Kit> kits = kitService.getSoftCopyKitsByPartId(part.getPartId(), manufacturerId);
                if (kits != null && kits.size() > 0) {
                    partViewerDetails.setInKit(true);
                    partViewerDetails.setKits(kits);
                }
            }
        }
        return partViewerDetails;
    }

    public void modelSetupComplete(Model model) {
        Timestamp now = new java.sql.Timestamp(Calendar.getInstance().getTime().getTime());

        Model existingModel = modelQuery.get(model.getModelId());
        existingModel.setIsSetupComplete(model.getIsSetupComplete());
        existingModel.setLeafNodes(model.getLeafNodes());
        existingModel.setModifiedDate(now);

        modelDao.updateModel(existingModel);

        modelUpdateService.update(model.getModelId());
    }

    public int getMachineId() {
        return 0;
    }
}
