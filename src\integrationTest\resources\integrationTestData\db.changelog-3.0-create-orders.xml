<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                   http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.0.xsd
">

    <!-- orders 1 & 2: enquiry, for same purchaser (1), same manufacturer (Cat) -->
    <!-- order 3: enquiry, for different purchaser (2), same manufacturer (Cat) -->
    <!-- order 4: enquiry, for different purchaser (3), different manufacturer (JCB) -->
    <!-- order 5: enquiry, for different purchaser again (4), different manufacturer (Liebherr) -->

    <changeSet author="AndyB" id="3.0-integration-test-data-create-orders-1">
        <sql stripComments="true">
            INSERT INTO public.orders (orderid, orderstatus, requesteddeliverydate, createddate, closeddate, createdbyuserid, modifiedbyuserid, emailaddress, contactname, contactnumber, manufacturersubentityid, modifieddate, percentagediscount, shippingprice, shippingaddressid, billingaddressid, estimateddeliverydate, purchaseorder, price, readbymanufacturer, readbycustomer, notes, parentorderid, customorderid, customordernumber, serialnumber, stockorder, deliveryname, deliverynumber, parentsubentityid, associateddporderid, currencyid, associatedorderid, visorderid, vissalesorderno_x, warehouseid, shippingrequirementid, shippingrequirementexpressdelivery, shippingrequirementasinstruction)
            VALUES (
            1,
            'SUBMITTED', 	            --orderstatus,
            '2022-06-07 15:49:31.754', 	--requesteddeliverydate,
            '2022-06-07 15:49:31.754', 	--createddate,
            NULL, 	                    --closeddate,
            1, 	                        --createdbyuserid,
            1, 	                        --modifiedbyuserid,
            '<EMAIL>', 	    --emailaddress,
            'Martyn Thomas', 	        --contactname,
            '012-345-6789',	            --contactnumber,
            1, 	                        --manufacturersubentityid,
            '2022-06-20 13:16:13', 	    --modifieddate,
            30, 	                    --percentagediscount,
            90, 	                    --shippingprice,
            1, 	                        --shippingaddressid,
            1, 	                        --billingaddressid,
            '2022-06-24 11:00:00', 	    --estimateddeliverydate,
            '6978', 	                --purchaseorder,
            NULL, 	                    --price,
            true, 	                    --readbymanufacturer,
            false, 	                    --readbycustomer,
            NULL, 	                    --notes,
            NULL, 	                    --parentorderid,
            2531, 	                    --customorderid,
            NULL, 	                    --customordernumber,
            'NA', 	                    --serialnumber,
            false, 	                    --stockorder,
            'Martyn Thomas', 	        --deliveryname,
            '012-345-6789', 	        --deliverynumber,
            NULL, 	                    --parentsubentityid,
            NULL, 	                    --associateddporderid,
            1, 	                        --currencyid,
            NULL, 	                    --associatedorderid,
            NULL, 	                    --visorderid,
            '', 	                    --vissalesorderno_x,
            NULL, 	                    --warehouseid,
            NULL, 	                    --shippingrequirementid,
            false, 	                    --shippingrequirementexpressdelivery,
            NULL);	                    --shippingrequirementasinstruction
        </sql>
    </changeSet>
    <changeSet author="AndyB" id="3.0-integration-test-data-create-orders-2">
        <sql stripComments="true">
            INSERT INTO public.orders (orderid, orderstatus, requesteddeliverydate, createddate, closeddate, createdbyuserid, modifiedbyuserid, emailaddress, contactname, contactnumber, manufacturersubentityid, modifieddate, percentagediscount, shippingprice, shippingaddressid, billingaddressid, estimateddeliverydate, purchaseorder, price, readbymanufacturer, readbycustomer, notes, parentorderid, customorderid, customordernumber, serialnumber, stockorder, deliveryname, deliverynumber, parentsubentityid, associateddporderid, currencyid, associatedorderid, visorderid, vissalesorderno_x, warehouseid, shippingrequirementid, shippingrequirementexpressdelivery, shippingrequirementasinstruction)
            VALUES (
            2,
            'SUBMITTED', 	            --orderstatus,
            '2023-06-07 15:49:31.754', 	--requesteddeliverydate,
            '2023-06-07 15:49:31.754', 	--createddate,
            NULL, 	                    --closeddate,
            1, 	                        --createdbyuserid,
            1, 	                        --modifiedbyuserid,
            '<EMAIL>', 	    --emailaddress,
            'Thomas Martyn', 	        --contactname,
            '012-345-6789',	            --contactnumber,
            1, 	                        --manufacturersubentityid,
            '2023-06-20 13:16:13', 	    --modifieddate,
            30, 	                    --percentagediscount,
            90, 	                    --shippingprice,
            1, 	                        --shippingaddressid,
            1, 	                        --billingaddressid,
            '2023-06-24 11:00:00', 	    --estimateddeliverydate,
            '1234', 	                --purchaseorder,
            NULL, 	                    --price,
            true, 	                    --readbymanufacturer,
            false, 	                    --readbycustomer,
            NULL, 	                    --notes,
            NULL, 	                    --parentorderid,
            2354, 	                    --customorderid,
            NULL, 	                    --customordernumber,
            'NA', 	                    --serialnumber,
            false, 	                    --stockorder,
            'Thomas Martyn', 	        --deliveryname,
            '012-345-6789', 	        --deliverynumber,
            NULL, 	                    --parentsubentityid,
            NULL, 	                    --associateddporderid,
            1, 	                        --currencyid,
            NULL, 	                    --associatedorderid,
            NULL, 	                    --visorderid,
            '', 	                    --vissalesorderno_x,
            NULL, 	                    --warehouseid,
            NULL, 	                    --shippingrequirementid,
            false, 	                    --shippingrequirementexpressdelivery,
            NULL);	                    --shippingrequirementasinstruction
        </sql>
    </changeSet>
    <changeSet author="AndyB" id="3.0-integration-test-data-create-orders-3">
        <sql stripComments="true">
            INSERT INTO public.orders (orderid, orderstatus, requesteddeliverydate, createddate, closeddate, createdbyuserid, modifiedbyuserid, emailaddress, contactname, contactnumber, manufacturersubentityid, modifieddate, percentagediscount, shippingprice, shippingaddressid, billingaddressid, estimateddeliverydate, purchaseorder, price, readbymanufacturer, readbycustomer, notes, parentorderid, customorderid, customordernumber, serialnumber, stockorder, deliveryname, deliverynumber, parentsubentityid, associateddporderid, currencyid, associatedorderid, visorderid, vissalesorderno_x, warehouseid, shippingrequirementid, shippingrequirementexpressdelivery, shippingrequirementasinstruction)
            VALUES (
            3,
            'PREPARING',                --orderstatus,
            '2024-06-07 15:49:31.754', 	--requesteddeliverydate,
            '2024-06-07 15:49:31.754', 	--createddate,
            NULL, 	                    --closeddate,
            1, 	                        --createdbyuserid,
            1, 	                        --modifiedbyuserid,
            '<EMAIL>', 	    --emailaddress,
            'John Martyn', 	            --contactname,
            '012-345-6789',	            --contactnumber,
            2, 	                        --manufacturersubentityid,
            '2023-06-20 13:16:13', 	    --modifieddate,
            30, 	                    --percentagediscount,
            90, 	                    --shippingprice,
            1, 	                        --shippingaddressid,
            1, 	                        --billingaddressid,
            '2023-06-24 11:00:00', 	    --estimateddeliverydate,
            '5432', 	                --purchaseorder,
            NULL, 	                    --price,
            true, 	                    --readbymanufacturer,
            false, 	                    --readbycustomer,
            NULL, 	                    --notes,
            NULL, 	                    --parentorderid,
            7895, 	                    --customorderid,
            NULL, 	                    --customordernumber,
            'NA', 	                    --serialnumber,
            false, 	                    --stockorder,
            'John Martyn', 	            --deliveryname,
            '012-345-6789', 	        --deliverynumber,
            2, 	                        --parentsubentityid,
            NULL, 	                    --associateddporderid,
            1, 	                        --currencyid,
            NULL, 	                    --associatedorderid,
            NULL, 	                    --visorderid,
            '', 	                    --vissalesorderno_x,
            NULL, 	                    --warehouseid,
            NULL, 	                    --shippingrequirementid,
            false, 	                    --shippingrequirementexpressdelivery,
            NULL);	                    --shippingrequirementasinstruction
        </sql>
    </changeSet>
    <changeSet author="AndyB" id="3.0-integration-test-data-create-orders-4">
        <sql stripComments="true">
            INSERT INTO public.orders (orderid, orderstatus, requesteddeliverydate, createddate, closeddate, createdbyuserid, modifiedbyuserid, emailaddress, contactname, contactnumber, manufacturersubentityid, modifieddate, percentagediscount, shippingprice, shippingaddressid, billingaddressid, estimateddeliverydate, purchaseorder, price, readbymanufacturer, readbycustomer, notes, parentorderid, customorderid, customordernumber, serialnumber, stockorder, deliveryname, deliverynumber, parentsubentityid, associateddporderid, currencyid, associatedorderid, visorderid, vissalesorderno_x, warehouseid, shippingrequirementid, shippingrequirementexpressdelivery, shippingrequirementasinstruction)
            VALUES (
            4,
            'ACTIVE', 	                --orderstatus,
            '2022-06-07 15:49:31.754', 	--requesteddeliverydate,
            '2022-06-07 15:49:31.754', 	--createddate,
            NULL, 	                    --closeddate,
            1, 	                        --createdbyuserid,
            1, 	                        --modifiedbyuserid,
            '<EMAIL>', 	    --emailaddress,
            'Martyn Thomas', 	        --contactname,
            '012-345-6789',	            --contactnumber,
            3, 	                        --manufacturersubentityid,
            '2022-06-20 13:16:13', 	    --modifieddate,
            30, 	                    --percentagediscount,
            90, 	                    --shippingprice,
            1,   	                    --shippingaddressid,
            1,   	                    --billingaddressid,
            '2022-06-24 11:00:00', 	    --estimateddeliverydate,
            '6978', 	                --purchaseorder,
            NULL, 	                    --price,
            true, 	                    --readbymanufacturer,
            false, 	                    --readbycustomer,
            NULL, 	                    --notes,
            NULL, 	                    --parentorderid,
            2531, 	                    --customorderid,
            NULL, 	                    --customordernumber,
            'NA', 	                    --serialnumber,
            false, 	                    --stockorder,
            'Martyn Thomas', 	        --deliveryname,
            '012-345-6789', 	        --deliverynumber,
            NULL, 	                    --parentsubentityid,
            NULL, 	                    --associateddporderid,
            1, 	                        --currencyid,
            NULL, 	                    --associatedorderid,
            NULL, 	                    --visorderid,
            '', 	                    --vissalesorderno_x,
            NULL, 	                    --warehouseid,
            NULL, 	                    --shippingrequirementid,
            false, 	                    --shippingrequirementexpressdelivery,
            NULL);	                    --shippingrequirementasinstruction
        </sql>
    </changeSet>
    <changeSet author="AndyB" id="3.0-integration-test-data-create-orders-5">
        <sql stripComments="true">
            INSERT INTO public.orders (orderid, orderstatus, requesteddeliverydate, createddate, closeddate, createdbyuserid, modifiedbyuserid, emailaddress, contactname, contactnumber, manufacturersubentityid, modifieddate, percentagediscount, shippingprice, shippingaddressid, billingaddressid, estimateddeliverydate, purchaseorder, price, readbymanufacturer, readbycustomer, notes, parentorderid, customorderid, customordernumber, serialnumber, stockorder, deliveryname, deliverynumber, parentsubentityid, associateddporderid, currencyid, associatedorderid, visorderid, vissalesorderno_x, warehouseid, shippingrequirementid, shippingrequirementexpressdelivery, shippingrequirementasinstruction)
            VALUES (
            5,
            'ACTIVE', 	                --orderstatus,
            '2022-06-07 15:49:31.754', 	--requesteddeliverydate,
            '2022-06-07 15:49:31.754', 	--createddate,
            NULL, 	                    --closeddate,
            1, 	                        --createdbyuserid,
            1, 	                        --modifiedbyuserid,
            '<EMAIL>', 	    --emailaddress,
            'Martyn Thomas', 	        --contactname,
            '012-345-6789',	            --contactnumber,
            4, 	                        --manufacturersubentityid,
            '2022-06-20 13:16:13', 	    --modifieddate,
            30, 	                    --percentagediscount,
            90, 	                    --shippingprice,
            1, 	                        --shippingaddressid,
            1,   	                    --billingaddressid,
            '2022-06-24 11:00:00', 	    --estimateddeliverydate,
            '6978', 	                --purchaseorder,
            NULL, 	                    --price,
            true, 	                    --readbymanufacturer,
            false, 	                    --readbycustomer,
            NULL, 	                    --notes,
            NULL, 	                    --parentorderid,
            2531, 	                    --customorderid,
            NULL, 	                    --customordernumber,
            'NA', 	                    --serialnumber,
            false, 	                    --stockorder,
            'Martyn Thomas', 	        --deliveryname,
            '012-345-6789', 	        --deliverynumber,
            NULL, 	                    --parentsubentityid,
            NULL, 	                    --associateddporderid,
            1, 	                        --currencyid,
            NULL, 	                    --associatedorderid,
            NULL, 	                    --visorderid,
            '', 	                    --vissalesorderno_x,
            NULL, 	                    --warehouseid,
            NULL, 	                    --shippingrequirementid,
            false, 	                    --shippingrequirementexpressdelivery,
            NULL);	                    --shippingrequirementasinstruction
        </sql>
    </changeSet>
    <changeSet author="AndyB" id="3.0-integration-test-data-create-orders-6">
        <sql stripComments="true">
            INSERT INTO public.orders (orderid, orderstatus, requesteddeliverydate, createddate, closeddate, createdbyuserid, modifiedbyuserid, emailaddress, contactname, contactnumber, manufacturersubentityid, modifieddate, percentagediscount, shippingprice, shippingaddressid, billingaddressid, estimateddeliverydate, purchaseorder, price, readbymanufacturer, readbycustomer, notes, parentorderid, customorderid, customordernumber, serialnumber, stockorder, deliveryname, deliverynumber, parentsubentityid, associateddporderid, currencyid, associatedorderid, visorderid, vissalesorderno_x, warehouseid, shippingrequirementid, shippingrequirementexpressdelivery, shippingrequirementasinstruction)
            VALUES (
            6,
            'SUBMITTED', 	            --orderstatus,
            '2023-06-07 15:49:31.754', 	--requesteddeliverydate,
            '2023-06-07 15:49:31.754', 	--createddate,
            NULL, 	                    --closeddate,
            1, 	                        --createdbyuserid,
            1, 	                        --modifiedbyuserid,
            '<EMAIL>', 	    --emailaddress,
            'Thomas Martyn', 	        --contactname,
            '012-345-6789',	            --contactnumber,
            1, 	                        --manufacturersubentityid,
            '2023-06-20 13:16:13', 	    --modifieddate,
            30, 	                    --percentagediscount,
            90, 	                    --shippingprice,
            1, 	                        --shippingaddressid,
            1, 	                        --billingaddressid,
            '2023-06-24 11:00:00', 	    --estimateddeliverydate,
            '1234', 	                --purchaseorder,
            NULL, 	                    --price,
            true, 	                    --readbymanufacturer,
            false, 	                    --readbycustomer,
            NULL, 	                    --notes,
            NULL, 	                    --parentorderid,
            2354, 	                    --customorderid,
            NULL, 	                    --customordernumber,
            'NA', 	                    --serialnumber,
            false, 	                    --stockorder,
            'Thomas Martyn', 	        --deliveryname,
            '012-345-6789', 	        --deliverynumber,
            NULL, 	                    --parentsubentityid,
            NULL, 	                    --associateddporderid,
            1, 	                        --currencyid,
            NULL, 	                    --associatedorderid,
            NULL, 	                    --visorderid,
            '', 	                    --vissalesorderno_x,
            NULL, 	                    --warehouseid,
            NULL, 	                    --shippingrequirementid,
            false, 	                    --shippingrequirementexpressdelivery,
            NULL);	                    --shippingrequirementasinstruction
        </sql>
    </changeSet>
    <changeSet author="AndyB" id="3.0-integration-test-data-create-orders-7">
        <sql stripComments="true">
            INSERT INTO public.orders (orderid, orderstatus, requesteddeliverydate, createddate, closeddate, createdbyuserid, modifiedbyuserid, emailaddress, contactname, contactnumber, manufacturersubentityid, modifieddate, percentagediscount, shippingprice, shippingaddressid, billingaddressid, estimateddeliverydate, purchaseorder, price, readbymanufacturer, readbycustomer, notes, parentorderid, customorderid, customordernumber, serialnumber, stockorder, deliveryname, deliverynumber, parentsubentityid, associateddporderid, currencyid, associatedorderid, visorderid, vissalesorderno_x, warehouseid, shippingrequirementid, shippingrequirementexpressdelivery, shippingrequirementasinstruction)
            VALUES (
            7,
            'SUBMITTED', 	            --orderstatus,
            '2023-06-07 15:49:31.754', 	--requesteddeliverydate,
            '2023-06-07 15:49:31.754', 	--createddate,
            NULL, 	                    --closeddate,
            1, 	                        --createdbyuserid,
            1, 	                        --modifiedbyuserid,
            '<EMAIL>', 	    --emailaddress,
            'Thomas Martyn', 	        --contactname,
            '012-345-6789',	            --contactnumber,
            1, 	                        --manufacturersubentityid,
            '2023-06-20 13:16:13', 	    --modifieddate,
            30, 	                    --percentagediscount,
            90, 	                    --shippingprice,
            1, 	                        --shippingaddressid,
            1, 	                        --billingaddressid,
            '2023-06-24 11:00:00', 	    --estimateddeliverydate,
            '1234', 	                --purchaseorder,
            NULL, 	                    --price,
            true, 	                    --readbymanufacturer,
            false, 	                    --readbycustomer,
            NULL, 	                    --notes,
            NULL, 	                    --parentorderid,
            2354, 	                    --customorderid,
            NULL, 	                    --customordernumber,
            'NA', 	                    --serialnumber,
            false, 	                    --stockorder,
            'Thomas Martyn', 	        --deliveryname,
            '012-345-6789', 	        --deliverynumber,
            NULL, 	                    --parentsubentityid,
            NULL, 	                    --associateddporderid,
            1, 	                        --currencyid,
            NULL, 	                    --associatedorderid,
            NULL, 	                    --visorderid,
            '', 	                    --vissalesorderno_x,
            NULL, 	                    --warehouseid,
            NULL, 	                    --shippingrequirementid,
            false, 	                    --shippingrequirementexpressdelivery,
            NULL);	                    --shippingrequirementasinstruction
        </sql>
    </changeSet>
</databaseChangeLog>
