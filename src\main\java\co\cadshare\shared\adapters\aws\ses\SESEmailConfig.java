package co.cadshare.shared.adapters.aws.ses;

import com.amazonaws.internal.StaticCredentialsProvider;
import com.amazonaws.regions.Region;
import com.amazonaws.regions.Regions;
import com.amazonaws.services.simpleemail.AmazonSimpleEmailServiceClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class SESEmailConfig {
    @Autowired
    private StaticCredentialsProvider staticCredentialsProvider;

    @<PERSON>(name = "emailClient")
    public AmazonSimpleEmailServiceClient emailClient() {
        AmazonSimpleEmailServiceClient emailClient = new AmazonSimpleEmailServiceClient(staticCredentialsProvider);
        emailClient.setRegion(Region.getRegion(Regions.EU_WEST_1));
        return emailClient;
    }
}
