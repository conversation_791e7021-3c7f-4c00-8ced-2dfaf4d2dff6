package co.cadshare.modelMgt.models.core;

import co.cadshare.modelMgt.models.boundary.UploadModelService;
import co.cadshare.modelMgt.models.core.processor.PropertiesProcessor;
import co.cadshare.shared.core.manufacturer.ManufacturerConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;

public class MetadataParser {

    private static final Logger logger = LoggerFactory.getLogger(UploadModelService.class);
    private final Model model;
    private final MetadataWrapper metadata;
    private final PropertiesProcessor propertiesProcessor;
    private final String autodeskUrn;

    public MetadataParser(Model model, MetadataWrapper metadata, PropertiesProcessor propertiesProcessor) {
        this.model = model;
        this.metadata = metadata;
        this.autodeskUrn = model.getAutodeskUrn();
        this.propertiesProcessor = propertiesProcessor;
    }


    public boolean isGuidProvidedByMetadata() throws Exception {
        if(metadata.isParseable()) {
            return metadata.isGuidProvidedByMetadata();
        } else {
            logger.info("guid metadata not yet available for urn [{}]", autodeskUrn);
            model.retriedPartsMissing("Parts not available from Autodesk manifest");
            return false;
        }
    }

    public String retrieveGuidFromMetadata() throws Exception {
        if(isGuidProvidedByMetadata()) {
            String guid = metadata.getGuid();
            logger.info("guid [{}] retrieved for urn [{}]", guid, autodeskUrn);
            return guid;
        } else return null;
    }

    public boolean guidMetadataIsParseable(MetadataWrapper guidMetadata) throws Exception {
        if (guidMetadata.isParseable())
            return true;
        else {
            logger.info("metadata for guid not yet available for urn [{}] and guid [{}]",
                    autodeskUrn,
                    guidMetadata.getGuid());
            model.retriedPartsMissing("Parts not available from Autodesk manifest");
            return false;
        }
    }

    public ParsedMetadata parseMetadataForGuid(MetadataWrapper propertiesForGuid,
                                               MetadataWrapper guidMetaData,
                                               ManufacturerConfig config,
                                               String guid) throws Exception {

        ParsedMetadata parsedMetadata = new ParsedMetadata();
        if (propertiesForGuidAreParseable(propertiesForGuid, guid)) {
            MetadataDataExtended combinedProperties = propertiesProcessor.getCombinedProperties(propertiesForGuid,
                    guidMetaData,
                    config);
            List<MetadataObjectExtended> flatList = propertiesProcessor.toFlatList(combinedProperties.getObjects());
            parsedMetadata = new ParsedMetadata(flatList);
            logger.info("Retrieved a total of {} objects relating to  urn {} and guid {}",
                    parsedMetadata.get().size(),
                    autodeskUrn,
                    guid);
            if (!parsedMetadata.metadataToBeUploaded()) {
                logger.info("properties for guid not yet available for urn [{}] and guid [{}]", autodeskUrn, guid);
                model.retriedPartsMissing("Parts not available from Autodesk manifest");
            } else if (!parsedMetadata.isValid()) {
                logger.info("There was an issue batch updating metadata relating to urn {} and guid {} (part validation failed)",
                        model.getAutodeskUrn(),
                        guid);
                model.partValidationFailed();
            }
        }
        return parsedMetadata;
    }

    private boolean propertiesForGuidAreParseable(MetadataWrapper propertiesForGuid, String guid) throws Exception {
        boolean parseable = propertiesForGuid.isParseable();
        String loggerMessage = parseable ?
                "properties for guid [{}] retrieved for urn [{}]" :
                "properties for guid [{}] not yet available for urn [{}]";
        logger.info(loggerMessage, guid, autodeskUrn);
        return parseable;
    }
}
