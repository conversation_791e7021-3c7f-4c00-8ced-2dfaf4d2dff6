package co.cadshare.modelMgt.models.adapters.database;

import co.cadshare.shared.adapters.database.ImageEntityMapper;
import co.cadshare.modelMgt.publications.adapters.database.PublicationsDealerEntityMapper;
import co.cadshare.modelMgt.shared.core.Publication;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper(uses = {ImageEntityMapper.class, PublicationsDealerEntityMapper.class})
public interface ModelsPublicationEntityMapper {
    ModelsPublicationEntityMapper Instance = Mappers.getMapper(ModelsPublicationEntityMapper.class);

    Publication entityToCore(ModelsPublicationEntity publication);

    ModelsPublicationEntity coreToEntity(Publication publication);
    List<Publication> entitiesToCores(List<ModelsPublicationEntity> publication);
    List<ModelsPublicationEntity> coresToEntities(List<Publication> publications);

}
