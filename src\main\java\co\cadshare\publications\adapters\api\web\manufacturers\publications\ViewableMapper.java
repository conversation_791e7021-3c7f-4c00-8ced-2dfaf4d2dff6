package co.cadshare.publications.adapters.api.web.manufacturers.publications;

import co.cadshare.models.core.Model;
import co.cadshare.publications.core.Dealer;
import org.mapstruct.Mapper;

import java.util.ArrayList;
import java.util.List;

@Mapper
public abstract class ViewableMapper {
    public String modelToViewableString(Model model) {
        return model.getModelName() ;
    }

    public List<String> modelsToViewables(List<Model> models){
        List<String> viewables = new ArrayList<>();
        models.forEach(model -> viewables.add(modelToViewableString(model)));
        return viewables;
    }
}
