package co.cadshare.controller;

import co.cadshare.models.core.model.viewable.StateDetail;
import co.cadshare.shared.core.user.User;
import co.cadshare.services.SoftCopyStateDetailService;

import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RequestMapping("/statedetails/viewable/{viewableId}")
@RestController
public class SoftCopyStateDetailController {

    private SoftCopyStateDetailService stateDetailService;

    public SoftCopyStateDetailController(SoftCopyStateDetailService stateDetailService) {
        this.stateDetailService = stateDetailService;
    }

    @GetMapping("/{stateDetailId}")
    public StateDetail getStateDetail(@PathVariable Integer stateDetailId) {
        return stateDetailService.getStateDetail(stateDetailId);
    }

    /**
     * A variant on the getStateDetail call that will return a list of stateDetails based on the query parameters.
     *
     * @param topLevelStateDetailId - the ID of the top level state detail that will be returned. If null, the overall top level stateDetail will be returned.
     * @param levels - the depth in the hierarchy to retrieve stateDetails.StateDetailController 0 indicates only the stateDetail identified by the topLevelStateDetailId should be
     * returned. 1 will return that stateDetail plus all its direct children. 2 will return that stateDetail, all its children, and its children's children.
     * Etc.
     * @param type SNAPSHOT or PDF
     * @return
     */
    @GetMapping
    public List<StateDetail> getStateDetails(@PathVariable int viewableId, String topLevelStateDetailId, Integer levels) {
        return stateDetailService.getStateDetails(viewableId, topLevelStateDetailId, levels);
    }

    @PostMapping
    public Integer createStateDetail(@AuthenticationPrincipal User currentUser, @RequestBody StateDetail stateDetail, @PathVariable int viewableId) {
        stateDetail.setModifiedByUserId(currentUser.getUserId());
        stateDetail.setCreatedByUserId(currentUser.getUserId());
        Integer stateDetailId = stateDetailService.createStateDetail(stateDetail, viewableId);
        return stateDetailId;
    }

    @PutMapping("/statedetails/{stateDetailId}")
    public void updateStateDetail(@AuthenticationPrincipal User currentUser, @RequestBody StateDetail stateDetail, @PathVariable String stateDetailId,
        @PathVariable int viewableId) {
        stateDetail.setModifiedByUserId(currentUser.getUserId());
        stateDetailService.updateStateDetail(stateDetail, viewableId);
    }

    @DeleteMapping()
    public void deleteStateDetailByStateId(@PathVariable int viewableId, String stateId, boolean deleteChildren) {
        stateDetailService.deleteStateDetailByStateId(stateId, viewableId, deleteChildren);
    }
}
