/*
 * Copyright 2016 Bell.
 */
package co.cadshare.masterParts.adapters.api.web;

import co.cadshare.aspects.access.languages.CanUseLanguage;
import co.cadshare.shared.core.Language;
import co.cadshare.masterParts.boundary.MasterPartService;
import co.cadshare.models.core.Model;
import co.cadshare.masterParts.core.MasterPart;
import co.cadshare.domainmodel.part.Part;
import co.cadshare.masterParts.core.MasterPartDetails;
import co.cadshare.shared.core.user.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Slf4j
@Controller
@RequestMapping("/masterPart")
public class MasterPartController {

	private final MasterPartService masterPartService;

	@Autowired
	public MasterPartController(MasterPartService masterPartService) {
		this.masterPartService = masterPartService;
	}

	//Master Part Details Services
	@GetMapping(value = "/{masterPartId}")
	@CanUseLanguage
	public HttpEntity<MasterPartDetails> getMasterPartDetails(@AuthenticationPrincipal User currentUser,
	                                                        @PathVariable int masterPartId,
	                                                        @RequestParam(value = "language", required = false) Language language) throws Exception {

		log.info("ACCESS: User [{}], getMasterPartDetails, masterpartid [{}]", currentUser.accessDetails(), masterPartId);
		Integer languageId = language != null ? currentUser.findLanguage(language).getLanguageId() : null;
		MasterPartDetails partViewerDetails = masterPartService.getPartViewerDetails(masterPartId,
				languageId,
				currentUser.obtainDefaultLanguage(),
				currentUser.getManufacturerId());
		return new ResponseEntity<>(partViewerDetails, HttpStatus.OK);
	}


	@PostMapping(consumes = "application/json")
	public HttpEntity<?> createMasterPart(@AuthenticationPrincipal User currentUser,
	                                    @RequestBody MasterPart masterPart) {

		log.info("ACCESS: User [{}], createMasterPart, for partNumber [{}]", currentUser.accessDetails(), masterPart.getPartNumber());
		int masterPartId = 0;
		try {
		  masterPartId = masterPartService.createMasterPart(masterPart, currentUser.getManufacturerId());
		} catch (Exception ex) {
		  return new ResponseEntity<>(HttpStatus.FOUND);
		}
		log.info("Master Part with id [{}] created", masterPartId);
		return new ResponseEntity<>(masterPartId, HttpStatus.OK);
	}

	//Master Part Details Services
	@DeleteMapping(value = "/{masterPartId}")
	@CanUseLanguage
	public HttpEntity<Boolean> deleteMasterPart(@AuthenticationPrincipal User currentUser,
	                                          @PathVariable int masterPartId,
	                                          @RequestParam(value = "language", required = false) Language language) throws Exception {

		log.info("ACCESS: User [{}], deleteMasterPart, masterpartid [{}]", currentUser.accessDetails(), masterPartId);
		Integer languageId = language != null ? currentUser.findLanguage(language).getLanguageId() : null;
		Boolean deleted = masterPartService.deleteMasterPart(masterPartId,
				languageId,
				currentUser.obtainDefaultLanguage(),
				currentUser.getManufacturerId());
		return new ResponseEntity<>(deleted, HttpStatus.OK);
	}

  @GetMapping(value = "/{masterPartId}/stock")
  public HttpEntity<Double> getMasterPartStock(@AuthenticationPrincipal User currentUser,
                                               @PathVariable int masterPartId) {

		log.info("ACCESS: User [{}], getMasterPartStock, masterpartid [{}]", currentUser.accessDetails(), masterPartId);
		Double stockLevel = masterPartService.getMasterPartStock(masterPartId);
		return new ResponseEntity<>(stockLevel, HttpStatus.OK);
	}

  @PutMapping(value = "/{masterPartId}/note")
  public HttpEntity<Boolean> updateMasterPartNote(@AuthenticationPrincipal User currentUser,
                                                  @PathVariable int masterPartId,
                                                  @RequestBody MasterPart note) {

		log.info("ACCESS: User [{}], getMasterPartNote, masterpartid [{}]", currentUser.accessDetails(), masterPartId);
		note.setMasterPartId(masterPartId);
		Boolean updated = masterPartService.updateMasterPartNote(note);
		return new ResponseEntity<>(updated, HttpStatus.OK);
	}

	@DeleteMapping(value = "/{masterPartId}/note")
	public HttpEntity<Boolean> deleteMasterPartNote(@AuthenticationPrincipal User currentUser,
	                                                @PathVariable int masterPartId) {

		log.info("ACCESS: User [{}], deleteMasterPartNote, masterPartId [{}]", currentUser.accessDetails(), masterPartId);
		Boolean deleted = masterPartService.deleteMasterPartNote(masterPartId);
		return new ResponseEntity<>(deleted, HttpStatus.OK);
	}

	@PutMapping(value = "/{masterPartId}/stock")
	public HttpEntity<Boolean> updateMasterPartStock(@AuthenticationPrincipal User currentUser,
	                                                 @PathVariable int masterPartId,
	                                                 @RequestBody Part partInventory) {

		log.info("ACCESS: User [{}], getMasterPartInventory, masterpartid [{}]", currentUser.accessDetails(), masterPartId);
		partInventory.setMasterPartId(masterPartId);
		Boolean updated = masterPartService.updateMasterPartStock(partInventory);
		return new ResponseEntity<>(updated, HttpStatus.OK);
	}

  @GetMapping(value = "/{masterPartId}/models")
  public HttpEntity<List<Model>> getModelsContainingMasterPart(@AuthenticationPrincipal User currentUser,
                                                               @PathVariable int masterPartId) {

    log.info("ACCESS: User [{}], getModelsContainingMasterPart, masterpartid [{}]", currentUser.accessDetails(), masterPartId);
    List<Model> models = masterPartService.getModelsForMasterPart(masterPartId);
    return new ResponseEntity<>(models, HttpStatus.OK);
  }

  @PutMapping(value = "/weight", consumes = "application/json", produces = "application/json")
  public HttpEntity<List<MasterPart>> getMasterPartsWeight (@AuthenticationPrincipal User currentUser, @RequestBody List<Integer> masterPartIds) {
    List<MasterPart> response = masterPartService.getMasterPartsWeight(masterPartIds);
    return new ResponseEntity<>(response, HttpStatus.OK);
  }
}
