package co.cadshare.glue;

import co.cadshare.main.Application;
import io.cucumber.java.en.Given;
import io.cucumber.java.en.Then;
import io.cucumber.spring.CucumberContextConfiguration;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.context.SpringBootContextLoader;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.web.server.LocalServerPort;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.ContextConfiguration;

import javax.persistence.EntityManager;

import static io.restassured.RestAssured.given;

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT, classes = Application.class)
@ActiveProfiles("integration")
@CucumberContextConfiguration
@ContextConfiguration(classes = Application.class, loader = SpringBootContextLoader.class)
public class AuthStepsIT {

    @Value("${web.api.client.secret}")
    public String CLIENT_SECRET;
    @Value("${web.api.client.id}")
    public String CLIENT_ID;
	@LocalServerPort
	String port;
	@Autowired
	protected EntityManager entityManager;
	@Autowired
	CadshareIT auth;

	@Autowired
	public AuthStepsIT(CadshareIT auth) {
		this.auth = auth;
	}

    @Given("I am a Manufacturer with email address {}")
    public void iAmLoggedInAsAManufacturer(String emailAddress) {
        ManufacturerUserIT manufacturerUser = new ManufacturerUserIT(port, CLIENT_ID, CLIENT_SECRET, entityManager);
		manufacturerUser.logIn(emailAddress);
		auth.setLoggedInUser(manufacturerUser);
    }

    @Given("I am a Dealer with email address {}")
    public void iAmLoggedInAsADealerWithEmailAddress(String emailAddress) {
		DealerUserIT dealerUser = new DealerUserIT(port, CLIENT_ID, CLIENT_SECRET, entityManager);
		dealerUser.logIn(emailAddress);
		auth.setLoggedInUser(dealerUser);
    }

	@Given("I am a DealerPlus with email address {}")
	public void iAmADealerPlusWithEmailAddress(String emailAddress) {
		DealerPlusUserIT dealerPlusUser = new DealerPlusUserIT(port, CLIENT_ID, CLIENT_SECRET, entityManager);
		dealerPlusUser.logIn(emailAddress);
		auth.setLoggedInUser(dealerPlusUser);
	}
}
