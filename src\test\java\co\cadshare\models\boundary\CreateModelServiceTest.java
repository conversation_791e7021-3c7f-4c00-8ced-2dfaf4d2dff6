package co.cadshare.models.boundary;

import co.cadshare.shared.core.user.User;
import co.cadshare.aspects.logging.ServiceLoggingAspect;
import co.cadshare.shared.core.manufacturer.ManufacturerSettings;
import co.cadshare.models.core.Model;
import co.cadshare.shared.adapters.database.manufacturer.ManufacturerDao;
import co.cadshare.services.ModelUpdateService;
import com.flextrade.jfixture.rules.FixtureRule;
import org.junit.Before;
import org.junit.Rule;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@EnableAspectJAutoProxy
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(classes = {CreateModelService.class, ServiceLoggingAspect.class})
public class CreateModelServiceTest {

    @MockBean
    private AutodeskPort autodeskPort;
    @MockBean
    private ManufacturerDao manufacturerDao;
    @MockBean
    private ModelCommandPort commandPort;
    @MockBean
    private UserQueryPort userQueryPort;
    @Mock
    private ModelUpdateService modelUpdateService;
    @Autowired
    private CreateModelService out;
    private User user;
    @Mock
    private Model model;
    @Mock
    private Model errorModel;
    private ManufacturerSettings manufacturerSettings = new ManufacturerSettings(){{
        setSvf2Enabled(true);
        setManufacturerId(1);
    }};

    @Rule
    public FixtureRule fr = FixtureRule.initFixtures();

    @Before
    public void Before() {
        MockitoAnnotations.initMocks(this);
        user = new User() {{
            setUserId(1);
            setFirstName("firstName");
            setLastName("lastName");
            setManufacturerId(10);
        }};
    }

    @Test
    public void CreateModelSuccess() throws Exception {
        when(userQueryPort.getApiUserForManufacturer(1)).thenReturn(user);
        Integer returnVal = Integer.valueOf(1);
        when(commandPort.create(user, model)).thenReturn(returnVal);
        when(manufacturerDao.getManufacturerSettingsById(user.getManufacturerId())).thenReturn(manufacturerSettings);
        when(model.notFinishedTranslating()).thenReturn(true);
        Integer result = out.createModel(model, 1);
        assertEquals(returnVal, result);
        verify(autodeskPort, times(1)).translateModel(model, model.getTranslateType());
        verify(commandPort, times(1)).update(user, model);
    }

    @Test(expected = RuntimeException.class)
    public void CreateModelFailureException() throws Exception {
        when(userQueryPort.getApiUserForManufacturer(1)).thenReturn(user);
        when(commandPort.create(user, errorModel)).thenThrow(new RuntimeException("terrible"));
        out.createModel(errorModel, 1);
    }

}
