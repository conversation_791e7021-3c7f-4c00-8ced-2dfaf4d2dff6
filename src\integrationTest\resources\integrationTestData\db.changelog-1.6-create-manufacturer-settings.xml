<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                   http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.0.xsd
">

    <!-- Caterpillar -->
    <changeSet author="AndyB" id="1.6-integration-test-data-create-manufacturer-settings-1">
        <sql stripComments="true">
            INSERT INTO public.manufacturersettings (manufacturerid, previewstocklevelenabled, previewpricingenabled, partsearchenabled, edgingenableddefault, viewercolour, hideisolateenabled, dashboardenabled, requiredserialnumber, enquirypurchaseorder, enquiriesonly, displaymanufacturersubentityid, customersearchmanualsonly, softcopyenabled, svf2enabled, externallyprocessed, dealerplusenabled, partialshippingenabled, pricelistsenabled, defaultcurrencyid, faviconurl, termsandconditionsurl, enquirypotoliveenabled, searchonlypartnumbers, tabtitle, paymentenabled, stockwarehousesenabled, userregistrationenabled, additionalpartsenabled, externallypublishorder, customerexplodeenabled, overridefordirectorders, contactuspageenabled, addresscreationenabled, hideunpublishedparts, autopublishable, showfooter)
            VALUES (
            1, 	--manufacturerid,
            false, 	--previewstocklevelenabled,
            false, 	--previewpricingenabled,
            false, 	--partsearchenabled,
            true, 	--edgingenableddefault,
            'rgb(135, 206, 250)', 	--viewercolour,
            false, 	--hideisolateenabled,
            false, 	--dashboardenabled,
            false, 	--requiredserialnumber,
            false, 	--enquirypurchaseorder,
            false, 	--enquiriesonly,
            false, 	--displaymanufacturersubentityid,
            false, 	--customersearchmanualsonly,
            false, 	--softcopyenabled,
            false, 	--svf2enabled,
            false, 	--externallyprocessed,
            false, 	--dealerplusenabled,
            true, 	--partialshippingenabled,
            false, 	--pricelistsenabled,
            1, 	--defaultcurrencyid,
            NULL, 	--faviconurl,
            NULL, 	--termsandconditionsurl,
            false, 	--enquirypotoliveenabled,
            false, 	--searchonlypartnumbers,
            NULL, 	--tabtitle,
            false, 	--paymentenabled,
            false, 	--stockwarehousesenabled,
            false, 	--userregistrationenabled,
            true, 	--additionalpartsenabled,
            false, 	--externallypublishorder,
            false, 	--customerexplodeenabled,
            false,	--overridefordirectorders,
            true, 	--contactuspageenabled,
            true, 	--addresscreationenabled,
            false, 	--hideunpublishedparts,
            false, 	--autopublishable,
            TRUE	--showfooter
            );
        </sql>
    </changeSet>

    <!-- jcb -->
    <changeSet author="AndyB" id="1.1-integration-test-data-create-manufacturersettings-2">
        <sql stripComments="true">
            INSERT INTO public.manufacturersettings (manufacturerid, previewstocklevelenabled, previewpricingenabled, partsearchenabled, edgingenableddefault, viewercolour, hideisolateenabled, dashboardenabled, requiredserialnumber, enquirypurchaseorder, enquiriesonly, displaymanufacturersubentityid, customersearchmanualsonly, softcopyenabled, svf2enabled, externallyprocessed, dealerplusenabled, partialshippingenabled, pricelistsenabled, defaultcurrencyid, faviconurl, termsandconditionsurl, enquirypotoliveenabled, searchonlypartnumbers, tabtitle, paymentenabled, stockwarehousesenabled, userregistrationenabled, additionalpartsenabled, externallypublishorder, customerexplodeenabled, overridefordirectorders, contactuspageenabled, addresscreationenabled, hideunpublishedparts, autopublishable, showfooter)
            VALUES (
            2, 	--manufacturerid,
            false, 	--previewstocklevelenabled,
            false, 	--previewpricingenabled,
            false, 	--partsearchenabled,
            true, 	--edgingenableddefault,
            'rgb(135, 206, 250)', 	--viewercolour,
            false, 	--hideisolateenabled,
            false, 	--dashboardenabled,
            false, 	--requiredserialnumber,
            false, 	--enquirypurchaseorder,
            false, 	--enquiriesonly,
            false, 	--displaymanufacturersubentityid,
            false, 	--customersearchmanualsonly,
            false, 	--softcopyenabled,
            false, 	--svf2enabled,
            false, 	--externallyprocessed,
            false, 	--dealerplusenabled,
            true, 	--partialshippingenabled,
            TRUE, 	--pricelistsenabled,
            1, 	--defaultcurrencyid,
            NULL, 	--faviconurl,
            NULL, 	--termsandconditionsurl,
            false, 	--enquirypotoliveenabled,
            false, 	--searchonlypartnumbers,
            NULL, 	--tabtitle,
            false, 	--paymentenabled,
            FALSE, 	--stockwarehousesenabled,
            false, 	--userregistrationenabled,
            true, 	--additionalpartsenabled,
            false, 	--externallypublishorder,
            false, 	--customerexplodeenabled,
            false,	--overridefordirectorders,
            true, 	--contactuspageenabled,
            true, 	--addresscreationenabled,
            false, 	--hideunpublishedparts,
            true, 	--autopublishable,
            true	--showfooter
            );
        </sql>
    </changeSet>

    <!-- Liebherr -->
    <changeSet author="AndyB" id="1.1-integration-test-data-create-manufacturersettings-3">
        <sql stripComments="true">
            INSERT INTO public.manufacturersettings (manufacturerid, previewstocklevelenabled, previewpricingenabled, partsearchenabled, edgingenableddefault, viewercolour, hideisolateenabled, dashboardenabled, requiredserialnumber, enquirypurchaseorder, enquiriesonly, displaymanufacturersubentityid, customersearchmanualsonly, softcopyenabled, svf2enabled, externallyprocessed, dealerplusenabled, partialshippingenabled, pricelistsenabled, defaultcurrencyid, faviconurl, termsandconditionsurl, enquirypotoliveenabled, searchonlypartnumbers, tabtitle, paymentenabled, stockwarehousesenabled, userregistrationenabled, additionalpartsenabled, externallypublishorder, customerexplodeenabled, overridefordirectorders, contactuspageenabled, addresscreationenabled, hideunpublishedparts, autopublishable, showfooter)
            VALUES (
            3, 	--manufacturerid,
            false, 	--previewstocklevelenabled,
            false, 	--previewpricingenabled,
            false, 	--partsearchenabled,
            true, 	--edgingenableddefault,
            'rgb(135, 206, 250)', 	--viewercolour,
            false, 	--hideisolateenabled,
            false, 	--dashboardenabled,
            false, 	--requiredserialnumber,
            false, 	--enquirypurchaseorder,
            false, 	--enquiriesonly,
            false, 	--displaymanufacturersubentityid,
            false, 	--customersearchmanualsonly,
            false, 	--softcopyenabled,
            false, 	--svf2enabled,
            false, 	--externallyprocessed,
            false, 	--dealerplusenabled,
            true, 	--partialshippingenabled,
            FALSE, 	--pricelistsenabled,
            1, 	--defaultcurrencyid,
            NULL, 	--faviconurl,
            NULL, 	--termsandconditionsurl,
            false, 	--enquirypotoliveenabled,
            false, 	--searchonlypartnumbers,
            NULL, 	--tabtitle,
            false, 	--paymentenabled,
            TRUE, 	--stockwarehousesenabled,
            false, 	--userregistrationenabled,
            true, 	--additionalpartsenabled,
            false, 	--externallypublishorder,
            false, 	--customerexplodeenabled,
            false,	--overridefordirectorders,
            true, 	--contactuspageenabled,
            true, 	--addresscreationenabled,
            false, 	--hideunpublishedparts,
            false, 	--autopublishable,
            TRUE	--showfooter
            );
        </sql>
    </changeSet>

    <!-- Terex -->
    <changeSet author="AndyB" id="1.1-integration-test-data-create-manufacturersettings-4">
        <sql stripComments="true">
            INSERT INTO public.manufacturersettings (manufacturerid, previewstocklevelenabled, previewpricingenabled, partsearchenabled, edgingenableddefault, viewercolour, hideisolateenabled, dashboardenabled, requiredserialnumber, enquirypurchaseorder, enquiriesonly, displaymanufacturersubentityid, customersearchmanualsonly, softcopyenabled, svf2enabled, externallyprocessed, dealerplusenabled, partialshippingenabled, pricelistsenabled, defaultcurrencyid, faviconurl, termsandconditionsurl, enquirypotoliveenabled, searchonlypartnumbers, tabtitle, paymentenabled, stockwarehousesenabled, userregistrationenabled, additionalpartsenabled, externallypublishorder, customerexplodeenabled, overridefordirectorders, contactuspageenabled, addresscreationenabled, hideunpublishedparts, autopublishable, showfooter)
            VALUES (
            4, 	                    --manufacturerid,
            false, 	                --previewstocklevelenabled,
            false, 	                --previewpricingenabled,
            false, 	                --partsearchenabled,
            true, 	                --edgingenableddefault,
            'rgb(135, 206, 250)', 	--viewercolour,
            false, 	                --hideisolateenabled,
            false, 	                --dashboardenabled,
            false, 	                --requiredserialnumber,
            false, 	                --enquirypurchaseorder,
            false, 	                --enquiriesonly,
            false, 	                --displaymanufacturersubentityid,
            false, 	                --customersearchmanualsonly,
            false, 	                --softcopyenabled,
            false, 	                --svf2enabled,
            false, 	                --externallyprocessed,
            false, 	                --dealerplusenabled,
            true, 	                --partialshippingenabled,
            TRUE, 	                --pricelistsenabled,
            1, 	                    --defaultcurrencyid,
            NULL, 	                --faviconurl,
            NULL, 	                --termsandconditionsurl,
            false, 	                --enquirypotoliveenabled,
            false, 	                --searchonlypartnumbers,
            NULL, 	                --tabtitle,
            false, 	                --paymentenabled,
            TRUE, 	                --stockwarehousesenabled,
            false, 	                --userregistrationenabled,
            true, 	                --additionalpartsenabled,
            false, 	                --externallypublishorder,
            false, 	                --customerexplodeenabled,
            false,	                --overridefordirectorders,
            true, 	                --contactuspageenabled,
            true, 	                --addresscreationenabled,
            false, 	                --hideunpublishedparts,
            false, 	                --autopublishable,
            TRUE	                --showfooter
            );
        </sql>
    </changeSet>


</databaseChangeLog>
