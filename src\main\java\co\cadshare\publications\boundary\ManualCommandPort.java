package co.cadshare.publications.boundary;

import co.cadshare.publications.core.Manual;
import co.cadshare.publications.core.ManualStatus;

import java.util.ArrayList;
import java.util.List;

public interface ManualCommandPort {
    int createManual(Manual manual);

    Boolean deleteModelFromManualModelMap(int modelId);

    Boolean deleteTechDocFromManualTechDocMap(int techDocId);

    Boolean deleteVideoFromManualVideoMap(int videoId);

    Boolean deleteKitFromManualKitMap(int kitId);

    void updateManualStatus(int manualId, ManualStatus manualStatus);

    Boolean deleteManual(int manualId);

    int updateManual(Manual manual);

    void deleteManualModelMap(int manualId, List<Integer> modelMapToArchive);

    void deleteManualTechDocMap(int manualId, List<Integer> techDocMappingsToDelete);

    void deleteManualVideoMap(int manualId, List<Integer> videoMappingsToDelete);

    void deleteManualKitMap(int manualId, List<Integer> kitMappingsToDelete);

    void createManualModelMap(int manualId, int modelId);

    void createManualTechDocMap(int manualId, int techDocId);

    void createManualVideoMap(int manualId, int videoId);

    void createManualKitMap(int manualId, int kitId);

    void assignManualToManufacturerSubEntities(int manualId, List<Integer> manufacturerSubEntityIdList);

    boolean clearMappingsForManual(int manualId,  ArrayList<Integer> assignedManualIds);

    boolean assignManufacturerSubEntityToManuals(int manufacturerSubEntityId, List<Integer> assignedManualIds);

    boolean clearMappingsForSubEntity(int manufacturerSubEntityId);
}
