/*
 * Copyright 2016 Bell.
 */
package co.cadshare.modelMgt.ranges.adapters.api.web;

import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import co.cadshare.domainmodel.machine.Machine;
import co.cadshare.domainmodel.range.Range;
import co.cadshare.shared.core.user.User;
import co.cadshare.modelMgt.ranges.boundary.RangeService;

/**
 * TODO(dallanmc) Description of class.
 */
@Controller
@RequestMapping("/range")
public class RangeController {

    private static final Logger logger = LoggerFactory.getLogger(RangeController.class);

    @Autowired
    private RangeService rangeService;

  @PreAuthorize("hasRole('ROLE_SUPER_USER') or hasRole('ROLE_MANUFACTURER') and #currentUser.manufacturerId == #range.manufacturerId")
    @RequestMapping(method = RequestMethod.POST, consumes = "application/json")
    public HttpEntity<Integer> createRange(@AuthenticationPrincipal User currentUser, @RequestBody Range range) {

        logger.info("ACCESS: User [{}], createRange, range [{}]", currentUser.accessDetails(), range.toString());
        int rangeId = rangeService.createRange(range);

        logger.info("Range with id [{}] created", rangeId);
        return new ResponseEntity<>(rangeId, HttpStatus.OK);
    }

    @RequestMapping(value = "/{rangeId}/machines", method = RequestMethod.GET)
    public HttpEntity<List<Machine>> getMachinesForRange(@AuthenticationPrincipal User currentUser, @PathVariable int rangeId) throws Exception {

        List<Machine> machineList = rangeService.getMachinesForRange(rangeId);

        return new ResponseEntity<List<Machine>>(machineList, HttpStatus.OK);
    }

}
