package co.cadshare.addresses.adapters.database;

import co.cadshare.addresses.core.ExternalAddress;
import co.cadshare.addresses.core.UserAddressMap;
import co.cadshare.shared.adapters.database.addresses.AddressEntity;
import co.cadshare.utils.CycleAvoidingMappingContext;
import org.mapstruct.Context;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper(uses = UserAddressMapEntityMapper.class)
public interface InternalAddressEntityMapper {

    InternalAddressEntityMapper Instance = Mappers.getMapper(InternalAddressEntityMapper.class);

    @Mapping(source="userAddressMaps", target="userMaps", qualifiedByName="mapsEntityToCore")
    ExternalAddress entityToCore(AddressEntity source, @Context CycleAvoidingMappingContext context);

    @Named("mapsEntityToCore")
    public static List<UserAddressMap> mapsEntityToCore(List<UserAddressMapEntity> entities, @Context CycleAvoidingMappingContext context) {
        return UserAddressMapEntityMapper.Instance.entitiesToCores(entities, context);
    }
}