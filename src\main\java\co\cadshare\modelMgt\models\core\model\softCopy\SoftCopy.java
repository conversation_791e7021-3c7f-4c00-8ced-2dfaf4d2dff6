/*
 * Copyright 2016 Bell.
 */
package co.cadshare.modelMgt.models.core.model.softCopy;

import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.sql.Timestamp;

@Data
@NoArgsConstructor
public class SoftCopy {

    private int id;
    private int viewableId;
    private String name;
    private String description;
    private LeaderType leaderType;
    private Timestamp createdDate;
    private int createdByUserId;
    private Timestamp modifiedDate;
    private int modifiedByUserId;

    private String createdByUserFirstName;
    private String createdByUserLastName;
    private String createdByUserFullName;

    public static SoftCopy createFrom(SoftCopy softcopy) {
        return softcopy.builder()
            .build();
    }

    @Builder
    public SoftCopy(int id, int viewableId, String name, String description, LeaderType leaderType, Timestamp createdDate, int createdByUserId,
                    Timestamp modifiedDate, int modifiedByUserId) {
        this.id = id;
        this.viewableId = viewableId;
        this.name = name;
        this.description = description;
        this.leaderType = leaderType;
        this.createdDate = createdDate;
        this.createdByUserId = createdByUserId;
        this.modifiedDate = modifiedDate;
        this.modifiedByUserId = modifiedByUserId;
    }

    public void setCreatedByUserLastName(String lastName) {
        this.createdByUserLastName = lastName;
        this.createdByUserFullName = this.createdByUserFirstName + " " + this.createdByUserLastName;
    }

}
