package co.cadshare.modelMgt.publicationCategories.core;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import java.sql.Timestamp;

@Data
public class PublicationCategory {

    private Integer id;
	private String name;
    private Integer manufacturerId;
    private boolean deleted;
    private Integer createdByUserId;
    private Integer modifiedByUserId;
    private Timestamp createdDate;
    private Timestamp modifiedDate;

	public void update(PublicationCategory publicationCategory) {
		this.name = publicationCategory.getName();
	}
}