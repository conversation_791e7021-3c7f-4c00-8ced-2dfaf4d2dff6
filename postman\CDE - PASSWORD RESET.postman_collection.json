{"info": {"_postman_id": "b2c18380-95cc-43b6-b427-0fcbf6ca3f5d", "name": "CDE - PASSWORD RESET", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "300252"}, "item": [{"name": "CDEResetPassword", "event": [{"listen": "prerequest", "script": {"exec": ["let userIds = pm.collectionVariables.get(\"userIds\");", "", "if(!userIds || userIds.length == 0) {", "    userIds = [28,72,55,654,655,619,71,104,620,149,898,585,971,970,569,291,264,572,570,982,281,571,573,574,983,984,629,630,1154,1143,1141,1142,31,20,17,30,21,29,47,57,59,73,63,58,259,86,90,92,93,95,113,119,117,112,110,268,159,166,214,232,233,231,229,235,238,236,237,245,280,251,260,255,253,258,286,277,283,282,267,256,275,254,239,234,266,257,279,356,400,428,409,481,298,329,330,363,365,366,407,408,411,438,490,491,493,1220];", "} //106 users", "", "let currentUserId = userIds.shift();", "console.log(\"CURRENT USER: \" + currentUserId)", "pm.collectionVariables.set(\"userId\", currentUserId);", "pm.collectionVariables.set(\"userIds\", userIds);"], "type": "text/javascript"}}, {"listen": "test", "script": {"exec": ["if (userIds && userIds.length > 0){", "    console.log(\"Continue and loop\");", "} else {", "    console.log(\"End of userIds Loop\");", "}", "", "pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", ""], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "Site-Url", "value": "https://cdepartsassist.cadshare.com"}, {"key": "Authorization", "value": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJleHAiOjE2NTQwMjk5MDMsInVzZXJfbmFtZSI6IjE2IiwiYXV0aG9yaXRpZXMiOlsiUk9MRV9PcmRlciIsIlJPTEVfRGFzaGJvYXJkIiwiUk9MRV9TZWN1cml0eSIsIlJPTEVfTUFOVUZBQ1RVUkVSIiwiUk9MRV9QdWJsaWNhdGlvbiIsIlJPTEVfUGFydHMiLCJST0xFX1Byb2R1Y3RzIiwiUk9MRV9DdXN0b21lciIsIlJPTEVfQWRtaW4iXSwianRpIjoiMjViYmI2ZmUtMDVhYy00YjFjLWIzNjgtYTMzZGNmMTYwZjQ5IiwiY2xpZW50X2lkIjoiY2xpZW50YXBwIiwic2NvcGUiOlsicmVhZCIsIndyaXRlIl19.Lv4uDL6UXC-acz43uAjfyfFK4Sg_uSN4C6wd-dfaG4Q"}], "url": {"raw": "https://d11egzqtog0uej.cloudfront11111.net/user/{{userId}}/password/reset?language=EN", "protocol": "https", "host": ["d11egzqtog0uej", "cloudfront11111", "net"], "path": ["user", "{{userId}}", "password", "reset"], "query": [{"key": "language", "value": "EN"}]}}, "response": []}], "variable": [{"key": "userId", "value": ""}, {"key": "userIds", "value": ""}]}