package co.cadshare.ranges.boundary;

import co.cadshare.shared.core.user.User;
import co.cadshare.aspects.logging.ServiceLoggingAspect;
import co.cadshare.models.boundary.UserQueryPort;
import co.cadshare.ranges.core.Range;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.MockitoAnnotations;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.when;

@EnableAspectJAutoProxy
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(classes = {CreateRangeService.class, ServiceLoggingAspect.class})
public class CreateRangeServiceTest {

    @MockBean
    private RangeCommandPort commandPort;
    @Autowired
    private CreateRangeService out;
    @MockBean
    private UserQueryPort userQueryPort;
    private User user;
    private Range range;
    private Range errorRange = new Range();

    @Before
    public void Before() {
        MockitoAnnotations.initMocks(this);
        range = buildRange();
        user = new User() {{
            setUserId(1);
            setFirstName("firstName");
            setLastName("lastName");
        }};
    }

    @Test
    public void CreateRangeSuccess() {
        Integer returnVal = Integer.valueOf(1);
        when(userQueryPort.getApiUserForManufacturer(1)).thenReturn(user);
        when(commandPort.create(user, range)).thenReturn(returnVal);
        Integer result = out.create(user, range);
        assertEquals(returnVal, result);
    }

    @Test(expected = RuntimeException.class)
    public void CreateRangeFailureException() throws Exception {
        when(userQueryPort.getApiUserForManufacturer(1)).thenReturn(user);
        when(commandPort.create(user, errorRange)).thenThrow(new RuntimeException("terrible"));
        out.create(user, errorRange);
    }

    private Range buildRange() {
        Range range = new Range();
        return range;
    }
}
