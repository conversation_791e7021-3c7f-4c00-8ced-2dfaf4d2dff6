package co.cadshare.modelMgt.products.adapters.api.web;

import co.cadshare.modelMgt.products.adapters.api.GetProductResponseDto;
import co.cadshare.modelMgt.products.adapters.api.ProductMapper;
import co.cadshare.modelMgt.products.core.Product;
import org.junit.Test;

import java.sql.Timestamp;
import java.util.Calendar;

import static org.junit.Assert.assertEquals;

public class ProductMapperTest {


	@Test
	public void testProductToGetResponseDto() {

		Product sourceProduct = new Product();
		sourceProduct.setId(1);
		sourceProduct.setName("Product Name");
		sourceProduct.setRangeId(1);
		sourceProduct.setRangeName("Range Name");
		sourceProduct.setCreatedDate(new java.sql.Timestamp(Calendar.getInstance().getTime().getTime()));

		GetManufacturerProductResponseDto target = ProductMapper.Instance.productToGetManufacturerResponseDto(sourceProduct);

		assertEquals(sourceProduct.getId(), target.getId());
		assertEquals(sourceProduct.getName(), target.getName());
		assertEquals(sourceProduct.getRangeName(), target.getRange());
		assertEquals(sourceProduct.getCreatedDate(), target.getCreatedDate());
	}
}
