/*
 * Copyright 2016 Bell.
 */
package co.cadshare.modelMgt.models.core;

import java.util.List;

import lombok.Data;

/**
 * TODO(dallanmc) Description of class.
 */
@Data
public class MetadataObjectExtended {
    private Integer objectid;
    private String name;
    private String partNumber;
    private List<MetadataObjectExtended> objects;
    private Integer parentObjectId;
    private String description;
    private String weight;
    private String massUnit;
    private String alternatePartNumber;
    private String sparePartIdentifier;
    private boolean sparePart;
    private boolean sellablePart;

    public MetadataObjectExtended(){}

    public MetadataObjectExtended(String name) {
        this.name = name;
    }

    public boolean isValid() {
        return (name != null &&
                name.length() <= 1000 &&
                !name.isEmpty() &&
                (partNumber == null || partNumber.length() <= 1000) &&
                (description == null || description.length() <= 200) &&
                (weight == null || weight.length() <= 50) &&
                (massUnit == null || massUnit.length() <= 50) &&
                (alternatePartNumber == null || alternatePartNumber.length() <= 1000));
    }
    
}
