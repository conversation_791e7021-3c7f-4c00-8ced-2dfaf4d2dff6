package co.cadshare.api.user;

import co.cadshare.domainmodel.GenericMessageResponse;
import co.cadshare.shared.core.user.User;
import co.cadshare.services.ManufacturerService;
import co.cadshare.services.ManufacturerSubEntityService;
import co.cadshare.services.PermissionsService;
import java.io.IOException;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/api/users")
public class UserApiController {

  @Autowired
  UserService userService;

  @Autowired
  ManufacturerSubEntityService manufacturerSubEntityService;

  @Autowired
  ManufacturerService manufacturerService;

  @Autowired
  PermissionsService permissionsService;

  private static final Logger logger = LoggerFactory.getLogger(UserApiController.class);

  @PreAuthorize("hasRole('ROLE_API_MANUFACTURER')")
  @PostMapping(consumes = "application/json")
  public HttpEntity<Integer> createUser(@AuthenticationPrincipal User currentUser, @RequestBody APIUser user) throws Exception {
    logger.info("API ACCESS: User [{}], createUser, user [{}]", currentUser.accessDetails(), user.toString());

    int userId = userService.createUser(currentUser, user);

    logger.info("User with id [{}] created", userId);

    return new ResponseEntity<>(userId, HttpStatus.OK);
  }

  @PreAuthorize("hasRole('ROLE_API_MANUFACTURER')")
  @GetMapping(value = "/{userId}")
  public HttpEntity<APIUser> findCustomerByUserId(@AuthenticationPrincipal User currentUser, @PathVariable int userId) throws IOException {
    logger.info("API ACCESS: User [{}], findCustomerByUserId", currentUser.accessDetails());

    User user = userService.findCustomerByUserId(userId);

    ResponseEntity response = permissionsCheck(currentUser, user);
    return response;
  }

  @PreAuthorize("hasRole('ROLE_API_MANUFACTURER')")
  @PutMapping(value = "/{userId}", consumes = "application/json")
  public HttpEntity<GenericMessageResponse> updateUser(@AuthenticationPrincipal User currentUser, @RequestBody APIUser apiUser, @PathVariable int userId) throws Exception {
    logger.info("API ACCESS: User [{}], updateUser, userId [{}], user [{}]", currentUser.accessDetails(), userId, apiUser.toString());

    try {
      //Test permissions if allowed to update user initially
      User user = userService.findCustomerByUserId(userId);
      hasAdminPermissionsForUser(currentUser, user);

      //Test permissions to access all values being updated too
      apiUser.setUserId(userId);
      User updatedUser = apiUser.toUser();
      hasAdminPermissionsForUser(currentUser, updatedUser);
      hasAdminPermissionsForSubEntity(currentUser, apiUser.getCompanyId());
    } catch (Exception ex) {
      return new ResponseEntity<>(null, HttpStatus.FORBIDDEN);
    }

    userService.updateUser(currentUser.getUserId(), apiUser, userId);

    return new ResponseEntity<>(new GenericMessageResponse("Successfully updated user"), HttpStatus.OK);
  }

  @PreAuthorize("hasRole('ROLE_API_MANUFACTURER')")
  @RequestMapping(value = "/{userId}", method = RequestMethod.DELETE)
  public HttpEntity<Boolean> deleteUser(@AuthenticationPrincipal User currentUser, @PathVariable int userId) throws IOException {
    logger.info("API ACCESS: User [{}], delete user", currentUser.accessDetails());

    try {
      hasAdminPermissionsForUserId(currentUser, userId);
    } catch (Exception ex) {
      return new ResponseEntity<>(null, HttpStatus.FORBIDDEN);
    }
    boolean success = userService.deleteUser(userId);

    return new ResponseEntity<>(success, HttpStatus.OK);
  }


  private ResponseEntity permissionsCheck(User currentUser, User user) {
    APIUser apiUser = APIUser.createFrom(user);
    try {
      hasAdminPermissionsForUser(currentUser, user);
    } catch (Exception ex) {
      return new ResponseEntity<>(null, HttpStatus.FORBIDDEN);
    }
    return new ResponseEntity<>(apiUser, HttpStatus.OK);
  }

  private boolean hasAdminPermissionsForUserId(User currentUser, int userId) throws Exception {
    return permissionsService.userHasAdminPermissionsForUser(currentUser, userId);
  }

  private boolean hasAdminPermissionsForUser(User currentUser, User user) throws Exception {
    return permissionsService.userHasAdminPermissionsForUser(currentUser, user);
  }

  private boolean hasAdminPermissionsForSubEntity(User currentUser, int manufacturerSubEntityId) throws Exception {
    return permissionsService.hasAdminPermissionsForSubEntity(currentUser, manufacturerSubEntityId);
  }
}
