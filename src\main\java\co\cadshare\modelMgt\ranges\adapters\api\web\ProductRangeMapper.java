package co.cadshare.modelMgt.ranges.adapters.api.web;

import co.cadshare.domainmodel.range.Range;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface ProductRangeMapper {

	ProductRangeMapper Instance = Mappers.getMapper(ProductRangeMapper.class);

	@Mapping(source="rangeId", target="id")
	ProductRangeListItemDto RangeToDto(Range range);

	List<ProductRangeListItemDto> RangesToDtos(List<Range> ranges);

}
