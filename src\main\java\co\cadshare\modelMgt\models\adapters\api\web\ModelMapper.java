package co.cadshare.modelMgt.models.adapters.api.web;

import co.cadshare.modelMgt.models.core.Model;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface ModelMapper {
    ModelMapper Instance = Mappers.getMapper(ModelMapper.class);

    @Mapping(source="machineName", target = "productName")
    List<ModelForPartNumberResponseDto> ModelsToModelForPartNumberResponsesDto(List<Model> models);

    ModelForPartNumberResponseDto ModelToModelForPartNumberResponsesDto(Model model);
}
