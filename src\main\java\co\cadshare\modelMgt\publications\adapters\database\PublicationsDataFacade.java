package co.cadshare.modelMgt.publications.adapters.database;

import co.cadshare.modelMgt.publications.boundary.*;
import co.cadshare.modelMgt.publications.core.Manual;
import co.cadshare.modelMgt.shared.boundary.PublicationQueryPort;
import co.cadshare.modelMgt.shared.core.Product;
import co.cadshare.persistence.dealer.DealerPlusPublicationDao;
import co.cadshare.shared.core.user.User;
import co.cadshare.modelMgt.shared.core.Publication;
import co.cadshare.shared.boundary.QueryFilter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.sql.Timestamp;
import java.util.Calendar;
import java.util.List;

@Service
public class PublicationsDataFacade implements PublicationCommandPort, PublicationQueryPort, ProductCommandPort {

    private final ManualQueryPort manualQueryPort;
    private final PublicationsRepo publicationsRepo;
    private final PublicationsComplexQueryRepo publicationsQueryRepo;
	private final DealerPlusPublicationDao dealerManualDao;
	private final ManualDao manualDao;

    @Autowired
    public PublicationsDataFacade(ManualQueryPort manualQueryPort,
                                  PublicationsRepo publicationRepo,
                                  PublicationsComplexQueryRepo publicationsQueryRepo,
                                  DealerPlusPublicationDao dealerManualDao,
                                  ManualDao manualDao) {
        this.manualQueryPort = manualQueryPort;
        this.publicationsRepo = publicationRepo;
        this.publicationsQueryRepo = publicationsQueryRepo;
	    this.dealerManualDao = dealerManualDao;
	    this.manualDao = manualDao;
    }

    @Override
    public Publication get(Integer id) {
        PublicationsPublicationEntity entity = this.publicationsRepo.getOne(id);
        return PublicationEntityMapper.Instance.entityToCore(entity);
    }

    @Override
    public List<Publication> getList() {
        List<PublicationsPublicationEntity> entities = this.publicationsRepo.findAll();
        return PublicationEntityMapper.Instance.entitiesToCores(entities);
    }

    public List<Publication> getList(QueryFilter filter) {
        List<Manual> manuals = this.manualQueryPort.getFilteredList(filter);
        return ManualMapper.Instance.manualsToPublications(manuals);
    }

    @Override
    public Integer create(User user, Publication publication) {
		PublicationsPublicationEntity entity = PublicationEntityMapper.Instance.coreToEntity(publication);
	    Timestamp now = new Timestamp(Calendar.getInstance().getTime().getTime());
	    entity.setCreatedDate(now);
		entity.setCreatedByUserId(user.getUserId());
		entity.setModifiedDate(now);
		entity.setModifiedByUserId(user.getUserId());
        PublicationsPublicationEntity savedEntity = this.publicationsRepo.save(entity);
        return savedEntity.getId();
}

    @Override
    public void update(User user, Publication publication) throws Exception {
	    PublicationsPublicationEntity entity = PublicationEntityMapper.Instance.coreToEntity(publication);
	    Timestamp now = new Timestamp(Calendar.getInstance().getTime().getTime());
	    entity.setModifiedDate(now);
	    entity.setModifiedByUserId(user.getUserId());
        this.publicationsRepo.save(entity);
    }

    @Override
    public void delete(User user, Publication publication) throws Exception {
        this.publicationsRepo.delete(PublicationEntityMapper.Instance.coreToEntity(publication));
    }

    @Override
    public List<Publication> getPublicationsForManufacturer(Integer manufacturerId) {
        List<PublicationsPublicationEntity> publications = this.publicationsQueryRepo.getPublicationsForManufacturer(manufacturerId);
        return PublicationEntityMapper.Instance.entitiesToCores(publications);
    }

	@Override
	public List<Integer> getPublicationsForPublicationCategories(List<Integer> publicationCategories) {
		return dealerManualDao.getPublicationsForPublicationCategories(publicationCategories);
	}

    @Override
    public List<Publication> getPublicationsForPublicationCategory(Integer publicationCategoryId) {
        List<PublicationsPublicationEntity> publications = this.publicationsQueryRepo.getPublicationsForPublicationCategory(publicationCategoryId);
        return PublicationEntityMapper.Instance.entitiesToCores(publications);
    }

	@Override
	public List<Publication> getPublicationsAssignedToPurchaser(int purchaserId) {
		List<PublicationsPublicationEntity> publications = this.publicationsQueryRepo.getPublicationsForPurchaser(purchaserId);
		return PublicationEntityMapper.Instance.entitiesToCores(publications);
	}

	@Override
    public void updateProduct(User user, Product product) throws Exception {

    }

	@Override
	public void unassignPublicationsFromDealerPlusCustomers(List<Integer> publicationsToBeUnassigned, Integer purchaserId) {
		dealerManualDao.unassignPublicationsFromDealerPlusCustomers(purchaserId, publicationsToBeUnassigned);
	}

    @Override
    public void assignPublicationsToPurchaser(Integer purchaserId, List<Integer> publications) {
        manualDao.assignPurchaserToPublications(purchaserId, publications);
    }

    @Override
    public void unassignPublicationsFromPurchaser(int purchaserId) {
        manualDao.unassignPublicationsForPurchaser(purchaserId);
    }
}
