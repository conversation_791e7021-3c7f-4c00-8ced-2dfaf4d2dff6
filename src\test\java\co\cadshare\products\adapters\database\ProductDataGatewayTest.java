package co.cadshare.products.adapters.database;

import co.cadshare.shared.core.user.User;
import co.cadshare.aspects.logging.ServiceLoggingAspect;
import co.cadshare.products.core.Product;
import co.cadshare.products.boundary.ProductCommandPort;
import co.cadshare.products.boundary.ProductQueryPort;
import co.cadshare.shared.adapters.database.ProductEntity;
import co.cadshare.shared.adapters.database.ProductEntityMapper;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.MockitoAnnotations;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.*;

@EnableAspectJAutoProxy
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(classes = {ProductDataGateway.class, ServiceLoggingAspect.class})
public class ProductDataGatewayTest {

    @MockBean
    private ProductRepo productRepo;
    @MockBean
    private ProductComplexQueryRepo productQueryRepo;
    @Autowired
    ProductCommandPort cmdOut;
    @Autowired
    ProductQueryPort queryOut;
    private User user;
    private Product product;
    private ProductEntity productEntity;
    private ProductEntity productEntityWithId;
    private Product errorProduct;
    private ProductEntity errorProductEntity;

    @Before
    public void Before() {
        MockitoAnnotations.initMocks(this);
        product = buildProduct();
        errorProduct = buildProduct();
        user = new User() {{
            setUserId(1);
            setFirstName("firstName");
            setLastName("lastName");
        }};
        productEntity = ProductEntityMapper.Instance.coreToEntity(product);
        errorProductEntity = ProductEntityMapper.Instance.coreToEntity(errorProduct);
        productEntityWithId = ProductEntityMapper.Instance.coreToEntity(product);
        productEntityWithId.setId(Integer.valueOf(1));
    }

    @Test
    public void CreateProductSuccess() {
        when(productRepo.save(any(ProductEntity.class))).thenReturn(productEntityWithId);
        Integer result = cmdOut.create(user, product);
        verify(productRepo, times(1)).save(argThat(new ProductEntityMatcher(productEntity)));
        assertEquals(1, result);
    }

    @Test(expected = RuntimeException.class)
    public void CreateProductFailureException() throws Exception {
        when(productRepo.save(any(ProductEntity.class))).thenThrow(new RuntimeException("terrible"));
        cmdOut.create(user, errorProduct);
    }

    @Test
    public void UpdateProductSuccess()  throws Exception {
        when(productRepo.save(productEntity)).thenReturn(productEntityWithId);
        cmdOut.update(user, product);
        verify(productRepo, times(1)).save(argThat(new ProductEntityMatcher(productEntity)));
    }

    @Test(expected = RuntimeException.class)
    public void UpdateProductFailureException() throws Exception {
        doThrow(new RuntimeException("terrible")).when(productRepo).save(any(ProductEntity.class));
        cmdOut.update(user, errorProduct);
        verify(productRepo, times(1)).save(argThat(new ProductEntityMatcher(errorProductEntity)));
    }

    @Test
    public void DeleteProductSuccess()  throws Exception {
        doNothing().when(productRepo).delete(productEntity);
        cmdOut.delete(user, product);
    }

    @Test(expected = RuntimeException.class)
    public void DeleteProductFailureException() throws Exception {
        doThrow(new RuntimeException("terrible")).when(productRepo).save(any(ProductEntity.class));
        cmdOut.delete(user, errorProduct);
        verify(productRepo, times(1)).save(argThat(new ProductEntityMatcher(errorProductEntity)));
    }

    private Product buildProduct() {
        Product product = new Product();
        return product;
    }
}

