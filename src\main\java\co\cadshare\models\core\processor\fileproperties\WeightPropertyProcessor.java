/*
 * Copyright 2016 Bell.
 */
package co.cadshare.models.core.processor.fileproperties;

import co.cadshare.models.core.MetadataObjectExtended;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.LinkedHashMap;
import java.util.List;

@Component
public class WeightPropertyProcessor extends AbstractPropertiesProcessor implements FilePropertiesProcessor, InitializingBean {

    @Value("#{'${properties.processor.weight.synonyms}'.split(',')}")
    List<String> weightSynonyms;
    @Override
    public void setProperties(LinkedHashMap properties, MetadataObjectExtended object) {
        object.setWeight(getPropertyValue(properties));
    }

    @Override
    public List<String> getSynonyms() {
        return weightSynonyms;
    }

    @Override
    public void setSynonyms(List<String> synonyms) {
        weightSynonyms = synonyms;
    }
}
