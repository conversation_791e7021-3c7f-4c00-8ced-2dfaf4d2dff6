package co.cadshare.modelMgt.publications.adapters.database;

import co.cadshare.modelMgt.shared.core.Viewable;
import co.cadshare.modelMgt.viewables.adapters.database.ViewableEntityViewMapper;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.sql.Timestamp;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.when;

public class ViewableEntityViewMapperTest {

	@Mock
	private PublicationsViewableEntityView entityView;

	@Before()
	public void before() {
		MockitoAnnotations.initMocks(this);
	}

	@Test
	public void test() {
		when(entityView.getId()).thenReturn(1);
		when(entityView.getName()).thenReturn("Test Viewable");
		when(entityView.isSetupComplete()).thenReturn(true);
		when(entityView.getCreatedDate()).thenReturn(new Timestamp(System.currentTimeMillis()));
		when(entityView.getCreatedByUserId()).thenReturn(1);
		when(entityView.getModifiedByUserId()).thenReturn(2);
		when(entityView.getModifiedDate()).thenReturn(new Timestamp(System.currentTimeMillis()));
		when(entityView.getRetries()).thenReturn(0);
	    // Act
	    Viewable viewable = ViewableEntityViewMapper.Instance.entityToCore(entityView);

	    // Assert
	    assertEquals(entityView.getName(), viewable.getName());
	    assertEquals(entityView.isSetupComplete(), viewable.isSetupComplete());
		assertEquals(entityView.getRetries(), viewable.getRetries());
	    assertEquals(entityView.getCreatedDate(), viewable.getCreatedDate());
	    assertEquals(entityView.getCreatedByUserId(), viewable.getCreatedByUserId());
	    assertEquals(entityView.getModifiedByUserId(), viewable.getModifiedByUserId());
	    assertEquals(entityView.getModifiedDate(), viewable.getModifiedDate());
	}
}
