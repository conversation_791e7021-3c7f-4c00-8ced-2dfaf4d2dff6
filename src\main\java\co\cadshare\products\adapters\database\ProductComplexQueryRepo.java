package co.cadshare.products.adapters.database;

import co.cadshare.shared.adapters.database.ProductEntity;
import co.cadshare.shared.adapters.database.QProductEntity;
import com.querydsl.jpa.impl.JPAQueryFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import javax.persistence.EntityManager;
import java.util.List;

@Repository
public class ProductComplexQueryRepo {

    private JPAQueryFactory queryFactory;

    @Autowired
    public ProductComplexQueryRepo(EntityManager entityManager) {
        this.queryFactory = new JPAQueryFactory(entityManager);
    }

    public List<ProductEntity> getProductsForRange(Integer rangeId) {
        QProductEntity product = QProductEntity.productEntity;
        return queryFactory.selectFrom(product)
                .where(product.rangeId.eq(rangeId)
                        .and(product.deleted.eq(false)))
                .fetch();
    }

}

