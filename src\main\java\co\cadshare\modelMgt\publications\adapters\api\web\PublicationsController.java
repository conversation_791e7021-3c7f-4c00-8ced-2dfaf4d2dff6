package co.cadshare.modelMgt.publications.adapters.api.web;

import co.cadshare.aspects.access.roles.CanAccessManufacturer;
import co.cadshare.aspects.access.roles.IsManufacturer;
import co.cadshare.exceptions.InternalServerErrorCustomException;
import co.cadshare.exceptions.NoCadshareDataFoundException;
import co.cadshare.exceptions.UnprocessableEntityException;
import co.cadshare.modelMgt.publications.boundary.*;
import co.cadshare.shared.core.user.User;
import co.cadshare.modelMgt.shared.core.Publication;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Description;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.*;

import java.util.List;


@RestController
@RequestMapping("/manufacturers/{manufacturer-id}/publications")
public class PublicationsController {
    private final CreatePublicationService createPublicationService;
    private final GetPublicationService getPublicationService;
    private final PublishPublicationService publishPublicationService;
	private final UpdatePublicationService updatePublicationService;
	private final DeletePublicationService deletePublicationService;
    private final AssignPublicationsService assignPublicationsService;

    @Autowired
    public PublicationsController(CreatePublicationService createPublicationService,
                                  GetPublicationService getPublicationService,
                                  PublishPublicationService publishPublicationService,
                                  UpdatePublicationService updatePublicationService,
                                  DeletePublicationService deletePublicationService,
                                  AssignPublicationsService assignPublicationsService){
        this.createPublicationService = createPublicationService;
        this.getPublicationService = getPublicationService;
        this.publishPublicationService = publishPublicationService;
	    this.updatePublicationService = updatePublicationService;
	    this.deletePublicationService = deletePublicationService;
        this.assignPublicationsService = assignPublicationsService;
    }

    @PostMapping(path = "/create-and-publish", consumes = "application/json", produces = "application/json")
    @PreAuthorize("hasRole('ROLE_MANUFACTURER')")
    @ResponseStatus(HttpStatus.OK)
    @Operation(summary = "Create and unpublish a Publication")
    public ResponseEntity<PostPublicationResponseDto> createAndPublishPublication(@PathVariable("manufacturer-id") int manufacturerId,
                                                                        @AuthenticationPrincipal User currentUser,
                                                                        @RequestBody PostCreateAndPublishRequestDto createAndPublish) throws Exception {

        int createdId = this.publishPublicationService.createAndAutoPublishPublication(currentUser, createAndPublish.getViewableId());
        return new ResponseEntity<>(new PostPublicationResponseDto(createdId), HttpStatus.OK);
    }

    @PostMapping(consumes = "application/json", produces = "application/json")
    @PreAuthorize("hasRole('ROLE_MANUFACTURER')")
    @ResponseStatus(HttpStatus.OK)
    @Operation(summary = "Create an unpublished Publication with a name")
    public ResponseEntity<PostPublicationResponseDto> createPublication(@PathVariable("manufacturer-id") int manufacturerId,
                                                                        @AuthenticationPrincipal User currentUser,
                                                                        @RequestBody PostPublicationRequestDto postPublication) {
        try {
		    Integer createdId = this.createPublicationService.create(currentUser, PublicationMapper.Instance.postRequestDtoToCommand(postPublication, currentUser));
		    return new ResponseEntity<>(new PostPublicationResponseDto(createdId), HttpStatus.OK);
	    } catch (NoCadshareDataFoundException e) {
		    throw new UnprocessableEntityException(e.getMessage());
	    }
	}

    @PutMapping(path = "/{publication-id}", consumes = "application/json")
    @PreAuthorize("hasRole('ROLE_MANUFACTURER')")
    @ResponseStatus(HttpStatus.OK)
    @Operation(summary = "Maintain the details of a Publication")
    public ResponseEntity<Void> updatePublication(@PathVariable("manufacturer-id") int manufacturerId,
                                         @PathVariable("publication-id") int publicationId,
                                         @AuthenticationPrincipal User currentUser,
                                         @RequestBody PutPublicationRequestDto putPublication) {
	    try {
		    UpdatePublicationCommand updatePublicationCommand = PublicationMapper.Instance.putRequestDtoToCommand(putPublication,
				    currentUser,
				    publicationId);
		    this.updatePublicationService.update(currentUser, updatePublicationCommand);
		    return new ResponseEntity<>(HttpStatus.OK);
	    } catch (NoCadshareDataFoundException e) {
		    throw new UnprocessableEntityException(e.getMessage());
	    } catch (Exception e) {
		    throw new InternalServerErrorCustomException(e.getMessage());
	    }
    }

    @DeleteMapping(path = "/{publication-id}")
    @CanAccessManufacturer
    @IsManufacturer
    @ResponseStatus(HttpStatus.OK)
    @Operation(summary = "Delete a Publication")
    public ResponseEntity<Void> deletePublication(@PathVariable("manufacturer-id") int manufacturerId,
                                            @PathVariable("publication-id") int publicationId,
                                            @AuthenticationPrincipal User currentUser) throws Exception {
        this.deletePublicationService.delete(currentUser, publicationId);
		return new ResponseEntity<>(HttpStatus.OK);
    }

	@PostMapping(path = "/{publication-id}/publish")
	@CanAccessManufacturer
	@IsManufacturer
	@ResponseStatus(HttpStatus.OK)
	@Operation(summary = "Publish a Publication")
	public ResponseEntity<Void> publishPublication(@PathVariable("manufacturer-id") int manufacturerId,
	                                              @PathVariable("publication-id") int publicationId,
	                                              @AuthenticationPrincipal User currentUser) throws Exception {
		this.publishPublicationService.publish(currentUser, publicationId);
		return new ResponseEntity<>(HttpStatus.OK);
	}

	@PostMapping(path = "/{publication-id}/unpublish")
	@CanAccessManufacturer
	@IsManufacturer
	@ResponseStatus(HttpStatus.OK)
	@Operation(summary = "Unpublish a Publication")
	public ResponseEntity<Void> unpublishPublication(@PathVariable("manufacturer-id") int manufacturerId,
	                                               @PathVariable("publication-id") int publicationId,
	                                               @AuthenticationPrincipal User currentUser) throws Exception {
		this.publishPublicationService.unpublish(currentUser, publicationId);
		return new ResponseEntity<>(HttpStatus.OK);
	}

    @GetMapping(path = "/{publication-id}", produces = "application/json")
    @CanAccessManufacturer
    @IsManufacturer
    @ResponseStatus(HttpStatus.OK)
    @Operation(summary = "Get the details of a specific Publication")
    public ResponseEntity<GetPublicationResponseDto> getPublication(@PathVariable("manufacturer-id") int manufacturerId,
                                                                    @PathVariable("publication-id") int publicationId,
                                                                    @AuthenticationPrincipal User currentUser) {

        Publication publication = this.getPublicationService.get(publicationId);
        GetPublicationResponseDto getResponseDto = PublicationMapper.Instance.coreToDto(publication);
        return new ResponseEntity<>(getResponseDto, HttpStatus.OK);
    }

    @GetMapping(produces = "application/json")
	@CanAccessManufacturer
    @IsManufacturer
    @ResponseStatus(HttpStatus.OK)
    @Operation(summary = "Get the summary details of a list of Publications")
    public ResponseEntity<GetPublicationsListResponseDto> getPublications(@PathVariable("manufacturer-id") int manufacturerId,
                                                                          @AuthenticationPrincipal User currentUser) {
        List<Publication> publications = this.getPublicationService.getPublicationsForManufacturer(manufacturerId);
        List<GetPublicationListItemResponseDto> publicationDtos = PublicationMapper.Instance.coresToListItemDtos(publications);
        GetPublicationsListResponseDto response = new GetPublicationsListResponseDto() {{
            setPublications(publicationDtos);
        }};
        return new ResponseEntity<>(response, HttpStatus.OK);
    }

    @CanAccessManufacturer
    @IsManufacturer
    @PostMapping("/assign-to-purchaser")
    @Description("Assign publications to a purchaser")
    public HttpEntity<Void> assignPublications(@AuthenticationPrincipal User currentUser,
                                                        @PathVariable("manufacturer-id") int manufacturerId,
                                                        @RequestBody PostAssignPublicationsDto dto) throws Exception {

        AssignPublicationsCommand command = AssignPublicationsMapper.Instance.dtoToCommand(dto);
        assignPublicationsService.assignPublicationsToPurchaser(currentUser, command);
        return new ResponseEntity<>(HttpStatus.OK);
    }
}
