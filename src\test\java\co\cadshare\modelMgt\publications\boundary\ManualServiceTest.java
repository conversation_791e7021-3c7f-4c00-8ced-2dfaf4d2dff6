package co.cadshare.modelMgt.publications.boundary;

import co.cadshare.modelMgt.publications.boundary.ManualCommandPort;
import co.cadshare.modelMgt.publications.boundary.ManualQueryPort;
import co.cadshare.modelMgt.publications.boundary.ManualService;
import co.cadshare.shared.core.manufacturer.subentity.ManufacturerSubEntity;
import co.cadshare.masterParts.adapters.database.MasterPartExtensionsDao;
import co.cadshare.modelMgt.ranges.adapters.database.RangeDao;
import co.cadshare.shared.boundary.ManufacturerSubEntityQueryPort;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

import org.mockito.MockitoAnnotations;

import java.util.ArrayList;

public class ManualServiceTest {

    @Mock
    private ManualQueryPort manualQuery;
    @Mock
    private ManualCommandPort manualCommand;
    @Mock
    private RangeDao rangeDao;
    @Mock
    private MasterPartExtensionsDao masterPartExtensionsDao;
    @Mock
    private ManufacturerSubEntityQueryPort manufacturerSubEntityQueryPort;
    private ManualService out;

    private ArrayList<Integer> dealerWithCustomersIds = new ArrayList<Integer>(){{
        add(1);
        add(2);
        add(3);
        add(4);
    }};


    private ArrayList<Integer> singleDealerIdList = new ArrayList<Integer>(){{add(1);}};
    private ArrayList<Integer> differentSingleDealerIdList = new ArrayList<Integer>(){{add(5);}};
    private ArrayList<Integer> multipleNewDealersIdList = new ArrayList<Integer>(){{ add(5); add(6);}};
    private ArrayList<Integer> noDealersIdList = new ArrayList<Integer>();
    private ArrayList<ManufacturerSubEntity> returnedDealerWithNoCustomers = new ArrayList<ManufacturerSubEntity>(){{
        add(new ManufacturerSubEntity(){{
            setManufacturerSubEntityId(1);
            setManufacturerSubEntityType(ManufacturerSubEntityType.DEALER);
        }});
    }};

    private ArrayList<ManufacturerSubEntity> returnedDealerWithCustomers = new ArrayList<ManufacturerSubEntity>() {{
        add(new ManufacturerSubEntity(){{
            setManufacturerSubEntityId(1);
            setManufacturerSubEntityType(ManufacturerSubEntityType.DEALER);
        }});
        add(new ManufacturerSubEntity(){{
            setManufacturerSubEntityId(2);
            setParentSubEntityId(1);
            setManufacturerSubEntityType(ManufacturerSubEntityType.CUSTOMER);
        }});
        add(new ManufacturerSubEntity(){{
            setManufacturerSubEntityId(3);
            setParentSubEntityId(1);
            setManufacturerSubEntityType(ManufacturerSubEntityType.CUSTOMER);
        }});
        add(new ManufacturerSubEntity(){{
            setManufacturerSubEntityId(4);
            setParentSubEntityId(1);
            setManufacturerSubEntityType(ManufacturerSubEntityType.CUSTOMER);
        }});
    }};

    private ArrayList<ManufacturerSubEntity> returnedMultipleDealersOneWithCustomers = new ArrayList<>();

    private ArrayList<ManufacturerSubEntity> returnedNoDealers = new ArrayList<>();

    @Before
    public void Before() {
        MockitoAnnotations.initMocks(this);
        out = new ManualService(manualQuery,
                manualCommand,
                rangeDao,
                masterPartExtensionsDao,
                manufacturerSubEntityQueryPort);

        returnedMultipleDealersOneWithCustomers.addAll(returnedDealerWithCustomers);
        returnedMultipleDealersOneWithCustomers.add(new ManufacturerSubEntity(){{
            setManufacturerSubEntityId(5);
            setManufacturerSubEntityType(ManufacturerSubEntityType.DEALER);
        }});
    }

    @Test
    public void NoChangeNoUpdates() {
        when(manufacturerSubEntityQueryPort.getManufacturerSubEntitiesForManual(1)).thenReturn(returnedDealerWithNoCustomers);
        out.assignManualToManufacturerSubEntities(1, singleDealerIdList);
        verify(manualCommand, never()).clearMappingsForManual(anyInt(), any());
        verify(manualCommand, never()).assignManualToManufacturerSubEntities(anyInt(), any());
    }

    @Test
    public void AssignToAdditionalPurchasers() {
        when(manufacturerSubEntityQueryPort.getManufacturerSubEntitiesForManual(1)).thenReturn(returnedNoDealers);
        out.assignManualToManufacturerSubEntities(1, differentSingleDealerIdList);
        verify(manualCommand, never()).clearMappingsForManual(anyInt(), any());
        verify(manualCommand, times(1)).assignManualToManufacturerSubEntities(1, differentSingleDealerIdList);
    }

    @Test
    public void UnassignFromExistingPurchasers() {
        when(manufacturerSubEntityQueryPort.getManufacturerSubEntitiesForManual(1)).thenReturn(returnedDealerWithCustomers);
        out.assignManualToManufacturerSubEntities(1, noDealersIdList);
        verify(manualCommand, times(1)).clearMappingsForManual(1, dealerWithCustomersIds);
        verify(manualCommand, never()).assignManualToManufacturerSubEntities(anyInt(), any());
    }

    @Test
    public void AssignAndUnassignToAndFromPurchasers() {
        when(manufacturerSubEntityQueryPort.getManufacturerSubEntitiesForManual(1)).thenReturn(returnedDealerWithCustomers);
        out.assignManualToManufacturerSubEntities(1, multipleNewDealersIdList);
        verify(manualCommand, times(1)).clearMappingsForManual(1, dealerWithCustomersIds);
        verify(manualCommand, times(1)).assignManualToManufacturerSubEntities(1, multipleNewDealersIdList);
    }
}
