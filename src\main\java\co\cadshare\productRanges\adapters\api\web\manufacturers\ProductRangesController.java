package co.cadshare.productRanges.adapters.api.web.manufacturers;

import co.cadshare.shared.core.user.User;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;

@RestController
@RequestMapping("/manufacturers/{manufacturer-id}/product-ranges")
public class ProductRangesController {

    @GetMapping(produces = "application/json")
    @PreAuthorize("hasRole('ROLE_SUPER_USER') or hasRole('ROLE_MANUFACTURER') and #currentUser.manufacturerId == manufacturerId")
    @ResponseStatus(HttpStatus.OK)
    @Operation(summary = "Get the summary details of the list of all Product Ranges belonging to the Manufacturer")
    public ResponseEntity<GetProductRangeListResponseDto> getProductRanges(@PathVariable("manufacturer-id") int manufacturerId,
                                                                          @AuthenticationPrincipal User currentUser) {

        List<ProductRangeListItemDto> productRanges = new ArrayList<>();
        for (Integer i = 1; i < manufacturerId  + 1; i++) {
            Integer finalI = i;
            ProductRangeListItemDto productRange = new ProductRangeListItemDto() {{
                setId(finalI);
                setName("Sample Product Range Name".concat(finalI.toString()));
            }};
            productRanges.add(productRange);
        }
        GetProductRangeListResponseDto response = new GetProductRangeListResponseDto()
        {{ setProductRanges(productRanges);}};

        return new ResponseEntity<>(response, HttpStatus.OK);
    }
}
