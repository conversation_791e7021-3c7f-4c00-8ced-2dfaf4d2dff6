package co.cadshare.masterKits.adapters.api.web;

import co.cadshare.masterKits.core.MasterKitPriceSearchResult;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface MasterKitPriceSearchResultMapper {

    MasterKitPriceSearchResultMapper Instance = Mappers.getMapper(MasterKitPriceSearchResultMapper.class);

    @Mapping(source="currencySymbol", target="currencyIdentifier")
    MasterKitPriceSearchResponseDto coreToDto(MasterKitPriceSearchResult core);
    List<MasterKitPriceSearchResponseDto> coresToDtos(List<MasterKitPriceSearchResult> core);
}
