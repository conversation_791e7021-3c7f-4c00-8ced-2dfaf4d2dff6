package co.cadshare.preauth;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.fail;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;

import co.cadshare.oauth.domainmodel.OAuthUser;
import co.cadshare.shared.core.user.User;
import co.cadshare.oauth.services.CadshareUserDetailsService;
import co.cadshare.users.adapters.database.UserDetailsDao;
import com.flextrade.jfixture.annotations.Fixture;
import com.flextrade.jfixture.rules.FixtureRule;
import org.junit.Rule;
import org.junit.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.security.oauth2.common.DefaultOAuth2AccessToken;
import org.springframework.security.oauth2.common.OAuth2AccessToken;
import org.springframework.security.oauth2.provider.OAuth2Authentication;
import org.springframework.security.oauth2.provider.token.DefaultTokenServices;
import org.springframework.security.oauth2.provider.token.TokenEnhancer;

public class CadshareTokenCreatorTest {

    private CadshareTokenCreator cadshareTokenCreator;

    @Mock
    private UserDetailsDao userDetailsDao;

    @Mock
    private CadshareUserDetailsService userDetailsService;

    @Mock
    private DefaultTokenServices defaultTokenServices;

    @Mock
    private TokenEnhancer tokenEnhancer;

    @Fixture
    private OAuthUser oAuthUserFixture;

    private DefaultOAuth2AccessToken oAuth2AccessTokenFixture;
    private DefaultOAuth2AccessToken enhancedOAuth2AccessTokenFixture;

    @Rule
    public FixtureRule fr = FixtureRule.initFixtures();


    public CadshareTokenCreatorTest() {
        MockitoAnnotations.initMocks(this);
        cadshareTokenCreator = new CadshareTokenCreator(userDetailsDao, userDetailsService, defaultTokenServices, tokenEnhancer);
        oAuth2AccessTokenFixture = new DefaultOAuth2AccessToken("TestToken");
        enhancedOAuth2AccessTokenFixture = new DefaultOAuth2AccessToken("EnhancedTestToken");
    }

    @Test
    public void createsToken() {
        String testUsername = "1";
        String testUserId = "1";

        User testUser = new User();
        testUser.setUserId(1);
        testUser.setEmailAddress(testUsername);

        when(userDetailsDao.findByEmailAddressAndManufacturer(testUsername, 1)).thenReturn(testUser);
        when(userDetailsService.loadUserByUsername(testUserId)).thenReturn(oAuthUserFixture);
        when(defaultTokenServices.createAccessToken(any(OAuth2Authentication.class))).thenReturn(oAuth2AccessTokenFixture);
        when(tokenEnhancer.enhance(eq(oAuth2AccessTokenFixture), any(OAuth2Authentication.class))).thenReturn(enhancedOAuth2AccessTokenFixture);

        OAuth2AccessToken result = cadshareTokenCreator.createTokenForUsername(testUserId, 1);

        assertEquals(enhancedOAuth2AccessTokenFixture,result);
    }

    @Test(expected = UsernameNotFoundException.class)
    public void throwsAppropriateExceptionOnUsernameNotFound() {
        String testUsername = "1";
        String testUserId = "1";

        User testUser = new User();
        testUser.setUserId(1);

        when(userDetailsDao.findByEmailAddressAndManufacturer(testUsername, 1)).thenReturn(testUser);
        when(userDetailsService.loadUserByUsername(testUserId)).thenThrow(new UsernameNotFoundException(String.format("Error looking up userId %s", testUserId)));

        OAuth2AccessToken result = cadshareTokenCreator.createTokenForUsername(testUserId, 1);

        fail("Should have thrown an exception.");
    }
}
