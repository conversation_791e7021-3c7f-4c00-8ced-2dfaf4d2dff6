package co.cadshare.masterKits.adapters.api.web;

import co.cadshare.masterParts.core.Translation;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

@Mapper
public interface TranslationMapper {

    TranslationMapper Instance = Mappers.getMapper(TranslationMapper.class);

    @Mapping(source="description", target="translation")
    @Mapping(source="displayText", target="languageDisplay")
    GetMasterKitTitleResponseDto coreToGetResponseDto(Translation core);
}
