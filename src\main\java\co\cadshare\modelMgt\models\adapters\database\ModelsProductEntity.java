package co.cadshare.modelMgt.models.adapters.database;

import lombok.Data;

import javax.persistence.*;
        import java.sql.Timestamp;

@Data
@Entity
@Table(name="machine")
public class ModelsProductEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name="machineid")
    private Integer id;

    private String name;

    @Column(name="rangeid")
    private int rangeId;

    @Column(name="archived")
    private boolean deleted;

    @Column(name="createdbyuserid")
    private Integer createdByUserId;

    @Column(name="modifiedbyuserid")
    private Integer modifiedByUserId;

    @Column(name="createddate")
    private Timestamp createdDate;

    @Column(name="modifieddate")
    private Timestamp modifiedDate;
}


