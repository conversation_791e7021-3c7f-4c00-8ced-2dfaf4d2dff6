package co.cadshare.inventory.core;

import lombok.Data;

import java.util.List;

@Data
public class BomUpload {

    ModelPartsFileHeaders headers;
    List<String []> dataRows;

    public BomUpload(List<String[]> input) throws UnparseableModelPartsFileException {
        if(input.isEmpty())
            throw new UnparseableModelPartsFileException("CSV must contain at least 2 columns");

        this.headers = new ModelPartsFileHeaders(input.get(0));
        this.dataRows = input.subList(1, input.size());
    }

    public boolean hasRowsToProcess() {
        return !this.dataRows.isEmpty();
    }

    @Data
    private class ModelPartsFileHeaders {
        public ModelPartsFileHeaders(String[] headers) throws UnparseableModelPartsFileException {
            if (headers.length < 2)
                throw new UnparseableModelPartsFileException("CSV must contain at least 2 columns");
        }
    }

    public class UnparseableModelPartsFileException extends Exception {
        public UnparseableModelPartsFileException(String message){
            super(message);
        }
    }


    public void setModelParts(List<ModelPart> modelParts) {
    }
}
