package co.cadshare.publications.boundary;

import co.cadshare.shared.core.user.User;
import co.cadshare.shared.boundary.CommandPort;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import co.cadshare.publications.core.Publication;

@Service
public class CreatePublicationService {

    private final CommandPort<Publication, Integer> publicationCommandPort;

    @Autowired
    public CreatePublicationService(CommandPort<Publication, Integer> publicationCommandPort) {
        this.publicationCommandPort = publicationCommandPort;
    }

    public Integer create(User user, Publication publication){
        return this.publicationCommandPort.create(user, publication);
    }
}
