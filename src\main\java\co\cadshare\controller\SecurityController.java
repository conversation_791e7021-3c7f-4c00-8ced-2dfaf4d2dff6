package co.cadshare.controller;

import java.sql.Timestamp;
import java.util.Calendar;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import co.cadshare.domainmodel.security.ManufacturerSecurity;
import co.cadshare.domainmodel.security.Watermark;
import co.cadshare.shared.core.user.User;
import co.cadshare.services.SecurityService;
import co.cadshare.users.boundary.UsersService;

@Controller
@RequestMapping("/security")
public class SecurityController {

  @Autowired
  private SecurityService securityService;

  @Autowired
  private UsersService usersService;

  private final Logger log = LoggerFactory.getLogger(getClass());

  @PreAuthorize("hasRole('ROLE_MANUFACTURER') and #currentUser.manufacturerId == #manufacturerId")
  @RequestMapping(value = "/manufacturer/{manufacturerId}", method = RequestMethod.GET)
  public HttpEntity<ManufacturerSecurity> getSecuritySettingsForManufacturer(@AuthenticationPrincipal User currentUser, @PathVariable int manufacturerId) throws Exception {

    log.info("ACCESS: User [{}], getSecuritySettingsForManufacturer, manufacturer [{}]", currentUser, manufacturerId);

    ManufacturerSecurity permissions = securityService.getManufacturerPermissions(manufacturerId);

    return new ResponseEntity<ManufacturerSecurity>(permissions, HttpStatus.OK);
  }

  @PreAuthorize("hasRole('ROLE_MANUFACTURER') and #currentUser.manufacturerId == #manufacturerId")
  @RequestMapping(value = "/manufacturer/{manufacturerId}", method = RequestMethod.POST)
  public HttpEntity<Integer> upsertSecuritySettingsForManufacturer(@AuthenticationPrincipal User currentUser, @PathVariable int manufacturerId, @RequestBody ManufacturerSecurity permissions) throws Exception {

    log.info("ACCESS: User [{}], updateSecuritySettingsForManufacturer, manufacturer [{}]", currentUser.accessDetails(), manufacturerId);

    Timestamp now = new java.sql.Timestamp(Calendar.getInstance().getTime().getTime());
    permissions.setModifiedByUserId(currentUser.getUserId());
    permissions.setModifiedDate(now);

    int key = securityService.upsertManufacturerPermissions(manufacturerId, permissions);

    return new ResponseEntity<>(key, HttpStatus.OK);
  }

  @RequestMapping(value = "/user/{userId}/lock", method = RequestMethod.GET)
  public HttpEntity<Boolean> lockUser(@PathVariable int userId) throws Exception {
    boolean lockable = usersService.lockUser(userId);

    return new ResponseEntity<Boolean>(lockable, HttpStatus.OK);
  }

  @RequestMapping(value = "/user/mfa/{authCode}", method = RequestMethod.GET)
  public HttpEntity<Boolean> getSecuritySettingsForManufacturer(@AuthenticationPrincipal User currentUser, @PathVariable String authCode) throws Exception {

    boolean valid = usersService.verifyAuthCode(currentUser.getUserId(), authCode);

    return new ResponseEntity<Boolean>(valid, HttpStatus.OK);
  }

  @RequestMapping(value = "/user/mfa/{userId}/{authCode}", method = RequestMethod.GET)
  public HttpEntity<Boolean> getSecuritySettingsForManufacturer(@PathVariable int userId, @PathVariable String authCode) throws Exception {

    boolean valid = usersService.verifyAuthCode(userId, authCode);

    return new ResponseEntity<Boolean>(valid, HttpStatus.OK);
  }

  @PreAuthorize("hasRole('ROLE_MANUFACTURER') and #currentUser.manufacturerId == #manufacturerId")
  @RequestMapping(value = "/manufacturer/{manufacturerId}/watermark", method = RequestMethod.POST)
  public HttpEntity<Integer> upsertManufacturerWatermark(@AuthenticationPrincipal User currentUser, @PathVariable int manufacturerId, @RequestBody Watermark watermark) throws Exception {

    int key = securityService.upsertManufacturerWatermark(manufacturerId, watermark, currentUser.getUserId());

    return new ResponseEntity<Integer>(key, HttpStatus.OK);
  }

  @PreAuthorize("hasRole('ROLE_MANUFACTURER') and #currentUser.manufacturerId == #manufacturerId")
  @RequestMapping(value = "/manufacturer/{manufacturerId}/watermark", method = RequestMethod.GET)
  public HttpEntity<Watermark> getManufacturerWatermark(@AuthenticationPrincipal User currentUser, @PathVariable int manufacturerId) throws Exception {

    Watermark watermark = securityService.getManufacturerWatermark(manufacturerId);

    return new ResponseEntity<Watermark>(watermark, HttpStatus.OK);
  }

  @PreAuthorize("hasRole('ROLE_MANUFACTURER_SUB_ENTITY_DEALER') or hasRole('ROLE_MANUFACTURER_SUB_ENTITY_DEALER_PLUS') or hasRole('ROLE_MANUFACTURER_SUB_ENTITY_CUSTOMER') and #currentUser.manufacturerSubEntityId == #manufacturerSubEntityId")
  @RequestMapping(value = "/manufacturerSubEntity/{manufacturerSubEntityId}/watermark", method = RequestMethod.GET)
  public HttpEntity<Watermark> getManufacturerSubEntityWatermark(@AuthenticationPrincipal User currentUser, @PathVariable int manufacturerSubEntityId) throws Exception {

    Watermark watermark = securityService.getManufacturerSubEntityWatermark(manufacturerSubEntityId);

    return new ResponseEntity<Watermark>(watermark, HttpStatus.OK);
  }
}
