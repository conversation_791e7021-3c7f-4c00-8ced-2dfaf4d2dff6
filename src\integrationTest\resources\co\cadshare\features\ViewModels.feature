Feature: View Models in Model Viewer

  Scenario Outline: View Part Details as Manufacturer
    Given I am a Manufacturer with <NAME_EMAIL>
    When I identify a <selectedPart> in a model
    Then I can see the details of <selectedPart>
    Examples:
      | selectedPart |
      | Cat-part-1   |

  Scenario Outline: View Part Details as Manufacturer on behalf of Dealer
    Given I am a Manufacturer with <NAME_EMAIL>
    When I identify a <selectedPart> in a model
    Then I can see details of <selectedPart> on behalf of <dealerUserId>
    Examples:
      | selectedPart | dealerUserId |
      | Cat-part-1   | 2            |

  Scenario Outline: Cannot View Part Details as Manufacturer on behalf of invalid Dealer
    Given I am a Manufacturer with <NAME_EMAIL>
    When I identify a <selectedPart> in a model
    Then I cannot see details of <selectedPart> on behalf of <dealerUserId>
    Examples:
      | selectedPart | dealerUserId |
      | Cat-part-1   | 5            |