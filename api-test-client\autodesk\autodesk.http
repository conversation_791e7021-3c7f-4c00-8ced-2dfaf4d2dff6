@autodeskModelUrn="dXJuOmFkc2sub2JqZWN0czpvcy5vYmplY3Q6d2ViYXBwLmNhZHNoYXJlLmNvLzE2OTgzMzI0MTAyMzIxNzAtMDAwMDM2MDk3OS56aXA="

// Ba<PERSON> Auth Encoded is base64 encoding of client_id:secret from env variables
# @name autodeskToken
POST {{AUTODESK_HOST_URL}}/{{AUTODESK_AUTH_API}}
Content-Type: application/x-www-form-urlencoded
Accept: application/json
Authorization: {{$dotenv BASIC_AUTH_BASE64_ENCODED}}

grant_type=client_credentials&scope=data:read

###

//Get Metadata
# @name guid

GET {{AUTODESK_HOST_URL}}/{{AUTODESK_DERIVATIVES_API}}/designdata/{{autodeskModelUrn}}/metadata
Content-Type: application/json
Authorization: Bearer {{autodeskToken.response.body.access_token}}


###

//Get Modelview Metadata

GET {{AUTODESK_HOST_URL}}/{{AUTODESK_DERIVATIVES_API}}//designdata/{{autodeskModelUrn}}/metadata/{{guid.response.body.data.metadata[0].guid}}
Authorization: Bearer {{autodeskToken.response.body.access_token}}

###

//Get Modelview Metadata properties

GET {{AUTODESK_HOST_URL}}/{{AUTODESK_DERIVATIVES_API}}//designdata/{{autodeskModelUrn}}/metadata/{{guid.response.body.data.metadata[0].guid}}/properties?forceget=true
Content-Type: application/json
Authorization: Bearer {{autodeskToken.response.body.access_token}}