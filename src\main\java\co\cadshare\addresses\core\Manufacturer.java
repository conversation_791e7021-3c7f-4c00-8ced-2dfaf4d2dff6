package co.cadshare.addresses.core;

import co.cadshare.utils.ObjectUtilsExtension;
import lombok.Data;
import lombok.experimental.ExtensionMethod;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Data
@ExtensionMethod(ObjectUtilsExtension.class)
public class Manufacturer extends co.cadshare.shared.core.manufacturer.Manufacturer {

    private List<ExternalPurchaser> externalPurchasers;

    public Manufacturer() {
        this.externalPurchasers = new ArrayList<>();
    }

    public void addPurchaser(ExternalPurchaser purchaser) {
        externalPurchasers.add(purchaser);
    }

    private Optional<ExternalPurchaser> findPurchaserForAddress(ExternalAddress address) {
        Optional<ExternalPurchaser> relevantPurchaser = externalPurchasers.stream()
                .filter(purchaser -> address.appliesTo(purchaser))
                .findFirst();
        return relevantPurchaser;
    }

    public List<ExternalAddress> archiveAllExternalAddresses() {
        List<ExternalAddress> archivedAddresses = new ArrayList<>();
        for (ExternalPurchaser purchaser : externalPurchasers)
            archivedAddresses.addAll(purchaser.deleteAllExistingExternalAddresses());
        Stream<ExternalAddress> sorted = archivedAddresses.stream().sorted(Comparator.comparingInt(ExternalAddress::getId));
        return sorted.collect(Collectors.toList());
    }
}
