
Final version:

-- Reference: Customer_ManufacturerSubEntity [Table: Customer]
-- Recreation cause: Column order changed
ALTER TABLE "customer"
    DROP CONSTRAINT "customer_manufacturersubentity";

-- Reference: Orders_Customer [Table: Orders]
-- Recreation cause: Column order changed
ALTER TABLE "orders"
    DROP CONSTRAINT "orders_customer";

-- Reference: CustomerUsers_Customer [Table: CustomerUsers]
-- Recreation cause: Column order changed
ALTER TABLE "customerusers"
    DROP CONSTRAINT "customerusers_customer";

-- Reference: CustomerUsers_User [Table: CustomerUsers]
-- Recreation cause: Column order changed
ALTER TABLE "customerusers"
    DROP CONSTRAINT "customerusers_user";

-- Reference: CustomerSerialNumberMap_SerialNumber [Table: CustomerSerialNumberMap]
-- Recreation cause: Column order changed
ALTER TABLE "customerserialnumbermap"
    DROP CONSTRAINT "customerserialnumbermap_serialnumber";

-- Reference: CustomerSerialNumberMap_Customer [Table: CustomerSerialNumberMap]
-- Recreation cause: Column order changed
ALTER TABLE "customerserialnumbermap"
    DROP CONSTRAINT "customerserialnumbermap_customer";

ALTER TABLE public.customer RENAME TO company;
ALTER TABLE public.CustomerSerialNumberMap RENAME TO CompanySerialNumberMap;
ALTER TABLE public.CustomerUsers RENAME TO CompanyUsers;

ALTER TABLE company DROP CONSTRAINT "customer_pk";
ALTER TABLE company RENAME customerid TO companyid;
ALTER TABLE company ADD CONSTRAINT "company_pk" PRIMARY KEY ("companyid");

ALTER TABLE orders RENAME customerid TO companyid;
ALTER TABLE companyusers RENAME customerid TO companyid;
ALTER TABLE companyserialnumbermap RENAME customerid TO companyid;

ALTER TABLE "company" ADD CONSTRAINT "company_manufacturersubentity"
    FOREIGN KEY ("manufacturersubentityid")
    REFERENCES "manufacturersubentity" ("manufacturersubentityid")  
    NOT DEFERRABLE 
    INITIALLY IMMEDIATE
;

-- Reference: Orders_Company [Table: Orders]
ALTER TABLE "orders" ADD CONSTRAINT "orders_company"
    FOREIGN KEY ("companyid")
    REFERENCES "company" ("companyid")  
    NOT DEFERRABLE 
    INITIALLY IMMEDIATE
;

-- Reference: CompanyUsers_Company [Table: CompanyUsers]
ALTER TABLE "companyusers" ADD CONSTRAINT "companyusers_company"
    FOREIGN KEY ("companyid")
    REFERENCES "company" ("companyid")  
    NOT DEFERRABLE 
    INITIALLY IMMEDIATE
;

-- Reference: CompanyUsers_User [Table: CompanyUsers]
ALTER TABLE "companyusers" ADD CONSTRAINT "companyusers_user"
    FOREIGN KEY ("userid")
    REFERENCES "users" ("userid")  
    NOT DEFERRABLE 
    INITIALLY IMMEDIATE
;

-- Reference: CompanySerialNumberMap_SerialNumber [Table: CompanySerialNumberMap]
ALTER TABLE "companyserialnumbermap" ADD CONSTRAINT "companyserialnumbermap_serialnumber"
    FOREIGN KEY ("serialnumberid")
    REFERENCES "serialnumber" ("serialnumberid")  
    NOT DEFERRABLE 
    INITIALLY IMMEDIATE
;

-- Reference: CompanySerialNumberMap_Company [Table: CompanySerialNumberMap]
ALTER TABLE "companyserialnumbermap" ADD CONSTRAINT "companyserialnumbermap_company"
    FOREIGN KEY ("companyid")
    REFERENCES "company" ("companyid")  
    NOT DEFERRABLE 
    INITIALLY IMMEDIATE
;

-- Column: ManufacturerSubEntityId
-- [#2] Adding NOT NULL column (no DEFAULT). Fill with data.
ALTER TABLE "Manufacturer"
    ADD "ManufacturerSubEntityId" int  NOT NULL;

-- Reference: Manufacturer_ManufacturerSubEntity [Table: Manufacturer]
ALTER TABLE "manufacturer" ADD CONSTRAINT "manufacturer_manufacturersubentity"
    FOREIGN KEY ("manufacturersubentityid")
    REFERENCES "manufacturersubentity" ("manufacturersubentityid")  
    NOT DEFERRABLE 
    INITIALLY IMMEDIATE
;

UPDATE "usertype" SET "usertypename" = 'COMPANY' WHERE usertypeid = 2

ALTER TABLE "users" ALTER COLUMN "password" DROP NOT NULL;

ALTER TABLE "serialnumber" ALTER COLUMN "serialNumber" DROP NOT NULL;

ALTER TABLE "manufacturersubentity" ADD COLUMN "companyid" INT;