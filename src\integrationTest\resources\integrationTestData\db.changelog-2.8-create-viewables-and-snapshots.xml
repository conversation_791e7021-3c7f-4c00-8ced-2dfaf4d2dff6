<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                   http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.0.xsd
">

    <changeSet author="AndyB" id="2.8-integration-test-data-create-viewables-and-snapshots-1">
        <sql stripComments="true">
            INSERT INTO public.viewable(id, modelid, createddate, createdbyuserid, modifieddate, modifiedbyuserid, linedrawingenabled, viewlocked, type, edgingenabled, background)
            VALUES (1, 2, '2021-11-23 11:54:11.983', 1, '2021-11-23 11:54:11.983', 1, false, false, 'CORE', false, 'fgfd');
            INSERT INTO public.statedetail(
            id, viewableid, stateid, parentid, state, sequence, statename, notes, imgurl, createdbyuserid, createddate, modifiedbyuserid, modifieddate, explodeaxis)
            VALUES (
            1,                          --id
            1,                          --viewableid
            'ROOT',                     --stateid
            null,                        --parentid
            null,                       --state
            null,                       --sequence
            'statename',                --statename
            null,                       --notes
            'https://s3.amazonaws.com/cadshare-test-bucket/manufacturer/4/13af-ea7d-e469-6c35-216e-914c-8a1e-7880.png', --imgurl
            1,                          --createdbyuserid
            '2021-11-23 11:54:11.983',  --createddate
            1,                          --modifiedbyuserid
            '2021-11-23 11:54:11.983',  --modifieddate
            'RADIAL');

        </sql>
    </changeSet>

</databaseChangeLog>
