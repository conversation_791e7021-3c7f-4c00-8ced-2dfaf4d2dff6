package co.cadshare.masterKits.adapters.database;

import lombok.Data;
import javax.persistence.*;
import java.util.List;

@Data
@Entity
@Table(name="mp_kit")
public class MkMasterKitEntity {

    @Id
    @GeneratedValue (strategy = GenerationType.IDENTITY)
    @Column(name="id")
    private Integer id;
    private String title;
    private String description;
    private boolean deleted;

    @OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY, orphanRemoval = true)
    @JoinColumn(name="kitid", nullable = false)
    private List<MkMasterPartKitMapEntity> masterParts;


}

