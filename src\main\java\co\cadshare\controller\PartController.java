package co.cadshare.controller;

import co.cadshare.aspects.access.languages.CanUseLanguage;
import co.cadshare.shared.core.Language;

import java.util.List;

import co.cadshare.utils.ObjectUtilsExtension;
import lombok.experimental.ExtensionMethod;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import co.cadshare.domainmodel.part.Part;
import co.cadshare.shared.core.user.User;
import co.cadshare.services.PartService;
import org.springframework.web.bind.annotation.RequestParam;

@ExtensionMethod(ObjectUtilsExtension.class)
@Controller
@RequestMapping("/part")
public class PartController {

  private final PartService partService;

  @Autowired
  public PartController (PartService partService) {
    this.partService = partService;
  }

  private static final Logger logger = LoggerFactory.getLogger(PartController.class);

  @RequestMapping(method = RequestMethod.POST, consumes = "application/json")
  public HttpEntity<Integer> createPart(@AuthenticationPrincipal User currentUser, @RequestBody Part part) {

    logger.info("ACCESS: User [{}], createPart, part [{}]", currentUser.accessDetails(), part.toString());
    part.setCreatedByUserId(currentUser.getUserId());
    part.setModifiedByUserId(currentUser.getUserId());
    int partId = partService.createPart(part);

    logger.info("Part with id [{}] created", partId);
    return new ResponseEntity<>(partId, HttpStatus.OK);
  }

  @RequestMapping(value = "/{partId}", method = RequestMethod.GET)
  @CanUseLanguage
  public HttpEntity<Part> getPart(@AuthenticationPrincipal User currentUser,
                                  @PathVariable int partId,
                                  @RequestParam(value = "language", required = false) Language language) throws Exception {

    logger.info("ACCESS: User [{}], getPart, partId [{}]", currentUser.accessDetails(), partId);
	Integer languageId = language != null ? currentUser.findLanguage(language).getLanguageId() : null;
    Part part = partService.getPart(partId, languageId, currentUser.obtainDefaultLanguage());
    return new ResponseEntity<>(part, HttpStatus.OK);
  }

  @RequestMapping(method = RequestMethod.PUT)
  public HttpEntity<List<Integer>> updatePart(@AuthenticationPrincipal User currentUser, @RequestBody List<Part> parts) {

    logger.info("ACCESS: User [{}], updateParts, number of parts to update [{}]", currentUser.accessDetails(), parts.size());

    List<Integer> partids = partService.updateParts(parts, currentUser);
    return new ResponseEntity<>(partids, HttpStatus.OK);
  }
}
