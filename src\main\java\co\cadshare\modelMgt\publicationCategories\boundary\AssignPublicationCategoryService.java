package co.cadshare.modelMgt.publicationCategories.boundary;

import co.cadshare.aspects.logging.Log;
import co.cadshare.modelMgt.publicationCategories.core.PublicationCategory;
import co.cadshare.modelMgt.publications.boundary.PublicationCommandPort;
import co.cadshare.modelMgt.shared.boundary.PublicationQueryPort;
import co.cadshare.modelMgt.shared.core.Publication;
import co.cadshare.modelMgt.shared.boundary.PurchasersQueryPort;
import co.cadshare.modelMgt.shared.core.Purchaser;
import co.cadshare.shared.core.user.User;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

@Service
public class AssignPublicationCategoryService {

    private final PublicationCategoryCommandPort publicationCategoryCommandPort;
	private final PublicationCategoryQueryPort publicationCategoryQueryPort;
	private final PurchasersQueryPort purchasersQueryPort;
	private final PublicationQueryPort publicationQueryPort;
	private final PublicationCommandPort publicationCommandPort;

    @Autowired
    public AssignPublicationCategoryService(PublicationCategoryCommandPort publicationCategoryCommandPort,
                                            PublicationCategoryQueryPort publicationCategoryQueryPort,
                                            PurchasersQueryPort purchasersQueryPort,
                                            PublicationQueryPort publicationQueryPort,
                                            PublicationCommandPort publicationCommandPort) {
        this.publicationCategoryCommandPort = publicationCategoryCommandPort;
	    this.publicationCategoryQueryPort = publicationCategoryQueryPort;
	    this.purchasersQueryPort = purchasersQueryPort;
	    this.publicationQueryPort = publicationQueryPort;
	    this.publicationCommandPort = publicationCommandPort;
    }

    @Log
    @Transactional
    public void assignPublicationCategoriesToPurchaser(User user, AssignPublicationCategoriesCommand command) throws Exception {
		Purchaser purchaser = purchasersQueryPort.getPurchaser(command.getPurchaserId());
		if(purchaser.isDealerPlus()) {
			List<PublicationCategory> currentPublicationCategories = publicationCategoryQueryPort.getAssignedPublicationCategoriesForPurchaser(command.getPurchaserId());
			List<Integer> publicationCategoriesToBeRemoved = new ArrayList<>();
			currentPublicationCategories.forEach(category -> {
				if (!command.getPublicationCategoryIds().contains(category.getId())) {
					publicationCategoriesToBeRemoved.add(category.getId());
				}
			});

			if (!publicationCategoriesToBeRemoved.isEmpty()) {
				List<Integer> publications = publicationQueryPort.getPublicationsForPublicationCategories(publicationCategoriesToBeRemoved);
				List<Integer> publicationsToBeUnassigned;
				if (publications != null && !publications.isEmpty()) {
					List<Publication> assignedPublications = publicationQueryPort.getPublicationsAssignedToPurchaser(command.getPurchaserId());
					//if publications are not assigned to purchasers directly, then add to unassign list
					List<Integer> toBeUnassigned = new ArrayList<>();
					publications.forEach(publication -> {
						if (assignedPublications.stream().noneMatch(ap -> ap.getId().equals(publication))) {
							toBeUnassigned.add(publication);
						}
					});
					publicationsToBeUnassigned = toBeUnassigned;
				} else {
					publicationsToBeUnassigned = publications;
				}
				if (!publicationsToBeUnassigned.isEmpty()) {
					publicationCommandPort.unassignPublicationsFromDealerPlusCustomers(publicationsToBeUnassigned, command.getPurchaserId());
				}
			}
		}
		publicationCategoryCommandPort.clearPublicationCategoriesForPurchaser(command.getPurchaserId());
		if (!command.getPublicationCategoryIds().isEmpty()) {
			publicationCategoryCommandPort.assignPublicationCategoriesToPurchaser(command.getPurchaserId(),
					command.getPublicationCategoryIds(),
					user);
		}
    }


}
