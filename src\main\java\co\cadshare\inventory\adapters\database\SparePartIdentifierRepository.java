package co.cadshare.inventory.adapters.database;

import java.util.HashMap;
import java.util.Map;
import java.util.Set;
import java.util.Map.Entry;

import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.stereotype.Repository;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Repository
public class SparePartIdentifierRepository {
    private NamedParameterJdbcTemplate namedParamJdbcTemplate;
    private final static String UPDATE_CRITICAL_PART = "UPDATE part SET criticalSparePart = :criticalSparePart WHERE partNumber = :partNumber";

    public SparePartIdentifierRepository(NamedParameterJdbcTemplate namedParamJdbcTemplate) {
        this.namedParamJdbcTemplate = namedParamJdbcTemplate;
    }

    public void updateCriticalSparePart(Map<String, Boolean> parts) {
        Map<String, Object>[] args = (Map<String, Object>[]) new HashMap[parts.size()];
        int i = 0;
        Set<Entry<String, Boolean>> entrySet = parts.entrySet();
        for (Map.Entry<String, Boolean> part : entrySet) {
            Map<String, Object> arg = new HashMap<>();
            arg.put("partNumber", part.getKey());
            arg.put("criticalSparePart", part.getValue());
            args[i] = arg;
            i++;
        }
        int[] status = namedParamJdbcTemplate.batchUpdate(UPDATE_CRITICAL_PART, args);
        i = 0;
        for(int s : status) {
            if (s != 0) {
                log.debug("Part with id " + args[i].get("partNumber") + " not found and could not be updated");
            } else {
                log.debug("Part with id " + args[i].get("partNumber") + " updated");
            }
            i++;
        }
    }
}
