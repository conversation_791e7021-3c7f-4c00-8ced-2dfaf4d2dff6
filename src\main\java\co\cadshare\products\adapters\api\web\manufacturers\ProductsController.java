package co.cadshare.products.adapters.api.web.manufacturers;

import co.cadshare.shared.core.user.User;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;

@RestController("ProductsInternalController")
@RequestMapping("/manufacturers/{manufacturer-id}/product-ranges/{product-range-id}/products")
public class ProductsController {

    @GetMapping(produces = "application/json")
    @PreAuthorize("hasRole('ROLE_SUPER_USER') or hasRole('ROLE_MANUFACTURER') and #currentUser.manufacturerId == manufacturerId")
    @ResponseStatus(HttpStatus.OK)
    @Operation(summary = "Get the summary details of the list of all Products belonging to the specified Product Range")
    public ResponseEntity<GetProductListResponseDto> getProducts(@PathVariable("manufacturer-id") int manufacturerId,
                                                                 @PathVariable("product-range-id") int productRangeId,
                                                                           @AuthenticationPrincipal User currentUser) {

        List<ProductListItemDto> products = new ArrayList<>();
        for (Integer i = 1; i < manufacturerId  + 1; i++) {
            Integer finalI = i;
            ProductListItemDto product = new ProductListItemDto() {{
                setId(finalI);
                setName("Sample Product Name".concat(finalI.toString()));
            }};
            products.add(product);
        }
        GetProductListResponseDto response = new GetProductListResponseDto()
        {{ setProducts(products);}};

        return new ResponseEntity<>(response, HttpStatus.OK);
    }
}
