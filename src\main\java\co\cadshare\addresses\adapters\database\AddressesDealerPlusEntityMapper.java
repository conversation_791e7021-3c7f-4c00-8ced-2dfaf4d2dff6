package co.cadshare.addresses.adapters.database;

import co.cadshare.addresses.core.ExternalDealerPlus;
import co.cadshare.addresses.core.ExternalPurchaser;
import co.cadshare.addresses.core.User;
import co.cadshare.utils.CycleAvoidingMappingContext;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper(uses = { ExternalAddressEntityMapper.class, AddressesUserEntityMapper.class})
public interface AddressesDealerPlusEntityMapper {

    AddressesDealerPlusEntityMapper Instance = Mappers.getMapper(AddressesDealerPlusEntityMapper.class);

    //CoreToEntity
    
    @Mapping(source="users", target="users", qualifiedByName="userCoreToEntity")
    AddressesDealerPlusEntity externalDealerPlusCoreToEntity(ExternalDealerPlus dealer);
    
    List<AddressesDealerPlusEntity> externalPurchaserCoresToDealerPlusEntities(List<ExternalPurchaser> dealers);

    
    //Entity To Core
    
    @Mapping(source="users", target="users", qualifiedByName="userEntityToCore")
    ExternalDealerPlus entityToExternalPurchaserCore(AddressesDealerPlusEntity dealer);

    List<ExternalDealerPlus> entitiesToExternalPurchaserCores(List<AddressesDealerPlusEntity> dealers);
    
    
    //Named   

    @Named("userEntityToCore")
    public static User userEntityToCore(AddressesUserEntity entity) {
        return AddressesUserEntityMapper.Instance.entityToCore(entity, new CycleAvoidingMappingContext());
    }

    @Named("userCoreToEntity")
    public static AddressesUserEntity userCoreToEntity(User core) {
        return AddressesUserEntityMapper.Instance.coreToEntity(core, new CycleAvoidingMappingContext());
    }

}
