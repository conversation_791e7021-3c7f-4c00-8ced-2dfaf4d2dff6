/*
 * Copyright 2016 Bell.
 */
package co.cadshare.inventory.boundary;

import co.cadshare.domainmodel.part.PartDetails;
import co.cadshare.inventory.core.*;
import co.cadshare.modelMgt.models.boundary.ModelService;
import co.cadshare.modelMgt.models.core.Model;
import co.cadshare.shared.boundary.S3StoragePort;
import co.cadshare.shared.core.manufacturer.ManufacturerProgress;
import co.cadshare.domainmodel.part.Part;

import java.io.IOException;
import java.util.List;

import co.cadshare.inventory.core.BomUpload;
import co.cadshare.shared.adapters.database.manufacturer.ManufacturerProgressDao;
import co.cadshare.shared.core.user.User;
import co.cadshare.utils.ObjectExtension;
import lombok.experimental.ExtensionMethod;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import co.cadshare.persistence.PartDao;

@Service
@ExtensionMethod(ObjectExtension.class)
public class PartsLoadService {

  private final ModelService modelService;
  private final PartDao partDao;
  private final ManufacturerProgressDao manufacturerProgressDao;
  private final S3StoragePort s3StorageClient;
  private static final Logger logger = LoggerFactory.getLogger(PartsLoadService.class);

  public PartsLoadService(ModelService modelService,
                          PartDao partDao,
                          ManufacturerProgressDao manufacturerProgressDao,
                          S3StoragePort s3StorageClient) {
    this.modelService = modelService;
    this.partDao = partDao;
    this.manufacturerProgressDao = manufacturerProgressDao;
    this.s3StorageClient = s3StorageClient;
  }

  @Async
  public void downloadPartDetailsForModel(User user, Integer modelId) {

    Model model = modelService.getModel(modelId);
    int manufacturerId = user.getManufacturerId();
    ManufacturerProgress progress = new ManufacturerBomDownloadProgress(manufacturerId, model);
    try {
      int progressId = manufacturerProgressDao.createManufacturerProgress(progress);
      progress.setId(progressId);
      List<Part> parts = partDao.getDistinctPartsForModelId(modelId);
      String bomCsv = BomBuilder.buildFromParts(parts);
      if(bomCsv.isNotNull()) {
        String url = s3StorageClient.uploadCSVForViewableBOM(bomCsv, manufacturerId);
        progress.setS3Url(url);
      }
      progress.complete();
    } catch (Exception exception) {
      progress.error();
      throw exception;
    } finally {
      manufacturerProgressDao.updateManufacturerProgress(progress);
    }
  }

  @Async
  public void uploadPartDetailsForModel(int modelId,
                                        BomUploadConfiguration uploadConfiguration,
                                        BomUpload bomUploadFile,
                                        User user) throws IOException {

    uploadConfiguration.validate();
    Model model = modelService.getModel(modelId);
    ManufacturerProgress progress = new ManufacturerBomUploadProgress(user.getManufacturerId(), model);
    try {
      int progressId = manufacturerProgressDao.createManufacturerProgress(progress);
      progress.setId(progressId);
      if (bomUploadFile.hasRowsToProcess()) {
        BomParser parser = new BomParser(modelId, uploadConfiguration, bomUploadFile);
        List<PartDetails> partsDetails = parser.parseBomToPartDetails();
        partDao.partDetailsUpdate(partsDetails);
      }
      progress.complete();
    } catch (Exception exception) {
      progress.error();
      logger.error("Error during upload of part details", exception);
      throw exception;
    } finally {
      manufacturerProgressDao.updateManufacturerProgress(progress);
    }
  }

  public List<String> getHeaderMappings(String file) throws Exception {
    return BomParser.parseBomHeader(file);
  }
}
