package co.cadshare.publications.adapters.api.web;

import co.cadshare.aspects.access.languages.CanUseLanguage;
import co.cadshare.shared.core.Language;
import co.cadshare.masterParts.boundary.MasterPartKitService;
import co.cadshare.publications.core.Manual;
import co.cadshare.masterParts.core.extensions.kit.Kit;
import co.cadshare.shared.core.user.User;
import co.cadshare.publications.boundary.ManualService;
import co.cadshare.services.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;

@Controller
@PreAuthorize("hasRole('ROLE_MANUFACTURER_SUB_ENTITY_DEALER_PLUS')")
@RequestMapping("/dealerplus/manual")
public class DealerManualController {

  private final ManualService manualService;
  private final MasterPartKitService masterPartKitService;
  private final PermissionsService permissionsService;

  @Autowired
  public DealerManualController(ManualService manualService,
                                MasterPartKitService masterPartKitService,
                                PermissionsService permissionsService) {
    this.manualService = manualService;
    this.masterPartKitService = masterPartKitService;
    this.permissionsService = permissionsService;
  }

  private static final Logger logger = LoggerFactory.getLogger(DealerManualController.class);

  @GetMapping("/{manualId}")
  public HttpEntity<Manual> getManual(@AuthenticationPrincipal User currentUser, @PathVariable int manualId) {

    logger.info("ACCESS: User [{}], dealerplus - getManual, manualId [{}]", currentUser.accessDetails(), manualId);

    Manual manual = manualService.getManual(manualId);
    return new ResponseEntity<>(manual, HttpStatus.OK);
  }

  @RequestMapping(value = "/{manualId}/assign", method = RequestMethod.PUT, consumes = "application/json")
  public HttpEntity<Integer> assignManufacturerSubEntityToManual(@AuthenticationPrincipal User currentUser,
                                                                  @PathVariable int manualId, @RequestBody ArrayList<Integer> manufacturerSubEntityId) {

    logger.info("ACCESS: User [{}], dealerplus - assignManufacturerSubEntityToManual, manualIds [{}]",
            currentUser.accessDetails(), manufacturerSubEntityId.toString());

    try {
      hasPermissionsForManual(currentUser, manualId);
    } catch (Exception ex) {
      return new ResponseEntity<>(null, HttpStatus.FORBIDDEN);
    }

    manualService.assignManualToManufacturerSubEntities(manualId, manufacturerSubEntityId);

    logger.info("Manual [{}] successfully assigned to manufacturerSubEntityId id [{}]",  manualId,
            manufacturerSubEntityId);
    return new ResponseEntity<>(HttpStatus.OK);
  }

	@GetMapping("/{manualId}/kits")
	@CanUseLanguage
	public HttpEntity<List<Kit>> getMasterPartKitsByManualId(@AuthenticationPrincipal User currentUser,
	                                                         @PathVariable int manualId,
	                                                         @RequestParam(value = "language", required = false) Language language) throws Exception {

		logger.info("ACCESS: User [{}], dealerplus - getMasterPartKitsByManualId, manual id [{}]", currentUser.accessDetails(), manualId);
	    Integer languageId = language != null ? currentUser.findLanguage(language).getLanguageId() : null;
		List<Kit> kitList = masterPartKitService.getKitsByManualId(manualId, languageId, currentUser.obtainDefaultLanguage(), null);
		return new ResponseEntity<>(kitList, HttpStatus.OK);
    }

	private void hasPermissionsForManual(User currentUser, Integer manualId) throws Exception {
		permissionsService.hasDealerPermissionsToManual(currentUser, manualId);
	}
}
