/*
 * Copyright 2016 Bell.
 */
package co.cadshare.modelMgt.ranges.adapters.database;

import java.sql.Timestamp;
import java.util.Calendar;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import co.cadshare.persistence.ManufacturerSubEntityDao;
import co.cadshare.persistence.dealer.DealerPlusPublicationDao;
import co.cadshare.modelMgt.publications.boundary.ManualQueryPort;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.namedparam.BeanPropertySqlParameterSource;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.jdbc.support.GeneratedKeyHolder;
import org.springframework.jdbc.support.KeyHolder;
import org.springframework.stereotype.Repository;

import co.cadshare.domainmodel.range.Range;

/**
 * TODO(dallanmc) Description of class.
 */
@Repository
public class RangeDao {

    private static final Logger logger = LoggerFactory.getLogger(RangeDao.class);


    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Autowired
    private ManualQueryPort manualDao;

    @Autowired
    private DealerPlusPublicationDao dealerManualDao;

    @Autowired
    private ManufacturerSubEntityDao manufacturerSubEntityDao;

    @Autowired
    private NamedParameterJdbcTemplate namedParamJdbcTemplate;

    private final static String CREATE_RANGE = "INSERT INTO range (name, description, manufacturerId, createdDate) "
            + "VALUES( :name, :description, :manufacturerId, :createdDate)";

    private final static String GET_RANGES_FOR_MANUFACTURER_ID = "SELECT * FROM range WHERE manufacturerId = :manufacturerId";

    public int createRange(Range range) {

        Timestamp now = new Timestamp(Calendar.getInstance().getTime().getTime());
        range.setCreatedDate(now);

        KeyHolder keyHolder = new GeneratedKeyHolder();

        BeanPropertySqlParameterSource namedParameters = new BeanPropertySqlParameterSource(range);
        int result = namedParamJdbcTemplate.update(CREATE_RANGE, namedParameters, keyHolder, new String[]{"rangeid"});
        return keyHolder.getKey().intValue();
    }

    public List<Range> getRangesForManufacturer(int manufacturerId) {
        Map<String, Object> parameters = new HashMap<String, Object>();
        parameters.put("manufacturerId", manufacturerId);

        List<Range> rangeList = (List<Range>) namedParamJdbcTemplate.query(GET_RANGES_FOR_MANUFACTURER_ID,
                parameters, new BeanPropertyRowMapper<Range>(Range.class));

        return rangeList;
    }

    private final static String GET_RANGE_FOR_MACHINE_ID = "SELECT r.* FROM range r INNER JOIN machine mac ON mac.rangeid = r.rangeid WHERE mac.machineid = :machineId";

    public Range getRangeForMachineId(int machineId) {
        Map<String, Object> parameters = new HashMap<String, Object>();
        parameters.put("machineId", machineId);

        Range range = namedParamJdbcTemplate.queryForObject(GET_RANGE_FOR_MACHINE_ID,
                parameters, new BeanPropertyRowMapper<Range>(Range.class));

        return range;
    }

    private final static String CHECK_IF_RANGE_NAME_IN_USE = "SELECT COUNT(*) FROM range WHERE manufacturerId = :manufacturerId AND LOWER(name) = LOWER(:name)";

    public boolean rangeNameAlreadyExistsForManufacturer(String name, int manufacturerId) {

        Map<String, Object> parameters = new HashMap<String, Object>();
        parameters.put("manufacturerId", manufacturerId);
        parameters.put("name", name);

        Integer count = namedParamJdbcTemplate.queryForObject(CHECK_IF_RANGE_NAME_IN_USE, parameters, Integer.class);

        boolean exists = false;
        if (count > 0) {
            exists = true;
        }

        return exists;
    }

    private static final String GET_ASSIGNED_RANGE_IDS_FROM_MANUFACTURER_SUB_ENTITY_RANGE_MAP = "SELECT DISTINCT(rangeid) "
            + "FROM manufacturersubentityrangemap  "
            + "WHERE manufacturersubentityid = :manufacturerSubEntityId ";

    public List<Integer> getAssignedRangeIdsForPurchaser(int manufacturerSubEntityId) {
        Map<String, Object> parameters = new HashMap<String, Object>();
        parameters.put("manufacturerSubEntityId", manufacturerSubEntityId);

        return namedParamJdbcTemplate.queryForList(GET_ASSIGNED_RANGE_IDS_FROM_MANUFACTURER_SUB_ENTITY_RANGE_MAP,
                parameters, Integer.class);
    }



}
