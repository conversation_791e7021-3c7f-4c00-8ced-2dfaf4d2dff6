package co.cadshare.modelMgt.publications.boundary;

import co.cadshare.modelMgt.shared.core.Publication;
import co.cadshare.modelMgt.shared.boundary.PublicationQueryPort;
import co.cadshare.shared.boundary.GetService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class GetPublicationService  extends GetService<Publication, Integer> {

    private final PublicationQueryPort publicationQueryPort;

    @Autowired
    public GetPublicationService(PublicationQueryPort queryPort){
        super(queryPort);
        this.publicationQueryPort = queryPort;
    }

    public List<Publication> getPublicationsForManufacturer(int manufacturerId){
        return this.publicationQueryPort.getPublicationsForManufacturer(manufacturerId);
    }

	public List<Publication> getPublicationsForPurchaser(int purchaserId) {
		return publicationQueryPort.getPublicationsAssignedToPurchaser(purchaserId);
	}
}
