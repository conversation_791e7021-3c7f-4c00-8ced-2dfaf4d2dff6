/*
 * Copyright 2016 Bell.
 */
package co.cadshare.masterParts.adapters.api.web;

import co.cadshare.aspects.access.languages.CanUseLanguage;
import co.cadshare.masterParts.boundary.MasterPartNonModelledService;
import co.cadshare.masterParts.core.extensions.nonModelled.NonModelled;
import co.cadshare.shared.core.Language;
import co.cadshare.shared.core.user.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

@Slf4j
@Controller
@RequestMapping("/masterPart")
public class MasterPartNonModelledController {

  private final MasterPartNonModelledService nonModelledService;

  @Autowired
  public MasterPartNonModelledController(MasterPartNonModelledService nonModelledService) {
    this.nonModelledService = nonModelledService;
  }

  //Nonmodelled Parts Services
  @RequestMapping(value = "/{masterPartId}/nonModelled", method = RequestMethod.GET)
  @CanUseLanguage
  public HttpEntity<NonModelled> getNonModelledForPartById(@AuthenticationPrincipal User currentUser,
                                                           @PathVariable int masterPartId,
                                                           @RequestParam(value = "language", required = false) Language language) throws Exception {

    log.info("ACCESS: User [{}], getNonModelledForPartById, partId [{}]", currentUser.accessDetails(), masterPartId);
	Integer languageId = language != null ? currentUser.findLanguage(language).getLanguageId() : null;
    NonModelled nonModelled = nonModelledService.getNonModelledForPartById(masterPartId, languageId, currentUser.obtainDefaultLanguage());
    return new ResponseEntity<>(nonModelled, HttpStatus.OK);
  }


  @PostMapping(value = "/{masterPartId}/nonModelled", consumes = "application/json")
  public HttpEntity<Boolean> createNonModelledSet(@AuthenticationPrincipal User currentUser,
                                                  @PathVariable int masterPartId,
                                                  @RequestBody NonModelled nonModelled) {

    log.info("ACCESS: User [{}], createNonModelledSet, for masterpart Id [{}]", currentUser.accessDetails(), masterPartId);
    nonModelled.setMasterPartId(masterPartId);
    boolean success =  nonModelledService.createNonModelled(nonModelled);
    log.info("NonModelled with id [{}] created", success);
    return new ResponseEntity<>(success, HttpStatus.OK);
  }

  @PutMapping(value = "/{masterPartId}/nonModelled", consumes = "application/json")
  public HttpEntity<Boolean> editNonModelled(@AuthenticationPrincipal User currentUser,
                                             @PathVariable int masterPartId,
                                             @RequestBody NonModelled nonModelled) {

    log.info("ACCESS: User [{}], editNonModelled, NonModelledId [{}]", currentUser.accessDetails(), masterPartId);
    nonModelled.setMasterPartId(masterPartId);
    boolean response =  nonModelledService.updateNonModelled(nonModelled);
    return new ResponseEntity<>(response, HttpStatus.OK);
  }

  @DeleteMapping(value = "/{masterPartId}/nonModelled")
  public HttpEntity<Boolean> deleteNonModelled(@AuthenticationPrincipal User currentUser,
                                               @PathVariable int masterPartId) {

    log.info("ACCESS: User [{}], deleteNonModelled, id [{}]", currentUser.accessDetails(), masterPartId);
    Boolean isDeleted = nonModelledService.deleteNonModelled(masterPartId);
    return new ResponseEntity<Boolean>(isDeleted, HttpStatus.OK);
  }

}
