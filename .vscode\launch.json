{
    // Use IntelliSense to learn about possible attributes.
    // Hover to view descriptions of existing attributes.
    // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
    "version": "0.2.0",
    "configurations": [
        {
            "type": "java",
            "name": "Current File",
            "request": "launch",
            "mainClass": "${file}"
        },
        {
            "type": "java",
            "name": "CryptoConfig",
            "request": "launch",
            "mainClass": "co.cadshare.crypto.config.CryptoConfig",
            "projectName": "rest-webservices"
        },
        {
            "type": "java",
            "name": "Application",
            "request": "launch",
            "mainClass": "co.cadshare.main.Application",
            "projectName": "rest-webservices"
        },
        {
            "type": "java",
            "name": "NumberWordConverter",
            "request": "launch",
            "mainClass": "co.cadshare.utils.NumberWordConverter",
            "projectName": "rest-webservices"
        },
        {
            "type": "java",
            "name": "PhoneUtils",
            "request": "launch",
            "mainClass": "co.cadshare.utils.PhoneUtils",
            "projectName": "rest-webservices"
        },
        {
            "type": "java",
            "name": "StringUtils",
            "request": "launch",
            "mainClass": "co.cadshare.utils.StringUtils",
            "projectName": "rest-webservices"
        },
        
        {
            "type": "java",
            "shortenCommandLine": "auto"
        },
        {
            "type": "java",
            "name": "SparePartIdentifierController",
            "request": "launch",
            "mainClass": "co.cadshare.csvBomUpload.adapters.api.ext.SparePartIdentifierController",
            "projectName": "rest-webservices"
          }
    ]
}