package co.cadshare.modelMgt.publications.boundary;

import co.cadshare.media.core.Image;
import co.cadshare.modelMgt.shared.core.Publication;
import co.cadshare.shared.boundary.MediaQueryPort;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class FeaturedViewableImageHydrator implements PublicationAttributeHydrator {

	private final MediaQueryPort mediaQueryPort;

	@Autowired
	public FeaturedViewableImageHydrator(MediaQueryPort mediaQueryPort) {
		this.mediaQueryPort = mediaQueryPort;
	}

	@Override
	public void hydrate(PublicationCommand command, Publication publication) {
		if(command.hasFeaturedViewableImage()) {
			Image image = mediaQueryPort.get(command.getFeaturedViewableImageId());
			publication.setFeaturedViewableImage(image);
		} else {
			publication.setFeaturedViewableImage(null);
		}
	}
}
