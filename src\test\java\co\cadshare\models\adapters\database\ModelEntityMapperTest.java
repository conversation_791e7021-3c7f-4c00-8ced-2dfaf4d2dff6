package co.cadshare.models.adapters.database;

import co.cadshare.models.core.model.AutodeskStatus;
import co.cadshare.models.core.Model;
import org.junit.Test;

import static org.junit.Assert.assertTrue;


public class ModelEntityMapperTest {

    @Test
    public void Map2dTest()  {
        ModelsModelEntity entity = new ModelsModelEntity(){{
            setIs2d(true);
            setIsSetupComplete(true);
            setAutodeskStatus(AutodeskStatus.FAILED);
        }};
        Model model = ModelsModelEntityMapper.Instance.entityToCore(entity);
        assertTrue(model.getIs2d());
    }

    @Test
    public void MapSetupCompleteTest()  {
        ModelsModelEntity entity = new ModelsModelEntity(){{
            setIs2d(true);
            setIsSetupComplete(true);
            setAutodeskStatus(AutodeskStatus.FAILED);
        }};
        Model model = ModelsModelEntityMapper.Instance.entityToCore(entity);
        assertTrue(model.getIsSetupComplete());
    }
}
