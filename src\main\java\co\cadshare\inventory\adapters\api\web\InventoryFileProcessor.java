package co.cadshare.inventory.adapters.api.web;

import co.cadshare.inventory.core.InventoryFile;
import com.opencsv.CSVReader;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStreamReader;
import java.util.List;

@Component
@Slf4j
public class InventoryFileProcessor {

    @Autowired
    public InventoryFileProcessor() {
    }
    private List<String[]> readAll(MultipartFile file) throws IOException {

        try (CSVReader reader = new CSVReader(new InputStreamReader(file.getInputStream(), "UTF-8"))){
            List<String[]> list;
            list = reader.readAll();
            return list;
        }
    }

    public InventoryFile convert(MultipartFile file) throws InventoryFile.UnparseableInventoryFileException, IOException {
        InventoryFile inventoryFile = new InventoryFile(readAll(file));
        return inventoryFile;
    }
}
