package co.cadshare.glue;

import io.cucumber.spring.ScenarioScope;
import lombok.Setter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

@Setter
@Component
@ScenarioScope
public class CadshareIT {

	private static final Logger log = LoggerFactory.getLogger(CadshareIT.class);

	private UserIT loggedInUser;

	public UserIT loggedInUser() {
		if(loggedInUser != null)
			return loggedInUser;
		log.info("No user logged in");
		return null;
	}

}
