/*
 * Copyright 2016 Bell.
 */
package co.cadshare.controller;

import co.cadshare.models.core.model.pdfPage.PdfPage;
import co.cadshare.models.core.model.pdfPage.PdfPages;
import co.cadshare.shared.core.user.User;
import co.cadshare.services.PdfPageService;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PostAuthorize;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequestMapping("/pdf")
public class PdfPageController {

    private PdfPageService pdfPageService;

    public PdfPageController(PdfPageService pdfPageService) {
        this.pdfPageService = pdfPageService;
    }


    @PostMapping(value = "/{modelId}", consumes = "application/json")
    @PreAuthorize("@permissionsDao.hasAccessToModel(#currentUser.getUserId(), #modelId)")
    public Boolean createPdfPages(@AuthenticationPrincipal User currentUser, @PathVariable int modelId, @RequestBody PdfPages pdfPages) {
        log.info("ACCESS: User [{}], createPdfPages", currentUser.accessDetails());
        pdfPageService.createPdfPages(pdfPages.getPdfPage(), modelId, currentUser.getUserId());

        log.info("PDF pages created");
        return true;
    }

    @PutMapping(value = "/{modelId}", consumes = "application/json")
    @PreAuthorize("@permissionsDao.hasAccessToModel(#currentUser.getUserId(), #modelId)")
    public Boolean updatePdfPages(@AuthenticationPrincipal User currentUser, @PathVariable int modelId, @RequestBody PdfPages pdfPages) {

        log.info("ACCESS: User [{}], updatePdfPages, model id [{}]", currentUser.accessDetails(), modelId);
        pdfPageService.updatePdfPages(pdfPages.getPdfPage(), modelId, currentUser.getUserId());
        return true;
    }

    @GetMapping(value = "/{modelId}")
    @PostAuthorize("@permissionsDao.hasAccessToModel(#currentUser.getUserId(), #modelId)")
    public List<PdfPage> getPdfPagesByModelId(@AuthenticationPrincipal User currentUser, @PathVariable int modelId) {
        log.info("ACCESS: User [{}], getModelStateByModelId, model ID: [{}]", currentUser.accessDetails(), modelId);
        return pdfPageService.getPdfPagesForModel(modelId);
    }
}
