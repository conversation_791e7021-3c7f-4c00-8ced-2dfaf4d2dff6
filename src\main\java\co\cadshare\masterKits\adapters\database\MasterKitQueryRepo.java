package co.cadshare.masterKits.adapters.database;

import co.cadshare.masterParts.core.MasterPartType;
import co.cadshare.shared.adapters.database.BlazeQueryRepo;
import co.cadshare.shared.adapters.database.MasterKitEntity;
import co.cadshare.shared.core.Language;
import co.cadshare.utils.ObjectExtension;
import com.blazebit.persistence.CriteriaBuilder;
import com.blazebit.persistence.CriteriaBuilderFactory;
import com.blazebit.persistence.view.EntityViewManager;
import com.blazebit.persistence.view.EntityViewSetting;
import lombok.experimental.ExtensionMethod;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import javax.persistence.EntityManager;
import java.util.List;

@Repository
@ExtensionMethod(ObjectExtension.class)
public class MasterKitQueryRepo extends BlazeQueryRepo {

    @Autowired
    public MasterKitQueryRepo(EntityManager entityManager,
                              EntityViewManager entityViewManager,
                              CriteriaBuilderFactory factory) {
        super(entityManager, entityViewManager, factory);
    }

	public List<MasterKitEntityView> getKitsByPartNumberOrDescriptionForManufacturer(String partNumber,
	                                                                              String description,
	                                                                              int manufacturerId,
	                                                                              Language language) {

		return getKitsByPartNumberOrDescription("publications.manufacturerId",
				partNumber,
				description,
				manufacturerId,
				language);
	}

    public List<MasterKitEntityView> getKitsByPartNumberOrDescriptionForPurchaser(String partNumber,
                                                                                  String description,
                                                                                  int purchaserId,
                                                                                  Language language) {

		return getKitsByPartNumberOrDescription("publications.purchasers.id",
				partNumber,
				description,
				purchaserId,
				language);
    }

	private List<MasterKitEntityView> getKitsByPartNumberOrDescription(String ownerWhereClause,
	                                                                  String partNumber,
                                                                      String description,
                                                                      int ownerId,
                                                                      Language language) {

		CriteriaBuilder<MasterKitEntity> criteriaBuilder =
				factory.create(entityManager, MasterKitEntity.class)
						.where("deleted").eq(false)
						.where(ownerWhereClause).eq(ownerId)
						.where("masterParts.masterPartDetail.type").eq(MasterPartType.KIT);

		if(partNumber.isNotNull() && !partNumber.trim().isEmpty())
			criteriaBuilder
					.where("masterParts.masterPartDetail.partNumber")
					.like(false).value(wrapSearchCriteria(partNumber))
					.noEscape();
		else
			criteriaBuilder
					.where("masterParts.masterPartDetail.translations.language.code")
					.eq(language.getLanguageCode())
					.where("masterParts.masterPartDetail.translations.description")
					.like(false).value(wrapSearchCriteria(description))
					.noEscape();

		return entityViewManager.applySetting(EntityViewSetting.create(MasterKitEntityView.class), criteriaBuilder)
				.getResultList();
	}
}
