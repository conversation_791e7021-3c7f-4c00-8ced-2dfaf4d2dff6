package co.cadshare.shared.adapters.database;

import lombok.Data;

import javax.persistence.*;

@Data
@Entity(name="media")
@Inheritance(strategy = InheritanceType.SINGLE_TABLE)
@DiscriminatorColumn(name="mediatype", discriminatorType = DiscriminatorType.STRING)
public abstract class MediaEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name="mediaid")
    private Integer id;
    private String description;
    @Column(name="locationurl")
    private String locationUrl;
}
