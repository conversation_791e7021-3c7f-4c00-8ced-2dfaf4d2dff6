package co.cadshare.ranges.adapters.database;

import lombok.Data;

import javax.persistence.*;
import java.sql.Timestamp;

@Data
@Entity
@Table(name = "Range")
public class RangeEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "rangeid")
    private Integer id;

    private String name;

    @Column(name = "manufacturerid")
    private int manufacturerId;

    private boolean deleted;

    @Column(name = "createdbyuserid")
    private Integer createdByUserId;

    @Column(name = "modifiedbyuserid")
    private Integer modifiedByUserId;

    @Column(name = "createddate")
    private Timestamp createdDate;

    @Column(name = "modifieddate")
    private Timestamp modifiedDate;
}

