package co.cadshare.products.core;

import org.junit.Before;
import org.junit.Ignore;
import org.junit.Test;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;

public class ProductTest {

    private Product out;

    @Before
    public void Before() {
        this.out = new Product();
    }

    @Test
    public void CheckProductIsNotNullTest() {
        assertTrue(out != null);
    }
}