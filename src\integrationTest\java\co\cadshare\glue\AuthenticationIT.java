package co.cadshare.glue;

import co.cadshare.exceptions.UnauthenticatedException;
import co.cadshare.main.Application;
import io.cucumber.spring.CucumberContextConfiguration;
import io.cucumber.spring.ScenarioScope;
import lombok.Setter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootContextLoader;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.stereotype.Component;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.ContextConfiguration;

@Component
@ScenarioScope
public class AuthenticationIT {

	//@Autowired
	//private DealerUserIT dealerUser;
	//@Autowired
	//private ManufacturerUserIT manufacturerUser;
	//@Autowired
	//private DealerPlusUserIT dealerPlusUser;

	@Setter
	private UserIT loggedInUser;

	public UserIT getLoggedInUser() {
		if(loggedInUser == null) throw new UnauthenticatedException("User is not logged in.");
		return loggedInUser;
	}
}
