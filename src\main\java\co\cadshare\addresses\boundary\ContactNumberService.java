package co.cadshare.addresses.boundary;

import co.cadshare.addresses.core.ContactNumber;
import co.cadshare.addresses.adapters.database.ContactNumberDao;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class ContactNumberService {

  @Autowired
  private ContactNumberDao contactNumberDao;

  public List<ContactNumber> getAllNumbersForUser(int userId) {
    List<ContactNumber> numberList = contactNumberDao.getNumbersForUser(userId);
    return numberList;
  }

  public int createNumberForUser(int userId, ContactNumber contactNumber) {
    return contactNumberDao.createContactNumber(userId, contactNumber);
  }

  public int updateNumber(int userId, ContactNumber contactNumber) {
    return contactNumberDao.updateContactNumber(userId, contactNumber);
  }

  public boolean deleteNumber(int userId, int numberId) {
    return contactNumberDao.deleteContactNumber(userId, numberId);
  }

  public ContactNumber getContactNumberById(int numberId) {
    return contactNumberDao.getContactNumberById(numberId);
  }
}
