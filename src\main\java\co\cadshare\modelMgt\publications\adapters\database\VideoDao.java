/*
 * Copyright 2016 Bell.
 */
package co.cadshare.modelMgt.publications.adapters.database;

import co.cadshare.modelMgt.publications.core.Video;
import lombok.extern.slf4j.Slf4j;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.namedparam.MapSqlParameterSource;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.jdbc.support.GeneratedKeyHolder;
import org.springframework.jdbc.support.KeyHolder;
import org.springframework.stereotype.Repository;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Repository
public class VideoDao {

  private NamedParameterJdbcTemplate namedParamJdbcTemplate;

  public VideoDao(NamedParameterJdbcTemplate namedParamJdbcTemplate) {
    this.namedParamJdbcTemplate = namedParamJdbcTemplate;
  }

  private static final String VIDEO_BY_MANUFACTURER = "SELECT * FROM video WHERE manufacturerid = :manufacturerId";

  public List<Video> getVideoByManufacturerId(int manufacturerId) {
    Map<String, Object> parameters = new HashMap<String, Object>();
    parameters.put("manufacturerId", manufacturerId);

    // Check if user has a record for reading the latest comment in the thread.
    List<Video> videos =  namedParamJdbcTemplate.query(VIDEO_BY_MANUFACTURER, parameters, new BeanPropertyRowMapper<>(Video.class));
    return videos;
  }

  private static final String VIDEO_BY_ID = "SELECT * FROM video WHERE id = :id";

  public Video getVideoById(int videoId) {
    Map<String, Object> parameters = new HashMap<String, Object>();
    parameters.put("id", videoId);

    // Check if user has a record for reading the latest comment in the thread.
    Video video =  namedParamJdbcTemplate.queryForObject(VIDEO_BY_ID, parameters, new BeanPropertyRowMapper<>(Video.class));
    return video;
  }

  private final static String CREATE_VIDEO = "INSERT INTO video (name, description, url, manufacturerid) "
          + "VALUES( :name, :description, :url, :manufacturerId)";

  public int createVideo(Video video) {
    KeyHolder keyHolder = new GeneratedKeyHolder();

    MapSqlParameterSource parameters = new MapSqlParameterSource();
    parameters.addValue("name", video.getName());
    parameters.addValue("description", video.getDescription());
    parameters.addValue("url", video.getUrl());
    parameters.addValue("manufacturerId", video.getManufacturerId());

    int result = namedParamJdbcTemplate.update(CREATE_VIDEO, parameters, keyHolder, new String[] { "id" });
    return keyHolder.getKey().intValue();
  }

  private final static String UPDATE_VIDEO = "UPDATE video SET name = :name, description = :description, url = :url " +
          "WHERE id = :id";

  public boolean updateVideo(Video video) {
    Map<String, Object> parameters = new HashMap<String, Object>();
    parameters.put("name", video.getName());
    parameters.put("description", video.getDescription());
    parameters.put("url", video.getUrl());
    parameters.put("id", video.getId());

    int result = namedParamJdbcTemplate.update(UPDATE_VIDEO, parameters);
    return result > 0;
  }

  private static final String DELETE_VIDEO = "DELETE FROM video WHERE id = :videoId";

  public boolean deleteVideo(int videoId) {
    Map<String, Integer> namedParameters = new HashMap<String, Integer>();
    namedParameters.put("videoId", videoId);

    namedParamJdbcTemplate.update(DELETE_VIDEO, namedParameters);

    log.info("Deleted Video with ID [{}]", videoId);
    return true;
  }
}
