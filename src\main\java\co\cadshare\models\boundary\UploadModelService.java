package co.cadshare.models.boundary;

import co.cadshare.models.adapters.database.ModelDao;
import co.cadshare.models.core.*;
import co.cadshare.persistence.PartDao;
import co.cadshare.shared.adapters.database.manufacturer.ManufacturerConfigDao;
import co.cadshare.models.core.processor.PropertiesProcessor;
import com.autodesk.client.ApiException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataAccessException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.util.Arrays;
import java.util.List;

import static co.cadshare.models.core.model.AutodeskStatus.*;

@Service
public class UploadModelService {

    private static final Logger logger = LoggerFactory.getLogger(UploadModelService.class);
    public static final String POLL_RETURNED_OUTSTANDING_MODELS = "Poll returned [{}] outstanding models";
    public static final String GETTING_MANIFEST_FROM_AUTODESK = "Getting manifest from autodesk for model with id [{}] and urn [{}]";
    public static final String NULL_MANIFEST_RETURNED = "Manifest returned from autodesk was null";
    public static final String API_EXCEPTION_ERROR = "Error code from ApiException: [{}]";
    public static final String ERROR_CALLING_UPDATE_STATUS = "Error calling updateStatusFromAutodesk [{}] and urn [{}] : error [{}]";
    public static final String METHOD_COMPLETED = "Method updateStatusFromAutodesk has completed for model with id [{}] and urn [{}]. Total execution time was {} milliseconds";
    public static final String AUTODESK_CONNECTION_FAILURE = "Autodesk connection failure";
    public static final String UPDATING_METADATA_ISSUE = "There was an issue batch updating metadata relating to urn {} and model {}";
    public static final String BATCH_PART_UPDATE_FAILURE = "Batch part update failure writing to database";
    private final AutodeskPort autodeskQuery;
    private final ModelDao modelDao;
    private final PartDao partDao;
    private final List<String> outstandingStatusList = Arrays.asList(INPROGRESS.name(), PENDING.name(), UPLOADED.name());

    @Autowired
    public UploadModelService(AutodeskPort autodeskQuery,
                              ModelDao modelDao,
                              PartDao partDao) {
        this.autodeskQuery = autodeskQuery;
        this.modelDao = modelDao;
        this.partDao = partDao;
    }

    @Transactional
    public void pollModelsStatus() {
        List<Model> modelList = modelDao.getModels(outstandingStatusList);
        logger.info(POLL_RETURNED_OUTSTANDING_MODELS, modelList.size());
        modelList.parallelStream().forEach(this::updateModelMetadataFromAutodesk);
    }

    private void updateModelMetadataFromAutodesk(Model model) {
        long startTime = Instant.now().toEpochMilli();
        logger.info(GETTING_MANIFEST_FROM_AUTODESK, model.getModelId(), model.getAutodeskUrn());
        try {
            ModelManifestWrapper manifest = autodeskQuery.getManifest(model);
            if (manifest.isNotNull()) {
                if (manifest.statusOrProgressHasChanged(model)) {
                    if (model.readyForPartsUpload()) {
                        ParsedMetadata parsedMetadata = autodeskQuery.extractMetadata(model, manifest);
                        if (parsedMetadata.metadataToBeUploaded() && parsedMetadata.isValid())
                            uploadPartsMetadata(model, manifest, parsedMetadata);
                    }
                    modelDao.updateModel(model);
                }
            } else {
                model.retriedConnectionIssue(NULL_MANIFEST_RETURNED);
                modelDao.updateModel(model);
            }
        } catch (Exception e) {
            if (e instanceof ApiException) {
                // Ive seen 504 code come back when the urn is incorrect.
                logger.error(API_EXCEPTION_ERROR, (((ApiException) e).getCode()));
                //increment retries
                model.retriedConnectionIssue(AUTODESK_CONNECTION_FAILURE);
                modelDao.updateModel(model);
            }
            logger.error(ERROR_CALLING_UPDATE_STATUS, model.getModelId(), model.getAutodeskUrn(), e.getMessage());
        }
        long endTime = Instant.now().toEpochMilli();
        logger.info(METHOD_COMPLETED, model.getModelId(), model.getAutodeskUrn(), (endTime - startTime));
    }

    private void uploadPartsMetadata(Model model, ModelManifestWrapper manifest, ParsedMetadata parsedMetadata) {
        try {
            partDao.batchUploadPartsMetadata(parsedMetadata.get(), model.getModelId());
            model.partsProcessed(manifest);
        } catch (DataAccessException exception) {
            logger.info(UPDATING_METADATA_ISSUE, model.getAutodeskUrn(), model.getModelId());
            model.retriedConnectionIssue(BATCH_PART_UPDATE_FAILURE);
        }
    }


}
