package co.cadshare.modelMgt.publications.adapters.database;

import lombok.Data;
import org.hibernate.annotations.DiscriminatorOptions;

import javax.persistence.*;

@Data
@Entity
@Table(name="manufacturersubentity")
@Inheritance(strategy = InheritanceType.SINGLE_TABLE)
@DiscriminatorColumn(name="manufacturersubentitytypeid", discriminatorType = DiscriminatorType.INTEGER)
@DiscriminatorOptions(force = true)  //this is due to a bug in Hibernate 5.2.x versions - once upgraded, this can be removed
public class PublicationsPurchaserEntity {

	@Id
	@GeneratedValue (strategy = GenerationType.IDENTITY)
	@Column(name = "manufacturersubentityid")
	private Integer id;

	private String name;

	@Column(name="parentsubentityid")
	private Integer parentPurchaserId;
}
