package co.cadshare.media.core;

import co.cadshare.exceptions.BadClientRequestException;
import co.cadshare.shared.core.Media;
import lombok.Getter;
import org.springframework.http.MediaType;

import java.io.InputStream;
import java.sql.Timestamp;
import java.util.Arrays;
import java.util.Calendar;
import java.util.List;

public class Image extends Media {

	private String fileExt;
	private String mediaTypeStr;
	private final List<MediaType> acceptableMimeTypes = Arrays.asList(MediaType.IMAGE_PNG,
			MediaType.IMAGE_GIF,
			MediaType.IMAGE_JPEG);

	@Getter
	private InputStream stream;

	public Image() {
	}

	public Image(String mimeTypeAsString, InputStream stream) {
		this.mediaTypeStr = mimeTypeAsString;
		String imageMimePrefix = "image/";
		this.fileExt = mimeTypeAsString.startsWith(imageMimePrefix) ?
				mimeTypeAsString.substring(imageMimePrefix.length()) :
				mimeTypeAsString;
		setDescription(mimeTypeAsString);
		this.stream = stream;
	}

	public void validate() {
		if(!mediaTypeStr.isEmpty()) {
			if (!acceptableMimeTypes.contains(MediaType.parseMediaType(mediaTypeStr)))
				throw new BadClientRequestException("Your image file is not an accepted file type (png, gif or jpg only");
		}
	}

	public String buildImageFileName() {
		Timestamp now = new java.sql.Timestamp(Calendar.getInstance().getTime().getTime());
		return String.valueOf(now.getTime()).concat(".").concat(fileExt);
	}

}
