package co.cadshare.config;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Component
@Data
public class ApplicationProperties {

    @Value("${properties.almab.manufacturerid}")
    private int almabManufacturerId;
    @Value("${web.api.client.id}")
    private String webApiClientId;
    @Value("${web.api.client.secret}")
    private String webApiClientSecret;
    @Value("${external.api.client.id}")
    private String externalApiClientId;
    @Value("${external.api.client.secret}")
    private String externalApiClientSecret;
    @Value("${run.external.erp.tasks: true}")
    private boolean runExternalErpTasksEnabled;

}
