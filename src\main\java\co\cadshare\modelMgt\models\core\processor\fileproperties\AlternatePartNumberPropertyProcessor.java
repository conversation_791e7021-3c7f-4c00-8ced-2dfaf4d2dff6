/*
 * Copyright 2016 Bell.
 */
package co.cadshare.modelMgt.models.core.processor.fileproperties;

import co.cadshare.modelMgt.models.core.MetadataObjectExtended;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.LinkedHashMap;
import java.util.List;

@Component
public class AlternatePartNumberPropertyProcessor extends AbstractPropertiesProcessor
        implements FilePropertiesProcessor, InitializingBean {

    @Value("#{'${properties.processor.alternatePartNumber.synonyms}'.split(',')}")
    List<String> alternatePartNumberSynonyms;

    @Override
    public void setProperties(LinkedHashMap properties, MetadataObjectExtended object) {
        object.setAlternatePartNumber(getPropertyValue(properties));
    }

    @Override
    public List<String> getSynonyms() {
        return alternatePartNumberSynonyms;
    }

    @Override
    public void setSynonyms(List<String> synonyms) {
        alternatePartNumberSynonyms = synonyms;
    }
}
