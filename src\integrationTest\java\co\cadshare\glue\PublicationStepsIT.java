package co.cadshare.glue;

import co.cadshare.models.core.Model;
import co.cadshare.response.CustomerModel;
import io.cucumber.java.en.And;
import io.cucumber.java.en.Then;
import io.cucumber.java.en.When;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;


public class PublicationStepsIT {

    @Autowired
    private DealerUserIT dealerUser;
    private Model model;
	private final UserIT loggedInUser;

	@Autowired
	public PublicationStepsIT(AuthenticationIT auth) {
		loggedInUser = auth.getLoggedInUser();
	}

    @And("a Model with name {string} has been uploaded successfully")
    public void aModelWithNameHasBeenUploadedSuccessfully(String modelName) {
       model = loggedInUser.getModel(modelName);
    }

    @When("I publish the Viewable {string} automatically")
    public void iPublishTheViewableAutomatically(String viewableName) {
	    loggedInUser.autoPublishViewable(model, viewableName);
    }

    @Then("the Viewable {string} should be published as a Publication")
    public void theViewableShouldBePublished(String viewableName) {
	    loggedInUser.verifyPublicationExists(viewableName);
    }
/*
    @And("as a Dealer with email address {string} I can see a Publication named {string}")
    public void asADealerWithEmailAddressICanSeeAPublicationNamed(String emailAddress, String publicationName) {
	    loggedInUser.logIn(emailAddress);
        loggedInUser.verifyPublicationExists(publicationName);
    }

 */

    @And("the Publication {string} should have a default image")
    public void thePublicationShouldHaveADefaultImage(String publicationName) {
	    loggedInUser.publicationHasDefaultImage(model, publicationName);
    }

	@When("a Model with name {string} has been uploaded and published successfully")
	public void aModelWithNameHasBeenUploadedAndPublishedSuccessfully(String arg0) {
		
	}

	@Then("I can see a Publication named {string}")
	public void iCanSeeAPublicationNamed(String arg0) {
	}

	@Then("I can view all Viewables for a Publication named {}")
	public void iCanViewAllViewablesForAPublicationNamed(String publicationName) {
		List<CustomerModel> viewables = loggedInUser.getViewablesForPublication(publicationName);
		assert(!viewables.isEmpty());
	}
}
