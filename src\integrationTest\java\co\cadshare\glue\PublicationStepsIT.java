package co.cadshare.glue;

import co.cadshare.modelMgt.models.core.Model;
import co.cadshare.modelMgt.publicationCategories.adapters.api.web.GetPublicationCategoryResponseDto;
import co.cadshare.modelMgt.publications.adapters.api.web.*;
import co.cadshare.response.CustomerModel;
import co.cadshare.shared.adapters.api.web.CommonItemDto;
import co.cadshare.shared.core.manufacturer.subentity.ManufacturerSubEntity;
import io.cucumber.java.en.And;
import io.cucumber.java.en.Then;
import io.cucumber.java.en.When;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

import static org.junit.Assert.*;


public class PublicationStepsIT  {

	private PostPublicationRequestDto newPublication;
	private Model model;
	protected UserIT loggedInUser;
	private final CadshareIT cadshare;

	@Autowired
	public PublicationStepsIT(CadshareIT cadshare) {
		this.cadshare = cadshare;
	}

    @And("a Model with name {string} has been uploaded successfully")
    public void aModelWithNameHasBeenUploadedSuccessfully(String modelName) {
       model = cadshare.loggedInUser().getModel(modelName);
    }

    @When("I publish the Viewable {string} automatically")
    public void iPublishTheViewableAutomatically(String viewableName) {
	    cadshare.loggedInUser().autoPublishViewable(model, viewableName);
    }

    @Then("the Viewable {string} should be published as a Publication")
    public void theViewableShouldBePublished(String viewableName) {
	    cadshare.loggedInUser().verifyPublicationExists(viewableName);
    }

    @And("the Publication {string} should have a default image")
    public void thePublicationShouldHaveADefaultImage(String publicationName) {
	    cadshare.loggedInUser().publicationHasDefaultImage(model, publicationName);
    }

	@When("a Model with name {string} has been uploaded and published successfully")
	public void aModelWithNameHasBeenUploadedAndPublishedSuccessfully(String arg0) {
		
	}

	@Then("I can see a single Publication named {}")
	public void iCanSeeAPublicationNamed(String publicationName) {
		GetPublicationResponseDto publication =	cadshare.loggedInUser().getPublicationFromName(publicationName);
		assertNotNull(publication);
	}

	@Then("I can view all Viewables for a Publication named {}")
	public void iCanViewAllViewablesForAPublicationNamed(String publicationName) {
		List<CustomerModel> viewables = cadshare.loggedInUser().getViewablesForPublication(publicationName);
		assert(!viewables.isEmpty());
	}

	@Then("I can view all Publications for my Manufacturer")
	public void iCanViewAllPublicationsForMyManufacturer() {
		GetPublicationsListResponseDto publications = cadshare.loggedInUser().getPublicationsForManufacturer();
		assert(!publications.getPublications().isEmpty());
	}

	@When("I create this new Publication named {}")
	public void iCreateThisNewPublicationNamed(String publicationName) {
		newPublication.setName(publicationName);
		cadshare.loggedInUser().createPublication(newPublication);
	}

	@Then("I can see a Publication named {} in the list of Publications")
	public void iCanSeeAPublicationNamedInTheListOfPublications(String publicationName) {
		cadshare.loggedInUser().verifyPublicationExists(publicationName);
	}

	@And("I start with a new Publication")
	public void iStartWithANewPublication() {
		newPublication = cadshare.loggedInUser().getNewPublication();
	}

	@When("I select the PublicationCategory named {} for my Publication")
	public void iSelectThePublicationCategoryNamedForMyPublication(String publicationCategoryName) {
		GetPublicationCategoryResponseDto publicationCategory = cadshare.loggedInUser().searchForPublicationCategoryByName(publicationCategoryName);
		newPublication.setPublicationCategoryId(publicationCategory.getId());
	}

	@And("I select the Viewable named {} for my Publication")
	public void iSelectTheViewableNamedForMyPublication(String viewableName) {
		Model model = cadshare.loggedInUser().getModel(viewableName);
		PostPutPublicationViewableDto viewable = new PostPutPublicationViewableDto();
		viewable.setId(model.getModelId());
		newPublication.setViewables(Collections.singletonList(viewable));
	}

	@When("I update this existing Publication named {} with {}")
	public void iUpdateThisExistingPublicationNamedWith(String existingPublicationName, String updatedPublicationName) {
		GetPublicationListItemResponseDto existingPublication = cadshare.loggedInUser().getPublicationFromListByName(existingPublicationName);
		PutPublicationRequestDto updatedPublication = new PutPublicationRequestDto();
		updatedPublication.setName(updatedPublicationName);
		List<Integer> existingKitIds = existingPublication.getKits().stream()
			                            .map(CommonItemDto::getId)
			                            .collect(Collectors.toList());
		List<Integer> existingPurchaserIds = existingPublication.getCustomers().stream()
			                            .map(CommonItemDto::getId)
			                            .collect(Collectors.toList());
		List<PostPutPublicationViewableDto> existingViewables = existingPublication.getViewables().stream()
										.map(viewable -> new PostPutPublicationViewableDto(){{setId(viewable.getId());}})
										.collect(Collectors.toList());
		updatedPublication.setKits(existingKitIds);
		updatedPublication.setCustomers(existingPurchaserIds);
		updatedPublication.setViewables(existingViewables);
		cadshare.loggedInUser().updatePublication(updatedPublication, existingPublication.getId());

	}

	@When("I delete this existing Publication named {}")
	public void iDeleteThisExistingPublicationNamed(String publicationName) {
		GetPublicationListItemResponseDto existingPublication = cadshare.loggedInUser().getPublicationFromListByName(publicationName);
		cadshare.loggedInUser().deletePublication(existingPublication.getId().toString());
	}

	@Then("I can't see a Publication named {} in the list of Publications")
	public void iCanTSeeAPublicationNamedInTheListOfPublications(String publicationName) {
		cadshare.loggedInUser().verifyPublicationDoesntExist(publicationName);
	}

	@When("I publish this existing Publication named {}")
	public void iPublishThisExistingPublicationNamed(String publicationName) {
		publishExistingPublicationNamed(publicationName);
	}

	@Then("the Publication named {} is published")
	public void thePublicationNamedIsPublished(String publicationName) {
		GetPublicationListItemResponseDto existingPublication = cadshare.loggedInUser().getPublicationFromListByName(publicationName);
		assertTrue(existingPublication.isPublished());
	}

	@When("I unpublish this existing Publication named {}")
	public void iUnpublishThisExistingPublicationNamed(String publicationName) {
		GetPublicationListItemResponseDto existingPublication = cadshare.loggedInUser().getPublicationFromListByName(publicationName);
		cadshare.loggedInUser().unpublishPublication(existingPublication.getId().toString());
	}

	@Then("the Publication named {} is unpublished")
	public void thePublicationNamedIsUnpublished(String publicationName) {
		GetPublicationListItemResponseDto existingPublication = cadshare.loggedInUser().getPublicationFromListByName(publicationName);
		assertFalse(existingPublication.isPublished());
	}

	@And("a Purchaser exists")
	public void aPurchaserExists() {
		List<ManufacturerSubEntity> purchasersList = cadshare.loggedInUser().getPurchasersList();
		assertNotNull(purchasersList);
		assertFalse(purchasersList.isEmpty());
	}

	@And("I select any existing Purchaser for my new Publication")
	public void iSelectAnyExistingPurchaserForMyNewPublication() {
		List<ManufacturerSubEntity> purchasersList = cadshare.loggedInUser().getPurchasersList();
		ManufacturerSubEntity purchaser = purchasersList.get(0);
		newPublication.setCustomers(Collections.singletonList(purchaser.getManufacturerSubEntityId()));
	}

	@And("I create Publications named {} with PublicationCategories named {}")
	public void iCreatePublicationsNamedWithPublicationCategoriesNamed(String publicationsArray, String publicationCategoriesArray) {
		List<String> publications = Arrays.asList(publicationsArray.split(","));
		List<String> publicationCategories = Arrays.asList(publicationCategoriesArray.split(","));

		for (int i = 0; i < 2; i++) {
			newPublication = cadshare.loggedInUser().getNewPublication();
			GetPublicationCategoryResponseDto publicationCategory = cadshare.loggedInUser().searchForPublicationCategoryByName(publicationCategories.get(i));
			newPublication.setPublicationCategoryId(publicationCategory.getId());
			newPublication.setName(publications.get(i));
			cadshare.loggedInUser().createPublication(newPublication);
		}
	}

	@Then("I can see Publications named {} in the list of Publications")
	public void asADealerWithEmailAddressICanSeePublicationsNamed(String publicationsArray) {
		List<String> publications = Arrays.asList(publicationsArray.split(","));
		cadshare.loggedInUser().verifyPublicationExists(publications.get(0));
		cadshare.loggedInUser().verifyPublicationExists(publications.get(1));
	}

	@When("I assign Publications named {} to Purchaser named {}")
	public void iAssignPublicationsNamedToPurchaserNamed(String publicationNames, String dealerName) {
		cadshare.loggedInUser().assignPublicationsToDealer(publicationNames, dealerName);
	}

	@And("I publish this existing Publications named {}")
	public void iPublishThisExistingPublicationsNamed(String publicationNames) {
		String[] publicationNamesArray = publicationNames.split(",");
		for (String publicationName : publicationNamesArray)
			publishExistingPublicationNamed(publicationName);
	}

	private void publishExistingPublicationNamed(String publicationName) {
		GetPublicationListItemResponseDto existingPublication = cadshare.loggedInUser().getPublicationFromListByName(publicationName);
		cadshare.loggedInUser().publishPublication(existingPublication.getId().toString());
	}

}
