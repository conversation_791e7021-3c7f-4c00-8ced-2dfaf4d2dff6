package co.cadshare.models.adapters.database;

import org.mockito.ArgumentMatcher;

public class ModelEntityMatcher implements ArgumentMatcher<ModelCommandEntity> {

    private ModelCommandEntity left;

    public ModelEntityMatcher(ModelCommandEntity left) {
        this.left = left;
    }

    @Override
    public boolean matches(ModelCommandEntity right) {
        boolean returnVal = (left.getModelId() == null && right.getModelId() == null) ||
                left.getModelId() == right.getModelId();
        return returnVal;
    }
}
