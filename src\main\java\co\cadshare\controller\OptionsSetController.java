package co.cadshare.controller;

import co.cadshare.aspects.access.languages.CanUseLanguage;
import co.cadshare.shared.core.Language;
import co.cadshare.masterParts.core.extensions.optionsSet.OptionsSet;

import java.util.List;

import co.cadshare.utils.ManufacturerFinder;
import co.cadshare.utils.ObjectUtilsExtension;
import lombok.experimental.ExtensionMethod;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import co.cadshare.shared.core.user.User;
import co.cadshare.services.OptionsSetService;
import co.cadshare.services.PermissionsService;
import org.springframework.web.bind.annotation.RequestParam;

@ExtensionMethod(ObjectUtilsExtension.class)
@Controller
@RequestMapping("/optionsSet")
public class OptionsSetController {

  private static final Logger logger = LoggerFactory.getLogger(OptionsSetController.class);

  private final OptionsSetService optionsSetService;
  private final PermissionsService permissionsService;
  private final ManufacturerFinder manufacturerFinder;

  @Autowired
  public OptionsSetController (OptionsSetService optionsSetService,
                               PermissionsService permissionsService,
                               ManufacturerFinder manufacturerFinder) {
    this.optionsSetService = optionsSetService;
    this.permissionsService = permissionsService;
    this.manufacturerFinder = manufacturerFinder;
  }

  @RequestMapping(value = "/{id}", method = RequestMethod.GET)
  @CanUseLanguage
  public HttpEntity<OptionsSet> getOptionsSet(@AuthenticationPrincipal User currentUser,
                                              @PathVariable int id,
                                              @RequestParam(value = "language", required = false) Language language) throws Exception {

    logger.info("ACCESS: User [{}], getOptionsSet, optionsSetId [{}]", currentUser.accessDetails(), id);
	Integer languageId = language != null ? currentUser.findLanguage(language).getLanguageId() : null;
    int manufacturerId = manufacturerFinder.getUserManufacturerId(currentUser);
    OptionsSet optionsSet = optionsSetService.getOptionsSetById(id, languageId, currentUser.obtainDefaultLanguage(), manufacturerId);
    return new ResponseEntity<>(optionsSet, HttpStatus.OK);
  }

  @RequestMapping(value = "/model/{modelId}", method = RequestMethod.GET)
  @CanUseLanguage
  public HttpEntity<List<OptionsSet>> getOptionsSetForModel(@AuthenticationPrincipal User currentUser,
                                                            @PathVariable int modelId,
                                                            @RequestParam(value = "language", required = false) Language language) throws Exception {

    logger.info("ACCESS: User [{}], getOptionsSetForModel, modelId [{}]", currentUser.accessDetails(), modelId);
    try {
      userHasPermissionsForModel(currentUser.getUserId(), modelId);
    } catch (Exception ex) {
      return new ResponseEntity<>(null, HttpStatus.FORBIDDEN);
    }
	Integer languageId = language != null ? currentUser.findLanguage(language).getLanguageId() : null;
    List<OptionsSet> optionsSets = optionsSetService.getOptionsSetByModelId(modelId,languageId, currentUser.obtainDefaultLanguage());
    return new ResponseEntity<>(optionsSets, HttpStatus.OK);
  }

  @RequestMapping(value = "/part/{partId}", method = RequestMethod.GET)
  @CanUseLanguage
  public HttpEntity<List<OptionsSet>> getOptionsSetForPart(@AuthenticationPrincipal User currentUser,
                                                           @PathVariable int partId,
                                                           @RequestParam(value = "language", required = false) Language language) throws Exception {

    logger.info("ACCESS: User [{}], getOptionsSetForPart, partId [{}]", currentUser.accessDetails(), partId);
	Integer languageId = language != null ? currentUser.findLanguage(language).getLanguageId() : null;
    List<OptionsSet> optionsSets = optionsSetService.getOptionsSetByPartId(partId, languageId, currentUser.obtainDefaultLanguage());
    return new ResponseEntity<List<OptionsSet>>(optionsSets, HttpStatus.OK);
  }


  @RequestMapping(value = "model/{modelId}", method = RequestMethod.POST, consumes = "application/json")
  public HttpEntity<Integer> createOptionsSet(@AuthenticationPrincipal User currentUser,
                                              @PathVariable int modelId,
                                              @RequestBody OptionsSet optionsSet) {

    logger.info("ACCESS: User [{}], createOptionsSet, for model Id [{}]", currentUser.accessDetails(), modelId);

    try {
      userHasPermissionsForModel(currentUser.getUserId(), modelId);
    } catch (Exception ex) {
      return new ResponseEntity<>(null, HttpStatus.FORBIDDEN);
    }

    optionsSet.setCreatedByUserId(currentUser.getUserId());
    int optionSetId = optionsSetService.createOptionsSet(modelId, optionsSet);

    logger.info("OptionSet with id [{}] created", optionSetId);
    return new ResponseEntity<>(optionSetId, HttpStatus.OK);
  }

  @RequestMapping(value = "/{id}", method = RequestMethod.PUT, consumes = "application/json")
  public HttpEntity<Boolean> editOptionsSet(@AuthenticationPrincipal User currentUser,
                                            @PathVariable int id,
                                            @RequestBody OptionsSet optionsSet) {

    logger.info("ACCESS: User [{}], editOptionSet, OptionSetId [{}]", currentUser.accessDetails(), id);

    optionsSet.setModifiedByUserId(currentUser.getUserId());
    optionsSet.setId(id);

    optionsSetService.updateOptionsSet(optionsSet);

    return new ResponseEntity<>(HttpStatus.OK);
  }

  @RequestMapping(value = "/{id}", method = RequestMethod.DELETE)
  public HttpEntity<Boolean> deleteOptionsSet(@AuthenticationPrincipal User currentUser,
                                              @PathVariable int id) {

    logger.info("ACCESS: User [{}], deleteOptionSet, id [{}]", currentUser.accessDetails(), id);
    Boolean isDeleted = optionsSetService.deleteOptionsSet(id);

    return new ResponseEntity<Boolean>(isDeleted, HttpStatus.OK);
  }


  private boolean userHasPermissionsForModel(int userId, int modelId) throws Exception {
	  return permissionsService.userHasPermissionsToViewModel(userId, modelId);
  }
}
