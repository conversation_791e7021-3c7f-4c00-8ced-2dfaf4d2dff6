package co.cadshare.modelMgt.publicationCategories.adapters.database;

import co.cadshare.modelMgt.publicationCategories.core.PublicationCategory;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface PublicationCategoryEntityMapper {

    PublicationCategoryEntityMapper Instance = Mappers.getMapper(PublicationCategoryEntityMapper.class);

    PublicationCategory entityToCore(PublicationCategoryEntity entity);

    PublicationCategoryEntity coreToEntity(PublicationCategory publicationCategory);

    List<PublicationCategory> entitiesToCores(List<PublicationCategoryEntity> entities);

    List<PublicationCategoryEntity> coresToEntities(List<PublicationCategory> publicationCategories);

}
