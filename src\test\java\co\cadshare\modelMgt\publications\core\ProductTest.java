package co.cadshare.modelMgt.publications.core;

import co.cadshare.modelMgt.shared.core.Product;
import org.junit.Before;
import org.junit.Test;

import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;

public class ProductTest {

    Product out;

    @Before
    public void before() {
        out = new Product();
    }

    @Test
    public void thumbnailUrlNotSetAsNull() {
        assertTrue(out.thumbnailUrlNotSet());
    }

    @Test
    public void thumbnailUrlNotSetAsPlaceholder() {
        out.setThumbnailUrl("images/placeholder.jpg");
        assertTrue(out.thumbnailUrlNotSet());
    }

    @Test
    public void thumbnailUrlSet() {
        out.setThumbnailUrl("someOtherUrl.jpg");
        assertFalse(out.thumbnailUrlNotSet());
    }
}
