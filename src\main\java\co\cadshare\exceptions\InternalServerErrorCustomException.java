package co.cadshare.exceptions;

import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ResponseStatus;

@ResponseStatus(value = HttpStatus.INTERNAL_SERVER_ERROR)
public class InternalServerErrorCustomException extends RuntimeException {

	private static final String messagePrefix = "There was an unanticipated error on CADshare. ";

	public InternalServerErrorCustomException(String message) {
		super(messagePrefix.concat(message));
	}

	public InternalServerErrorCustomException(String message, Throwable throwable) {
		super(messagePrefix.concat(message), throwable);
	}
}
