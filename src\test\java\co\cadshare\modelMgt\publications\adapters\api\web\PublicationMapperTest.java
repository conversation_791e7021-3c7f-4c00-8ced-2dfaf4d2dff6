package co.cadshare.modelMgt.publications.adapters.api.web;

import co.cadshare.modelMgt.publications.boundary.CreatePublicationCommand;
import co.cadshare.modelMgt.publications.core.TechDoc;
import co.cadshare.modelMgt.publications.core.Video;
import co.cadshare.media.core.Image;
import co.cadshare.modelMgt.publicationCategories.core.PublicationCategory;
import co.cadshare.modelMgt.publications.core.Kit;
import co.cadshare.modelMgt.publications.core.ManualStatus;
import co.cadshare.modelMgt.shared.core.Publication;

import co.cadshare.modelMgt.shared.core.Product;
import co.cadshare.modelMgt.shared.core.Range;
import co.cadshare.modelMgt.shared.core.Viewable;
import co.cadshare.shared.core.user.User;
import org.junit.Test;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.List;

import static org.junit.Assert.*;

public class PublicationMapperTest {

	@Test
	public void PostPublicationRequestDtoToCommandFeaturedViewable() {
		User user = new User();
		user.setManufacturerId(456);
		PostPublicationRequestDto source = new PostPublicationRequestDto();
		PostPutPublicationViewableDto viewableSource = new PostPutPublicationViewableDto();
		viewableSource.setId(123);
		viewableSource.setFeaturedViewable(true);
		List<PostPutPublicationViewableDto> viewables = new ArrayList<>();
		viewables.add(viewableSource);
		source.setViewables(viewables);
		CreatePublicationCommand target = PublicationMapper.Instance.postRequestDtoToCommand(source, user);
		assertNotNull(target.getFeaturedViewableId());
		assertEquals(viewableSource.getId(), target.getFeaturedViewableId().intValue());
	}

	@Test
	public void PostPublicationRequestDtoToCommandNoFeaturedViewable() {
		User user = new User();
		user.setManufacturerId(456);
		PostPublicationRequestDto source = new PostPublicationRequestDto();
		PostPutPublicationViewableDto viewableSource = new PostPutPublicationViewableDto();
		viewableSource.setId(123);
		viewableSource.setFeaturedViewable(false);
		List<PostPutPublicationViewableDto> viewables = new ArrayList<>();
		viewables.add(viewableSource);
		source.setViewables(viewables);
		CreatePublicationCommand target = PublicationMapper.Instance.postRequestDtoToCommand(source, user);
		assertNull(target.getFeaturedViewableId());
	}

	@Test
	public void publicationToPublicationListItemDto() {

		Publication source = buildPublication();
		GetPublicationListItemResponseDto target = PublicationMapper.Instance.coreToListItemDto(source);
		assertAllStandardProps(source, target);
		assertEquals(source.getCreatedDate(), target.getCreatedDate());
	}

	@Test
	public void publicationToSinglePublicationDto() {

		Publication source = buildPublication();
		GetPublicationResponseDto target = PublicationMapper.Instance.coreToDto(source);
		assertAllStandardProps(source, target);
	}

	private static void assertAllStandardProps(Publication source, PublicationListItemDto target) {
		assertEquals(source.getId(), target.getId());
		assertEquals(source.getName(), target.getName());
		assertEquals(source.getSerialNumber(), target.getSerialNumber());
		assertTrue(target.isPublished());
		assertEquals(source.getPublicationCategory().getId().intValue(), target.getPublicationCategoryId());
		assertEquals(source.getPublicationCategory().getName(), target.getPublicationCategoryName());
		assertEquals(source.getViewables().size(), target.getViewables().size());
		assertEquals(source.getViewables().get(0).getId(), target.getViewables().get(0).getId().intValue());
		assertEquals(source.getViewables().get(0).getName(), target.getViewables().get(0).getName());
		assertEquals(source.getViewables().get(0).getProduct().getId(), target.getViewables().get(0).getProductId());
		assertEquals(source.getViewables().get(0).getProduct().getName(), target.getViewables().get(0).getProductName());
		assertEquals(source.getViewables().get(0).getProduct().getRange().getName(), target.getViewables().get(0).getRangeName());
		assertTrue(target.getViewables().get(0).isFeaturedViewable());
		assertEquals(source.getViewables().get(1).getId(), target.getViewables().get(1).getId().intValue());
		assertEquals(source.getViewables().get(1).getName(), target.getViewables().get(1).getName());
		assertEquals(source.getViewables().get(1).getProduct().getId(), target.getViewables().get(1).getProductId());
		assertEquals(source.getViewables().get(1).getProduct().getName(), target.getViewables().get(1).getProductName());
		assertEquals(source.getViewables().get(1).getProduct().getRange().getName(), target.getViewables().get(1).getRangeName());
		assertFalse(target.getViewables().get(1).isFeaturedViewable());
		assertEquals(source.getCoverImage().getLocationUrl(), target.getCoverImage().getUrl());
		assertEquals(source.getTechDocs().size(), target.getTechDocs().size());
		assertEquals(source.getTechDocs().get(0).getId(), target.getTechDocs().get(0).getId());
		assertEquals(source.getTechDocs().get(0).getName(), target.getTechDocs().get(0).getName());
		assertEquals(source.getTechDocs().get(1).getId(), target.getTechDocs().get(1).getId());
		assertEquals(source.getVideos().size(), target.getVideos().size());
		assertEquals(source.getVideos().get(0).getId(), target.getVideos().get(0).getId());
		assertEquals(source.getVideos().get(0).getName(), target.getVideos().get(0).getName());
		assertEquals(source.getVideos().get(1).getId(), target.getVideos().get(1).getId());
		assertEquals(source.getKits().get(0).getId(), target.getKits().get(0).getId());
		assertEquals(source.getKits().get(0).getDescription(), target.getKits().get(0).getName());
		assertEquals(source.getKits().get(1).getId(), target.getKits().get(1).getId());
	}

	private static Publication buildPublication() {

		PublicationCategory publicationCategory = new PublicationCategory();
		publicationCategory.setId(123);
		publicationCategory.setName("New Publication Category Name");

		Range range1 = new Range();
		range1.setId(1);
		range1.setName("2023 T3 Ranger");

		Product product1 = new Product();
		product1.setId(1);
		product1.setName("Transmission Module");
		product1.setRange(range1);

		Viewable viewable1 = new Viewable();
		viewable1.setId(456);
		viewable1.setName("Transmission System");
		viewable1.setProduct(product1);

		Product product2 = new Product();
		product2.setId(2);
		product2.setName("Transmission Module 4");
		product2.setRange(range1);

		Viewable viewable2 = new Viewable();
		viewable2.setId(789);
		viewable2.setName("Transmission System");
		viewable2.setProduct(product2);

		Image coverImage = new Image();
		coverImage.setId(1);
		coverImage.setLocationUrl("https://cover.image.url");

		TechDoc techDoc1 = new TechDoc();
		techDoc1.setId(1);
		techDoc1.setName("tech doc 1");
		techDoc1.setDescription("New Tech Doc Description");

		TechDoc techDoc2 = new TechDoc();
		techDoc2.setId(2);
		techDoc2.setName("tech doc 2");
		techDoc2.setDescription("New Tech Doc Description");

		Video video1 = new Video();
		video1.setId(1);
		video1.setName("Video title");
		video1.setUrl("https://video.url");
		video1.setManufacturerId(1);

		Video video2 = new Video();
		video2.setId(2);
		video2.setName("Video title");
		video2.setUrl("https://video.url");
		video2.setManufacturerId(1);

		Kit kit1 = new Kit();
		kit1.setId(1);
		kit1.setDescription("Kit 1");

		Kit kit2 = new Kit();
		kit2.setId(2);
		kit2.setDescription("Kit 2");

		Publication source = new Publication();
		source.setCreatedDate(new Timestamp(Calendar.getInstance().getTime().getTime()));
		source.setId(1);
		source.setName("New Publication Name");
		source.setSerialNumber("123-5678-2");
		source.setStatus(ManualStatus.Status.PUBLISHED);
		source.setPublicationCategory(publicationCategory);
		source.setFeaturedViewableId(456);
		source.setViewables(new ArrayList<Viewable>(){{add(viewable1); add(viewable2);}});
		source.setCoverImage(coverImage);
		source.setTechDocs(new ArrayList<TechDoc>(){{add(techDoc1); add(techDoc2);}});
		source.setVideos(new ArrayList<Video>(){{add(video1); add(video2);}});
		source.setKits(new ArrayList<Kit>(){{add(kit1); add(kit2);}});
		return source;
	}
}
