package co.cadshare.modelMgt.publications.adapters.database;

import co.cadshare.modelMgt.models.adapters.database.ModelsUserEntity;
import co.cadshare.modelMgt.models.core.model.AutodeskStatus;
import co.cadshare.modelMgt.models.core.model.FileType;
import co.cadshare.modelMgt.models.core.model.TranslateType;
import io.hypersistence.utils.hibernate.type.json.JsonType;
import lombok.Data;
import lombok.ToString;
import org.hibernate.annotations.Type;
import org.hibernate.annotations.TypeDef;

import javax.persistence.*;
import java.sql.Timestamp;
import java.util.List;

@Data
@Entity
@Table(name="model")
@TypeDef(
        name = "json",
        typeClass = JsonType.class
)
public class PublicationsModelEntity {

    @Id
    @GeneratedValue (strategy = GenerationType.IDENTITY)
    @Column(name="modelid")
    private Integer modelId;

    @Column(name="modelname")
    private String modelName;

	@ManyToOne
	@JoinColumn(name = "machineid")
	private PublicationsProductEntity product;

	@ManyToMany(mappedBy = "models")
	private List<PublicationsPublicationEntity> publications;

	@Column(name="archived")
	private boolean deleted;

	@Column(name = "createdbyuserid")
	private Integer createdByUserId;

	@Column(name="modifiedbyuserid")
	private Integer modifiedByUserId;

	@Column(name="createddate")
	private Timestamp createdDate;

	@Column(name="modifieddate")
	private Timestamp modifiedDate;

	@Column(name="issetupcomplete")
	private boolean isSetupComplete;

	private int retries;
}

