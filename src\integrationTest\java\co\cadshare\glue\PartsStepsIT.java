package co.cadshare.glue;

import co.cadshare.shared.core.manufacturer.subentity.ManufacturerSubEntity;
import co.cadshare.utils.ObjectUtilsExtension;
import io.cucumber.datatable.DataTable;
import io.cucumber.java.ParameterType;
import io.cucumber.java.en.And;
import io.cucumber.java.en.Given;
import io.cucumber.java.en.Then;
import io.cucumber.java.en.When;
import io.restassured.response.Response;
import lombok.experimental.ExtensionMethod;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

import static org.junit.Assert.*;

@ExtensionMethod(ObjectUtilsExtension.class)
public class PartsStepsIT extends BasicStepsIT {

    @Given("a {listOfStrings} I want to purchase are available")
    public void aListOfPartsIWantToPurchaseAreAvailable(List<String> listOfParts) {
        System.out.println(listOfParts);
        //challenge here is to get part from part numbers (part search only works on master parts)
        //good chance that assumptions to be made on selection of part from model
        //or masterpart equivalent exists
    }
}