package co.cadshare.glue;

import co.cadshare.utils.ObjectUtilsExtension;
import io.cucumber.java.en.Given;
import lombok.experimental.ExtensionMethod;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

@ExtensionMethod(ObjectUtilsExtension.class)
public class PartsStepsIT {


	protected UserIT loggedInUser;
	private final CadshareIT cadshare;

	@Autowired
	public PartsStepsIT(CadshareIT cadshare) {
		this.cadshare = cadshare;
	}

	@Given("a {listOfStrings} I want to purchase are available")
    public void aListOfPartsIWantToPurchaseAreAvailable(List<String> listOfParts) {
        System.out.println(listOfParts);
        //challenge here is to get part from part numbers (part search only works on master parts)
        //good chance that assumptions to be made on selection of part from model
        //or masterpart equivalent exists
    }
}