package co.cadshare.inventory.core;

import co.cadshare.models.core.Model;
import co.cadshare.shared.core.manufacturer.ManufacturerProgress;

public class SparePartUploadProgress extends ManufacturerProgress {

    public SparePartUploadProgress() { super(); }

    public SparePartUploadProgress(int manufacturerId, Model model) {
        super();
        setManufacturerId(manufacturerId);
        setMachineId(model.getMachineId());
        setModelId(model.getModelId());
        setProcess(Process.VIEWABLE_SPARE_PART_UPLOAD);
    }
}
