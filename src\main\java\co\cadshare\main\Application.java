package co.cadshare.main;

import com.blazebit.persistence.integration.view.spring.EnableEntityViews;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.security.config.annotation.method.configuration.EnableGlobalMethodSecurity;

@ComponentScan("co.cadshare")
@EnableAsync
@EnableScheduling
@EnableCaching
@EnableGlobalMethodSecurity(prePostEnabled = true)
@SpringBootApplication
@EnableJpaRepositories(basePackages = "co.cadshare.*.adapters.database")
@EntityScan("co.cadshare.*.adapters.database")
@ComponentScan(basePackages = "co.cadshare.csvBomUpload")
@EnableEntityViews("co.cadshare.*.adapters.database")
public class Application {

    public static void main(String[] args) {
        SpringApplication.run(Application.class, args);
    }

}
