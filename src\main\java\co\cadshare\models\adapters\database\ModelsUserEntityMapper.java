package co.cadshare.models.adapters.database;

import co.cadshare.shared.adapters.database.BaseUserEntity;
import co.cadshare.shared.core.user.User;
import co.cadshare.users.adapters.database.UserSettingsEntityMapper;
import co.cadshare.users.adapters.database.UsersNotificationSubscriptionEntityMapper;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper()
public interface ModelsUserEntityMapper {

    ModelsUserEntityMapper Instance = Mappers.getMapper(ModelsUserEntityMapper.class);

    @Mapping(source = "id", target = "userId")
    @Mapping(source = "userStatus", target = "userStatus")
    User entityToCore(ModelsUserEntity entity);

    @Mapping(source = "userId", target = "id")
    @Mapping(source = "userStatus", target = "userStatus")
    ModelsUserEntity coreToEntity(User core);

    List<User> entitiesToCores(List<ModelsUserEntity> entities);

    List<ModelsUserEntity> coresToEntities(List<User> cores);
}