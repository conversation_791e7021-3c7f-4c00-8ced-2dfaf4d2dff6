package co.cadshare.modelMgt.publications.adapters.database;

import lombok.Data;

import javax.persistence.*;
import java.sql.Timestamp;

@Data
@Entity
@Table(name="machine")
public class PublicationsProductEntity {

    @Id
    @GeneratedValue (strategy = GenerationType.IDENTITY)
    @Column(name="machineid")
    private Integer id;

    private String name;

    @ManyToOne
    @JoinColumn(name="rangeid")
    private PublicationsRangeEntity range;

    @Column(name="archived")
    private boolean deleted;

    @Column(name="createdbyuserid")
    private Integer createdByUserId;

    @Column(name="modifiedbyuserid")
    private Integer modifiedByUserId;

    @Column(name="createddate")
    private Timestamp createdDate;

    @Column(name="modifieddate")
    private Timestamp modifiedDate;

    @Column(name="thumbnailurl")
    private String thumbnailUrl;
}

