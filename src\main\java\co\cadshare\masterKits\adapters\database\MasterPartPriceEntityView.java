package co.cadshare.masterKits.adapters.database;

import co.cadshare.shared.adapters.database.MasterPartPriceEntity;
import com.blazebit.persistence.view.EntityView;
import com.blazebit.persistence.view.IdMapping;
import com.blazebit.persistence.view.Mapping;

@EntityView(MasterPartPriceEntity.class)
public interface MasterPartPriceEntityView {
    @IdMapping
    Integer getId();

    @Mapping("price")
    Float getValue();

    @Mapping("priceListIdentifier.id")
    int getPriceListIdentifierId();

    @Mapping("priceListIdentifier.currency.symbol")
    String getCurrencySymbol();
}
