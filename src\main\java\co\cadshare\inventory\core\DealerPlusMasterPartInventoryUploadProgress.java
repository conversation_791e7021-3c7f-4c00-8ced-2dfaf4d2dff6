package co.cadshare.inventory.core;

import co.cadshare.shared.core.manufacturer.ManufacturerProgress;

public class DealerPlusMasterPartInventoryUploadProgress extends ManufacturerProgress {

    public DealerPlusMasterPartInventoryUploadProgress() { super(); }

    public DealerPlusMasterPartInventoryUploadProgress(int manufacturerId, int dealerPlusSubEntityId) {
        super();
        setManufacturerId(manufacturerId);
        setDealerPlusSubEntityId(dealerPlusSubEntityId);
        setProcess(Process.MASTERPART_INVENTORY_UPLOAD);
    }
}
