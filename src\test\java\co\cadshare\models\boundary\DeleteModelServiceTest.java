package co.cadshare.models.boundary;

import co.cadshare.aspects.logging.ServiceLoggingAspect;
import co.cadshare.shared.core.user.User;
import co.cadshare.models.core.Model;
import com.flextrade.jfixture.annotations.Fixture;
import com.flextrade.jfixture.rules.FixtureRule;
import org.junit.Before;
import org.junit.Rule;
import org.junit.Test;
import org.mockito.MockitoAnnotations;
import org.junit.runner.RunWith;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import static org.mockito.Mockito.*;

@EnableAspectJAutoProxy
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(classes = {DeleteModelService.class, ServiceLoggingAspect.class})
public class DeleteModelServiceTest {

    @MockBean
    private ModelCommandPort commandPort;
    @MockBean
    private ModelQueryPort queryPort;
    @Autowired
    private DeleteModelService out;
    private User user;
    @Fixture
    private Model model;
    @Fixture
    private Model errorModel;

    @Rule
    public FixtureRule fr = FixtureRule.initFixtures();

    @Before
    public void Before() {
        MockitoAnnotations.initMocks(this);
        user = new User() {{
            setUserId(1);
            setFirstName("firstName");
            setLastName("lastName");
        }};
    }

    @Test
    public void DeleteModelSuccess() throws Exception {
        Integer returnVal = Integer.valueOf(1);
        when(queryPort.get(returnVal)).thenReturn(model);
        doNothing().when(commandPort).delete(user, model);
        out.delete(user, returnVal);
    }

    @Test(expected = RuntimeException.class)
    public void DeleteModelFailureException() throws Exception {
        Integer returnVal = Integer.valueOf(2);
        when(queryPort.get(returnVal)).thenReturn(errorModel);
        doThrow(new RuntimeException("terrible")).when(commandPort).delete(user, errorModel);
        out.delete(user, returnVal);
    }

}
