package co.cadshare.modelMgt.models.adapters.api.ext;

import co.cadshare.modelMgt.models.core.Model;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;
import java.util.List;

@Mapper
public interface ModelMapper {

    ModelMapper Instance = Mappers.getMapper(ModelMapper.class);

    @Mapping(source="postRequestDto.name", target="modelName")
    @Mapping(source="postRequestDto.fileName", target="originalFilename")
    @Mapping(source="postRequestDto.autodeskUrn", target="autodeskUrn")
    @Mapping(source="postRequestDto.fileType", target="fileType")
    @Mapping(source="postRequestDto.topLevelAssembly", target="topLevelAssembly")
    @Mapping(source="productId", target="machineId")
    Model postRequestDtoToModel(PostModelRequestDto postRequestDto, int rangeId, int productId);

    Model putRequestDtoToModel(PutModelRequestDto putRequestDto);

    @Mapping(source="modelId", target="id")
    @Mapping(source="modelName", target="name")
    @Mapping(source="autodeskStatus", target="cadshareStatus")
    GetModelResponseDto ModelToGetResponseDto(Model model);

    List<GetModelResponseDto> ModelToGetListResponseDto(List<Model> models);
}
