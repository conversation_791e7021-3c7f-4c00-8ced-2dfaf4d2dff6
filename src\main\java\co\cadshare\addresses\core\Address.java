package co.cadshare.addresses.core;

import java.sql.Timestamp;

import co.cadshare.utils.ObjectUtilsExtension;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import lombok.experimental.ExtensionMethod;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.lang.Nullable;

@Data
@ExtensionMethod(ObjectUtilsExtension.class)
public class Address {
    protected final String errorTemplate = "%s cannot be null or empty";
    private int id;
    private String companyName;
    private String addressLine1;
    private String addressLine2;
    private String city;
    private String state;
    private String postcode;
    private String country;
    private boolean archived;
    private Timestamp createdDate;
    private Integer createdByUserId;
    private Timestamp modifiedDate;
    private Integer modifiedByUserId;

    @Nullable
    private boolean taxExempt;
    @Nullable
    private String taxCode;

	@JsonIgnore
	protected final Logger logger = LoggerFactory.getLogger(getClass());

    public boolean hasAddressLine2() {
        return (this.getAddressLine2() != null && !this.getAddressLine2().isEmpty());
    }

	@JsonIgnore
	public boolean isValid() {

		String errorElement = validateAttributes();
		if(errorElement.isNotNull()) {
            String error = String.format(errorTemplate, errorElement);
	        logger.error(String.format("Address failed CADshare address validation, id [ %s ] | Cause: %s", id, error));
        }
		return errorElement.isNull();
    }

	protected String validateAttributes() {
		String errorElement = null;
		if(addressLine1.isNull())
		    errorElement = "addressLine1";
		if(city.isNull())
		    errorElement = "city";
		if(state.isNull())
		    errorElement = "state";
		if(postcode.isNull())
		    errorElement = "postcode";
		if(country.isNull())
		    errorElement = "country";
		return errorElement;
	}

    public void updateAddressDetails(Address addressChanges) {
        setAddressLine1(addressChanges.getAddressLine1());
        setAddressLine2(addressChanges.getAddressLine2());
        setCity(addressChanges.getCity());
        setState(addressChanges.getState());
        setPostcode(addressChanges.getPostcode());
        setCountry(addressChanges.getCountry());
        setArchived(false);
    }

}

