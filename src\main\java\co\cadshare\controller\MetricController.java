package co.cadshare.controller;

import co.cadshare.aspects.access.languages.CanUseLanguage;
import co.cadshare.shared.core.Language;
import co.cadshare.shared.core.manufacturer.subentity.ManufacturerSubEntity;
import co.cadshare.domainmodel.metric.*;
import co.cadshare.shared.core.user.User;
import co.cadshare.services.MetricService;
import co.cadshare.services.PermissionsService;
import co.cadshare.utils.ObjectUtilsExtension;
import co.cadshare.utils.PeriodHelper;
import lombok.experimental.ExtensionMethod;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.List;

@Controller
@ExtensionMethod(ObjectUtilsExtension.class)
@PreAuthorize("hasRole('Dashboard')")
@RequestMapping("/dashboard")
public class MetricController {

  private static final Logger logger = LoggerFactory.getLogger(MetricController.class);

  @Autowired
  private MetricService metricService;

  @Autowired
  private PermissionsService permissionsService;

  @Autowired
  private PeriodHelper periodHelper;

  @PreAuthorize("hasRole('ROLE_MANUFACTURER') and #currentUser.manufacturerId == #manufacturerId")
    @GetMapping(value = "/{manufacturerId}/totalEnquiries")
    public HttpEntity<MetricComparision> getTotalEnquiries(@AuthenticationPrincipal User currentUser, @PathVariable int manufacturerId,
                                                 @RequestParam(value = "manufacturerSubEntityId", required = false) Integer manufacturerSubEntityId,
                                                 @RequestParam(value = "period", required = false) Period period,
                                                 @RequestParam(value = "customStart", required = false) @DateTimeFormat(pattern = "dd/MM/yyyy") Date customStartDate,
                                                 @RequestParam(value = "customEnd", required = false) @DateTimeFormat(pattern = "dd/MM/yyyy") Date customEndDate,
                                                 @RequestParam(value = "currency", required = false) String currency) throws Exception {
      if (period == null) {
          period = Period.ALL_TIME;
      }

      MetricTimestamps metricTimestamps = periodHelper.calculateTimestamps(period, customStartDate, customEndDate);
      try {
            if (manufacturerSubEntityId != null) {
                hasAdminPermissionsForSubEntity(currentUser, manufacturerSubEntityId);
            }
        } catch (Exception ex) {
            return new ResponseEntity<>(null, HttpStatus.FORBIDDEN);
        }

      MetricComparision enquiryCount = metricService.getTotalEnquiries(manufacturerId, manufacturerSubEntityId, period, metricTimestamps, currency);
        return new ResponseEntity<>(enquiryCount, HttpStatus.OK);
    }

    @PreAuthorize("hasRole('ROLE_MANUFACTURER') and #currentUser.manufacturerId == #manufacturerId")
    @GetMapping(value = "/{manufacturerId}/totalQuotes")
    public HttpEntity<MetricComparision> totalQuotes(@AuthenticationPrincipal User currentUser, @PathVariable int manufacturerId,
                                                           @RequestParam(value = "manufacturerSubEntityId", required = false) Integer manufacturerSubEntityId,
                                                           @RequestParam(value = "period", required = false) Period period,
                                                           @RequestParam(value = "customStart", required = false) @DateTimeFormat(pattern = "dd/MM/yyyy") Date customStartDate,
                                                           @RequestParam(value = "customEnd", required = false) @DateTimeFormat(pattern = "dd/MM/yyyy") Date customEndDate,
                                                           @RequestParam(value = "currency", required = false) String currency) throws Exception {
        if (period == null) {
            period = Period.ALL_TIME;
        }

        MetricTimestamps metricTimestamps = periodHelper.calculateTimestamps(period, customStartDate, customEndDate);
        try {
            if (manufacturerSubEntityId != null) {
                hasAdminPermissionsForSubEntity(currentUser, manufacturerSubEntityId);
            }
        } catch (Exception ex) {
            return new ResponseEntity<>(null, HttpStatus.FORBIDDEN);
        }

        MetricComparision quoteCount = metricService.getTotalQuotes(manufacturerId, manufacturerSubEntityId, period, metricTimestamps, currency);
        return new ResponseEntity<>(quoteCount, HttpStatus.OK);
    }

    @PreAuthorize("hasRole('ROLE_MANUFACTURER') and #currentUser.manufacturerId == #manufacturerId")
    @GetMapping(value = "/{manufacturerId}/totalOrders")
    public HttpEntity<MetricComparision> getTotalOrders(@AuthenticationPrincipal User currentUser, @PathVariable int manufacturerId,
                                                           @RequestParam(value = "manufacturerSubEntityId", required = false) Integer manufacturerSubEntityId,
                                                           @RequestParam(value = "period", required = false) Period period,
                                                           @RequestParam(value = "customStart", required = false) @DateTimeFormat(pattern = "dd/MM/yyyy") Date customStartDate,
                                                           @RequestParam(value = "customEnd", required = false) @DateTimeFormat(pattern = "dd/MM/yyyy") Date customEndDate,
                                                           @RequestParam(value = "currency", required = false) String currency) throws Exception {
        //TOTAL ORDERS = Orders from Purchase order entered onwards
        if (period == null) {
            period = Period.ALL_TIME;
        }

        MetricTimestamps metricTimestamps = periodHelper.calculateTimestamps(period, customStartDate, customEndDate);
        try {
            if (manufacturerSubEntityId != null) {
                hasAdminPermissionsForSubEntity(currentUser, manufacturerSubEntityId);
            }
        } catch (Exception ex) {
            return new ResponseEntity<>(null, HttpStatus.FORBIDDEN);
        }

        MetricComparision orderCount = metricService.getTotalOrders(manufacturerId, manufacturerSubEntityId, period, metricTimestamps, currency);
        return new ResponseEntity<>(orderCount, HttpStatus.OK);
    }

    @PreAuthorize("hasRole('ROLE_MANUFACTURER') and #currentUser.manufacturerId == #manufacturerId")
    @GetMapping(value = "/{manufacturerId}/conversionRate")
    public HttpEntity<MetricComparision> getConversionRate(@AuthenticationPrincipal User currentUser, @PathVariable int manufacturerId,
                                                        @RequestParam(value = "manufacturerSubEntityId", required = false) Integer manufacturerSubEntityId,
                                                        @RequestParam(value = "period", required = false) Period period,
                                                        @RequestParam(value = "customStart", required = false) @DateTimeFormat(pattern = "dd/MM/yyyy") Date customStartDate,
                                                        @RequestParam(value = "customEnd", required = false) @DateTimeFormat(pattern = "dd/MM/yyyy") Date customEndDate,
                                                        @RequestParam(value = "currency", required = false) String currency) throws Exception {
        //TOTAL ORDERS = Orders from Purchase order entered onwards
        if (period == null) {
            period = Period.ALL_TIME;
        }

        MetricTimestamps metricTimestamps = periodHelper.calculateTimestamps(period, customStartDate, customEndDate);
        try {
            if (manufacturerSubEntityId != null) {
                hasAdminPermissionsForSubEntity(currentUser, manufacturerSubEntityId);
            }
        } catch (Exception ex) {
            return new ResponseEntity<>(null, HttpStatus.FORBIDDEN);
        }

        MetricComparision conversionRate = metricService.getConversionRate(manufacturerId, manufacturerSubEntityId, period, metricTimestamps, currency);
        return new ResponseEntity<>(conversionRate, HttpStatus.OK);
    }

    @PreAuthorize("hasRole('ROLE_MANUFACTURER') and #currentUser.manufacturerId == #manufacturerId")
    @GetMapping(value = "/{manufacturerId}/quoteValue")
    public HttpEntity<MetricComparision> getValueOfQuotes(@AuthenticationPrincipal User currentUser, @PathVariable int manufacturerId,
                                                @RequestParam(value = "manufacturerSubEntityId", required = false) Integer manufacturerSubEntityId,
                                                @RequestParam(value = "period", required = false) Period period,
                                                @RequestParam(value = "customStart", required = false) @DateTimeFormat(pattern = "dd/MM/yyyy") Date customStartDate,
                                                @RequestParam(value = "customEnd", required = false) @DateTimeFormat(pattern = "dd/MM/yyyy") Date customEndDate,
                                                @RequestParam(value = "currency", required = false) String currency) throws Exception {

        if (period == null) {
            period = Period.ALL_TIME;
        }

        MetricTimestamps metricTimestamps = periodHelper.calculateTimestamps(period, customStartDate, customEndDate);

        try {
            if (manufacturerSubEntityId != null) {
                hasAdminPermissionsForSubEntity(currentUser, manufacturerSubEntityId);
            }
        } catch (Exception ex) {
            return new ResponseEntity<>(null, HttpStatus.FORBIDDEN);
        }

        MetricComparision quoteValue = metricService.getValueOfQuotes(manufacturerId, manufacturerSubEntityId, period, metricTimestamps, currency);
        return new ResponseEntity<>(quoteValue, HttpStatus.OK);
    }

    @PreAuthorize("hasRole('ROLE_MANUFACTURER') and #currentUser.manufacturerId == #manufacturerId")
    @GetMapping(value = "/{manufacturerId}/orderValue")
    public HttpEntity<MetricComparision> getValueOfOrders(@AuthenticationPrincipal User currentUser, @PathVariable int manufacturerId,
                                                          @RequestParam(value = "manufacturerSubEntityId", required = false) Integer manufacturerSubEntityId,
                                                          @RequestParam(value = "period", required = false) Period period,
                                                          @RequestParam(value = "customStart", required = false) @DateTimeFormat(pattern = "dd/MM/yyyy") Date customStartDate,
                                                          @RequestParam(value = "customEnd", required = false) @DateTimeFormat(pattern = "dd/MM/yyyy") Date customEndDate,
                                                          @RequestParam(value = "currency", required = false) String currency) throws Exception {

        if (period == null) {
            period = Period.ALL_TIME;
        }

        MetricTimestamps metricTimestamps = periodHelper.calculateTimestamps(period, customStartDate, customEndDate);
        try {
            if (manufacturerSubEntityId != null) {
                hasAdminPermissionsForSubEntity(currentUser, manufacturerSubEntityId);
            }
        } catch (Exception ex) {
            return new ResponseEntity<>(null, HttpStatus.FORBIDDEN);
        }

        MetricComparision quoteValue = metricService.getValueOfOrders(manufacturerId, manufacturerSubEntityId, period, metricTimestamps, currency);
        return new ResponseEntity<>(quoteValue, HttpStatus.OK);
    }

    @PreAuthorize("hasRole('ROLE_MANUFACTURER') and #currentUser.manufacturerId == #manufacturerId")
    @GetMapping(value = "/{manufacturerId}/mostActiveCustomers")
    public HttpEntity<List<ManufacturerSubEntity>> getMostActiveCustomers(@AuthenticationPrincipal User currentUser, @PathVariable int manufacturerId,
                                                                          @RequestParam(value = "period", required = false) Period period,
                                                                          @RequestParam(value = "limit", required = false) Integer limit,
                                                                          @RequestParam(value = "customStart", required = false) @DateTimeFormat(pattern = "dd/MM/yyyy") Date customStartDate,
                                                                          @RequestParam(value = "customEnd", required = false) @DateTimeFormat(pattern = "dd/MM/yyyy") Date customEndDate) throws Exception {
        if (period == null) {
            period = Period.ALL_TIME;
        }
        MetricTimestamps metricTimestamps = periodHelper.calculateTimestamps(period, customStartDate, customEndDate);

        List<ManufacturerSubEntity> activeCustomers = metricService.getMostActiveCustomers(manufacturerId, period, metricTimestamps, limit);
        return new ResponseEntity<>(activeCustomers, HttpStatus.OK);
    }


    @PreAuthorize("hasRole('ROLE_MANUFACTURER') and #currentUser.manufacturerId == #manufacturerId")
    @GetMapping(value = "/{manufacturerId}/customersMostOrders")
    public HttpEntity<List<ManufacturerSubEntity>> getCustomersWithMostOrders(@AuthenticationPrincipal User currentUser, @PathVariable int manufacturerId,
                                                                       @RequestParam(value = "period", required = false) Period period,
                                                                       @RequestParam(value = "limit", required = false) Integer limit,
                                                                       @RequestParam(value = "customStart", required = false) @DateTimeFormat(pattern = "dd/MM/yyyy") Date customStartDate,
                                                                       @RequestParam(value = "customEnd", required = false) @DateTimeFormat(pattern = "dd/MM/yyyy") Date customEndDate,
                                                                       @RequestParam(value = "currency", required = false) String currency) throws Exception {
        if (period == null) {
            period = Period.ALL_TIME;
        }
        MetricTimestamps metricTimestamps = periodHelper.calculateTimestamps(period, customStartDate, customEndDate);

        List<ManufacturerSubEntity> activeCustomers = metricService.getCustomersWithMostOrders(manufacturerId, period, metricTimestamps, limit, currency);
        return new ResponseEntity<>(activeCustomers, HttpStatus.OK);
    }

    @PreAuthorize("hasRole('ROLE_MANUFACTURER') and #currentUser.manufacturerId == #manufacturerId")
    @GetMapping(value = "/{manufacturerId}/mostOrderedParts")
    @CanUseLanguage
    public HttpEntity<List<MetricPart>> getMostOrderedParts(@AuthenticationPrincipal User currentUser,
                                                      @PathVariable int manufacturerId,
                                                      @RequestParam(value = "manufacturerSubEntityId", required = false) Integer manufacturerSubEntityId,
                                                      @RequestParam(value = "period", required = false) Period period,
                                                      @RequestParam(value = "limit", required = false) Integer limit,
                                                      @RequestParam(value = "language", required = false) Language language,
                                                      @RequestParam(value = "customStart", required = false) @DateTimeFormat(pattern = "dd/MM/yyyy") Date customStartDate,
                                                      @RequestParam(value = "customEnd", required = false) @DateTimeFormat(pattern = "dd/MM/yyyy") Date customEndDate,
                                                      @RequestParam(value = "currency", required = false) String currency) throws Exception {

	    if (period == null) period = Period.ALL_TIME;
        MetricTimestamps metricTimestamps = periodHelper.calculateTimestamps(period, customStartDate, customEndDate);
        try {
            if (manufacturerSubEntityId != null) {
                hasAdminPermissionsForSubEntity(currentUser, manufacturerSubEntityId);
            }
        } catch (Exception ex) {
            return new ResponseEntity<>(null, HttpStatus.FORBIDDEN);
        }
	    Integer languageId = language != null ? currentUser.findLanguage(language).getLanguageId() : null;
        List<MetricPart> orderedParts = metricService.getMostOrderedParts(manufacturerId, manufacturerSubEntityId, period, metricTimestamps,
                limit, languageId, currentUser.obtainDefaultLanguage(), currency);
        return new ResponseEntity<>(orderedParts, HttpStatus.OK);
    }


    @PreAuthorize("hasRole('ROLE_MANUFACTURER') and #currentUser.manufacturerId == #manufacturerId")
    @GetMapping(value = "/{manufacturerId}/chart/orderValue")
    public HttpEntity<ChartResponse> getValueOfOrdersChart(@AuthenticationPrincipal User currentUser, @PathVariable int manufacturerId,
                                                           @RequestParam(value = "manufacturerSubEntityId", required = false) Integer manufacturerSubEntityId,
                                                           @RequestParam(value = "period", required = false) Period period,
                                                           @RequestParam(value = "currency", required = false) String currency) throws Exception {

        if (period != Period.LAST_6_MONTHS && period != Period.LAST_YEAR) {
            return new ResponseEntity<>(null, HttpStatus.valueOf("Invalid Date range. Please select 6 months or last year."));
        }

        try {
            if (manufacturerSubEntityId != null) {
                hasAdminPermissionsForSubEntity(currentUser, manufacturerSubEntityId);
            }
        } catch (Exception ex) {
            return new ResponseEntity<>(null, HttpStatus.FORBIDDEN);
        }

        ChartResponse valueOfOrders = metricService.getValueOfOrdersChart(manufacturerId, manufacturerSubEntityId, period, currency);
        return new ResponseEntity<>(valueOfOrders, HttpStatus.OK);
    }

    private boolean hasAdminPermissionsForSubEntity(User currentUser, int manufacturerSubEntityId) throws Exception {
        return permissionsService.hasAdminPermissionsForSubEntity(currentUser, manufacturerSubEntityId);
    }
}
