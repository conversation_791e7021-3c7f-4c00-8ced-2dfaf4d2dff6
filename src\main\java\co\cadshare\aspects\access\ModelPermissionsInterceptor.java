package co.cadshare.aspects.access;

import co.cadshare.shared.core.user.User;
import co.cadshare.services.PermissionsService;
import java.io.IOException;
import java.net.URI;
import java.net.URISyntaxException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.servlet.handler.HandlerInterceptorAdapter;

@Slf4j
public class ModelPermissionsInterceptor extends HandlerInterceptorAdapter {

    private static final int DEFAULT_MODEL_ID_PATH_POSITION = 2;

    private PermissionsService permissionsService;

    public ModelPermissionsInterceptor(PermissionsService permissionsService) {
        this.permissionsService = permissionsService;
    }

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws IOException {
        User user = (User) SecurityContextHolder.getContext().getAuthentication().getPrincipal();

        if (user.getUserId() != 0) {

            URI uri;
            try {
                uri = new URI(request.getRequestURI());
            } catch (URISyntaxException e) {
                log.error("Couldn't parse the URI: " + request.getRequestURI());
                throw new IllegalStateException("Couldn't read request URI.");
            }

            String[] segments = uri.getPath().split("/");
            int positionOfModelId = findModelIdPosition(segments);
            Integer modelId = Integer.parseInt(segments[positionOfModelId]); // assumes path like [/model/{modelId}/*]

            boolean hasPermission = false;
            try {
                hasPermission = permissionsService.userHasPermissionsToViewModel(user.getUserId(), modelId);
            } catch (Exception e) {
                //I have to do this since the permissionsService doesn't return false :(
            }

            if (hasPermission) {
                log.debug("User {} has permissions to access model with ID {}", user.getFirstName() + " " + user.getLastName(), modelId);
                return true;
            }
        }
        response.sendError(403, "Permission denied.");
        return false;
    }

    private int findModelIdPosition(String[] segments) {
        for (int i = 0; i < segments.length; i++) {
            if (segments[i].equals("model")) {
                return i + 1;
            }
        }
        return DEFAULT_MODEL_ID_PATH_POSITION;
    }
}
