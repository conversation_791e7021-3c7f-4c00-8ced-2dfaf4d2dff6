package co.cadshare.modelMgt.publications.adapters.database;

import co.cadshare.shared.adapters.database.BaseUserEntity;
import co.cadshare.shared.core.user.User;
import co.cadshare.users.adapters.database.UsersNotificationSubscriptionEntityMapper;
import co.cadshare.users.adapters.database.UserSettingsEntityMapper;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper(uses = {UserSettingsEntityMapper.class, UsersNotificationSubscriptionEntityMapper.class})
public interface PublicationsUserEntityMapper {

    PublicationsUserEntityMapper Instance = Mappers.getMapper(PublicationsUserEntityMapper.class);

    @Mapping(source = "id", target = "userId")
    @Mapping(source = "userStatus", target = "userStatus")
    User entityToCore(BaseUserEntity entity);

    @Mapping(source = "userId", target = "id")
    @Mapping(source = "userStatus", target = "userStatus")
    PublicationsUserEntity coreToEntity(User core);

    List<User> entitiesToCores(List<PublicationsUserEntity> entities);

    List<PublicationsUserEntity> coresToEntities(List<User> cores);
}