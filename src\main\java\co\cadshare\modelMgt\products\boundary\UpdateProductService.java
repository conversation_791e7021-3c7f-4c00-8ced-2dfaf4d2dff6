package co.cadshare.modelMgt.products.boundary;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.dao.EmptyResultDataAccessException;

import co.cadshare.exceptions.NotFoundException;
import co.cadshare.modelMgt.products.core.Product;
import co.cadshare.shared.core.user.User;
import co.cadshare.aspects.logging.Log;

@Service
public class UpdateProductService {

    private final ProductCommandPort commandPort;
    private final ProductQueryPort queryPort;

    @Autowired
    public UpdateProductService(ProductCommandPort commandPort,
                                    ProductQueryPort queryPort) {
        this.commandPort = commandPort;
        this.queryPort = queryPort;
    }

    @Log
    public void update(User user, Product product) throws Exception {
        try {
            this.queryPort.get(product.getId());
            this.commandPort.update(user, product);
        } catch (EmptyResultDataAccessException ex) {
            throw new NotFoundException("Product does not exist");
        }
    }
}
