package co.cadshare.products.boundary;

import co.cadshare.products.core.Product;
import co.cadshare.shared.boundary.GetService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.util.List;

@Service
public class GetProductService extends GetService<Product, Integer> {

    private ProductQueryPort complexQueryPort;

    @Autowired
    public GetProductService(ProductQueryPort queryPort) {
        super(queryPort);
        this.complexQueryPort = queryPort;
    }

    public List<Product> getProductsForRange(int manufacturerId){
        return this.complexQueryPort.getListForRange(manufacturerId);
    }

}
