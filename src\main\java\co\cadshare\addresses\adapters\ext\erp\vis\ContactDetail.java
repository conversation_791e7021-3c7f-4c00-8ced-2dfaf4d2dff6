package co.cadshare.addresses.adapters.ext.erp.vis;

import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class ContactDetail {
	@JacksonXmlProperty(localName = "TP_ADDRESS_TO_ID")
	private final String tradingPartnerAddressContactCompositeKey;

	@JacksonXmlProperty(localName = "CONTACT_ID")
	private final String contactId;

	@JacksonXmlProperty(localName = "CONTACT_NAME_X")
	private final String contactName;


}
