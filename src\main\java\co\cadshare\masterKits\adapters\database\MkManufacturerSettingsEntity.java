package co.cadshare.masterKits.adapters.database;

import co.cadshare.shared.adapters.database.CurrencyEntity;
import lombok.Data;

import javax.persistence.*;

@Data
@Entity
@Table(name="manufacturersettings")
public class MkManufacturerSettingsEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "manufacturerid")
    private Integer manufacturerId;

    @OneToOne
    @JoinColumn(name="defaultcurrencyid", referencedColumnName="id")
    private CurrencyEntity defaultCurrency;

}
