package co.cadshare.masterKits.adapters.database;

import lombok.Data;

import javax.persistence.*;

@Data
@Entity
@Table(name="parttranslation")
public class MkMasterPartTranslationEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name="id")
    private int id;

    private String description;

    @ManyToOne(cascade=CascadeType.DETACH, fetch=FetchType.LAZY)
    @JoinColumn(name="languageid")
    private MkLanguageEntity language;
}
