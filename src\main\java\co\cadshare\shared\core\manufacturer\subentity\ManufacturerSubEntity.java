/*
 * Copyright 2016 Bell.
 */
package co.cadshare.shared.core.manufacturer.subentity;

import co.cadshare.shared.core.Currency;
import co.cadshare.shared.core.user.settings.ManufacturerSubEntitySettings;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

import java.sql.Timestamp;

@Data
public class ManufacturerSubEntity {

    private int manufacturerSubEntityId;
    private ManufacturerSubEntityType manufacturerSubEntityType;
    private int manufacturerId;
    private Integer parentSubEntityId;
    private Integer priceListIdentifierId;
    private Integer warehouseId;
    private String name;
    private String description;
    private Timestamp createdDate;
    private Timestamp modifiedDate;
    private int modifiedByUserId;
    private int createdByUserId;
    private Timestamp closedDate;
    private int defaultDiscount;
    private Currency currency;

    private String visCustomerCode;

    private ManufacturerSubEntitySettings manufacturerSubEntitySettings;

    public enum ManufacturerSubEntityType {
        DEALER,
        CUSTOMER,
        REGIONAL_OFFICE,
        DEALER_PLUS
    }

    @JsonIgnore
    public boolean isDealer() {
        return manufacturerSubEntityType.equals(ManufacturerSubEntityType.DEALER);
    }

    @JsonIgnore
    public boolean isDealerPlus() {
        return manufacturerSubEntityType.equals(ManufacturerSubEntityType.DEALER_PLUS);
    }

    @JsonIgnore
    public boolean isCustomer() {
        return manufacturerSubEntityType.equals(ManufacturerSubEntityType.CUSTOMER);
    }

    @JsonIgnore
    public boolean hasPriceList() { return priceListIdentifierId != null; }

	@JsonIgnore
	public boolean isDealerPlusCustomer() {
		return isCustomer() && parentSubEntityId != null;
	}

    @JsonIgnore
    public boolean isDealerPlusDealer() {
        return isDealer() && parentSubEntityId != null;
    }

	@JsonIgnore
	public Integer dealerPlusId() {
		if(isDealerPlus()) return getManufacturerSubEntityId();
		if(isDealerPlusCustomer() || isDealerPlusDealer()) return parentSubEntityId;
		return null;
	}
}
