/*
 * Copyright 2016 Bell.
 */
package co.cadshare.modelMgt.models.core.processor.fileproperties;

import org.springframework.beans.factory.InitializingBean;

import java.util.LinkedHashMap;
import java.util.List;

/**
 * TODO(dallanmc) Description of class.
 */
public abstract class AbstractPropertiesProcessor implements FilePropertiesProcessor, InitializingBean {

    public String getPropertyValue(LinkedHashMap properties) {
        List<String> synonyms = getSynonyms();
        if (synonyms != null) {
            for (String synonym:getSynonyms()) {
                Object value = properties.get(synonym);
                if (value != null) {
                    if (value instanceof List)
                        value = ((List<?>) value).get(0);
                    return String.valueOf(value);
                }
            }
        }
        return null;
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        List<String> synonyms = getSynonyms();
        if (synonyms == null ||
                (synonyms != null && synonyms.size() == 1 && synonyms.get(0).startsWith("$"))) {
            throw new IllegalArgumentException("Synonyms for class " + getClass() + " are not initialised");
        }
    }
}
