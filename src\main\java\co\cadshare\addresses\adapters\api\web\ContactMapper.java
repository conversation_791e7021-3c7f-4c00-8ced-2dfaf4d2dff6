package co.cadshare.addresses.adapters.api.web;

import co.cadshare.addresses.core.ContactName;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface ContactMapper {

	ContactMapper Instance = Mappers.getMapper(ContactMapper.class);

	@Mapping(source="contactName", target="name")
	ContactDto coreToDto(ContactName contacts);

	List<ContactDto> coresToDtos(List<ContactName> contacts);

}
