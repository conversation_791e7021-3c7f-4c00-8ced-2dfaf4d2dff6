package co.cadshare.ranges.adapters.database;

import co.cadshare.shared.core.user.User;
import co.cadshare.aspects.logging.ServiceLoggingAspect;
import co.cadshare.ranges.core.Range;
import co.cadshare.ranges.boundary.RangeCommandPort;
import co.cadshare.ranges.boundary.RangeQueryPort;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.MockitoAnnotations;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.*;

@EnableAspectJAutoProxy
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(classes = {RangeDataGateway.class, ServiceLoggingAspect.class})
public class RangeDataGatewayTest {

    @MockBean
    private RangeRepo rangeRepo;
    @MockBean
    private RangeComplexQueryRepo rangeQueryRepo;
    @Autowired
    RangeCommandPort cmdOut;
    @Autowired
    RangeQueryPort queryOut;
    private User user;
    private Range range;
    private RangeEntity rangeEntity;
    private RangeEntity rangeEntityWithId;
    private Range errorRange;
    private RangeEntity errorRangeEntity;

    @Before
    public void Before() {
        MockitoAnnotations.initMocks(this);
        range = buildRange();
        errorRange = buildRange();
        user = new User() {{
            setUserId(1);
            setFirstName("firstName");
            setLastName("lastName");
        }};
        rangeEntity = RangeEntityMapper.Instance.coreToEntity(range);
        errorRangeEntity = RangeEntityMapper.Instance.coreToEntity(errorRange);
        rangeEntityWithId = RangeEntityMapper.Instance.coreToEntity(range);
        rangeEntityWithId.setId(Integer.valueOf(1));
    }

    @Test
    public void CreateRangeSuccess() {
        when(rangeRepo.save(any(RangeEntity.class))).thenReturn(rangeEntityWithId);
        Integer result = cmdOut.create(user, range);
        verify(rangeRepo, times(1)).save(argThat(new RangeEntityMatcher(rangeEntity)));
        assertEquals(1, result);
    }

    @Test(expected = RuntimeException.class)
    public void CreateRangeFailureException() throws Exception {
        when(rangeRepo.save(any(RangeEntity.class))).thenThrow(new RuntimeException("terrible"));
        cmdOut.create(user, errorRange);
    }

    @Test
    public void UpdateRangeSuccess()  throws Exception {
        when(rangeRepo.save(rangeEntity)).thenReturn(rangeEntityWithId);
        cmdOut.update(user, range);
        verify(rangeRepo, times(1)).save(argThat(new RangeEntityMatcher(rangeEntity)));
    }

    @Test(expected = RuntimeException.class)
    public void UpdateRangeFailureException() throws Exception {
        doThrow(new RuntimeException("terrible")).when(rangeRepo).save(any(RangeEntity.class));
        cmdOut.update(user, errorRange);
        verify(rangeRepo, times(1)).save(argThat(new RangeEntityMatcher(errorRangeEntity)));
    }

    @Test
    public void DeleteRangeSuccess()  throws Exception {
        doNothing().when(rangeRepo).delete(rangeEntity);
        cmdOut.delete(user, range);
    }

    @Test(expected = RuntimeException.class)
    public void DeleteRangeFailureException() throws Exception {
        doThrow(new RuntimeException("terrible")).when(rangeRepo).save(any(RangeEntity.class));
        cmdOut.delete(user, errorRange);
        verify(rangeRepo, times(1)).save(argThat(new RangeEntityMatcher(errorRangeEntity)));
    }

    private Range buildRange() {
        Range range = new Range();
        return range;
    }
}

