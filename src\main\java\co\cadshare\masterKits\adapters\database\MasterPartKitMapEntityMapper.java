package co.cadshare.masterKits.adapters.database;

import co.cadshare.masterKits.core.KitMasterPart;
import co.cadshare.masterKits.core.MasterPartDefaultPrice;
import co.cadshare.masterKits.core.PartMasterPart;
import co.cadshare.masterParts.core.MasterPartType;
import co.cadshare.shared.adapters.database.CurrencyEntityMapper;
import co.cadshare.shared.core.Currency;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper(uses={MkMasterPartTranslationEntityMapper.class, MkMasterPartPriceEntityMapper.class})
public interface MasterPartKitMapEntityMapper {

    MasterPartKitMapEntityMapper Instance = Mappers.getMapper(MasterPartKitMapEntityMapper.class);

    @Mapping(source = "masterPartDetail.id", target = "id")
    @Mapping(source="masterPartDetail.partNumber", target="partNumber")
    @Mapping(source="masterPartDetail.manufacturer.id", target="manufacturerId")
    @Mapping(source=".", target="price", qualifiedByName="priceMapping")
    @Mapping(source="masterPartDetail.translations", target="translations")
    @Mapping(source="id", target="masterPartMapId")
    @Mapping(source="masterPartDetail.prices", target="prices")
    KitMasterPart entityToCoreForKit(MkMasterPartKitMapEntity masterPartEntity);

    @Mapping(source="masterPartDetail.id", target = "id")
    @Mapping(source="masterPartDetail.partNumber", target="partNumber")
    @Mapping(source="masterPartDetail.manufacturer.id", target="manufacturerId")
    @Mapping(source=".", target="price", qualifiedByName="priceMapping")
    @Mapping(source="masterPartDetail.translations", target="translations")
    @Mapping(source="quantity", target="quantity")
    @Mapping(source="id", target="masterPartMapId")
    @Mapping(source="masterPartDetail.prices", target="prices")
    PartMasterPart entityToCore(MkMasterPartKitMapEntity masterPartEntity);

    List<PartMasterPart> entitiesToCores(List<MkMasterPartKitMapEntity> entities);

    @Mapping(source="core.id", target = "masterPartDetail.id")
 //   @Mapping(source="kitId", target="kitId")
    @Mapping(source="core.masterPartMapId", target="id")
    @Mapping(source="core.partNumber", target="masterPartDetail.partNumber")
    @Mapping(source="core.manufacturerId", target="masterPartDetail.manufacturer.id")
    @Mapping(source="core", target="masterPartDetail.type", qualifiedByName="determinePartMasterPartType")
    @Mapping(source="core.price.price", target="masterPartDetail.price")
    @Mapping(source="core.translations", target="masterPartDetail.translations")
    MkMasterPartKitMapEntity coreToEntity(PartMasterPart core, int kitId);

    @Mapping(source="core.id", target = "masterPartDetail.id")
  //  @Mapping(source="kitId", target="kitId")
    @Mapping(source="core.masterPartMapId", target="id")
    @Mapping(source="core.partNumber", target="masterPartDetail.partNumber")
    @Mapping(source="core.manufacturerId", target="masterPartDetail.manufacturer.id")
    @Mapping(source="core", target="masterPartDetail.type", qualifiedByName="determineKitMasterPartType")
    @Mapping(source="core.price.price", target="masterPartDetail.price")
    @Mapping(source="core.translations", target="masterPartDetail.translations")
    @Mapping(source="core.prices", target="masterPartDetail.prices")
    MkMasterPartKitMapEntity coreToEntity(KitMasterPart core, int kitId);

    List<MkMasterPartKitMapEntity> coresToEntities(List<PartMasterPart> cores, @Context int kitId);

    @Named("determinePartMasterPartType")
    public static MasterPartType determinePartMasterPartType(PartMasterPart kitMasterPart) {
        return MasterPartType.PART;
    }

    @Named("determineKitMasterPartType")
    public static MasterPartType determineKitMasterPartType(KitMasterPart kitMasterPart) {
        return MasterPartType.KIT;
    }

    @Named("priceMapping")
    public static MasterPartDefaultPrice priceMapping(MkMasterPartKitMapEntity entity){
        if(entity.getMasterPartDetail().getPrice() == null) return null;
        Currency defaultCurrency =
                CurrencyEntityMapper.Instance.entityToCore(entity.getMasterPartDetail().getManufacturer().getSettings().getDefaultCurrency());
        return new MasterPartDefaultPrice() {{
            setPrice(entity.getMasterPartDetail().getPrice());
            setCurrency(defaultCurrency);
        }};
    }

}
