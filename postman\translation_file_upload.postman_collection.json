{"info": {"_postman_id": "93ee12f9-21a5-410c-ad8f-45ba2d661f55", "name": "translation_file_upload", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "300252"}, "item": [{"name": "localhost:5000/translation/", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.H3p40p-5Lq7Dfo1JKV69RduMgY6DgUXrEY7YS7XKKp0", "type": "string"}]}, "method": "POST", "header": [{"key": "b", "value": "", "type": "text", "disabled": true}, {"key": "Accept-<PERSON><PERSON><PERSON>", "value": "", "type": "text", "disabled": true}], "body": {"mode": "formdata", "formdata": [{"key": "file", "type": "file", "src": "/C:/workspaces/rest-webservices/src/test/resources/translations/valid/translation_100000_lines.csv"}]}, "url": {"raw": "localhost:8080/translation/", "host": ["localhost"], "port": "8080", "path": ["translation", ""]}}, "response": []}, {"name": "INVENTORY UPLOAD", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.BB9mBuJGpqJIKz76hJdEb6h-0HumAgLW2mU9Uzw0Tsg", "type": "string"}]}, "method": "POST", "header": [{"key": "b", "value": "", "type": "text", "disabled": true}, {"key": "Accept-<PERSON><PERSON><PERSON>", "value": "", "type": "text", "disabled": true}], "body": {"mode": "formdata", "formdata": [{"key": "file", "type": "file", "src": "/Users/<USER>/Documents/CadShare - Docs/MasterPartUploads/Inventory/INVENTORYUPLOAD Nulls.csv"}]}, "url": {"raw": "localhost:8080/inventory", "host": ["localhost"], "port": "8080", "path": ["inventory"]}}, "response": []}]}