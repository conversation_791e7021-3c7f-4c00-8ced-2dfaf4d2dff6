package co.cadshare.shared.adapters.aws.config;

import com.amazonaws.auth.BasicAWSCredentials;
import com.amazonaws.internal.StaticCredentialsProvider;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.PropertySource;

@Configuration
public class AWSCredentialsProvider {

    @Value("${aws.access.key.id}")
    String accessKeyId;

    @Value("${aws.secret.access.key}")
    String secretAccessKey;

    @Bean
    public StaticCredentialsProvider staticCredentialsProvider() {
        return new StaticCredentialsProvider(new BasicAWSCredentials(
                accessKeyId, secretAccessKey));
    }

}
