package co.cadshare.glue;

import co.cadshare.modelMgt.viewables.adapters.api.web.GetViewableListResponseDto;
import co.cadshare.modelMgt.viewables.adapters.api.web.PostFilterViewablesResponseDto;
import co.cadshare.modelMgt.viewables.adapters.api.web.PostSearchViewablesResponseDto;
import io.cucumber.java.en.And;
import io.cucumber.java.en.Then;
import io.cucumber.java.en.When;
import org.springframework.beans.factory.annotation.Autowired;

import static org.junit.Assert.*;

public class ViewableStepsIT {

	private final CadshareIT cadshare;
	private PostSearchViewablesResponseDto searchResponse;
	private PostFilterViewablesResponseDto filterResponse;

	@Autowired
	public ViewableStepsIT(CadshareIT cadshare) {
		this.cadshare = cadshare;
	}

	@Then("I can view all Viewables belonging to my Manufacturer")
	public void iCanViewAllViewablesBelongingToMyManufacturer() {
		GetViewableListResponseDto viewables = cadshare.loggedInUser().getAllViewables();
		assertNotNull(viewables);
		assertFalse(viewables.getViewables().isEmpty());
	}

	@And("a Viewable with name {} doesn't exist")
	public void aViewableWithNameDoesnTExist(String name) {
		/*
		GetViewableListResponseDto publicationCategories = auth.getLoggedInUser().getPublicationCategoriesList();
		boolean found = publicationCategories.getPublicationCategories().stream()
				.anyMatch(pc -> pc.getName().equals(name));
		assertFalse(found);
		 */
	}

	@When("I create a new Viewable named {}")
	public void iCreateANewViewableNamed(String name) {
	//	auth.getLoggedInUser().createViewable(name);
	}

	@Then("a Viewable with name {} exists")
	public void aNewViewableWithNameNowExists(String viewableName) {
		searchResponse = cadshare.loggedInUser().searchViewablesByName(viewableName);
		assertNotNull(searchResponse);
		assertFalse(searchResponse.getSearchResults().isEmpty());
	}

	@When("I delete the Viewable named {}")
	public void iDeleteTheViewableNamed(String name) {
		/*
		GetViewableListResponseDto publicationCategories = auth.getLoggedInUser().getPublicationCategoriesList();
		Optional<GetViewableListItemResponseDto> found = publicationCategories.getPublicationCategories().stream()
				.filter(pc -> pc.getName().equals(name)).findFirst();
		assertTrue(found.isPresent());
		auth.getLoggedInUser().deleteViewable(found.get().getId().toString());

		 */
	}

	@When("I update the Viewable named {} to {}")
	public void iUpdateTheViewableNamed(String oldName, String newName) {
		/*
		GetViewableListResponseDto publicationCategories = auth.getLoggedInUser().getPublicationCategoriesList();
		Optional<GetViewableListItemResponseDto> found = publicationCategories.getPublicationCategories().stream()
				.filter(pc -> pc.getName().equals(oldName)).findFirst();
		assertTrue(found.isPresent());
		auth.getLoggedInUser().updateViewable(newName, found.get().getId().toString());

		 */
	}

	@When("I get the Viewable named {}")
	public void iGetTheViewableNamed(String name) {
		/*
		GetViewableListResponseDto publicationCategories = auth.getLoggedInUser().getPublicationCategoriesList();
		Optional<GetViewableListItemResponseDto> found = publicationCategories.getPublicationCategories().stream()
				.filter(pc -> pc.getName().equals(name)).findFirst();
		assertTrue(found.isPresent());
		GetViewableResponseDto Viewable = auth.getLoggedInUser().getViewable(found.get().getId().toString());
		assertNotNull(Viewable);
		assertEquals(name, Viewable.getName());

		 */
	}

	@When("I search Viewables by name {}")
	public void iSearchViewablesByName(String viewableName) {
		searchResponse = cadshare.loggedInUser().searchViewablesByName(viewableName);
		assertNotNull(searchResponse);
	}

	@Then("I can view {} Viewables with matching name {} belonging to my Manufacturer")
	public void iCanViewAllViewablesWithMatchingNameBelongingToMyManufacturer(int viewableCount,String viewableName) {
		assertEquals(viewableCount, searchResponse.getSearchResults().size());
		if (viewableCount > 0)
			assertTrue(searchResponse.getSearchResults().get(0).getName().contains(viewableName));
	}

	@When("I filter Viewables by {}")
	public void iFilterViewablesByProduct(int productId) {
		filterResponse = cadshare.loggedInUser().filterViewablesByProduct(productId);
		assertNotNull(filterResponse);
	}

	@Then("I can view {} Viewables with matching product {} belonging to my Manufacturer")
	public void iCanViewViewablesWithMatchingProductBelongingToMyManufacturer(int viewableCount, int productId) {
		assertEquals(viewableCount, filterResponse.getFilterResults().size());
		if (viewableCount > 0)
			assertEquals((int) filterResponse.getFilterResults().get(0).getProduct().getId(), productId);
	}
}
