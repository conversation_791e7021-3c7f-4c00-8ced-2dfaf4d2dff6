package co.cadshare.modelMgt.publications.adapters.database;

import co.cadshare.modelMgt.publications.core.Manual;
import co.cadshare.modelMgt.shared.core.Publication;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface ManualMapper {

    ManualMapper Instance = Mappers.getMapper(ManualMapper.class);

    @Mapping(source = "id", target = "manualId")
    @Mapping(source = "name", target = "manualName")
    Manual publicationToManual(Publication publication);

    @Mapping(source = "manualId", target = "id")
    @Mapping(source = "manualName", target = "name")
    Publication manualToPublication(Manual manual);

    List<Publication> manualsToPublications(List<Manual> manual);
}
