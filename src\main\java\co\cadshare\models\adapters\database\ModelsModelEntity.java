package co.cadshare.models.adapters.database;

import co.cadshare.models.core.model.AutodeskStatus;
import co.cadshare.models.core.model.FileType;
import co.cadshare.models.core.model.TranslateType;
import co.cadshare.publications.adapters.database.PublicationsPartEntity;
import co.cadshare.shared.adapters.database.ProductEntity;
import io.hypersistence.utils.hibernate.type.json.JsonType;
import lombok.Data;
import lombok.ToString;
import org.hibernate.annotations.Type;
import org.hibernate.annotations.TypeDef;

import javax.persistence.*;
import java.sql.Timestamp;
import java.util.List;

@Data
@Entity
@Table(name="model")
@TypeDef(
        name = "json",
        typeClass = JsonType.class
)
public class ModelsModelEntity {

    @Id
    @GeneratedValue (strategy = GenerationType.IDENTITY)
    @Column(name="modelid")
    private Integer modelId;

    @Column(name="modelname")
    private String modelName;

    @Column(name="modeldescription")
    private String modelDescription;

    @ToString.Exclude
    @ManyToMany(cascade = { CascadeType.PERSIST })
    @JoinTable(
            name = "manualmodelmap",
            joinColumns = { @JoinColumn(name = "modelid") },
            inverseJoinColumns = { @JoinColumn(name = "manualid") }
    )
    private List<ModelsPublicationEntity> publications;

    @ToString.Exclude
    @ManyToOne
    @JoinColumn(name = "createdbyuserid")
    private ModelsUserEntity createdBy;

    @ManyToOne
    @JoinColumn(name = "machineid")
    private ProductEntity product;

    @OneToMany(mappedBy = "model")
    private List<PublicationsPartEntity> parts;

    @Enumerated(EnumType.STRING)
    @Column(name="autodeskstatus")
    private AutodeskStatus autodeskStatus;

    @Enumerated(EnumType.STRING)
    @Column(name="filetype")
    private FileType fileType;

    @Enumerated(EnumType.STRING)
    @Column(name="translatetype")
    private TranslateType translateType;

    @Column(name="toplevelassembly")
    private String topLevelAssembly;

    @Column(name="filename")
    private String filename;

    @Column(name="originalfilename")
    private String originalFilename;

    @Column(name="autodeskurn")
    private String autodeskUrn;

    @Column(name="autodeskprogress")
    private String autodeskProgress;

    private Boolean is2d;

    @Type(type = "json")
    @Column(columnDefinition = "jsonb", name="leafnodes")
    private String leafNodes;

    @Column(name="issetupcomplete")
    private Boolean isSetupComplete;

    private int retries;

    @Column(name="reasonforfailure")
    private String reasonForFailure;

    private boolean archived;

    @Column(name="modifiedbyuserid")
    private Integer modifiedByUserId;

    @Column(name="createddate")
    private Timestamp createdDate;

    @Column(name="modifieddate")
    private Timestamp modifiedDate;
}

