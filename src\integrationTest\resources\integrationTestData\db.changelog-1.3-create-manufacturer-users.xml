<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                   http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.0.xsd
">

    <changeSet author="AndyB" id="1.3-integration-test-data-create-manufacturer-users-1">
        <sql>
            INSERT INTO manufacturerusers(userid, manufacturerid)
            VALUES ((select userid
            FROM users
            WHERE emailaddress = '<EMAIL>'),
            (select manufacturerid
            from manufacturer
            where subdomain = 'caterpillar.cadshare.com'));
        </sql>
    </changeSet>

    <changeSet author="AndyB" id="1.3-integration-test-data-create-manufacturer-users-2">
        <sql>
            INSERT INTO manufacturerusers(userid, manufacturerid)
            VALUES ((select userid
            FROM users
            WHERE emailaddress = '<EMAIL>'),
            (select manufacturerid
            from manufacturer
            where subdomain = 'jcb.cadshare.com'));
        </sql>
    </changeSet>

    <changeSet author="AndyB" id="1.3-integration-test-data-create-manufacturer-users-3">
        <sql>
            INSERT INTO manufacturerusers(userid, manufacturerid)
            VALUES ((select userid
            FROM users
            WHERE emailaddress = '<EMAIL>'),
            (select manufacturerid
            from manufacturer
            where subdomain = 'liebherr.cadshare.com'));
        </sql>
    </changeSet>

    <changeSet author="AndyB" id="1.3-integration-test-data-create-manufacturer-users-4">
        <sql>
            INSERT INTO manufacturerusers(userid, manufacturerid)
            VALUES ((select userid
            FROM users
            WHERE emailaddress = '<EMAIL>'),
            (select manufacturerid
            from manufacturer
            where subdomain = 'terex.cadshare.com'));
        </sql>
    </changeSet>
</databaseChangeLog>
