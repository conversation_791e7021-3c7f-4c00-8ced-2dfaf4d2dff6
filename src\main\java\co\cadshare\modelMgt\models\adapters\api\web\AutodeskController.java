package co.cadshare.modelMgt.models.adapters.api.web;

import co.cadshare.modelMgt.models.boundary.AutodeskPort;
import com.autodesk.client.auth.Credentials;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

@Slf4j
@Controller
public class AutodeskController {

    private AutodeskPort autodesk;

    @Autowired
    public AutodeskController(AutodeskPort autodesk) {

        this.autodesk = autodesk;
    }

    @RequestMapping("/token/autodesk")
    @ResponseBody
    public HttpEntity<Credentials> getAutodeskToken() throws Exception {
        Credentials credentials = autodesk.getAutodeskTokenForWebClient();
        return new ResponseEntity<>(credentials, HttpStatus.OK);
    }
}
