package co.cadshare.addresses.adapters.ext.erp.vis;

import co.cadshare.addresses.core.ExternalAddress;
import co.cadshare.addresses.core.ExternalAddressContact;
import co.cadshare.addresses.core.ExternalPurchaser;
import org.mapstruct.Context;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper(uses={ExternalAddressContactMapper.class})
public interface ExternalAddressMapper {

    ExternalAddressMapper Instance = Mappers.getMapper(ExternalAddressMapper.class);

    @Mapping(source="source.tradePartnerAddressId", target="externalRefId")
    @Mapping(source="source.streetAddress1", target="addressLine1")
    @Mapping(source="source.streetAddress2", target="addressLine2")
    @Mapping(source="source.city", target ="city")
    @Mapping(source="source.stateProvince", target="state")
    @Mapping(source="source.zipPostalCode", target="postcode")
    @Mapping(source="source.country", target="country")
    @Mapping(source="manufacturerId", target="manufacturerId")
    @Mapping(source="purchaser.id", target="purchaserId")
    @Mapping(source="source.contactList", target="contactMaps")
    @Mapping(ignore = true, target = "id")
    ExternalAddress externalToCore(AddressDetail source, int manufacturerId, ExternalPurchaser purchaser);

    List<ExternalAddress> externalToCores(List<AddressDetail> source, @Context int manufacturerId, @Context ExternalPurchaser purchaser);

    default ExternalAddress mapContext(AddressDetail source, @Context int manufacturerId, @Context ExternalPurchaser purchaser) {
        return externalToCore(source, manufacturerId, purchaser);
    }

    @Named("mapPurchaser")
    public static int mapPurchaser(ExternalPurchaser purchaser) {
        return purchaser.getId();
    }


}