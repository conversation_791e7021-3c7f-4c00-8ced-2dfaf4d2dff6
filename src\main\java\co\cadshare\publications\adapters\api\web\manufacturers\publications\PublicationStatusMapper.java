package co.cadshare.publications.adapters.api.web.manufacturers.publications;

import co.cadshare.publications.core.ManualStatus;
import org.mapstruct.Mapper;

@Mapper
public abstract class PublicationStatusMapper {
    public String statusToString(ManualStatus.Status status) {
        return status.toString();
    }

    public ManualStatus.Status dtoStringToStatus(String status) {
        return ManualStatus.Status.valueOf(status);
    }
}
