package co.cadshare.domainmodel.security;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import io.swagger.annotations.ApiModelProperty;
import java.sql.Timestamp;
import lombok.Data;

@JsonInclude(Include.NON_NULL)
@Data
public class Watermark {

  private Integer id;
  private String imageUrl;
  private boolean enabled;

  private int manufacturerId;
  
  @ApiModelProperty(hidden = true)
  private Timestamp createdDate;
  @ApiModelProperty(hidden = true)
  private Integer createdByUserId;
  @ApiModelProperty(hidden = true)
  private Timestamp modifiedDate;
  @ApiModelProperty(hidden = true)
  private Integer modifiedByUserId;

}
