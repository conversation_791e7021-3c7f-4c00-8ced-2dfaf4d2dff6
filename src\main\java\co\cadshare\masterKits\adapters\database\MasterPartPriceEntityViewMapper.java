package co.cadshare.masterKits.adapters.database;

import co.cadshare.masterKits.core.MasterKitPriceSearchResult;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface MasterPartPriceEntityViewMapper {

    MasterPartPriceEntityViewMapper Instance = Mappers.getMapper(MasterPartPriceEntityViewMapper.class);

    MasterKitPriceSearchResult entityToCore(MasterPartPriceEntityView entityView);

    List<MasterKitPriceSearchResult> entitiesToCores(List<MasterPartPriceEntityView> entityViews);
}
