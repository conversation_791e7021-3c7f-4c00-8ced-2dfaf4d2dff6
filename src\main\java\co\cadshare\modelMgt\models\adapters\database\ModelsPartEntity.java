package co.cadshare.modelMgt.models.adapters.database;

import lombok.Data;

import javax.persistence.*;

@Data
@Entity
@Table(name = "part")
public class ModelsPartEntity {
    @Id
    @GeneratedValue
    @Column(name = "partid")
    private Integer id;

    @Column(name = "partnumber")
    private String partNumber;

    @ManyToOne
    @JoinColumn(name = "modelid")
    private ModelsModelEntity model;
}
