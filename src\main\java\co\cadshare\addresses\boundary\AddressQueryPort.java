package co.cadshare.addresses.boundary;

import co.cadshare.addresses.core.Address;
import co.cadshare.addresses.core.ContactName;
import co.cadshare.orders.adapters.ext.erp.vis.ExternalAddressContactKeys;

import java.util.List;

public interface AddressQueryPort {
    Address getAddressById(int addressId);

    List<Address> getAddressesForUser(int userId);

    Address getAddressForWareHouse(int warehouseId);

	List<ContactName> getAllNamesForUser(int userId);

	List<ContactName> getAllNamesForAddressForUser(int addressId, int userId);

	ExternalAddressContactKeys getExternalAddressContactKeys(int addressId, int contactId);
}
