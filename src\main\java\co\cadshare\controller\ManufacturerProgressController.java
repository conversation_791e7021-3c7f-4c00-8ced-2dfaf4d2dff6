package co.cadshare.controller;

import co.cadshare.shared.core.manufacturer.ManufacturerProgress;
import co.cadshare.shared.core.manufacturer.ManufacturerProgressResult;
import co.cadshare.shared.core.user.User;
import co.cadshare.services.ManufacturerProgressService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Slf4j
@Controller
@RequestMapping("/manufacturer/progress")
public class ManufacturerProgressController {

  @Autowired
  private ManufacturerProgressService manufacturerProgressService;

  @PreAuthorize("hasRole('ROLE_MANUFACTURER') and #currentUser.manufacturerId == #manufacturerId")
  @RequestMapping(value = "/{manufacturerId}", method = RequestMethod.GET)
  public HttpEntity<List<ManufacturerProgressResult>> getManufacturerProgressTasksByManufacturerId(@AuthenticationPrincipal User currentUser,
                                                                                 @PathVariable int manufacturerId,
                                                                                 @RequestParam(value = "process", required = false) List<ManufacturerProgress.Process> process,
                                                                                 @RequestParam(value = "machineId", required = false) Integer machineId,
                                                                                 @RequestParam(value = "modelId", required = false) Integer modelId) throws Exception {

    log.info("ACCESS: User [{}], getManufacturerProgressTasksById, manufacturer [{}]", currentUser, manufacturerId);
    List<ManufacturerProgressResult> progress = manufacturerProgressService.getManufacturerProgressTasksByManufacturerId(manufacturerId, process, machineId, modelId);

    return new ResponseEntity<>(progress, HttpStatus.OK);
  }

  @PreAuthorize("hasRole('ROLE_MANUFACTURER')")
  @RequestMapping(value = "/delete/{progressId}", method = RequestMethod.DELETE)
  public ResponseEntity<Void> deleteManufacturerProgress(@PathVariable int progressId) {
    manufacturerProgressService.deleteManufacturerProgress(progressId);
    return ResponseEntity.ok().build();
  }
}
