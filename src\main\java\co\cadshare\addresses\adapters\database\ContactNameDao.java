package co.cadshare.addresses.adapters.database;

import co.cadshare.addresses.core.ContactName;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.namedparam.BeanPropertySqlParameterSource;
import org.springframework.jdbc.core.namedparam.MapSqlParameterSource;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.jdbc.support.GeneratedKeyHolder;
import org.springframework.jdbc.support.KeyHolder;
import org.springframework.stereotype.Repository;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Repository
public class ContactNameDao {

  @Autowired
  private NamedParameterJdbcTemplate namedParamJdbcTemplate;

  private final static String SELECT_NAME_BY_ID = "SELECT * FROM contactname WHERE id=:nameId";

  public ContactName getContactNameById(int nameId) {

    Map<String, Object> parameters = new HashMap<String, Object>();
    parameters.put("nameId", nameId);

    ContactName name = (ContactName) namedParamJdbcTemplate.queryForObject(SELECT_NAME_BY_ID, parameters, new BeanPropertyRowMapper<>(ContactName.class));
    return name;
  }

  private final static String GET_NAMES_FOR_USER_ID = "SELECT n.* FROM contactname n INNER JOIN user_contactname_map unm ON unm.contactnameid = n.id WHERE unm.userid = :userId ";

  public List<ContactName> getNamesForUser(int userId) {
    Map<String, Object> parameters = new HashMap<>();
    parameters.put("userId", userId);

    List<ContactName> nameList = namedParamJdbcTemplate.query(GET_NAMES_FOR_USER_ID, parameters, new BeanPropertyRowMapper<>(ContactName.class));

    return nameList;
  }

	private final static String GET_NAMES_FOR_ADDRESS_AND_USER = "SELECT n.* FROM contactname n INNER JOIN user_contactname_map unm ON unm.contactnameid = n.id WHERE unm.userid = :userId ";

	public List<ContactName> getNamesForAddressAndUser(int addressId, int userId) {
		Map<String, Object> parameters = new HashMap<>();
		parameters.put("addressId", addressId);
		parameters.put("userId", userId);

		List<ContactName> nameList = namedParamJdbcTemplate.query(GET_NAMES_FOR_ADDRESS_AND_USER, parameters, new BeanPropertyRowMapper<>(ContactName.class));

		return nameList;
	}

  private static final String CREATE_NAME = "INSERT INTO contactname (contactname) "
      + "VALUES(:contactName)";

  private static final String CREATE_USER_NAME_MAP = "INSERT INTO user_contactname_map (contactnameid, userid) "
      + "VALUES(:contactNameId, :userId)";

  public Integer createContactName(int userId, ContactName name) {

    BeanPropertySqlParameterSource namedParameters = new BeanPropertySqlParameterSource(name);
    KeyHolder keyHolder = new GeneratedKeyHolder();

    namedParamJdbcTemplate.update(CREATE_NAME, namedParameters, keyHolder, new String[] { "id" });
    int nameId = keyHolder.getKey().intValue();

    MapSqlParameterSource mapperParameters = new MapSqlParameterSource();
    mapperParameters.addValue("contactNameId", nameId);
    mapperParameters.addValue("userId", userId);

    namedParamJdbcTemplate.update(CREATE_USER_NAME_MAP, mapperParameters);

    return nameId;
  }
  
  public int updateContactName(int userId, ContactName name) {
    deleteContactName(userId, name.getId());
    int addressId = createContactName(userId, name);
    return addressId;
  }

  private static final String DELETE_NAME= "DELETE FROM contactname where id = :nameId";
  private static final String DELETE_USER_NAME_MAP = "DELETE FROM user_contactname_map where contactnameid = :nameId AND userid = :userId";

  public Boolean deleteContactName(int userId, int nameId) {
    Map namedParameters = new HashMap();
    namedParameters.put("nameId", nameId);
    namedParameters.put("userId", userId);

    namedParamJdbcTemplate.update(DELETE_USER_NAME_MAP, namedParameters);
    namedParamJdbcTemplate.update(DELETE_NAME, namedParameters);

    return true;
  }

  private final static String UPDATE_CONTACT_VISIBILITY_ID = "UPDATE contactname SET viscontactid = :visContactId " +
          "WHERE id = :id";
  public int updateVisibilityInfoByContactNameId(Integer contactNameId, Integer visContactId) throws Exception {
    Map<String,Integer> params = new HashMap<>();
    params.put("id",contactNameId);
    params.put("visContactId",visContactId);
    return namedParamJdbcTemplate.update(UPDATE_CONTACT_VISIBILITY_ID, params);
  }

}
