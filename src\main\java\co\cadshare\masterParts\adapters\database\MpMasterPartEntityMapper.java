package co.cadshare.masterParts.adapters.database;

import co.cadshare.masterParts.core.MasterPart;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface MpMasterPartEntityMapper {

    MpMasterPartEntityMapper Instance = Mappers.getMapper(MpMasterPartEntityMapper.class);

    MasterPart entityToCore(MpMasterPartEntity entity);

    MpMasterPartEntity coreToEntity(MasterPart masterPart);

    List<MasterPart> entitiesToCores(List<MpMasterPartEntity> entities);

    List<MpMasterPartEntity> coresToEntities(List<MasterPart> masterParts);

}
