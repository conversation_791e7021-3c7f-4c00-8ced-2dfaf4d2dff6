package co.cadshare.controller.dealer;

import co.cadshare.domainmodel.s3.PresignedUrlRequest;
import co.cadshare.shared.boundary.S3StoragePort;
import co.cadshare.shared.core.user.User;
import co.cadshare.users.boundary.UsersService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import java.net.URL;

@Controller
@PreAuthorize("hasRole('ROLE_MANUFACTURER_SUB_ENTITY_DEALER_PLUS')")
@RequestMapping("/dealerplus/aws/s3/getpresignedurl")
public class DealerAwsS3Controller {

    @Autowired
    S3StoragePort s3StorageClient;
    @Autowired
    UsersService userService;

    private final Logger log = LoggerFactory.getLogger(getClass());

    @PostMapping(value = "/order", consumes = "application/json")
    @ResponseBody
    public HttpEntity<String> getOrderInvoicePresignedUrl(@AuthenticationPrincipal User currentUser,
                                                   @RequestBody PresignedUrlRequest presignedUrlRequest) {

        log.info("ACCESS: User [{}], dealerplus - getPresignedUrl - Order", currentUser.accessDetails());
        User user = userService.findByUserId(currentUser.getUserId());
        URL presignedUrl = s3StorageClient.getDealerFileURLForOrderInvoiceUpload(presignedUrlRequest.getObjectKey(), user);
        String jsonURL = "{\"URL\": \"" + presignedUrl.toString() + "\"}";
        return new ResponseEntity<>(jsonURL, HttpStatus.OK);
    }
}
