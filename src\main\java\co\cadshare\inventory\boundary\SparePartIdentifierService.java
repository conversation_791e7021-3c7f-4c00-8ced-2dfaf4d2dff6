package co.cadshare.inventory.boundary;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

import co.cadshare.inventory.core.SparePartUploadProgress;
import co.cadshare.shared.adapters.database.manufacturer.ManufacturerProgressDao;
import co.cadshare.shared.core.user.User;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import co.cadshare.inventory.adapters.database.SparePartIdentifierRepository;
import co.cadshare.shared.core.manufacturer.ManufacturerProgress;
import co.cadshare.inventory.core.BomUpload;
import co.cadshare.inventory.core.BomUpload.UnparseableModelPartsFileException;
import co.cadshare.exceptions.NotFoundException;
import co.cadshare.modelMgt.models.boundary.ModelService;
import co.cadshare.modelMgt.models.core.Model;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
public class SparePartIdentifierService {

    private final SparePartIdentifierRepository sparePartIdentifierRepository;
    private final ModelService modelService;
    private final ManufacturerProgressDao manufacturerProgressDao;

    @Autowired
    public SparePartIdentifierService(SparePartIdentifierRepository sparePartIdentifierRepository,
                                      ModelService modelService,
                                      ManufacturerProgressDao manufacturerProgressDao){
        this.sparePartIdentifierRepository = sparePartIdentifierRepository;
        this.modelService = modelService;
        this.manufacturerProgressDao = manufacturerProgressDao;
    }

    @Async
    public void uploadSparePartIdentifiers(int modelId, BomUpload modelPartsFile, User currentUser) throws IOException, UnparseableModelPartsFileException {
        ManufacturerProgress progress = new SparePartUploadProgress(currentUser.getManufacturerId(), getModel(modelId));
        try {
            int progressId = manufacturerProgressDao.createManufacturerProgress(progress);
            progress.setId(progressId);
            Map<String, Boolean> sparePartIdentifiers = processCsvAndSave(modelPartsFile, modelId);
            sparePartIdentifierRepository.updateCriticalSparePart(sparePartIdentifiers);
            progress.complete();
        } catch (Exception ex) {
            log.error("Error - uploadSparePartIdentifiers: [{}]", ex.getMessage());
            progress.error();
            throw ex;
        } finally {
            manufacturerProgressDao.updateManufacturerProgress(progress);
        }
    }

    private Model getModel(int modelId) {
        try {
            return modelService.getModel(modelId);
        } catch (NotFoundException e) {
            throw new RuntimeException("Error getting model details: " + e.getMessage());
        }
    }

    private Map<String, Boolean> processCsvAndSave(BomUpload modelPartsFile, int modelId) {
        Map<String, Boolean> sparePartIdentifiers = new HashMap<String, Boolean>();

        int partNumberIndex = 0;
        int sparePartIdentifierIndex = 1;

        for (String[] row : modelPartsFile.getDataRows()) {
            String partNumber = row[partNumberIndex];
            String sparePartValue = row[sparePartIdentifierIndex];
            // Perform logic to determine if the part is a spare part
            boolean isCriticalSparePart = ("1".equals(sparePartValue) || "2".equals(sparePartValue) || "3".equals(sparePartValue));
            // Add the spare part identifier to the list
            sparePartIdentifiers.put(partNumber, isCriticalSparePart);
        }
        return sparePartIdentifiers;
    }
}


