package co.cadshare.ranges.adapters.api.ext;

import co.cadshare.config.ApplicationProperties;
import co.cadshare.shared.core.user.User;
import co.cadshare.ranges.boundary.CreateRangeService;
import co.cadshare.ranges.boundary.UpdateRangeService;
import co.cadshare.ranges.boundary.DeleteRangeService;
import co.cadshare.ranges.boundary.GetRangeService;
import co.cadshare.ranges.core.Range;
import co.cadshare.users.boundary.UsersService;
import co.cadshare.shared.adapters.api.ext.ExternalApiController;
import io.swagger.v3.oas.annotations.Operation;
import org.apache.commons.lang3.NotImplementedException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.*;

import java.util.List;


@RestController
@RequestMapping(value = "/api/manufacturers/{manufacturer-id}/ranges", headers = "X-API-Version=1")
public class RangesController extends ExternalApiController {

    private final CreateRangeService createRangeService;
    private final UpdateRangeService updateRangeService;
    private final DeleteRangeService deleteRangeService;
    private final GetRangeService getRangeService;

    @Autowired
    public RangesController(CreateRangeService createRangeService,
                            UpdateRangeService updateRangeService,
                            DeleteRangeService deleteRangeService,
                            GetRangeService getRangeService,
                            UsersService userService,
                            ApplicationProperties properties){
        super(userService, properties);
        this.createRangeService = createRangeService;
        this.updateRangeService = updateRangeService;
        this.deleteRangeService = deleteRangeService;
        this.getRangeService = getRangeService;
    }

    @PostMapping(consumes = "application/json", produces = "application/json")
    @ResponseStatus(HttpStatus.OK)
    @Operation(summary = "Create a Range")
    public ResponseEntity<PostRangeResponseDto> postRange(@PathVariable("manufacturer-id") int manufacturerId,
                                                                @AuthenticationPrincipal User currentUser,
                                                                @RequestBody PostRangeRequestDto postRange) {

        checkManufacturerPermissions(currentUser, manufacturerId);
        Range range = RangeMapper.Instance.postRequestDtoToRange(postRange);
        range.setManufacturerId(manufacturerId);

        Integer createdId = this.createRangeService.create(range, manufacturerId);
        PostRangeResponseDto response = new PostRangeResponseDto() {{
            setRangeId(createdId);
        }};
        return new ResponseEntity<>(response, HttpStatus.OK);
    }

    @PutMapping(path = "/{range-id}", consumes = "application/json")
    @ResponseStatus(HttpStatus.OK)
    @Operation(summary = "Maintain the details of a Range")
    public ResponseEntity putRange(@PathVariable("manufacturer-id") int manufacturerId,
                                         @PathVariable("range-id") Integer rangeId,
                                         @AuthenticationPrincipal User currentUser,
                                         @RequestBody PutRangeRequestDto putRange) throws Exception {

        checkManufacturerPermissions(currentUser, manufacturerId);
        Range range = RangeMapper.Instance.putRequestDtoToRange(putRange);
        range.setManufacturerId(manufacturerId);
        range.setId(rangeId);
        this.updateRangeService.update(range, manufacturerId);
        return new ResponseEntity<>(HttpStatus.OK);
    }

    //@DeleteMapping(path = "/{range-id}")
    @PreAuthorize("hasRole('ROLE_MANUFACTURER')")
    @ResponseStatus(HttpStatus.OK)
    @Operation(summary = "Delete a Range")
    public ResponseEntity deleteRange(@PathVariable("manufacturer-id") int manufacturerId,
                                            @PathVariable("range-id") Integer rangeId,
                                            @AuthenticationPrincipal User currentUser) throws Exception {

        throw new NotImplementedException("This action is not yet supported.");
        //this.deleteRangeService.delete(currentUser, rangeId);
        //return new ResponseEntity<>(HttpStatus.OK);
    }

    @GetMapping(path = "/{range-id}", produces = "application/json")
    @ResponseStatus(HttpStatus.OK)
    @Operation(summary = "Get the details of a specific Range")
    public ResponseEntity<GetRangeResponseDto> getRange(@PathVariable("manufacturer-id") int manufacturerId,
                                            @PathVariable("range-id") Integer rangeId,
                                            @AuthenticationPrincipal User currentUser
                                                        ) {

        checkManufacturerPermissions(currentUser, manufacturerId);
        Range range = this.getRangeService.get(rangeId);
        GetRangeResponseDto getResponseDto = RangeMapper.Instance.RangeToGetResponseDto(range);
        return new ResponseEntity<>(getResponseDto, HttpStatus.OK);
    }

    @GetMapping(produces = "application/json")
    @ResponseStatus(HttpStatus.OK)
    @Operation(summary = "Get the summary details of the list of all Ranges belonging to the Manufacturer")
    public ResponseEntity<GetRangeListResponseDto> getRanges(@PathVariable("manufacturer-id") int manufacturerId,
                                                                          @AuthenticationPrincipal User currentUser) {

        checkManufacturerPermissions(currentUser, manufacturerId);
        List<Range> ranges = this.getRangeService.getRangesForManufacturer(manufacturerId);
        List<GetRangeResponseDto> rangeResponses = RangeMapper.Instance.RangeToGetListResponseDto(ranges);
        GetRangeListResponseDto getListResponseDto = new GetRangeListResponseDto(){{
            setRanges(rangeResponses);
        }};
        return new ResponseEntity<>(getListResponseDto, HttpStatus.OK);
    }

}
