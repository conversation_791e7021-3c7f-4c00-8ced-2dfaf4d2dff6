package co.cadshare.addresses.adapters.database;

import co.cadshare.addresses.core.ExternalContact;
import co.cadshare.addresses.core.UserContactMap;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface UserContactMapEntityMapper {

	UserContactMapEntityMapper Instance = Mappers.getMapper(UserContactMapEntityMapper.class);

	//CoreToEntity
	UserContactMapEntity coreToEntity(UserContactMap core);

	List<UserContactMapEntity> coresToEntities(List<UserContactMap> cores);


	//Entity To Core

	UserContactMap entityToCore(UserContactMapEntity entity);

	List<UserContactMap > entitiesToCores(List<UserContactMapEntity> entities);

}
