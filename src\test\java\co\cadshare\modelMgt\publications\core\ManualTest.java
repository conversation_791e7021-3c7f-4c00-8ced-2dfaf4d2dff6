package co.cadshare.modelMgt.publications.core;

import co.cadshare.modelMgt.shared.core.Product;
import co.cadshare.modelMgt.shared.core.Purchaser;
import co.cadshare.modelMgt.shared.core.Viewable;
import co.cadshare.shared.core.manufacturer.subentity.ManufacturerSubEntity;
import co.cadshare.shared.core.user.User;
import org.junit.Before;
import org.junit.Test;
import org.mockito.MockitoAnnotations;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import static org.junit.Assert.*;

public class ManualTest {

    private final ArrayList<Integer> dealerWithoutCustomer = new ArrayList<Integer>(){{
        add(2);
    }};
    private final ArrayList<ManufacturerSubEntity> existingDealerWithNoCustomers = new ArrayList<ManufacturerSubEntity>(){{
        add(new ManufacturerSubEntity(){{
            setManufacturerSubEntityId(1);
            setManufacturerSubEntityType(ManufacturerSubEntityType.DEALER);
        }});
    }};
    private final List<ManufacturerSubEntity> oneDealerAndCustomer = buildSingleDealerAndCustomer();
    private final List<ManufacturerSubEntity> singleDealerWithCustomers = buildDealerWithCustomers();
    private final List<ManufacturerSubEntity> existingDealerPlusWithDealer = buildSingleDealerPlusWithDealer();
    private final List<ManufacturerSubEntity> multipleExistingDealersOneWithCustomers = new ArrayList<>();
    private final List<ManufacturerSubEntity> noExistingDealersAssigned = new ArrayList<>();
    private final List<Integer> dealerPlusWithDealerIds = new ArrayList<Integer>() {{
        add(1);
        add(2);
    }};

    private final List<Integer> dealerPlusWithChildDealerIdNotListed = new ArrayList<Integer>() {{
        add(1);
    }};

    private final ArrayList<Integer> dealerWithCustomersIds = new ArrayList<Integer>(){{
        add(1);
        add(2);
        add(3);
        add(4);
    }};
    private final ArrayList<Integer> multipleDealersOneWithCustomersIds = new ArrayList<Integer>(){{
        add(1);
        add(5);
        add(2);
        add(3);
        add(4);
    }};
    private final ArrayList<Integer> singleDealerIdList = new ArrayList<Integer>(){{add(1);}};
    private final ArrayList<Integer> singleDealerWithCustomersIdList = new ArrayList<Integer>(){{add(1);add(2);add(3);add(4);}};
    private final ArrayList<Integer> differentSingleDealerIdList = new ArrayList<Integer>(){{add(5);}};
    private final ArrayList<Integer> differentDealerInIdList = new ArrayList<Integer>(){{add(5);}};
    private final ArrayList<Integer> singleNewDealerIdList = new ArrayList<Integer>(){{add(1); add(5);}};
    private final ArrayList<Integer> multipleDealersIdList = new ArrayList<Integer>(){{ add(1); add(5); add(6);}};
    private final ArrayList<Integer> multipleNewDealersIdList = new ArrayList<Integer>(){{ add(5); add(6);}};
    private final ArrayList<Integer> noDealersIdList = new ArrayList<Integer>();
    private List<Integer> unassignList;
    private List<Integer> assignList;

    private Viewable viewable;
    private Manual out;
    private User user;

    @Before
    public void Before() {
        MockitoAnnotations.initMocks(this);
        unassignList = new ArrayList<>();
        assignList = new ArrayList<>();
        multipleExistingDealersOneWithCustomers.addAll(singleDealerWithCustomers);
        multipleExistingDealersOneWithCustomers.add(new ManufacturerSubEntity(){{
            setManufacturerSubEntityId(5);
            setManufacturerSubEntityType(ManufacturerSubEntityType.DEALER);
        }});
        viewable = buildViewableWithProductAndRange();
        user = new User();
        user.setManufacturerId(1);
    }



    @Test
    public void AssignManualToSameDealerPlusWithDealer() {
        Manual.determinePurchasersForManual(dealerPlusWithDealerIds, existingDealerPlusWithDealer, unassignList, assignList);
        assertEquals(0, unassignList.size());
        assertEquals(0, assignList.size());
    }

    @Test
    public void AssignManualToSameDealerPlusWithChildDealer() {
        Manual.determinePurchasersForManual(dealerPlusWithChildDealerIdNotListed, existingDealerPlusWithDealer, unassignList, assignList);
        assertEquals(0, unassignList.size());
        assertEquals(0, assignList.size());
    }

    @Test
    public void AssignManualToFirstSingleDealer() {
        Manual.determinePurchasersForManual(differentSingleDealerIdList, noExistingDealersAssigned, unassignList, assignList);
        assertEquals(0, unassignList.size());
        assertEquals(differentDealerInIdList.size(), assignList.size());
        assertEquals(differentDealerInIdList.get(0), assignList.get(0));
    }

    @Test
    public void AssignManualToFirstMultipleDealers() {

        Manual.determinePurchasersForManual(multipleDealersIdList, noExistingDealersAssigned, unassignList, assignList);
        assertEquals(0, unassignList.size());
        assertEquals(multipleDealersIdList.size(), assignList.size());
        assertEquals(multipleDealersIdList.get(1), assignList.get(1));
    }

    @Test
    public void AssignManualToSameDealerWithCustomersAndNewDealer() {

        Manual.determinePurchasersForManual(singleNewDealerIdList, existingDealerWithNoCustomers, unassignList, assignList);
        assertEquals(0, unassignList.size());
        assertEquals(differentSingleDealerIdList.size(), assignList.size());
        assertEquals(differentSingleDealerIdList.get(0), assignList.get(0));
    }

    @Test
    public void AssignManualToSameDealerWithCustomersAndMultipleNewDealers() {

        Manual.determinePurchasersForManual(multipleDealersIdList, existingDealerWithNoCustomers, unassignList, assignList);
        assertEquals(0, unassignList.size());
        assertEquals(multipleNewDealersIdList.size(), assignList.size());
        assertEquals(multipleNewDealersIdList.get(1), assignList.get(1));
    }

    @Test
    public void AssignManualToSameMultipleDealersAndOneNewDealer() {
        ArrayList<Integer> singleNewDealerInAdditionToMultipleIdList = new ArrayList<Integer>(){{add(6);}};

        Manual.determinePurchasersForManual(multipleDealersIdList, multipleExistingDealersOneWithCustomers, unassignList, assignList);

        assertEquals(0, unassignList.size());
        assertEquals(singleNewDealerInAdditionToMultipleIdList.size(), assignList.size());
        assertEquals(singleNewDealerInAdditionToMultipleIdList.get(0), assignList.get(0));
     }

    @Test
    public void AssignManualToSameMultipleDealersAndMultipleNewDealers() {

        ArrayList<Integer> multipleNewDealerInAdditionToMultipleIdList = new ArrayList<Integer>(){{add(6); add(7);}};
        multipleDealersIdList.add(7);
        Manual.determinePurchasersForManual(multipleDealersIdList, multipleExistingDealersOneWithCustomers, unassignList, assignList);

        assertEquals(0, unassignList.size());
        assertEquals(multipleNewDealerInAdditionToMultipleIdList.size(), assignList.size());
        assertEquals(multipleNewDealerInAdditionToMultipleIdList.get(1), assignList.get(1));
    }

    @Test
    public void UnassignManualFromDealerWithCustomers() {

        Manual.determinePurchasersForManual(noDealersIdList, singleDealerWithCustomers, unassignList, assignList);

        assertEquals(0, assignList.size());
        assertEquals(dealerWithCustomersIds.size(), unassignList.size());
        assertEquals(dealerWithCustomersIds.get(0), unassignList.get(0));
    }

    @Test
    public void UnassignManualFromCustomers() {

        Manual.determinePurchasersForManual(singleDealerIdList, oneDealerAndCustomer, unassignList, assignList);

        assertEquals(0, assignList.size());
        assertEquals(dealerWithoutCustomer.size(), unassignList.size());
        assertEquals(dealerWithoutCustomer.get(0), unassignList.get(0));
    }

    @Test
    public void UnassignManualFromMultipleDealersOneWithCustomers() {

        Manual.determinePurchasersForManual(noDealersIdList, multipleExistingDealersOneWithCustomers, unassignList, assignList);

       Collections.sort(multipleDealersOneWithCustomersIds);

        assertEquals(0, assignList.size());
        assertEquals(multipleDealersOneWithCustomersIds.size(), unassignList.size());

        assertEquals(multipleDealersOneWithCustomersIds.get(1), unassignList.get(1));
      }

    @Test
    public void UnassignManualFromSingleDealersOneWithCustomers() {

        Manual.determinePurchasersForManual(singleDealerIdList, multipleExistingDealersOneWithCustomers, unassignList, assignList);

        assertEquals(0, assignList.size());
        assertEquals(differentDealerInIdList.size(), unassignList.size());
        assertEquals(differentDealerInIdList.get(0), unassignList.get(0));
    }

    @Test
    public void AssignManualToSameDealerWithCustomers() {

        Manual.determinePurchasersForManual(singleDealerWithCustomersIdList, singleDealerWithCustomers, unassignList, assignList);

        assertEquals(0, assignList.size());
        assertEquals(0, unassignList.size());
    }

    @Test
    public void AssignManualToSameMultipleDealersOneWithCustomers() {

        ArrayList<Integer> customerIdsWithoutDealer = dealerWithCustomersIds;
        customerIdsWithoutDealer.remove(0);

        Manual.determinePurchasersForManual(singleDealerIdList, singleDealerWithCustomers, unassignList, assignList);

        assertEquals(0, assignList.size());
        assertEquals(0, unassignList.size());
    }

    @Test
    public void AssignManualToSameDealerWithNoCustomers() {

        Manual.determinePurchasersForManual(singleDealerIdList, existingDealerWithNoCustomers, unassignList, assignList);

        assertEquals(0, assignList.size());
        assertEquals(0, unassignList.size());
    }

    @Test
    public void AssignManualToDifferentDealerFromDealerWithCustomers() {

        Manual.determinePurchasersForManual(differentSingleDealerIdList, singleDealerWithCustomers, unassignList, assignList);

        assertEquals(differentSingleDealerIdList.size(), assignList.size());
        assertEquals(dealerWithCustomersIds.size(), unassignList.size());
    }

    @Test
    public void AssignManualToMultipleDifferentDealersFromDealerWithCustomers() {

        Manual.determinePurchasersForManual(multipleNewDealersIdList, singleDealerWithCustomers, unassignList, assignList);

        assertEquals(multipleNewDealersIdList.size(), assignList.size());
        assertEquals(multipleNewDealersIdList.get(0), assignList.get(0));
        assertEquals(dealerWithCustomersIds.size(), unassignList.size());
        assertEquals(dealerWithCustomersIds.get(0), unassignList.get(0));
    }

    @Test
    public void shouldHaveManufacturerIdFromUser() {
        out = Manual.buildForImmediatePublication(viewable, user);
        assertEquals(user.getManufacturerId().intValue(), out.getManufacturerId());
    }

    @Test
    public void shouldHaveModelIdFromViewable() {
        out = Manual.buildForImmediatePublication(viewable, user);
        assertEquals(viewable.getId(), out.getModelId().get(0).intValue());
    }

    @Test
    public void shouldHavePublishedStatus() {
        out = Manual.buildForImmediatePublication(viewable, user);
        assertEquals(ManualStatus.Status.PUBLISHED, out.getStatus());
    }

    @Test
    public void shouldHaveNameAndSerialNumberDerivedFromViewableName() {
        out = Manual.buildForImmediatePublication(viewable, user);
        assertEquals(viewable.getName(), out.getManualName());
        assertEquals("0987-7890", out.getSerialNumber());
    }

    @Test
    public void shouldHaveNotHaveSerialNumberIfNoSpaces() {
        viewable.setName("0987-7890-LongNameWithoutSpace");
        out = Manual.buildForImmediatePublication(viewable, user);
        assertEquals("", out.getSerialNumber());
    }

    @Test
    public void shouldBeAssignedToPurchasers() {
        viewable.setPurchasersAssignedToRange(buildAssignedPurchasersList(3));
        out = Manual.buildForImmediatePublication(viewable, user);
        assertTrue(out.shouldBeAssignedToPurchasers());
    }

    @Test
    public void shouldNotBeAssignedToPurchasers() {
        viewable.setPurchasersAssignedToRange(buildAssignedPurchasersList(0));
        out = Manual.buildForImmediatePublication(viewable, user);
        assertFalse(out.shouldBeAssignedToPurchasers());
    }

    @Test
    public void shouldReturnListOfPurchasersToBeAssigned() {
        viewable.setPurchasersAssignedToRange(buildAssignedPurchasersList(4));
        out = Manual.buildForImmediatePublication(viewable, user);
        assertEquals(4, out.getPurchasersAssignedToRanges().size());
    }

    @Test
    public void shouldReturnNoPurchasersToBeAssigned() {
        viewable.setPurchasersAssignedToRange(buildAssignedPurchasersList(0));
        out = Manual.buildForImmediatePublication(viewable, user);
        assertEquals(0, out.getPurchasersAssignedToRanges().size());
    }

    @Test
    public void shouldSetFeaturedModelUrl() {
        List<Snapshot> snapshots = new ArrayList<>();
        Snapshot snapshot = new Snapshot();
        snapshot.setStateId("ROOT");
        snapshot.setImgUrl("imgurl");
        snapshots.add(snapshot);
        viewable.setSnapshots(snapshots);
        out = Manual.buildForImmediatePublication(viewable, user);
        assertEquals("imgurl", out.getFeaturedModelUrl());
    }


    private static List<Purchaser> buildAssignedPurchasersList(int count) {
        List<Purchaser> assignedPurchasers = new ArrayList<>();
        Purchaser purchaser;
        for (int i = 0; i < count; i++) {
            purchaser = new Purchaser();
            purchaser.setId(i);
            assignedPurchasers.add(purchaser);
        }
        return assignedPurchasers;
    }

    private List<ManufacturerSubEntity> buildDealerWithCustomers() {
        List<ManufacturerSubEntity> dealerWithCustomers = new ArrayList<>();
        dealerWithCustomers.add(new ManufacturerSubEntity(){{
            setManufacturerSubEntityId(1);
            setManufacturerSubEntityType(ManufacturerSubEntityType.DEALER);
        }});
        dealerWithCustomers.add(new ManufacturerSubEntity(){{
            setManufacturerSubEntityId(2);
            setParentSubEntityId(1);
            setManufacturerSubEntityType(ManufacturerSubEntityType.CUSTOMER);
        }});
        dealerWithCustomers.add(new ManufacturerSubEntity(){{
            setManufacturerSubEntityId(3);
            setParentSubEntityId(1);
            setManufacturerSubEntityType(ManufacturerSubEntityType.CUSTOMER);
        }});
        dealerWithCustomers.add(new ManufacturerSubEntity(){{
            setManufacturerSubEntityId(4);
            setParentSubEntityId(1);
            setManufacturerSubEntityType(ManufacturerSubEntityType.CUSTOMER);
        }});
        return dealerWithCustomers;
    }

    private List<ManufacturerSubEntity> buildSingleDealerAndCustomer() {
        List<ManufacturerSubEntity> oneDealerAndCustomer = new ArrayList<ManufacturerSubEntity>() {{
            add(new ManufacturerSubEntity() {{
                setManufacturerSubEntityId(1);
                setManufacturerSubEntityType(ManufacturerSubEntityType.DEALER);
            }});
            add(new ManufacturerSubEntity() {{
                setManufacturerSubEntityId(2);
                setManufacturerSubEntityType(ManufacturerSubEntityType.CUSTOMER);
            }});
        }};
        return oneDealerAndCustomer;
    }

    private List<ManufacturerSubEntity> buildSingleDealerPlusWithDealer() {
        List<ManufacturerSubEntity> oneDealerAndCustomer = new ArrayList<ManufacturerSubEntity>() {{
            add(new ManufacturerSubEntity() {{
                setManufacturerSubEntityId(1);
                setManufacturerSubEntityType(ManufacturerSubEntityType.DEALER_PLUS);
            }});
            add(new ManufacturerSubEntity() {{
                setManufacturerSubEntityId(2);
                setParentSubEntityId(1);
                setManufacturerSubEntityType(ManufacturerSubEntityType.DEALER);
            }});
        }};
        return oneDealerAndCustomer;
    }

    private Viewable buildViewableWithProductAndRange() {
        viewable = new Viewable();
        viewable.setName("0987-7890 Viewable Name");
        Product product = new Product();
        product.setThumbnailUrl("imgurl");
        viewable.setProduct(product);
        List<Snapshot> snapshots = new ArrayList<>();
        Snapshot snapshot = new Snapshot();
        snapshot.setStateId("ROOT");
        snapshot.setImgUrl("imgurl");
        snapshots.add(snapshot);
        viewable.setSnapshots(snapshots);
        return viewable;
    }

}
