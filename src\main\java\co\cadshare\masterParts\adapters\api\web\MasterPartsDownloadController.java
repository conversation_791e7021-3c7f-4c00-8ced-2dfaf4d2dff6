package co.cadshare.masterParts.adapters.api.web;

import co.cadshare.shared.core.user.User;
import co.cadshare.masterParts.boundary.MasterPartsDownloadService;
import com.google.gson.JsonObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/masterPartDownload")
@Slf4j
public class MasterPartsDownloadController {

    @Autowired
    private MasterPartsDownloadService masterPartsDownloadService;

    @Autowired
    private TranslationCsvFileProcessor csvProcessor;

    @GetMapping
    @PreAuthorize("hasRole('ROLE_MANUFACTURER') and hasRole('Parts')")
    public ResponseEntity<String> masterPartTranslationDownload(@AuthenticationPrincipal User currentUser) {
        log.info("Masterparts data download request for manufacturer id [{}]", currentUser.getManufacturerId());
        try {
            masterPartsDownloadService.downloadMasterPartTranslations(currentUser.getManufacturerId());
        } catch (Exception e) {
            JsonObject response = new JsonObject();
            response.addProperty("message", "Error processing file: " + e.getMessage());
            return new ResponseEntity<>(response.toString(), HttpStatus.BAD_REQUEST);
        }
        return new ResponseEntity<>(HttpStatus.ACCEPTED);
    }

    @GetMapping(value = "/headers", produces = MediaType.APPLICATION_OCTET_STREAM_VALUE)
    @PreAuthorize("hasRole('ROLE_MANUFACTURER') and hasRole('Parts')")
    public ResponseEntity masterPartFileTemplate(@AuthenticationPrincipal User currentUser) {
        log.info("Masterparts manufacturer languages template for manufacturer id [{}]", currentUser.getManufacturerId());
        String masterPartCSV = null;
        try {
            masterPartCSV = masterPartsDownloadService.downloadMasterPartTemplate(currentUser.getManufacturerId());
        } catch (Exception e) {
            JsonObject response = new JsonObject();
            response.addProperty("message", "Error processing request: " + e.getMessage());
            return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
        }
        return new ResponseEntity<>(masterPartCSV, HttpStatus.OK);
    }
}
