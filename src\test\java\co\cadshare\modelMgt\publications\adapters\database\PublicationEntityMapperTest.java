package co.cadshare.modelMgt.publications.adapters.database;

import co.cadshare.modelMgt.publicationCategories.adapters.database.PublicationCategoryEntity;
import co.cadshare.modelMgt.publications.core.*;
import co.cadshare.modelMgt.shared.core.Publication;
import co.cadshare.shared.adapters.database.ImageEntity;
import co.cadshare.shared.testData.PublicationTestData;
import org.junit.Test;
import org.mapstruct.factory.Mappers;

import java.util.Arrays;
import java.util.Comparator;
import java.util.HashSet;
import java.util.stream.Collectors;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertTrue;

public class PublicationEntityMapperTest {

    private final PublicationEntityMapper mapper = Mappers.getMapper(PublicationEntityMapper.class);

    @Test
    public void testEntityToCoreMapping() {

	    PublicationCategoryEntity publicationCategory = PublicationTestData.getPublicationCategoryEntity();

	    ImageEntity coverImage = PublicationTestData.getCoverImage();
		coverImage.setId(1);
		ImageEntity featuredViewableImage = PublicationTestData.getFeaturedViewableImage();
		featuredViewableImage.setId(2);
	    PublicationsRangeEntity range = PublicationTestData.getRangeEntity();
	    range.setId(1);
	    PublicationsProductEntity product = PublicationTestData.getProductEntity();
	    product.setId(2);
	    PublicationsModelEntity model = PublicationTestData.getModelEntity();
	    product.setRange(range);
	    model.setProduct(product);
	    model.setModelId(3);
		PublicationsTechDocEntity techDoc1 = PublicationTestData.getTechDocEntity();
		PublicationsTechDocEntity techDoc2 = PublicationTestData.getTechDocEntity();
		techDoc1.setId(1);
		techDoc2.setId(2);
		PublicationsVideoEntity video1 = PublicationTestData.getVideoEntity();
		PublicationsVideoEntity video2 = PublicationTestData.getVideoEntity();
		video1.setId(1);
		video2.setId(2);
		PublicationsKitEntity kit1 = PublicationTestData.getKitEntity();
		kit1.setId(1);
		PublicationsKitEntity kit2 = PublicationTestData.getKitEntity();
		kit2.setId(2);
		PublicationsPurchaserEntity dealer = PublicationTestData.getDealerEntity();
		PublicationsPurchaserEntity customer = PublicationTestData.getCustomerEntity();
	    PublicationsPublicationEntity entity = PublicationTestData.getPublicationEntity(publicationCategory, coverImage,
			    featuredViewableImage,
			    new HashSet<>(Arrays.asList(model)),
			    new HashSet<>(Arrays.asList(techDoc1, techDoc2)),
				new HashSet<>(Arrays.asList(video1, video2)),
			    new HashSet<>(Arrays.asList(kit1, kit2)),
				new HashSet<>(Arrays.asList(dealer, customer)));


        Publication publication = mapper.entityToCore(entity);

        assertEquals(entity.getId(), publication.getId());
        assertEquals(entity.getName(), publication.getName());
        assertEquals(entity.getStatus(), publication.getStatus());
		assertEquals(entity.isDeleted(), publication.isDeleted());
		assertEquals(entity.getFeaturedViewableId(), publication.getFeaturedViewableId());
		assertEquals(entity.getPublicationCategory().getId(), publication.getPublicationCategory().getId());
	    assertEquals(entity.getPublicationCategory().getName(), publication.getPublicationCategory().getName());
		assertEquals(entity.getCoverImage().getId().intValue(), publication.getCoverImage().getId());
		assertEquals(entity.getCoverImage().getLocationUrl(), publication.getCoverImage().getLocationUrl());
	    assertEquals(entity.getFeaturedViewableImage().getId().intValue(), publication.getFeaturedViewableImage().getId());
	    assertEquals(entity.getFeaturedViewableImage().getLocationUrl(), publication.getFeaturedViewableImage().getLocationUrl());
		assertEquals(range.getId().intValue(), publication.getViewables().get(0).getProduct().getRange().getId());
	    assertEquals(range.getName(), publication.getViewables().get(0).getProduct().getRange().getName());
		assertEquals(product.getId().intValue(), publication.getViewables().get(0).getProduct().getId());
	    assertEquals(product.getName(), publication.getViewables().get(0).getProduct().getName());
		assertEquals(1, publication.getViewables().size());
		assertEquals(model.getModelId().intValue(), publication.getViewables().get(0).getId());
	    assertEquals(model.getModelName(), publication.getViewables().get(0).getName());
		publication.getTechDocs().sort(Comparator.comparing(TechDoc::getId));
		assertEquals(techDoc1.getId(), publication.getTechDocs().get(0).getId());
	    assertEquals(techDoc1.getName(), publication.getTechDocs().get(0).getName());
	    assertEquals(techDoc1.getUrl(), publication.getTechDocs().get(0).getUrl());
	    assertEquals(techDoc2.getId(), publication.getTechDocs().get(1).getId());
	    publication.getVideos().sort(Comparator.comparing(Video::getId));
		assertEquals(video1.getId(), publication.getVideos().get(0).getId());
	    assertEquals(video1.getName(), publication.getVideos().get(0).getName());
	    assertEquals(video1.getUrl(), publication.getVideos().get(0).getUrl());
	    assertEquals(video2.getId(), publication.getVideos().get(1).getId());
		assertTrue(publication.getPurchasers().stream().filter(p -> p instanceof Dealer).count() == 1);
		Dealer publicationDealer = (Dealer) publication.getPurchasers().stream().filter(p -> p instanceof Dealer).collect(Collectors.toList()).get(0);
		assertEquals(dealer.getId(), publicationDealer.getId());
	    assertEquals(dealer.getName(), publicationDealer.getName());
	    assertTrue(publication.getPurchasers().stream().filter(p -> p instanceof Customer).count() == 1);
	    Customer publicationCustomer = (Customer) publication.getPurchasers().stream().filter(p -> p instanceof Customer).collect(Collectors.toList()).get(0);
		assertEquals(customer.getId(), publicationCustomer.getId());
	    assertEquals(customer.getName(), publicationCustomer.getName());
		assertEquals(model.isSetupComplete(), publication.getViewables().get(0).isSetupComplete());
    }
}
