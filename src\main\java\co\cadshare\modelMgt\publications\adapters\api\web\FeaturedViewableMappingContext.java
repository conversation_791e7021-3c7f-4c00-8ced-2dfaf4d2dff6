package co.cadshare.modelMgt.publications.adapters.api.web;

import co.cadshare.modelMgt.shared.core.Publication;
import org.mapstruct.AfterMapping;
import org.mapstruct.MappingTarget;

public class FeaturedViewableMappingContext {

	@AfterMapping
	public void afterMapping(@MappingTarget PublicationViewableListItemDto target, Publication source) {
		target.setFeaturedViewable(source.getFeaturedViewableId().equals(target.getId()));
	}
}
