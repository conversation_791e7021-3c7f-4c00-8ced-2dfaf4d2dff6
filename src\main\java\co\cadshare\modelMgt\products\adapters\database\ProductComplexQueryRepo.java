package co.cadshare.modelMgt.products.adapters.database;

import co.cadshare.modelMgt.publications.adapters.database.PublicationsProductEntity;
import co.cadshare.modelMgt.publications.adapters.database.QPublicationsProductEntity;
import co.cadshare.shared.adapters.database.ProductEntity;
import co.cadshare.shared.adapters.database.QProductEntity;
import com.querydsl.jpa.impl.JPAQueryFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import javax.persistence.EntityManager;
import java.util.List;

@Repository
public class ProductComplexQueryRepo {

    private JPAQueryFactory queryFactory;

    @Autowired
    public ProductComplexQueryRepo(EntityManager entityManager) {
        this.queryFactory = new JPAQueryFactory(entityManager);
    }

    public List<PublicationsProductEntity> getProductsForRange(Integer rangeId) {
        QPublicationsProductEntity product = QPublicationsProductEntity.publicationsProductEntity;
        return queryFactory.selectFrom(product)
                .where(product.range.id.eq(rangeId)
                        .and(product.deleted.eq(false)))
                .fetch();
    }

	public List<PublicationsProductEntity> getProductsForManufacturer(Integer manufacturerId) {
		QPublicationsProductEntity product = QPublicationsProductEntity.publicationsProductEntity;
		return queryFactory.selectFrom(product)
				.where(product.range.manufacturerId.eq(manufacturerId)
						.and(product.deleted.eq(false)))
				.fetch();
	}
}

