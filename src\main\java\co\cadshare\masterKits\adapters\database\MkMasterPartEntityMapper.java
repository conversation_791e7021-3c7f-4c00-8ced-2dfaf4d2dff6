package co.cadshare.masterKits.adapters.database;

import co.cadshare.masterKits.core.KitMasterPart;
import co.cadshare.masterKits.core.MasterPartDefaultPrice;
import co.cadshare.masterKits.core.PartMasterPart;
import co.cadshare.shared.adapters.database.CurrencyEntityMapper;
import co.cadshare.shared.core.Currency;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper(uses={MkMasterPartPriceEntityMapper.class,
        CurrencyEntityMapper.class, MkMasterPartTranslationEntityMapper.class})
public interface MkMasterPartEntityMapper {

    MkMasterPartEntityMapper Instance = Mappers.getMapper(MkMasterPartEntityMapper.class);

    @Mapping(source = "masterPartEntity.id", target = "id")
    @Mapping(source=".", target="price", qualifiedByName="priceMapping")
    @Mapping(source="prices", target="prices")
    @Mapping(source="manufacturer.id", target="manufacturerId")
    KitMasterPart entityToCoreForKit(MkMasterPartEntity masterPartEntity);

    @Mapping(source = "masterPartEntity.id", target = "id")
    @Mapping(source=".", target="price", qualifiedByName="priceMapping")
    @Mapping(source="manufacturer.id", target="manufacturerId")
    PartMasterPart entityToCore(MkMasterPartEntity masterPartEntity);

    @Mapping(source="id", target = "id")
    @Mapping(source="partNumber", target="partNumber")
    @Mapping(source="manufacturerId", target="manufacturer.id")
    @Mapping(expression="java(co.cadshare.masterParts.core.MasterPartType.PART)", target="type")
    @Mapping(source="price.price", target="price")
    @Mapping(source="translations", target="translations")
    MkMasterPartEntity coreToEntity(PartMasterPart core);


    List<PartMasterPart> entitiesToCores(List<MkMasterPartEntity> entities);

    @Named("priceMapping")
    public static MasterPartDefaultPrice priceMapping(MkMasterPartEntity masterPartEntity){
        Currency defaultCurrency =
                CurrencyEntityMapper.Instance.entityToCore(masterPartEntity.getManufacturer().getSettings().getDefaultCurrency());
        return new MasterPartDefaultPrice() {{
            setPrice(masterPartEntity.getPrice());
            setCurrency(defaultCurrency);
        }};
    }


}
