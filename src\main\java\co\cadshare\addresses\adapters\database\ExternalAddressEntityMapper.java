package co.cadshare.addresses.adapters.database;

import co.cadshare.addresses.core.ExternalAddress;
import co.cadshare.addresses.core.ExternalAddressContact;
import co.cadshare.addresses.core.UserAddressMap;
import co.cadshare.shared.adapters.database.addresses.ExternalAddressEntity;
import co.cadshare.utils.CycleAvoidingMappingContext;
import org.mapstruct.Context;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper(uses = { UserAddressMapEntityMapper.class, AddressContactMapEntityMapper.class})
public interface ExternalAddressEntityMapper {

    ExternalAddressEntityMapper Instance = Mappers.getMapper(ExternalAddressEntityMapper.class);

    @Mapping(source="userMaps", target="userAddressMaps", qualifiedByName="mapsUserAddressCoreToEntity")
    @Mapping(source="contactMaps", target="contactAddressMaps", qualifiedByName="mapsAddressContactCoreToEntity")
    ExternalAddressEntity coreToEntity(ExternalAddress source, @Context CycleAvoidingMappingContext context);

    @Mapping(source="userAddressMaps", target="userMaps", qualifiedByName="mapsUserAddressEntityToCore")
    @Mapping(source="contactAddressMaps", target="contactMaps", qualifiedByName="mapsAddressContactEntityToCore")
    ExternalAddress entityToCore(ExternalAddressEntity source, @Context CycleAvoidingMappingContext context);

    List<ExternalAddress> entitiesToCores(List<ExternalAddressEntity> sources);


	@Named("mapsUserAddressCoreToEntity")
	public static List<UserAddressMapEntity> mapsUserAddressCoreToEntity(List<UserAddressMap> entities, @Context CycleAvoidingMappingContext context) {
		return UserAddressMapEntityMapper.Instance.coresToEntities(entities, context);
	}

	@Named("mapsUserAddressEntityToCore")
	public static List<UserAddressMap> mapsUserAddressEntityToCore(List<UserAddressMapEntity> entities, @Context CycleAvoidingMappingContext context) {
		return UserAddressMapEntityMapper.Instance.entitiesToCores(entities, context);
	}

	@Named("mapsAddressContactCoreToEntity")
	public static List<AddressContactMapEntity> mapsAddressContactCoreToEntity(List<ExternalAddressContact> entities, @Context CycleAvoidingMappingContext context) {
		return AddressContactMapEntityMapper.Instance.coresToEntities(entities, context);
	}

    @Named("mapsAddressContactEntityToCore")
    public static List<ExternalAddressContact> mapsAddressContactEntityToCore(List<AddressContactMapEntity> entities, @Context CycleAvoidingMappingContext context) {
        return AddressContactMapEntityMapper.Instance.entitiesToCores(entities, context);
    }


}