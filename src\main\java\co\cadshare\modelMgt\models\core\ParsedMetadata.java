package co.cadshare.modelMgt.models.core;

import co.cadshare.shared.core.manufacturer.ManufacturerConfig;
import co.cadshare.utils.ObjectUtilsExtension;
import lombok.experimental.ExtensionMethod;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.List;
import java.util.Optional;

@ExtensionMethod(ObjectUtilsExtension.class)
public class ParsedMetadata  {

    private static final Logger logger = LoggerFactory.getLogger(ParsedMetadata.class);
    public static final String OVERRIDING_EXTENDED_METADATA = "Overriding extended metadata";
    private List<MetadataObjectExtended> metadata;

    public ParsedMetadata() { }

    public ParsedMetadata(List<MetadataObjectExtended> metadata) {
        this.metadata = metadata;
    }

    public boolean metadataToBeUploaded() {
        if(metadata == null)
            return false;
        return !metadata.isEmpty();
    }

    public boolean isValid() {
        if(metadataToBeUploaded())
            return metadata.stream().allMatch(MetadataObjectExtended::isValid);
        else
            return false;
    }

    public List<MetadataObjectExtended> get() {
        return metadata;
    }


    public void overrideExtendedProperties(ManufacturerConfig config,
                                           HashMap<Integer, List<String>> overrides) {
        if(config.isUseExtendedMetadata()) {
            logger.info(OVERRIDING_EXTENDED_METADATA);

            overrides.forEach((objectId, values) -> {
                Optional<MetadataObjectExtended> modelObject = metadata.stream()
                        .filter(collection -> collection.getObjectid().equals(objectId))
                        .findFirst();
                if(modelObject.isPresent()) {
                    if(values.get(0).isNotNull())
                        modelObject.get().setDescription(values.get(0));
                    if(values.size() > 1) {
                        if(values.get(1).isNotNull())
                            modelObject.get().setPartNumber(values.get(1));
                    }
                }
            });
        }
    }
}
