package co.cadshare.domainmodel.comment;

import java.sql.Timestamp;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;

import lombok.Data;

@JsonInclude(JsonInclude.Include.NON_NULL)
@Data
public class CommentThread {

    private Integer id;
    private int orderId;
    private Integer orderItemId;
    private boolean archived;
    private int createdByUserId;
    private int modifiedByUserId;
    private Timestamp createdDate;
    private Timestamp modifiedDate;
    
    private boolean unreadComment;

    private List<Comment> comments;
}
