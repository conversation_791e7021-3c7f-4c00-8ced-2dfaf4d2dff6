package co.cadshare.models.adapters.api.ext;

import co.cadshare.config.ApplicationProperties;
import co.cadshare.shared.core.user.User;
import co.cadshare.models.boundary.CreateModelService;
import co.cadshare.models.boundary.UpdateModelService;
import co.cadshare.models.boundary.DeleteModelService;
import co.cadshare.models.boundary.GetModelService;
import co.cadshare.models.core.Model;
import co.cadshare.users.boundary.UsersService;
import co.cadshare.shared.adapters.api.ext.ExternalApiController;
import io.swagger.v3.oas.annotations.Operation;
import org.apache.commons.lang3.NotImplementedException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.*;

import java.util.List;


@RestController
@RequestMapping(value = "/api/manufacturers/{manufacturer-id}/ranges/{range-id}/products/{product-id}/models", headers = "X-API-Version=1")
public class ModelsController extends ExternalApiController {

    private final CreateModelService createModelService;
    private final UpdateModelService updateModelService;
    private final DeleteModelService deleteModelService;
    private final GetModelService getModelService;

    @Autowired
    public ModelsController(CreateModelService createModelService,
                                    UpdateModelService updateModelService,
                                    DeleteModelService deleteModelService,
                                    GetModelService getModelService,
                            UsersService userService,
                            ApplicationProperties properties){

        super(userService, properties);
        this.createModelService = createModelService;
        this.updateModelService = updateModelService;
        this.deleteModelService = deleteModelService;
        this.getModelService = getModelService;
    }

    @PostMapping(consumes = "application/json", produces = "application/json")
    @ResponseStatus(HttpStatus.OK)
    @Operation(summary = "Create a Model")
    public ResponseEntity<PostModelResponseDto> postModel(@PathVariable("manufacturer-id") int manufacturerId,
                                                                @PathVariable("range-id") int rangeId,
                                                                @PathVariable("product-id") int productId,
                                                                @AuthenticationPrincipal User currentUser,
                                                                @RequestBody PostModelRequestDto postModel) throws Exception {

        checkManufacturerPermissions(currentUser, manufacturerId);
        Model model = ModelMapper.Instance.postRequestDtoToModel(postModel, rangeId, productId);
        Integer createdId = this.createModelService.createModel(model, manufacturerId);
        PostModelResponseDto response = new PostModelResponseDto() {{setModelId(createdId);}};
        return new ResponseEntity<>(response, HttpStatus.OK);
    }

    @PutMapping(path = "/{model-id}", consumes = "application/json")
    @ResponseStatus(HttpStatus.OK)
    @Operation(summary = "Maintain the details of a Model")
    public ResponseEntity putModel(@PathVariable("manufacturer-id") int manufacturerId,
                                    @PathVariable("range-id") int rangeId,
                                    @PathVariable("product-id") int productId,
                                    @PathVariable("model-id") Integer modelId,
                                    @AuthenticationPrincipal User currentUser,
                                    @RequestBody PutModelRequestDto putModel) throws Exception {

        throw new NotImplementedException("This action is not yet supported.");
        /*
        Model model = ModelMapper.Instance.putRequestDtoToModel(putModel);
        model.setProductId(productId);
        this.updateModelService.update(currentUser, model);
        return new ResponseEntity<>(HttpStatus.OK);
        */
    }

    @DeleteMapping(path = "/{model-id}")
    @ResponseStatus(HttpStatus.OK)
    @Operation(summary = "Delete a Model")
    public ResponseEntity deleteModel(@PathVariable("manufacturer-id") int manufacturerId,
                                        @PathVariable("range-id") int rangeId,
                                        @PathVariable("product-id") int productId,
                                        @PathVariable("model-id") Integer modelId,
                                        @AuthenticationPrincipal User currentUser) throws Exception {

        throw new NotImplementedException("This action is not yet supported.");
        /*
        this.deleteModelService.delete(currentUser, modelId);
        return new ResponseEntity<>(HttpStatus.OK);
        */
    }

    @GetMapping(path = "/{model-id}", produces = "application/json")
    @ResponseStatus(HttpStatus.OK)
    @Operation(summary = "Get the details of a specific Model")
    public ResponseEntity<GetModelResponseDto> getModel(@PathVariable("manufacturer-id") int manufacturerId,
                                                                        @PathVariable("range-id") int rangeId,
                                                                        @PathVariable("product-id") int productId,
                                                                        @PathVariable("model-id") Integer modelId,
                                                                        @AuthenticationPrincipal User currentUser) {

        Model model = this.getModelService.get(modelId);
        GetModelResponseDto getResponseDto = ModelMapper.Instance.ModelToGetResponseDto(model);
        return new ResponseEntity<>(getResponseDto, HttpStatus.OK);
    }

    @GetMapping(produces = "application/json")
    @ResponseStatus(HttpStatus.OK)
    @Operation(summary = "Get the summary details of the list of all Models belonging to the Manufacturer")
    public ResponseEntity<GetModelListResponseDto> getModels(@PathVariable("manufacturer-id") int manufacturerId,
                                                             @PathVariable("range-id") int rangeId,
                                                             @PathVariable("product-id") int productId,
                                                             @AuthenticationPrincipal User currentUser) {

        List<Model> models = this.getModelService.getModelsForProduct(productId);
        List<GetModelResponseDto> modelResponses = ModelMapper.Instance.ModelToGetListResponseDto(models);
        GetModelListResponseDto getListResponseDto = new GetModelListResponseDto(){{
            setModels(modelResponses);
        }};
        return new ResponseEntity<>(getListResponseDto, HttpStatus.OK);
    }

}
