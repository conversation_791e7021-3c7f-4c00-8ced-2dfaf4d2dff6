
package co.cadshare.modelMgt.publicationCategories.adapters.database;

import co.cadshare.shared.core.user.User;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.jdbc.core.BatchPreparedStatementSetter;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.stereotype.Repository;

import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.util.*;

@Repository
public class PublicationCategoryDao {

	private final JdbcTemplate jdbcTemplate;
	private final NamedParameterJdbcTemplate namedParamJdbcTemplate;

	@Autowired
	public PublicationCategoryDao(JdbcTemplate jdbcTemplate, 
	                              NamedParameterJdbcTemplate namedParamJdbcTemplate) {
		this.jdbcTemplate = jdbcTemplate;
		this.namedParamJdbcTemplate = namedParamJdbcTemplate;
	}

	public void assignPublicationCategoriesToPurchaser(int purchaserId, List<Integer> publicationCategories, User user) throws Exception {
		String ASSIGN_RANGES_TO_MANUFACTURER_SUB_ENTITY = "INSERT INTO purchaserpublicationcategorymap (purchaserid, publicationcategoryid, createddate, createdbyuserid) VALUES (?, ?, ?, ?)";
		
		Timestamp now = new Timestamp(Calendar.getInstance().getTime().getTime());
		try {
			jdbcTemplate.batchUpdate(ASSIGN_RANGES_TO_MANUFACTURER_SUB_ENTITY, new BatchPreparedStatementSetter() {

				@Override
				public void setValues(PreparedStatement ps, int i) throws SQLException {
					int publicationCategoryId = publicationCategories.get(i);
					ps.setInt(1, purchaserId);
					ps.setInt(2, publicationCategoryId);
					ps.setTimestamp(3, now);
					ps.setInt(4, user.getUserId());
				}

				@Override
				public int getBatchSize() {
					return publicationCategories.size();
				}
			});
		} catch (DuplicateKeyException e) {
			throw new Exception("Publication Category already assigned");
		}

	}
	
	public List<PublicationCategoryEntity> getAssignedPublicationCategoriesForPurchaser(int purchaserId) {

		String GET_PUBlICATION_CATEGORIES = "SELECT * FROM publicationcategory " +
				"WHERE id IN (" +
				"SELECT DISTINCT(publicationcategoryid) "
				+ "FROM purchaserpublicationcategorymap  "
				+ "WHERE purchaserid = :purchaserId ) ";

		Map<String, Object> parameters = new HashMap<String, Object>();
		parameters.put("purchaserId", purchaserId);

		return namedParamJdbcTemplate.queryForList(GET_PUBlICATION_CATEGORIES,
				parameters, PublicationCategoryEntity.class);
	}

	public void clearPublicationCategoriesForPurchaser(Integer purchaserId) {

		String DELETE_MANUFACTURER_SUB_ENTITIES_TO_RANGE_MAPS_BY_SUBENTITY_ID =
				"DELETE FROM purchaserpublicationcategorymap WHERE purchaserid = :purchaserId";

		Map<String, Integer> namedParameters = new HashMap<String, Integer>();
		namedParameters.put("purchaserId", purchaserId);

		namedParamJdbcTemplate.update(DELETE_MANUFACTURER_SUB_ENTITIES_TO_RANGE_MAPS_BY_SUBENTITY_ID, namedParameters);
	}

	public void updateAssignedPublicationCategoriesToPurchaser(int purchaserId, ArrayList<Integer> assignedPublicationCategories, User user) throws Exception {
		clearPublicationCategoriesForPurchaser(purchaserId);
		if (!assignedPublicationCategories.isEmpty())
			assignPublicationCategoriesToPurchaser(purchaserId, assignedPublicationCategories, user);
	}

}
