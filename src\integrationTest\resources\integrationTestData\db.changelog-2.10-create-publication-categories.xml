<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                   http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.0.xsd
">

    <changeSet author="AndyB" id="2.10-integration-test-data-create-publication-categories-1">
        <sql stripComments="true">

            INSERT INTO public.publicationcategory(
            id, name, manufacturerid, deleted, createddate, createdbyuserid, modifieddate, modifiedbyuserid)
            VALUES (
            1,                                  -- id
            'Publication for Caterpillar',      -- name
            1,                                  -- manufacturerid
            FALSE,                              -- deleted
            '2017-06-09 10:42:56.176',          -- createddate
            1,                                  -- createdbyuserid
            '2017-06-09 10:42:56.176',          -- modifieddate
            1);                                 -- modifiedbyuserid

        </sql>

    </changeSet>

</databaseChangeLog>
