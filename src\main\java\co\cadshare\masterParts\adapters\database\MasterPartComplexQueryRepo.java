package co.cadshare.masterParts.adapters.database;

import co.cadshare.shared.adapters.database.BlazeQueryRepo;
import co.cadshare.shared.adapters.database.MasterPartEntity;
import com.blazebit.persistence.CriteriaBuilder;
import com.blazebit.persistence.CriteriaBuilderFactory;
import com.blazebit.persistence.view.EntityViewManager;
import com.blazebit.persistence.view.EntityViewSetting;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import javax.persistence.EntityManager;
import java.util.List;
import java.util.stream.Collectors;

@Repository
public class MasterPartComplexQueryRepo extends BlazeQueryRepo {

    @Autowired
    public MasterPartComplexQueryRepo(EntityManager entityManager,
                                      EntityViewManager entityViewManager,
                                      CriteriaBuilderFactory factory) {
        super(entityManager, entityViewManager, factory);
    }

    public List<MasterPartSupersessionEntityView> getMasterPartsForSupersessionHistory(List<SupersededMasterPartEntityCte> supersededMasterParts,
                                                                                int manufacturerId) {

        List<Integer> ids = supersededMasterParts.stream()
                .map(SupersededMasterPartEntityCte::getMasterPartId)
                .collect(Collectors.toList());

        CriteriaBuilder<MasterPartEntity> criteriaBuilder =
                factory.create(entityManager, MasterPartEntity.class)
                        .where("manufacturer.id").eq(manufacturerId)
                        .where("id").in(ids);

        return entityViewManager.applySetting(EntityViewSetting.create(MasterPartSupersessionEntityView.class), criteriaBuilder)
                .getResultList();
    }



    public List<MasterPartEntityView> getMasterPartsForSupersession(List<Integer> ids, int manufacturerId) {
        CriteriaBuilder<MasterPartEntity> criteriaBuilder =
                factory.create(entityManager, MasterPartEntity.class)
                        .where("manufacturer.id").eq(manufacturerId)
                        .where("id").in(ids);

        return entityViewManager.applySetting(EntityViewSetting.create(MasterPartEntityView.class), criteriaBuilder)
                .getResultList();
    }


}

