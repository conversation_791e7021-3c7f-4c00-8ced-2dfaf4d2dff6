package co.cadshare.shared.boundary;

import co.cadshare.media.core.Image;
import co.cadshare.shared.core.user.User;

import java.io.InputStream;
import java.net.URL;

public interface S3StoragePort {
	String getBucketName();

	URL getFileURLForManufacturerUpload(String objectKey, Integer manufacturerId);

	URL getFileURLForMachineUpload(String objectKey, Integer manufacturerId);

	URL getFileURLForModelUpload(String objectKey, Integer manufacturerId, int machineId, int modelId);

	URL getFileURLForOrderInvoiceUpload(String objectKey, Integer manufacturerId);

	URL getDealerFileURLForOrderInvoiceUpload(String objectKey, User dealerUser);

	URL getFileURLForTechDocUpload(String objectKey, String fileType, Integer manufacturerId);

	String uploadCSVForMasterPartLanguage(String languageCSV, Integer manufacturerId);

	String uploadCSVForMasterPartInventory(String inventoryCSV, Integer manufacturerId);

	String uploadCSVForViewableBOM(String bomCSV, Integer manufacturerId);

	String uploadPropertiesDb(InputStream propertiesDbStream, Integer manufacturerId);

	String uploadImage(User user, Image image) throws Exception;

	boolean deleteS3File(String url);

	boolean deleteS3FileHelper(String existingS3Url, String currentS3Url);
}
