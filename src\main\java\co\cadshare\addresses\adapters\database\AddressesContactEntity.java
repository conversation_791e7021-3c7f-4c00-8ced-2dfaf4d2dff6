package co.cadshare.addresses.adapters.database;

import lombok.Data;

import javax.persistence.*;
import java.util.List;

@Data
@Entity
@Table(name="contactname")
public class AddressesContactEntity {

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private Integer id;

	@Column(name="contactname")
	private String name;

	@Column(name="externalrefid")
	private String externalRefId;

	@Column(name="deleted")
	private boolean deleted;
/*
	@OneToMany(fetch=FetchType.LAZY, orphanRemoval=true, mappedBy="contact", cascade = CascadeType.ALL)
	private List<UserContactMapEntity> userContactMaps;

 */
}
