package co.cadshare.media.core;

import co.cadshare.exceptions.BadClientRequestException;
import org.junit.Test;
import org.springframework.http.MediaType;

import java.io.ByteArrayInputStream;
import java.io.InputStream;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertTrue;

public class ImageTest {

	@Test
	public void constructorTest() {
		String mimeTypeAsString = MediaType.IMAGE_PNG_VALUE;
		InputStream stream = new ByteArrayInputStream(new byte[0]);
		Image out = new Image(mimeTypeAsString, stream);
		assertEquals(stream, out.getStream());
		out.validate();
		assertTrue(out.buildImageFileName().endsWith(".png"));
	}

	@Test(expected = BadClientRequestException.class)
	public void imageFailedValidation() {
		String incorrectMimeTypeAsString = MediaType.APPLICATION_OCTET_STREAM_VALUE;
		InputStream stream = new ByteArrayInputStream(new byte[0]);
		Image out = new Image(incorrectMimeTypeAsString, stream);
		out.validate();
	}
}
