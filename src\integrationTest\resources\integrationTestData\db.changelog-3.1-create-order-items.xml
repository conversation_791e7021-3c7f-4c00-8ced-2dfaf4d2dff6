<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                   http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.0.xsd
">

    <!-- orders 1 & 2: enquiry, for same purchaser (1), same manufacturer (Cat) -->
    <!-- order 3: enquiry, for different purchaser (2), same manufacturer (Cat) -->
    <!-- order 4: enquiry, for different purchaser (3), different manufacturer (JCB) -->
    <!-- order 5: enquiry, for different purchaser again (4), different manufacturer (Liebherr) -->

    <!-- order items for order 1 -->
    <changeSet author="AndyB" id="3.1-integration-test-data-create-order-items-1">
        <sql stripComments="true">
            INSERT INTO public.orderitem(orderitemid, partid, orderid, quantity, originalquantity,archived, partnumber)
            VALUES (1, 104, 1, 3, 0, FALSE, 'Cat-part-104');
        </sql>
    </changeSet>
    <changeSet author="AndyB" id="3.1-integration-test-data-create-order-items-2">
        <sql stripComments="true">
            INSERT INTO public.orderitem(orderitemid, partid, orderid, quantity, originalquantity, archived, partnumber)
            VALUES (2, 105, 1, 2, 0, FALSE, 'Cat-part-105');
        </sql>
    </changeSet>
    <changeSet author="AndyB" id="3.1-integration-test-data-create-order-items-3">
        <sql stripComments="true">
            INSERT INTO public.orderitem(orderitemid, partid, orderid, quantity, originalquantity, archived, partnumber)
            VALUES (3, 106, 1, 2, 0, FALSE, 'Cat-part-106');
        </sql>
    </changeSet>

    <!-- order items for order 2 -->
    <changeSet author="AndyB" id="3.1-integration-test-data-create-order-items-4">
        <sql stripComments="true">
            INSERT INTO public.orderitem(orderitemid, partid, orderid, quantity, originalquantity,archived, partnumber)
            VALUES (4, 104, 2, 3, 0, FALSE, 'Cat-part-104');
        </sql>
    </changeSet>
    <changeSet author="AndyB" id="3.1-integration-test-data-create-order-items-5">
        <sql stripComments="true">
            INSERT INTO public.orderitem(orderitemid, partid, orderid, quantity, originalquantity, archived, partnumber)
            VALUES (5, 105, 2, 2, 0, FALSE, 'Cat-part-105');
        </sql>
    </changeSet>
    <changeSet author="AndyB" id="3.1-integration-test-data-create-order-items-6">
        <sql stripComments="true">
            INSERT INTO public.orderitem(orderitemid, partid, orderid, quantity, originalquantity, archived, partnumber)
            VALUES (6, 106, 2, 2, 0, FALSE, 'Cat-part-106');
        </sql>
    </changeSet>

    <!-- order items for order 6 -->
    <changeSet author="AndyB" id="3.1-integration-test-data-create-order-items-7">
        <sql stripComments="true">
            INSERT INTO public.orderitem(orderitemid, partid, orderid, quantity, originalquantity,archived, partnumber, masterpartid)
            VALUES (7, 104, 6, 3, 0, FALSE, 'Cat-part-104', 104);
        </sql>
    </changeSet>
    <changeSet author="AndyB" id="3.1-integration-test-data-create-order-items-8">
        <sql stripComments="true">
            INSERT INTO public.orderitem(orderitemid, partid, orderid, quantity, originalquantity, archived, partnumber, masterpartid)
            VALUES (8, 105, 6, 2, 0, FALSE, 'Cat-part-105', 105);
        </sql>
    </changeSet>
    <changeSet author="AndyB" id="3.1-integration-test-data-create-order-items-9">
        <sql stripComments="true">
            INSERT INTO public.orderitem(orderitemid, partid, orderid, quantity, originalquantity, archived, partnumber, masterpartid)
            VALUES (9, 106, 6, 2, 0, FALSE, 'Cat-part-106', 106);
        </sql>
    </changeSet>

    <!-- order items for order 7 -->
    <changeSet author="AndyB" id="3.1-integration-test-data-create-order-items-10">
        <sql stripComments="true">
            INSERT INTO public.orderitem(orderitemid, partid, orderid, quantity, originalquantity, archived, partnumber, masterpartid, kitid)
            VALUES (10, null, 7, 2, 0, FALSE, 'Cat-kit-108', 108, 108);
        </sql>
    </changeSet>
</databaseChangeLog>
