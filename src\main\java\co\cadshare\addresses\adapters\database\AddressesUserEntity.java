package co.cadshare.addresses.adapters.database;

import co.cadshare.shared.adapters.database.BaseUserEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.*;
import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
@Entity
@Table(name = "users")
public class AddressesUserEntity extends BaseUserEntity {

    @OneToMany(cascade={ CascadeType.ALL }, fetch=FetchType.LAZY, orphanRemoval=true, mappedBy="user")
    private List<UserAddressMapEntity> userAddressMaps;
}

