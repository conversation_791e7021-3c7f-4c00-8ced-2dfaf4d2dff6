package co.cadshare.modelMgt.viewables.adapters.api.web;

import co.cadshare.modelMgt.shared.core.Viewable;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface ViewableMapper {

	ViewableMapper Instance = Mappers.getMapper(ViewableMapper.class);
	@Mapping(target = "range", source = "product.range")
	ViewableListItemDto viewableToViewableListItemDto(Viewable viewable);

	List<ViewableListItemDto> viewableToViewableListItemDto(List<Viewable> viewables);

}
