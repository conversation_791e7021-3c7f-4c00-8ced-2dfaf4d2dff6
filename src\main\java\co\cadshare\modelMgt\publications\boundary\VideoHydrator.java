package co.cadshare.modelMgt.publications.boundary;

import co.cadshare.modelMgt.shared.core.Publication;
import co.cadshare.modelMgt.publications.core.Video;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service
public class VideoHydrator implements PublicationAttributeHydrator {

	private final VideoQueryPort videoQueryPort;

	@Autowired
	public VideoHydrator(VideoQueryPort videoQueryPort) {
		this.videoQueryPort = videoQueryPort;
	}

	@Override
	public void hydrate(PublicationCommand command, Publication publication) {
		List<Video> videos = new ArrayList<>();
		if(command.hasVideos())
			command.getVideos().forEach(v -> videos.add(videoQueryPort.getVideoById(v)));
		publication.setVideos(videos);
	}
}
