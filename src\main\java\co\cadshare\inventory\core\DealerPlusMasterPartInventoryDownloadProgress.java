package co.cadshare.inventory.core;

import co.cadshare.shared.core.manufacturer.ManufacturerProgress;

public class DealerPlusMasterPartInventoryDownloadProgress extends ManufacturerProgress {

    public DealerPlusMasterPartInventoryDownloadProgress() { super(); }

    public DealerPlusMasterPartInventoryDownloadProgress(int manufacturerId, int dealerPlusSubEntityId) {
        super();
        setManufacturerId(manufacturerId);
        setDealerPlusSubEntityId(dealerPlusSubEntityId);
        setProcess(Process.MASTERPART_INVENTORY_EXPORT);
    }
}
