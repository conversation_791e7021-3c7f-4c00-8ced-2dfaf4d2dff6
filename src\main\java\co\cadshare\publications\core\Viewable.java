package co.cadshare.publications.core;

import co.cadshare.shared.core.user.User;
import co.cadshare.utils.ObjectExtension;
import co.cadshare.utils.ObjectListExtension;
import lombok.Data;
import lombok.experimental.ExtensionMethod;

import java.util.List;
import java.util.Optional;

@Data
@ExtensionMethod({ObjectListExtension.class, ObjectExtension.class})
public class Viewable {
    private int id;
    private String name;
    private Product product;
    private Boolean setupComplete;
    private List<Purchaser> purchasersAssignedToRange;
    private List<Snapshot> snapshots;

    public boolean isReadyForPublication(User user) {
        return user.getManufacturerSettings().isAutoPublishable() &&
                this.hasBeenInitialised();
    }

    private boolean hasBeenInitialised() {
        return setupComplete;
    }

    public void ensureProductHasThumbnail() {
        if(product.thumbnailUrlNotSet() && snapshots.existsAndHasItems()) {
            Optional<Snapshot> rootSnapShot = snapshots
                    .stream()
                    .filter(snapshot -> snapshot.getStateId().equals("ROOT"))
                    .findFirst();
            rootSnapShot.ifPresent(snapshot -> product.setThumbnailUrl(snapshot.getImgUrl()));
        }
    }
}
