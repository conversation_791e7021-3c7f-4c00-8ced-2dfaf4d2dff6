package co.cadshare.models.boundary;

import co.cadshare.models.core.Model;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class SearchModelsService {

    private ModelQueryPort modelQuery;

    @Autowired
    public SearchModelsService(ModelQueryPort modelQuery){
        this.modelQuery = modelQuery;
    }

    public List<Model> getPublishedModelsForPart(int dealerId, String masterPartNumber){
        PublishedModelsForMasterPartSearchCriteria searchCriteria = new PublishedModelsForMasterPartSearchCriteria() {{
            setDealerId(dealerId);
            setMasterPartNumber(masterPartNumber);
        }};

        return this.modelQuery.getPublishedModelsForMasterPart(searchCriteria);
    }
}
