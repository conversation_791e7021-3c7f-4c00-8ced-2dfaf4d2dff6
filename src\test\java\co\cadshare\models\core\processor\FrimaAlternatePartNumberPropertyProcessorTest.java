package co.cadshare.models.core.processor;

import co.cadshare.models.core.MetadataObjectExtended;
import co.cadshare.models.core.processor.fileproperties.AlternatePartNumberPropertyProcessor;
import co.cadshare.models.core.processor.fileproperties.NexatPartNumberPropertyProcessor;
import org.junit.Before;
import org.junit.Test;

import java.util.Collections;
import java.util.LinkedHashMap;

import static org.junit.Assert.assertEquals;

public class FrimaAlternatePartNumberPropertyProcessorTest {
    
    private AlternatePartNumberPropertyProcessor out;
    private LinkedHashMap<String, String> properties;
    private MetadataObjectExtended objectExtended;
    
    @Before
    public void Before() {
        out = new AlternatePartNumberPropertyProcessor();
        out.setSynonyms(Collections.singletonList("Component Name"));
        setProperties();
        objectExtended = new MetadataObjectExtended();
    }

    @Test
    public void shouldReturnAlternatePartNumber() {
        properties.put("Component Name", "460-000-P017");
        out.setProperties(properties, objectExtended);
        assertEquals("460-000-P017", objectExtended.getAlternatePartNumber());
    }


    private void setProperties() {
        properties = new LinkedHashMap<>();
        properties.put("Name", "460-000-P017-Name");
        properties.put("Component Appearance", "carbon steel");
    }
}
