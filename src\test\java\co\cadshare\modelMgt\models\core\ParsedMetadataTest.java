package co.cadshare.modelMgt.models.core;

import co.cadshare.modelMgt.models.core.MetadataObjectExtended;
import co.cadshare.modelMgt.models.core.ParsedMetadata;
import co.cadshare.shared.core.manufacturer.ManufacturerConfig;
import com.autodesk.client.ApiResponse;
import com.autodesk.client.model.Metadata;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.*;

import static org.junit.Assert.*;
import static org.mockito.Mockito.when;

public class ParsedMetadataTest {

    @Mock
    MetadataObjectExtended mockMetadataObjectExtended;
    private LinkedHashMap<Integer, List<String>> descs;
    private LinkedHashMap<Integer, List<String>> descsAndNumbers;
    private ManufacturerConfig config = new ManufacturerConfig();
    @Mock
    private ApiResponse<Metadata> mockMetadata;
    List<MetadataObjectExtended> metadata;
    private ParsedMetadata out;

    @Before
    public void Before() {
        MockitoAnnotations.initMocks(this);
        MetadataObjectExtended object = new MetadataObjectExtended();
        metadata = new ArrayList<>();
        object.setObjectid(1);
        object.setDescription("object desc 1");
        object.setPartNumber("object part number 1");
        object.setName("object name 1");
        metadata.add(object);
        object = new MetadataObjectExtended();
        object.setObjectid(2);
        object.setDescription("object desc 2");
        object.setPartNumber("object part number 2");
        object.setName("object name 2");
        metadata.add(object);
        object = new MetadataObjectExtended();
        object.setObjectid(3);
        object.setDescription("object desc 3");
        object.setPartNumber("object part number 3");
        object.setName("object name 3");
        metadata.add(object);
        metadata.add(mockMetadataObjectExtended);
        out = new ParsedMetadata(metadata);
        descs = new LinkedHashMap<>();
        descs.put(1, Collections.singletonList("Active Config Desc 1"));
        descs.put(3, Collections.singletonList("Active Config Desc 3"));
        descsAndNumbers = new LinkedHashMap<>();
        descsAndNumbers.put(1, Arrays.asList("Active Config Desc 1", "Active Config Part Number 1"));
        descsAndNumbers.put(2, Arrays.asList("Active Config Desc 2", "Active Config Part Number 2"));
    }

    @Test
    public void mergeSuccessOnDescription() {
        config.setUseExtendedMetadata(true);
        out.overrideExtendedProperties(config, descs);
        assertEquals(descs.get(1).get(0), out.get().get(0).getDescription());
        assertEquals(descs.get(3).get(0), out.get().get(2).getDescription());
        assertEquals("object desc 2", out.get().get(1).getDescription());
    }

    @Test
    public void noMergeRequiredOnDescription() {
        config.setUseExtendedMetadata(false);
        out.overrideExtendedProperties(config, descs);
        assertEquals("object desc 1", out.get().get(0).getDescription());
        assertEquals("object desc 3", out.get().get(2).getDescription());
        assertEquals("object desc 2", out.get().get(1).getDescription());
    }

    @Test
    public void mergeSuccessOnDescriptionAndNumber() {
        config.setUseExtendedMetadata(true);
        out.overrideExtendedProperties(config, descsAndNumbers);
        assertEquals(descsAndNumbers.get(1).get(0), out.get().get(0).getDescription());
        assertEquals(descsAndNumbers.get(1).get(1), out.get().get(0).getPartNumber());
        assertEquals(descsAndNumbers.get(2).get(0), out.get().get(1).getDescription());
        assertEquals(descsAndNumbers.get(2).get(1), out.get().get(1).getPartNumber());
        assertEquals("object desc 3", out.get().get(2).getDescription());
        assertEquals("object part number 3", out.get().get(2).getPartNumber());
    }

    @Test
    public void noMergeRequiredOnDescriptionAndNumber() {
        config.setUseExtendedMetadata(false);
        out.overrideExtendedProperties(config, descsAndNumbers);
        assertEquals("object desc 1", out.get().get(0).getDescription());
        assertEquals("object desc 3", out.get().get(2).getDescription());
        assertEquals("object desc 2", out.get().get(1).getDescription());
        assertEquals("object part number 1", out.get().get(0).getPartNumber());
        assertEquals("object part number 3", out.get().get(2).getPartNumber());
        assertEquals("object part number 2", out.get().get(1).getPartNumber());
    }

    @Test
    public void metadataToBeUploaded() {
        metadata = new ArrayList<>();
        metadata.add(new MetadataObjectExtended());
        assertTrue(out.metadataToBeUploaded());
    }

    @Test
    public void noMetadataToBeUploaded() {
        out = new ParsedMetadata();
        assertFalse(out.metadataToBeUploaded());
    }

    @Test
    public void noMetadataToBeUploadedEmpty() {
        metadata = new ArrayList<>();
        out = new ParsedMetadata(metadata);
        assertFalse(out.metadataToBeUploaded());
    }

    @Test
    public void isNotValid() {
        when(mockMetadataObjectExtended.isValid()).thenReturn(false);
        assertFalse(out.isValid());
    }

    @Test
    public void isValid() {
        when(mockMetadataObjectExtended.isValid()).thenReturn(true);
        assertTrue(out.isValid());
    }

    @Test
    public void get() {
        assertEquals(metadata, out.get());
    }

    @Test
    public void getNull() {
        ParsedMetadata parsedMetadata = new ParsedMetadata();
        assertNull(parsedMetadata.get());
    }

}
