package co.cadshare.products.boundary;

import co.cadshare.aspects.logging.ServiceLoggingAspect;
import co.cadshare.shared.core.user.User;
import co.cadshare.products.core.Product;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.MockitoAnnotations;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import static org.mockito.Mockito.*;

@EnableAspectJAutoProxy
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(classes = {UpdateProductService.class, ServiceLoggingAspect.class})
public class UpdateProductServiceTest {

    @MockBean
    private ProductCommandPort commandPort;
    @MockBean
    private ProductQueryPort queryPort;
    @Autowired
    private UpdateProductService out;
    private User user;
    private Product product;
    private Product errorProduct = new Product();

    @Before
    public void Before() {
        MockitoAnnotations.initMocks(this);
        product = buildProduct();
        user = new User() {{
            setUserId(1);
            setFirstName("firstName");
            setLastName("lastName");
        }};
    }

    @Test
    public void UpdateProductSuccess() throws Exception {
        doNothing().when(commandPort).update(user, product);
        out.update(user, product);
    }

    @Test(expected = RuntimeException.class)
    public void UpdateProductFailureException() throws Exception {
        doThrow(new RuntimeException("terrible")).when(commandPort).update(user, errorProduct);
        out.update(user, errorProduct);
    }

    private Product buildProduct() {
        Product product = new Product();
        return product;
    }
}
