package co.cadshare.modelMgt.models.adapters.database;

import co.cadshare.domainmodel.part.Part;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper(uses = {ModelsPartEntityMapper.class})
public interface ModelsPartEntityMapper {

    ModelsPartEntityMapper Instance = Mappers.getMapper(ModelsPartEntityMapper.class);

    Part clone(Part part);

    Part entityToCore(ModelsPartEntity entity);

    ModelsPartEntity coreToEntity(Part part);

    List<Part> entitiesToCores(List<ModelsPartEntity> entities);

    List<ModelsPartEntity> coresToEntities(List<Part> parts);

}
