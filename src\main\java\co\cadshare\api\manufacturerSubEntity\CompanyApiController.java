package co.cadshare.api.manufacturerSubEntity;

import co.cadshare.api.user.APIUser;
import co.cadshare.shared.core.manufacturer.subentity.ManufacturerSubEntity;
import co.cadshare.shared.core.user.User;
import co.cadshare.services.ManufacturerService;
import co.cadshare.services.ManufacturerSubEntityService;
import co.cadshare.services.PermissionsService;
import java.io.IOException;
import java.util.List;
import java.util.stream.Collectors;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/api/companies")
public class CompanyApiController {

  @Autowired
  ManufacturerService manufacturerService;

  @Autowired
  ManufacturerSubEntityService manufacturerSubEntityService;

  @Autowired
  PermissionsService permissionsService;

  private static final Logger logger = LoggerFactory.getLogger(CompanyApiController.class);

  @PreAuthorize("hasRole('ROLE_API_MANUFACTURER')")
  @GetMapping
  public HttpEntity<List<Company>> getManufacturerSubEntitiesForManufacturer(@AuthenticationPrincipal User currentUser) throws IOException {
    logger.info("API ACCESS: User [{}], getManufacturerSubEntitiesForManufacturer", currentUser.accessDetails());

    List<ManufacturerSubEntity> manufacturerSubEntities = manufacturerService.getManufacturerSubEntitiesForManufacturer(currentUser.getManufacturerId(), null);

    List<Company> companies = manufacturerSubEntities.stream()
        .map(subEntity -> Company.createFrom(subEntity))
        .collect(Collectors.toList());

    return new ResponseEntity<>(companies, HttpStatus.OK);
  }

  @PreAuthorize("hasRole('ROLE_API_MANUFACTURER')")
  @GetMapping(value = "/{companyId}/users")
  public HttpEntity<List<APIUser>> findUsersForSubEntityId(@AuthenticationPrincipal User currentUser, @PathVariable int companyId) throws IOException {
    logger.info("API ACCESS: User [{}], findCustomerByUserId", currentUser.accessDetails());

    try {
      hasAdminPermissionsForSubEntity(currentUser, companyId);
    } catch (Exception ex) {
      return new ResponseEntity<>(null, HttpStatus.FORBIDDEN);
    }

    List<User> users = manufacturerSubEntityService.getUsersForManufacturerSubEntity(companyId);

    List<APIUser> apiUsers = users.stream()
        .map(user -> APIUser.createFrom(user))
        .collect(Collectors.toList());

    return new ResponseEntity<>(apiUsers, HttpStatus.OK);
  }

  private boolean hasAdminPermissionsForSubEntity(User currentUser, int manufacturerSubEntityId) throws Exception {
    return permissionsService.hasAdminPermissionsForSubEntity(currentUser, manufacturerSubEntityId);
  }
}
