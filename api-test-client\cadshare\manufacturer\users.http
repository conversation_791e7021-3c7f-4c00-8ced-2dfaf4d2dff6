// log in

# @name cadshareAuthResponse
POST {{CADSHARE_URL}}/oauth/token
Content-Type: application/x-www-form-urlencoded
Authorization: {{$dotenv BASIC_AUTH_BASE64_ENCODED}}

username={{MANUFACTURER_USER_ID}}
&password={{STANDARD_PWD}}
&grant_type=password


###


// search for Master Parts with Part Number

@cadshareToken = Bearer {{cadshareAuthResponse.response.body.access_token}}

GET {{CADSHARE_URL}}/manufacturers/{{MANUFACTURER_ID}}/users
Authorization: {{cadshareToken}}


###

GET {{CADSHARE_URL}}/manufacturers/{{MANUFACTURER_ID}}/users/194
Authorization: {{cadshareToken}}

###

GET {{CADSHARE_URL}}/manufacturers/{{MANUFACTURER_ID}}/users/194/notification-subscriptions
Authorization: {{cadshareToken}}

###

POST {{CADSHARE_URL}}/manufacturers/{{MANUFACTURER_ID}}/users
Content-Type: application/json
Authorization: {{cadshareToken}}
Site-Url: https://local.cadshare.com/

{
	"firstName": "Tom",
	"lastName": "Thumb",
	"emailAddress": "<EMAIL>",
	"userPermissions": ["Admin", "Parts"],
	"userSettings": {
		"disableDiscountEditing": false
	},
	"notificationSubscriptions": [426, 429]
}


###

PUT {{CADSHARE_URL}}/manufacturers/{{MANUFACTURER_ID}}/users/331
Content-Type: application/json
Authorization: {{cadshareToken}}

{
	"firstName": "Tom",
	"lastName": "Thumb",
	"emailAddress": "<EMAIL>",
	"userPermissions": ["Admin", "Parts"],
	"userSettings": {
		"disableDiscountEditing": true
	},
	"notificationSubscriptions": [426, 429, 431]
}

###

PATCH {{CADSHARE_URL}}/manufacturers/{{MANUFACTURER_ID}}/users/194
Content-Type: application/json
Authorization: {{cadshareToken}}

{
	"notificationSubscriptions": [426, 429, 431]
}