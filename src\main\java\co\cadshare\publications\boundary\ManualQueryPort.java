package co.cadshare.publications.boundary;

import co.cadshare.domainmodel.serialnumber.SerialNumber;
import co.cadshare.publications.core.Manual;
import co.cadshare.response.CustomerManual;
import co.cadshare.shared.boundary.QueryFilter;

import java.util.List;

public interface ManualQueryPort {
    List<Manual> getFilteredList(QueryFilter filter);

    List<Manual> getManualsForModel(int modelId);

    Manual getManual(int manualId);

    List<CustomerManual> getManualsForManufacturerSubEntityId(int manufacturerSubEntityId);

    List<Integer> getAssignedManualIdsForManufacturerSubEntity(int manufacturerSubEntityId);

    List<Integer> getTechDocIdsByManualId(int manualId);

    List<Integer> getVideoIdsByManualId(int manualId);

    List<Integer> getKitsIdsByManualId(int manualId);

    List<Manual> getManualsForManufacturer(int manufacturerId, Boolean published, String sortBy, int resultSize);

    List<SerialNumber> getDetailManualsForManufacturer(int manufacturerId);

    List<Integer> getSubEntityIdsForManual(int manualId);

    List<String> getSubEntityNamesForManual(int manualId);

    List<Manual> getManualsForDealer(int dealerEntityId, Boolean published, String sortBy, int resultSize);

    List<Manual> getManualsForRange(int rangeId);
}
