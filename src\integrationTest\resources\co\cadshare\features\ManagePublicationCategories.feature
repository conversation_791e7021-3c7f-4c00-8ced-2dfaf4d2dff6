Feature: Manage PublicationCategories

  Scenario Outline: Get All PublicationCategories
    Given I am a Manufacturer with email address <emailAddress>
    Then I can view all PublicationCategories belonging to my Manufacturer
    Examples:
      | emailAddress                 |  |
      | <EMAIL> |  |


  Scenario Outline: Create new PublicationCategory as Manufacturer
    Given I am a Manufacturer with email address <emailAddress>
    And a publicationCategory with name <publicationCategoryName> doesn't exist
    When I create a new PublicationCategory named <publicationCategoryName>
    Then a PublicationCategory with name <publicationCategoryName> exists
    Examples:
      | emailAddress                 | publicationCategoryName |
      | <EMAIL> | New PublicationCategory |

Scenario Outline: Update PublicationCategory as Manufacturer
    Given I am a Manufacturer with email address <emailAddress>
    And a PublicationCategory with name <oldPublicationCategoryName> exists
    When I update the PublicationCategory named <oldPublicationCategoryName> to <newPublicationCategoryName>
    Then a PublicationCategory with name <newPublicationCategoryName> exists
    And a publicationCategory with name <oldPublicationCategoryName> doesn't exist
    Examples:
      | emailAddress                 | newPublicationCategoryName | oldPublicationCategoryName  |
      | <EMAIL> | New PublicationCategory    | Publication for Caterpillar |


  Scenario Outline: Delete PublicationCategory as Manufacturer
    Given I am a Manufacturer with email address <emailAddress>
    And I create a new PublicationCategory named <publicationCategoryName>
    And a PublicationCategory with name <publicationCategoryName> exists
    When I delete the PublicationCategory named <publicationCategoryName>
    Then a publicationCategory with name <publicationCategoryName> doesn't exist
    Examples:
      | emailAddress                 | publicationCategoryName      |
      | <EMAIL> | deleted Publication Category |

  Scenario Outline: Delete PublicationCategory Not Possible
    Given I am a Manufacturer with email address <emailAddress>
    And I create a new PublicationCategory named <publicationCategoryName>
    And I start with a new Publication
    When I select the PublicationCategory named <publicationCategoryName> for my Publication
    And I create this new Publication named <publicationName>
    Then I can't delete the PublicationCategory named <publicationCategoryName>
    Examples:
      | emailAddress                 | publicationName                 | publicationCategoryName | newViewableName     | purchaserName        |
      | <EMAIL> | New Publication for Caterpillar | PubCat for Publication  | Caterpillar Model 1 | Caterpillar Dealer 1 |


  Scenario Outline: Get PublicationCategory as Manufacturer
    Given I am a Manufacturer with email address <emailAddress>
    And I create a new PublicationCategory named <publicationCategoryName>
    And a PublicationCategory with name <publicationCategoryName> exists
    Then I get the PublicationCategory named <publicationCategoryName>
    Examples:
      | emailAddress                 | publicationCategoryName     |
      | <EMAIL> | Single Publication Category |
