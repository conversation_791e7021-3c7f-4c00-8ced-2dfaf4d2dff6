package co.cadshare.publications.adapters.database;

import co.cadshare.domainmodel.part.Part;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper(uses = {PublicationsPartEntityMapper.class})
public interface PublicationsPartEntityMapper {

    PublicationsPartEntityMapper Instance = Mappers.getMapper(PublicationsPartEntityMapper.class);

    Part clone(Part part);

    Part entityToCore(PublicationsPartEntity entity);

    PublicationsPartEntity coreToEntity(Part part);

    List<Part> entitiesToCores(List<PublicationsPartEntity> entities);

    List<PublicationsPartEntity> coresToEntities(List<Part> parts);

}
