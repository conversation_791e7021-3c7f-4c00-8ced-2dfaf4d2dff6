package co.cadshare.controller.dealer;

import co.cadshare.shared.core.manufacturer.subentity.ManufacturerSubEntity;
import co.cadshare.domainmodel.serialnumber.SerialNumber;
import co.cadshare.shared.core.user.User;
import co.cadshare.services.dealer.DealerService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Controller
@PreAuthorize("hasRole('ROLE_MANUFACTURER_SUB_ENTITY_DEALER_PLUS')")
@RequestMapping("/dealerplus/dealer")
public class DealerDealerController {

  @Autowired
  private DealerService dealerService;

  private final Logger log = LoggerFactory.getLogger(getClass());

  @PreAuthorize("#currentUser.manufacturerSubEntityId == #dealerEntityId")
  @RequestMapping(value = "/{dealerEntityId}/manualDetails", method = RequestMethod.GET)
  public HttpEntity<List<SerialNumber>> getManualsForDealer(@AuthenticationPrincipal User currentUser, @PathVariable int dealerEntityId,
  @RequestParam(value = "published", required = false) Boolean published) throws Exception {
    log.info("ACCESS: User [{}], getManualsForDealer, dealer [{}]", currentUser, dealerEntityId);
    List<SerialNumber> manualList = dealerService.getDetailedManualsForDealer(dealerEntityId, published);

    return new ResponseEntity<List<SerialNumber>>(manualList, HttpStatus.OK);
  }

  @PreAuthorize("#currentUser.manufacturerSubEntityId == #dealerEntityId")
  @RequestMapping(value = "/{dealerEntityId}/manufacturersubentities", method = RequestMethod.GET)
  public HttpEntity<List<ManufacturerSubEntity>> getManufacturerSubEntitiesForDealer(@AuthenticationPrincipal User currentUser, @PathVariable int dealerEntityId,
      @RequestParam(value = "subEntityType", required = false) ManufacturerSubEntity.ManufacturerSubEntityType subEntityType) throws Exception {
    log.info("ACCESS: User [{}], getManufacturerSubEntitiesForDealer, dealer [{}], subEntityType [{}]", currentUser, dealerEntityId, subEntityType);
    List<ManufacturerSubEntity> manufacturerSubEntityList = dealerService.getManufacturerSubEntitiesForDealer(dealerEntityId, subEntityType);

    return new ResponseEntity<List<ManufacturerSubEntity>>(manufacturerSubEntityList, HttpStatus.OK);
  }
}
