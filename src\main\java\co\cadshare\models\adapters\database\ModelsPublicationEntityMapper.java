package co.cadshare.models.adapters.database;

import co.cadshare.publications.adapters.database.CoverImageEntityMapper;
import co.cadshare.publications.adapters.database.PublicationsDealerEntityMapper;
import co.cadshare.publications.core.Publication;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper(uses = {CoverImageEntityMapper.class, PublicationsDealerEntityMapper.class})
public interface ModelsPublicationEntityMapper {
    ModelsPublicationEntityMapper Instance = Mappers.getMapper(ModelsPublicationEntityMapper.class);

    Publication entityToCore(ModelsPublicationEntity publication);

    ModelsPublicationEntity coreToEntity(Publication publication);
    List<Publication> entitiesToCores(List<ModelsPublicationEntity> publication);
    List<ModelsPublicationEntity> coresToEntities(List<Publication> publications);

}
