package co.cadshare.modelMgt.viewables.adapters.database;

import co.cadshare.modelMgt.publications.adapters.database.PublicationsPurchaserEntityViewMapper;
import co.cadshare.modelMgt.publications.adapters.database.PublicationsSnapshotEntityViewMapper;
import co.cadshare.modelMgt.publications.adapters.database.PublicationsViewableEntityView;
import co.cadshare.modelMgt.shared.core.Viewable;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper(uses = {
        PublicationsPurchaserEntityViewMapper.class,
        PublicationsSnapshotEntityViewMapper.class})
public interface ViewableEntityViewMapper {

    ViewableEntityViewMapper Instance = Mappers.getMapper(ViewableEntityViewMapper.class);

    Viewable entityToCore(PublicationsViewableEntityView entityView);

	List<Viewable> entitiesToCores(List<PublicationsViewableEntityView> entityView);
}
