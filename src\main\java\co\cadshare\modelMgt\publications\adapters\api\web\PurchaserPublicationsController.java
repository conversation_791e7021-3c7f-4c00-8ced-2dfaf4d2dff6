package co.cadshare.modelMgt.publications.adapters.api.web;

import co.cadshare.aspects.access.roles.*;
import co.cadshare.modelMgt.publications.boundary.*;
import co.cadshare.modelMgt.shared.core.Publication;
import co.cadshare.shared.core.user.User;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Description;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.*;

import java.util.List;


@RestController
@RequestMapping("/purchasers/{purchaser-id}/publications")
public class PurchaserPublicationsController {

    private final GetPublicationService getPublicationService;
    private final AssignPublicationsService assignPublicationsService;

    @Autowired
    public PurchaserPublicationsController(GetPublicationService getPublicationService,
                                           AssignPublicationsService assignPublicationsService){
        this.getPublicationService = getPublicationService;
        this.assignPublicationsService = assignPublicationsService;
    }

    @GetMapping(produces = "application/json")
	@CanAccessPurchaser
    @IsPurchaser
    @ResponseStatus(HttpStatus.OK)
    @Operation(summary = "Get the summary details of a list of Publications for a Purchaser")
    public ResponseEntity<GetPublicationsListResponseDto> getPublications(@AuthenticationPrincipal User currentUser,
																			@PathVariable("purchaser-id") int purchaserId) {
        List<Publication> publications = this.getPublicationService.getPublicationsForPurchaser(purchaserId);
        List<GetPublicationListItemResponseDto> publicationDtos = PublicationMapper.Instance.coresToListItemDtos(publications);
        GetPublicationsListResponseDto response = new GetPublicationsListResponseDto() {{
            setPublications(publicationDtos);
        }};
        return new ResponseEntity<>(response, HttpStatus.OK);
    }

    @GetMapping( path = "/{publication-id}", produces = "application/json")
    @CanAccessPurchaser
    @IsPurchaser
    @ResponseStatus(HttpStatus.OK)
    @Operation(summary = "Get the summary details of a single Publication for a Purchaser")
    public ResponseEntity<GetPublicationResponseDto> getPublication(@PathVariable("purchaser-id") int purchaserId,
                                                                    @PathVariable("publication-id") int publicationId,
                                                                    @AuthenticationPrincipal User currentUser) {

        Publication publication = this.getPublicationService.get(publicationId);
        GetPublicationResponseDto getResponseDto = PublicationMapper.Instance.coreToDto(publication);
        return new ResponseEntity<>(getResponseDto, HttpStatus.OK);
    }

    @IsDealerPlus
    @CanAccessPurchaser
    @PostMapping("/assign-to-purchaser")
    @Description("Assign publications to a purchaser")
    public HttpEntity<Void> assignPublications(@AuthenticationPrincipal User currentUser,
                                                        @PathVariable("purchaser-id") int purchaserId,
                                                        @RequestBody PostAssignPublicationsDto dto) throws Exception {

        AssignPublicationsCommand command = AssignPublicationsMapper.Instance.dtoToCommand(dto);
        assignPublicationsService.assignPublicationsToPurchaser(currentUser, command);
        return new ResponseEntity<>(HttpStatus.OK);
    }
}
