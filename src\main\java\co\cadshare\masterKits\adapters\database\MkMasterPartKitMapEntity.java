package co.cadshare.masterKits.adapters.database;

import lombok.Data;

import javax.persistence.*;

@Data
@Entity
@Table(name="masterpart_kit_map")
public class MkMasterPartKitMapEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name="id")
    private Integer id;

    @ManyToOne(cascade = {CascadeType.PERSIST, CascadeType.REFRESH, CascadeType.MERGE}, fetch = FetchType.LAZY)
    @JoinColumn(name = "masterpartid")
    private MkMasterPartEntity masterPartDetail;

    private int quantity;
}
