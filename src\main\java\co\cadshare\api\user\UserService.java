package co.cadshare.api.user;

import co.cadshare.shared.core.user.User;
import co.cadshare.shared.core.user.UserType;
import co.cadshare.persistence.ManufacturerSubEntityDao;
import co.cadshare.users.adapters.database.UserDetailsDao;
import java.util.ArrayList;
import java.util.Arrays;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
public class UserService {

  @Autowired
  private UserDetailsDao userDetailsDao;

  @Autowired
  private ManufacturerSubEntityDao manufacturerSubEntityDao;

  private final Logger logger = LoggerFactory.getLogger(getClass());

  @Transactional
  public int createUser(User apiManufacturerUser, APIUser user) throws Exception {

    if (user.getUserType() == null) {
      throw new IllegalArgumentException("User must have an associated customer company id");
    }

    User userToBeCreated = user.toUser();
    int userId = 0;

    if (userDetailsDao.isEmailAlreadyInUse(user.getEmailAddress(), apiManufacturerUser.getManufacturerId())) {
      //If User email already in user, confirm with current manufacturer that they can edit, else throw error until multi account email is handled
      User existingUser = userDetailsDao.findByEmailAddressAndManufacturer(user.getEmailAddress(), apiManufacturerUser.getManufacturerId());
      userId = existingUser.getUserId();
      if (apiManufacturerUser.getManufacturerId() != userToBeCreated.getManufacturerId()) {
        //User email account for a different manufacturer not handled yet
        throw new IllegalArgumentException("User email already associated with a different account");
      } else if (!existingUser.isActive()){
        //User has an existing account for this manufacturer, update details and reactivate account to match create request
        existingUser.setUserStatus(User.UserStatus.ACTIVE);
        updateUser(apiManufacturerUser.getUserId(), user, existingUser.getUserId());
      } else {
        //User account already active under selected manufacturer with that email. Update request should be sent instead.
        throw new IllegalArgumentException("User email already active with an account");
      }
    } else {
      //User email not already in system, user to be created as normal.
      userToBeCreated.setCreatedByUserId(apiManufacturerUser.getUserId());
      userToBeCreated.setModifiedByUserId(apiManufacturerUser.getUserId());

      userToBeCreated.setPassword("SSO No Password");
      userToBeCreated.setUserStatus(User.UserStatus.ACTIVE);
      userId = userDetailsDao.createUser(userToBeCreated);
      userToBeCreated.setUserId(userId);

      if (userToBeCreated.getUserType().name().startsWith("MANUFACTURER_SUB_ENTITY")) {
        if (userToBeCreated.getManufacturerSubEntityId() == null) {
          throw new IllegalArgumentException("User type is manufacturerSubEntity but manufacturerSubEntityId is not set");
        }
        manufacturerSubEntityDao.assignUserIdsToManufacturerSubEntity(new ArrayList<>(Arrays.asList(userId)), userToBeCreated.getManufacturerSubEntityId());
      } else {
        throw new IllegalArgumentException("Unknown user type " + userToBeCreated.getUserType());
      }
    }
    return userId;
  }

  public User findCustomerByUserId(int userId) {

    return userDetailsDao.findCustomerByUserId(userId);
  }

  public int updateUser(int currentUser, APIUser apiUser, int userId) throws Exception {
    User nominatedUser = userDetailsDao.findByUserId(userId);

    if (nominatedUser != null) {
      nominatedUser.setFirstName(apiUser.getFirstName());
      nominatedUser.setLastName(apiUser.getLastName());
      nominatedUser.setEmailAddress(apiUser.getEmailAddress());
      nominatedUser.setUserType(UserType.valueOf(apiUser.getUserType()));
      nominatedUser.setModifiedByUserId(currentUser);
    }

    if (nominatedUser.getManufacturerSubEntityId() != apiUser.getCompanyId()) {
      //User has changed companies and must be updated
      manufacturerSubEntityDao.updateAPIUserManufacturerSubentity(userId, apiUser.getCompanyId());
    }
    int result = userDetailsDao.updateUser(nominatedUser);

    if (result < 1) {
      throw new IllegalArgumentException("User Id not found");
    }

    return result;
  }

  public boolean deleteUser(int userId) {
    return userDetailsDao.deleteUser(userId);
  }

}
