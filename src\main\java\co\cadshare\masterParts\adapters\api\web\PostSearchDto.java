package co.cadshare.masterParts.adapters.api.web;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

@Data
public abstract class PostSearchDto {
    private String partNumber;
    private String partDescription;

    @JsonIgnore
    public boolean isSearchStringEmptyOrNull() {

        return (partNumber == null || partNumber.length() == 0)  &&
                (partDescription == null || partDescription.length() == 0);
    }
}
