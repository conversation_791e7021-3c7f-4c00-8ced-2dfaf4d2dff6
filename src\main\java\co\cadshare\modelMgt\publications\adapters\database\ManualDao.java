/*
 * Copyright 2016 Bell.
 */
package co.cadshare.modelMgt.publications.adapters.database;

import co.cadshare.modelMgt.publications.boundary.ManualQueryPort;
import co.cadshare.modelMgt.publications.core.Manual;
import co.cadshare.modelMgt.publications.core.ManualStatus;
import co.cadshare.shared.core.manufacturer.subentity.ManufacturerSubEntity;
import co.cadshare.domainmodel.serialnumber.SerialNumber;
import co.cadshare.persistence.ManufacturerSubEntityDao;
import co.cadshare.persistence.dealer.DealerPlusPublicationDao;
import co.cadshare.modelMgt.publications.boundary.ManualCommandPort;
import co.cadshare.response.CustomerManual;
import co.cadshare.shared.boundary.QueryFilter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.BatchPreparedStatementSetter;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.namedparam.BeanPropertySqlParameterSource;
import org.springframework.jdbc.core.namedparam.MapSqlParameterSource;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.jdbc.support.GeneratedKeyHolder;
import org.springframework.jdbc.support.KeyHolder;
import org.springframework.stereotype.Repository;

import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.sql.Types;
import java.util.*;

@Slf4j
@Repository
public class ManualDao implements ManualCommandPort, ManualQueryPort {

  @Autowired
  JdbcTemplate jdbcTemplate;
  @Autowired
  NamedParameterJdbcTemplate namedParamJdbcTemplate;
  @Autowired
  ManufacturerSubEntityDao manufacturerSubEntityDao;
  @Autowired
  DealerPlusPublicationDao dealerManualDao;

  @Autowired
  public ManualDao(JdbcTemplate jdbcTemplate,
                   NamedParameterJdbcTemplate namedParamJdbcTemplate,
                   ManufacturerSubEntityDao manufacturerSubEntityDao,
                   DealerPlusPublicationDao dealerManualDao) {

    this.jdbcTemplate = jdbcTemplate;
    this.namedParamJdbcTemplate = namedParamJdbcTemplate;
    this.manufacturerSubEntityDao = manufacturerSubEntityDao;
    this.dealerManualDao = dealerManualDao;
  }

  private static final String CREATE_MANUAL = "INSERT INTO manual (manualName, manualDescription, createdDate, createdByUserId, status, modifiedByUserId, modifiedDate, manufacturerid, serialNumber, featuredModelId, featuredModelUrl, useViewableImage) "
      + "VALUES( :manualName, :manualDescription, :createdDate, :createdByUserId, :status, :modifiedByUserId, :modifiedDate, :manufacturerId, :serialNumber, :featuredModelId, :featuredModelUrl, :useViewableImage)";

  private static final String CREATE_MANUAL_MODEL_MAP = "INSERT INTO manualmodelmap (manualid, modelid, archived) "
      + "VALUES( :manualid, :modelid, false)";

  private static final String CREATE_MANUAL_TECH_DOC_MAP = "INSERT INTO manual_techdoc_map (manualid, techdocid) "
      + "VALUES( :manualid, :techdocid)";

  private static final String CREATE_MANUAL_VIDEO_MAP = "INSERT INTO manual_video_map (manualid, videoid) "
          + "VALUES( :manualid, :videoid)";

  private static final String CREATE_MANUAL_KIT_MAP = "INSERT INTO manual_kit_map (manualid, kitid) "
          + "VALUES( :manualid, :kitid)";

  private static final String DELETE_MODEL_FROM_MANUAL_MODEL_MAP = "UPDATE manualmodelmap SET archived = TRUE WHERE modelid = :modelId";

  private static final String DELETE_TECH_DOC_FROM_MANUAL_TECH_DOC_MAP = "DELETE FROM manual_techdoc_map WHERE techdocid = :techDocId";

  private static final String DELETE_VIDEO_FROM_MANUAL_VIDEO_MAP = "DELETE FROM manual_video_map WHERE videoId = :videoId";

  private static final String DELETE_KIT_FROM_MANUAL_KIT_MAP = "DELETE FROM manual_kit_map WHERE kitid = :kitId";

  private final static String GET_MANUALS_FOR_MODEL_ID = "SELECT man.* FROM manual man INNER JOIN manualmodelmap mmm ON mmm.manualid = man.manualid WHERE mmm.modelId = :modelId  AND man.archived = FALSE";

  private final static String GET_MANUAL_FOR_MANUAL_ID = "SELECT * FROM manual WHERE manualId = :manualId AND manual.archived = FALSE ";

  private final static String GET_MANUALS = "SELECT * from manual WHERE manual.archived = FALSE ";
  @Override
  public int createManual(Manual manual) {

    Timestamp now = new Timestamp(Calendar.getInstance().getTime().getTime());
    manual.setCreatedDate(now);
    manual.setModifiedDate(now);
    BeanPropertySqlParameterSource namedParameters = new BeanPropertySqlParameterSource(manual);
    namedParameters.registerSqlType("fileType", Types.VARCHAR);
    namedParameters.registerSqlType("status", Types.VARCHAR);
    KeyHolder keyHolder = new GeneratedKeyHolder();

    namedParamJdbcTemplate.update(CREATE_MANUAL, namedParameters, keyHolder, new String[] { "manualid" });

    int manualid = keyHolder.getKey().intValue();

    if (manual.getModelId() != null && manual.getModelId().size() > 0) {
      for (int modelId : manual.getModelId()) {
        MapSqlParameterSource mapperParameters = new MapSqlParameterSource();
        mapperParameters.addValue("manualid", manualid);
        mapperParameters.addValue("modelid", modelId);

        namedParamJdbcTemplate.update(CREATE_MANUAL_MODEL_MAP, mapperParameters);
      }
    }

    if (manual.getTechDocId() != null && manual.getTechDocId().size() > 0) {
      for (int techDocId : manual.getTechDocId()) {
        MapSqlParameterSource mapperParameters = new MapSqlParameterSource();
        mapperParameters.addValue("manualid", manualid);
        mapperParameters.addValue("techdocid", techDocId);

        namedParamJdbcTemplate.update(CREATE_MANUAL_TECH_DOC_MAP, mapperParameters);
      }
    }

    if (manual.getVideoId() != null && manual.getVideoId().size() > 0) {
      for (int videoId : manual.getVideoId()) {
        MapSqlParameterSource mapperParameters = new MapSqlParameterSource();
        mapperParameters.addValue("manualid", manualid);
        mapperParameters.addValue("videoid", videoId);

        namedParamJdbcTemplate.update(CREATE_MANUAL_VIDEO_MAP, mapperParameters);
      }
    }

    if (manual.getKitId() != null && manual.getKitId().size() > 0) {
      for (int kitId : manual.getKitId()) {
        MapSqlParameterSource mapperParameters = new MapSqlParameterSource();
        mapperParameters.addValue("manualid", manualid);
        mapperParameters.addValue("kitid", kitId);

        namedParamJdbcTemplate.update(CREATE_MANUAL_KIT_MAP, mapperParameters);
      }
    }
    return manualid;
  }

  @Override
  public List<Manual> getFilteredList(QueryFilter filter) {
    return namedParamJdbcTemplate.query(filter.getQueryString(GET_MANUALS),
            filter.getParameters(),
            new BeanPropertyRowMapper<>(Manual.class));
  }


  @Override
  public Boolean deleteModelFromManualModelMap(int modelId) {
    Map namedParameters = new HashMap();
    namedParameters.put("modelId", modelId);

    namedParamJdbcTemplate.update(DELETE_MODEL_FROM_MANUAL_MODEL_MAP, namedParameters);
    return true;
  }

  @Override
  public Boolean deleteTechDocFromManualTechDocMap(int techDocId) {
    Map namedParameters = new HashMap();
    namedParameters.put("techDocId", techDocId);

    namedParamJdbcTemplate.update(DELETE_TECH_DOC_FROM_MANUAL_TECH_DOC_MAP, namedParameters);
    return true;
  }

  @Override
  public Boolean deleteVideoFromManualVideoMap(int videoId) {
    Map namedParameters = new HashMap();
    namedParameters.put("videoId", videoId);

    namedParamJdbcTemplate.update(DELETE_VIDEO_FROM_MANUAL_VIDEO_MAP, namedParameters);
    return true;
  }

  @Override
  public Boolean deleteKitFromManualKitMap(int kitId) {
    Map namedParameters = new HashMap();
    namedParameters.put("kitId", kitId);

    namedParamJdbcTemplate.update(DELETE_KIT_FROM_MANUAL_KIT_MAP, namedParameters);
    return true;
  }

  @Override
  public List<Manual> getManualsForModel(int modelId) {
    Map<String, Object> parameters = new HashMap<String, Object>();
    parameters.put("modelId", modelId);

    List<Manual> manualList = (List<Manual>) namedParamJdbcTemplate.query(GET_MANUALS_FOR_MODEL_ID, parameters,
        new BeanPropertyRowMapper<Manual>(Manual.class));

    return manualList;
  }

  @Override
  public Manual getManual(int manualId) {
    Map<String, Object> parameters = new HashMap<String, Object>();
    parameters.put("manualId", manualId);

    Manual manual = (Manual) namedParamJdbcTemplate.queryForObject(GET_MANUAL_FOR_MANUAL_ID, parameters,
        new BeanPropertyRowMapper<Manual>(Manual.class));

    manual.setModelId(getModelIdsByManualId(manualId));
    manual.setTechDocId(getTechDocIdsByManualId(manualId));
    manual.setVideoId(getVideoIdsByManualId(manualId));
    manual.setKitId(getKitsIdsByManualId(manualId));
    return manual;
  }

  private static final String UPDATE_MANUAL_STATUS = "UPDATE manual SET status = :status, modifieddate = :modifiedDate where manualId = :manualId";

  @Override
  public void updateManualStatus(int manualId, ManualStatus manualStatus) {
    Timestamp now = new Timestamp(Calendar.getInstance().getTime().getTime());

    MapSqlParameterSource namedParameters = new MapSqlParameterSource();
    namedParameters.addValue("manualId", manualId, Types.INTEGER);
    namedParameters.addValue("status", manualStatus.getStatus().toString(), Types.VARCHAR);
    namedParameters.addValue("modifiedDate", now);

    namedParamJdbcTemplate.update(UPDATE_MANUAL_STATUS, namedParameters);
  }

  private static final String DELETE_MANUAL = "UPDATE manual SET archived = TRUE where manualId = :manualId";

  @Override
  public Boolean deleteManual(int manualId) {
    Map namedParameters = new HashMap();
    namedParameters.put("manualId", manualId);

    namedParamJdbcTemplate.update(DELETE_MANUAL, namedParameters);
    return true;
  }

  private static final String GET_MANUALS_FROM_SERIAL_NUMBER_MAP_FOR_MANUFACTURER_SUB_ENTITY_ID = "SELECT man.*, "
      + "med.locationurl as coverImageUrl, "
      + "mod.modelid, mod.modelname, mod.autodeskurn as autodeskURN, mod.is2d, mac.name as machineName,  r.rangeid, r.name as rangeName "
      + "FROM manufacturersubentitymanualmap msemm "
      + "LEFT JOIN manual man ON man.manualid = msemm.manualid "
      + "LEFT JOIN manualmodelmap mmm ON man.manualid = mmm.manualid "
      + "LEFT JOIN model mod ON mod.modelid = mmm.modelid "
      + "LEFT JOIN machine mac ON mac.machineid = mod.machineid "
      + "LEFT JOIN range r ON r.rangeid = mac.rangeid "
      + "LEFT JOIN manual_techdoc_map mtdm ON mtdm.manualid = man.manualid "
		+ "LEFT JOIN media med ON med.mediaid = man.coverimageid "
      + "WHERE msemm.manufacturersubentityid = :manufacturerSubEntityId "
      + "AND man.status ='PUBLISHED' "
      + "AND man.archived = FALSE ";

  private static final String GET_MANUALS_FROM_RANGE_MAP_FOR_MANUFACTURER_SUB_ENTITY_ID = "SELECT r.rangeid, r.name as rangeName, man.*,"
      + "med.locationurl as coverImageUrl, "
      + "mod.modelid, mod.modelname, mod.autodeskurn as autodeskURN, mod.is2d, mac.name as machineName  "
      + "FROM ManufacturerSubEntityRangeMap msesnm " + "LEFT JOIN range r ON r.rangeid = msesnm.rangeid "
      + "LEFT JOIN machine mac ON mac.rangeid = r.rangeid " + "LEFT JOIN model mod ON mod.machineid = mac.machineid "
      + "LEFT JOIN manualmodelmap mmm ON mmm.modelid = mod.modelid "
      + "LEFT JOIN manual man ON man.manualid = mmm.manualid "
	  + "LEFT JOIN media med ON med.mediaid = man.coverimageid "
      + "WHERE manufacturersubentityid = :manufacturerSubEntityId AND (mod.autodeskstatus = 'PROPERTIES_PROCESSED' OR mod.autodeskstatus = 'PROPERTIES_PROCESSED_WITH_WARNINGS') AND man.status ='PUBLISHED' "
      + "AND man.archived = FALSE AND mod.archived = FALSE AND mac.archived = FALSE";

  private static final String MODELS_CONNECTED_TO_MANUAL = "SELECT COUNT(mmm.*) FROM manualModelMap mmm "
          + "INNER JOIN model mod ON mmm.modelid = mod.modelid "
          + "WHERE mmm.manualId = :manualId AND mmm.archived = false AND mod.archived = false ";



  @Override
  public List<CustomerManual> getManualsForManufacturerSubEntityId(int manufacturerSubEntityId) {
    Map<String, Object> parameters = new HashMap<String, Object>();
    parameters.put("manufacturerSubEntityId", manufacturerSubEntityId);

    List<CustomerManual> serialManualList = namedParamJdbcTemplate.query(
        GET_MANUALS_FROM_SERIAL_NUMBER_MAP_FOR_MANUFACTURER_SUB_ENTITY_ID, parameters,
        new BeanPropertyRowMapper<>(CustomerManual.class));

    List<CustomerManual> rangeManualList = (namedParamJdbcTemplate.query(
        GET_MANUALS_FROM_RANGE_MAP_FOR_MANUFACTURER_SUB_ENTITY_ID, parameters,
        new BeanPropertyRowMapper<>(CustomerManual.class)));

    List<CustomerManual> customerManualList = new ArrayList<>();
    serialManualList.forEach(customerManual -> {
      // The Machine object has an overriden equals method which just checks the
      // machine id
      if (!customerManualList.contains(customerManual)) {
        customerManualList.add(customerManual);
      }
    });

    // add both the lists together, serial number map list takes precedence if
    // the same machine id exists in both lists
    rangeManualList.forEach(customerManual -> {
      // The Machine object has an overriden equals method which just checks the
      // machine id
      if (!customerManualList.contains(customerManual)) {
        customerManualList.add(customerManual);
      }
    });

    // TODO - is this needed, added to return counts in first implementation,
    // might be redundant if we split the next load into a new page.
    customerManualList.forEach(customerManual -> {
      Map<String, Object> connectionsParameters = new HashMap<String, Object>();
      connectionsParameters.put("manualId", customerManual.getManualId());

      int count = namedParamJdbcTemplate.queryForObject(MODELS_CONNECTED_TO_MANUAL, connectionsParameters,
          Integer.class);

      customerManual.setAttachedModels(count);
    });

    return customerManualList;
  }

  private static final String GET_ASSIGNED_MANUAL_IDS_FROM_MANUFACTURER_SUB_ENTITY_MANUAL_MAP = "SELECT DISTINCT(man.manualid) "
          + "FROM manufacturersubentitymanualmap msemm "
          + "LEFT JOIN manual man ON man.manualid = msemm.manualid "
          + "WHERE msemm.manufacturersubentityid = :manufacturerSubEntityId "
          + "AND man.archived = FALSE ";

  @Override
  public List<Integer> getAssignedPublicationsForPurchaser(int manufacturerSubEntityId) {
    Map<String, Object> parameters = new HashMap<String, Object>();
    parameters.put("manufacturerSubEntityId", manufacturerSubEntityId);

    return namedParamJdbcTemplate.queryForList(GET_ASSIGNED_MANUAL_IDS_FROM_MANUFACTURER_SUB_ENTITY_MANUAL_MAP,
            parameters, Integer.class);
  }

  private static final String GET_MANUAL_MODEL_MAP = "SELECT modelId FROM manualmodelmap WHERE manualid = :manualId AND archived = FALSE";

  private List<Integer> getModelIdsByManualId(int manualId) {

    Map<String, Object> parameters = new HashMap<String, Object>();
    parameters.put("manualId", manualId);

    List<Integer> modelIdList = namedParamJdbcTemplate.queryForList(GET_MANUAL_MODEL_MAP, parameters, Integer.class);

    return modelIdList;
  }

  private static final String GET_MANUAL_TECH_DOC_MAP = "SELECT techdocid FROM manual_techdoc_map WHERE manualid = :manualId";

  @Override
  public List<Integer> getTechDocIdsByManualId(int manualId) {

    Map<String, Object> parameters = new HashMap<String, Object>();
    parameters.put("manualId", manualId);

    List<Integer> techDocIdList = namedParamJdbcTemplate.queryForList(GET_MANUAL_TECH_DOC_MAP, parameters, Integer.class);

    return techDocIdList;
  }

  private static final String GET_MANUAL_VIDEO_MAP = "SELECT videoid FROM manual_video_map WHERE manualid = :manualId";
  @Override
  public List<Integer> getVideoIdsByManualId(int manualId) {

    Map<String, Object> parameters = new HashMap<String, Object>();
    parameters.put("manualId", manualId);

    List<Integer> videoIdList = namedParamJdbcTemplate.queryForList(GET_MANUAL_VIDEO_MAP, parameters, Integer.class);

    return videoIdList;
  }

  private static final String GET_MANUAL_KIT_MAP = "SELECT kitid FROM manual_kit_map WHERE manualid = :manualId";
  @Override
  public List<Integer> getKitsIdsByManualId(int manualId) {

    Map<String, Object> parameters = new HashMap<String, Object>();
    parameters.put("manualId", manualId);

    List<Integer> kitIdList = namedParamJdbcTemplate.queryForList(GET_MANUAL_KIT_MAP, parameters, Integer.class);

    return kitIdList;

  }

  private static final String UPDATE_MANUAL = "UPDATE manual SET manualName = :manualName, serialNumber = :serialNumber, featuredModelId = :featuredModelId, featuredModelUrl = :featuredModelUrl, modifiedByUserId = :modifiedByUserId, modifiedDate = :modifiedDate, useViewableImage = :useViewableImage WHERE manualId = :manualId";

  @Override
  public int updateManual(Manual manual) {

    BeanPropertySqlParameterSource namedParameters = new BeanPropertySqlParameterSource(manual);

    namedParamJdbcTemplate.update(UPDATE_MANUAL, namedParameters);

    return manual.getManualId();
  }

  private static final String DELETE_MODEL_MANUAL_MAP = "DELETE FROM manualModelMap WHERE manualId = :manualId AND modelId IN (:modelId)";

  @Override
  public void deleteManualModelMap(int manualId, List<Integer> modelMapToArchive) {

    MapSqlParameterSource namedParameters = new MapSqlParameterSource();
    namedParameters.addValue("manualId", manualId, Types.INTEGER);
    namedParameters.addValue("modelId", modelMapToArchive, Types.INTEGER);

    namedParamJdbcTemplate.update(DELETE_MODEL_MANUAL_MAP, namedParameters);
  }

  private static final String DELETE_TECH_DOC_MANUAL_MAP = "DELETE FROM manual_techdoc_map WHERE manualId = :manualId AND techDocId IN (:techDocId)";

  @Override
  public void deleteManualTechDocMap(int manualId, List<Integer> techDocMappingsToDelete) {

    MapSqlParameterSource namedParameters = new MapSqlParameterSource();
    namedParameters.addValue("manualId", manualId, Types.INTEGER);
    namedParameters.addValue("techDocId", techDocMappingsToDelete, Types.INTEGER);

    namedParamJdbcTemplate.update(DELETE_TECH_DOC_MANUAL_MAP, namedParameters);
  }

  private static final String DELETE_VIDEO_MANUAL_MAP = "DELETE FROM manual_video_map WHERE manualId = :manualId AND videoId IN (:videoId)";

  @Override
  public void deleteManualVideoMap(int manualId, List<Integer> videoMappingsToDelete) {

    MapSqlParameterSource namedParameters = new MapSqlParameterSource();
    namedParameters.addValue("manualId", manualId, Types.INTEGER);
    namedParameters.addValue("videoId", videoMappingsToDelete, Types.INTEGER);

    namedParamJdbcTemplate.update(DELETE_VIDEO_MANUAL_MAP, namedParameters);
  }

  private static final String DELETE_KIT_MANUAL_MAP = "DELETE FROM manual_kit_map WHERE manualId = :manualId AND kitId IN (:kitId)";

  @Override
  public void deleteManualKitMap(int manualId, List<Integer> kitMappingsToDelete) {

    MapSqlParameterSource namedParameters = new MapSqlParameterSource();
    namedParameters.addValue("manualId", manualId, Types.INTEGER);
    namedParameters.addValue("kitId", kitMappingsToDelete, Types.INTEGER);

    namedParamJdbcTemplate.update(DELETE_KIT_MANUAL_MAP, namedParameters);
  }

  @Override
  public void createManualModelMap(int manualId, int modelId) {
    MapSqlParameterSource mapperParameters = new MapSqlParameterSource();
    mapperParameters.addValue("manualid", manualId);
    mapperParameters.addValue("modelid", modelId);

    namedParamJdbcTemplate.update(CREATE_MANUAL_MODEL_MAP, mapperParameters);
  }

  @Override
  public void createManualTechDocMap(int manualId, int techDocId) {
    MapSqlParameterSource mapperParameters = new MapSqlParameterSource();
    mapperParameters.addValue("manualid", manualId);
    mapperParameters.addValue("techdocid", techDocId);

    namedParamJdbcTemplate.update(CREATE_MANUAL_TECH_DOC_MAP, mapperParameters);
  }

  @Override
  public void createManualVideoMap(int manualId, int videoId) {
    MapSqlParameterSource mapperParameters = new MapSqlParameterSource();
    mapperParameters.addValue("manualid", manualId);
    mapperParameters.addValue("videoid", videoId);

    namedParamJdbcTemplate.update(CREATE_MANUAL_VIDEO_MAP, mapperParameters);
  }

  @Override
  public void createManualKitMap(int manualId, int kitId) {
    MapSqlParameterSource mapperParameters = new MapSqlParameterSource();
    mapperParameters.addValue("manualid", manualId);
    mapperParameters.addValue("kitid", kitId);

    namedParamJdbcTemplate.update(CREATE_MANUAL_KIT_MAP, mapperParameters);
  }

  private static final String GET_MANUALS_FOR_MANUFACTURER_ID_FILTERED =
      "SELECT * FROM manual AS man " +
          "WHERE man.manufacturerid = :manufacturerId " +
          "AND archived = false ";

  private static final String MANUAL_PUBLISHED_FILTER = " AND man.status = :status ";

  private static final String ORDER_BY = " ORDER BY ";

  @Override
  public List<Manual> getManualsForManufacturer(int manufacturerId, Boolean published, String sortBy, int resultSize) {

    Map<String, Object> parameters = new HashMap<String, Object>();
    parameters.put("manufacturerId", manufacturerId);

    String QUERY = GET_MANUALS_FOR_MANUFACTURER_ID_FILTERED;
    if (published != null) {
      String status = (published) ? "PUBLISHED" : "UNPUBLISHED";
      parameters.put("status", status);
      QUERY = QUERY.concat(MANUAL_PUBLISHED_FILTER);
    }
    QUERY = QUERY.concat(ORDER_BY);

    String filteredSQL = String.format(QUERY + "%s desc LIMIT %d", sortBy,
        resultSize);

    return namedParamJdbcTemplate.query(filteredSQL, parameters, new BeanPropertyRowMapper<>(Manual.class));
  }

  private static final String ASSIGN_MANUFACTURER_SUB_ENTITIES_TO_MANUAL = "INSERT INTO manufacturersubentitymanualmap (manualid, manufacturersubentityid) VALUES (?, ?)";

  @Override
  public void assignManualToManufacturerSubEntities(int manualId, List<Integer> manufacturerSubEntityIdList) {

    log.info("Assign SubEntities to Manual");
    int[] results = jdbcTemplate.batchUpdate(ASSIGN_MANUFACTURER_SUB_ENTITIES_TO_MANUAL,
            new BatchPreparedStatementSetter() {

              @Override
              public void setValues(PreparedStatement ps, int i) throws SQLException {
                int subEntity = manufacturerSubEntityIdList.get(i);
                ps.setInt(1, manualId);
                ps.setInt(2, subEntity);
              }

              @Override
              public int getBatchSize() {
                return manufacturerSubEntityIdList.size();
              }
            });

    log.info("Result of db call for assignSerialNumbersManufacturerSubEntity: [{}]", results);
  }


  private static final String DELETE_MANUFACTURER_SUB_ENTITIES_TO_MANUAL_MAPS_BY_MANUAL_ID = "DELETE FROM manufacturersubentitymanualmap WHERE manualid = %s AND manufacturersubentityid in (%s)";

  @Override
  public boolean clearMappingsForManual(int manualId, ArrayList<Integer> assignedManualIds) {

    if(assignedManualIds.size() > 0) {
      StringBuilder subEntities = new StringBuilder();
      assignedManualIds.forEach(mse -> subEntities.append(mse.toString().concat(",")));
      String subEntitiesStr = subEntities.toString();
      subEntitiesStr = subEntitiesStr.substring(0, subEntitiesStr.length() - 1);
      String updateStr = String.format(DELETE_MANUFACTURER_SUB_ENTITIES_TO_MANUAL_MAPS_BY_MANUAL_ID, manualId, subEntitiesStr);
      jdbcTemplate.execute(updateStr);
      log.info("Deleted SubEntity Mappings For Manual [{}]", manualId);
      return true;
    }
    return false;
  }

  private final static String GET_DETAILED_MANUALS_FOR_MANUFACTURER_ID = "SELECT DISTINCT ON (man.manualId) man.manualId, man.manualName, man.status AS manualstatus,  man.serialnumber, man.createddate, machine.name AS machinename, machine.machineid, range.rangeid, range.name AS rangename "
          + "FROM manual man "
          + "LEFT JOIN manualmodelmap mmm ON mmm.manualid = man.manualid "
          + "LEFT JOIN model ON model.modelid = mmm.modelid "
          + "LEFT JOIN machine ON machine.machineid = model.machineId "
          + "LEFT JOIN range ON range.rangeid = machine.rangeid "
          + "LEFT JOIN manufacturer ON manufacturer.manufacturerid = range.manufacturerid "
          + "WHERE manufacturer.manufacturerid = :manufacturerId "
          + "AND man.archived = FALSE AND model.archived = FALSE AND machine.archived = FALSE AND mmm.archived = FALSE";

  @Override
  public List<SerialNumber> getDetailManualsForManufacturer(int manufacturerId) {

    Map<String, Object> parameters = new HashMap<String, Object>();
    parameters.put("manufacturerId", manufacturerId);

    List<SerialNumber> serialNumberList = (List<SerialNumber>) namedParamJdbcTemplate.query(
            GET_DETAILED_MANUALS_FOR_MANUFACTURER_ID, parameters,
            new BeanPropertyRowMapper<SerialNumber>(SerialNumber.class));

    return serialNumberList;
  }

  private final static String GET_SUBENTITY_IDS_FOR_MANUAL = "SELECT manufacturersubentityid FROM manufacturersubentitymanualmap  "
          + "WHERE manualid = :manualId";

  @Override
  public List<Integer> getSubEntityIdsForManual(int manualId) {
    Map<String, Object> parameters = new HashMap<String, Object>();
    parameters.put("manualId", manualId);

    return namedParamJdbcTemplate.queryForList(GET_SUBENTITY_IDS_FOR_MANUAL, parameters, Integer.class);
  }

  private final static String GET_SUBENTITY_NAMES_FOR_MANUAL = "SELECT mse.name FROM manufacturersubentitymanualmap msemm "
          + "INNER JOIN manufacturersubentity mse ON msemm.manufacturersubentityid = mse.manufacturersubentityid "
          + "WHERE manualid = :manualId";

  @Override
  public List<String> getSubEntityNamesForManual(int manualId) {
    Map<String, Object> parameters = new HashMap<String, Object>();
    parameters.put("manualId", manualId);

    return namedParamJdbcTemplate.queryForList(GET_SUBENTITY_NAMES_FOR_MANUAL, parameters, String.class);
  }

  private static final String ASSIGN_MANUALS_TO_MANUFACTURER_SUB_ENTITY = "INSERT INTO manufacturersubentitymanualmap (manualid, manufacturersubentityid) VALUES (?, ?)";


  public boolean assignPurchaserToPublicationsold(int purchaserId, List<Integer> publications) {
    //If dealer plus unassign manuals to their customers also
    ManufacturerSubEntity purchaser = manufacturerSubEntityDao.getManufacturerSubEntity(purchaserId);
    if (purchaser.isDealerPlus()) {
      //Get all manuals assigned through publication prio to edit
      List<Integer> currentPublications = getAssignedPublicationsForPurchaser(purchaserId);

      List<Integer> deletedPublications = new ArrayList<>();
      currentPublications.forEach(manualId -> {
        if (!publications.contains(manualId)) {
          //If manual is not also assigned via publication add to list to be removed
          deletedPublications.add(manualId);
        }
      });

      //If manuals have been removed check which are to be unassigned
      if (!deletedPublications.isEmpty()) {
        //Get ranges currently assigned to the dealer
        List<Integer> currentPublicationCategories = dealerManualDao.getAssignedPublicationCategoriesForPurchaser(purchaserId);
        List<Integer> publicationsToBeUnassigned;
        if (currentPublicationCategories != null && !currentPublicationCategories.isEmpty()) {
          List<Integer> publicationsForPublicationCategories = dealerManualDao.getPublicationsForPublicationCategories(currentPublicationCategories);
          //Check manuals to be removed aren't still assigned via range to the Dealer Plus company
          List<Integer> toBeUnassigned = new ArrayList<>();
          deletedPublications.forEach(manualId -> {
            if (!publicationsForPublicationCategories.contains(manualId)) {
              //If manual is not also assigned via publication add to list to be removed
              toBeUnassigned.add(manualId);
            }
          });
          publicationsToBeUnassigned = toBeUnassigned;
        } else {
          publicationsToBeUnassigned = deletedPublications;
        }

        //Remove manual list from customers of current dealer subentity
        if (!publicationsToBeUnassigned.isEmpty()) {
          dealerManualDao.unassignPublicationsFromDealerPlusCustomers(purchaserId, publicationsToBeUnassigned);
        }
      }
    }

    boolean removedExisting = unassignPublicationsForPurchaser(purchaserId);
    if (removedExisting && publications.size() > 0) {
      assignPurchaserToPublications(purchaserId, publications);
    }
    return true;
  }

  @Override
  public void assignPurchaserToPublications(int purchaserId, List<Integer> publications) {
    log.info("Assign Manuals to subentity");
    int[] results = jdbcTemplate.batchUpdate(ASSIGN_MANUALS_TO_MANUFACTURER_SUB_ENTITY,
            new BatchPreparedStatementSetter() {

              @Override
              public void setValues(PreparedStatement ps, int i) throws SQLException {
                int manualId = publications.get(i);
                ps.setInt(1, manualId);
                ps.setInt(2, purchaserId);
              }

              @Override
              public int getBatchSize() {
                return publications.size();
              }
            });

    log.info("Result of db call for assignManufacturerSubEntityToManuals: [{}]", results);
  }

  private static final String DELETE_MANUFACTURER_SUB_ENTITIES_TO_MANUAL_MAPS_BY_SUBENTITY_ID =
          "DELETE FROM manufacturersubentitymanualmap WHERE manufacturersubentityid = :manufacturerSubEntityId";
  @Override
  public boolean unassignPublicationsForPurchaser(int manufacturerSubEntityId) {
    Map<String, Integer> namedParameters = new HashMap<String, Integer>();
    namedParameters.put("manufacturerSubEntityId", manufacturerSubEntityId);

    namedParamJdbcTemplate.update(DELETE_MANUFACTURER_SUB_ENTITIES_TO_MANUAL_MAPS_BY_SUBENTITY_ID, namedParameters);

    log.info("Deleted SubEntityManual Mappings For manufacturerSubEntityId [{}]", manufacturerSubEntityId);
    return true;
  }

  private static final String GET_MANUALS_FOR_DEALER_ID_FILTERED =
          "SELECT man.* FROM v_subentitymanuals v" +
                  "INNER JOIN manual man ON v.manualid = man.manualid " +
                  "WHERE v.manufacturersubentityid = :dealerEntityId " +
                  "AND man.archived = false ";

  @Override
  public List<Manual> getManualsForDealer(int dealerEntityId, Boolean published, String sortBy, int resultSize) {

    Map<String, Object> parameters = new HashMap<String, Object>();
    parameters.put("dealerEntityId", dealerEntityId);

    String QUERY = GET_MANUALS_FOR_DEALER_ID_FILTERED;
    if (published != null) {
      String status = (published) ? "PUBLISHED" : "UNPUBLISHED";
      parameters.put("status", status);
      QUERY = QUERY.concat(MANUAL_PUBLISHED_FILTER);
    }
    QUERY = QUERY.concat(ORDER_BY);

    String filteredSQL = String.format(QUERY + "%s desc LIMIT %d", sortBy,
            resultSize);

    return namedParamJdbcTemplate.query(filteredSQL, parameters, new BeanPropertyRowMapper<>(Manual.class));
  }

  private static final String GET_MANUALS_FOR_RANGE = "SELECT DISTINCT man.* "
          + "FROM manual man LEFT JOIN manualmodelmap mmm ON man.manualid = mmm.manualid "
          + "LEFT JOIN model mod ON mmm.modelid = mod.modelid LEFT JOIN machine mac ON mod.machineid = mac.machineid "
          + "WHERE mac.rangeid = :rangeId AND (mod.autodeskstatus = 'PROPERTIES_PROCESSED' OR mod.autodeskstatus = 'PROPERTIES_PROCESSED_WITH_WARNINGS') "
          + "AND man.status ='PUBLISHED' AND man.archived = FALSE AND mod.archived = FALSE AND mac.archived = FALSE";
  @Override
  public List<Manual> getManualsForRange(int rangeId) {
    Map<String, Object> parameters = new HashMap<>();
    parameters.put("rangeId", rangeId);
    return namedParamJdbcTemplate.query(GET_MANUALS_FOR_RANGE, parameters, new BeanPropertyRowMapper<>(Manual.class));
  }
}
