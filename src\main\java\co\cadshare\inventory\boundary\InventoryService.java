package co.cadshare.inventory.boundary;

import co.cadshare.inventory.core.MasterPartInventoryDownloadProgress;
import co.cadshare.inventory.core.MasterPartInventoryUploadProgress;
import co.cadshare.shared.adapters.aws.s3.S3StorageClient;
import co.cadshare.shared.core.manufacturer.ManufacturerProgress;
import co.cadshare.masterParts.core.MasterPart;
import co.cadshare.inventory.core.InventoryFile;
import co.cadshare.persistence.InventoryDao;
import co.cadshare.shared.adapters.database.manufacturer.ManufacturerProgressDao;
import co.cadshare.masterParts.adapters.database.MasterPartDao;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.time.Instant;
import java.util.List;

@Slf4j
@Service
public class InventoryService {

    private String PART_NUMBER_COLUMN_NAME = "Part Number";

    @Autowired
    private InventoryDao inventoryDao;

    @Autowired
    private MasterPartDao masterPartDao;

    @Autowired
    private ManufacturerProgressDao manufacturerProgressDao;

    @Autowired
    S3StorageClient s3StorageClient;

    @Async
    public void saveStock(Integer manufacturerId, InventoryFile inventoryFile) {
        log.info("Processing Inventory stock file with headers [{}] and [{}] updates", inventoryFile.getHeaders(),
                inventoryFile.getDataRows().size());

        Instant startDbInsert = Instant.now();
        inventoryDao.insertInventoryStockData(inventoryFile, manufacturerId);
        log.info("Translation file processed. Db operation took {} milliseconds", Instant.now().minusMillis(startDbInsert.toEpochMilli()).toEpochMilli());
    }

     @Async
    public void savePrice(Integer manufacturerId, InventoryFile inventoryFile) {
        log.info("Processing Inventory price file with headers [{}] and [{}] updates", inventoryFile.getHeaders(),
                inventoryFile.getDataRows().size());

        Instant startDbInsert = Instant.now();
        inventoryDao.insertInventoryPriceData(inventoryFile, manufacturerId);
        log.info("Translation file processed. Db operation took {} milliseconds", Instant.now().minusMillis(startDbInsert.toEpochMilli()).toEpochMilli());
    }

    @Async
    public void save(Integer manufacturerId, InventoryFile inventoryFile) {
        log.info("Processing Inventory file with headers [{}] and [{}] updates", inventoryFile.getHeaders(),
                inventoryFile.getDataRows().size());
        Instant startDbInsert = Instant.now();

        int numberOfHeaders = inventoryFile.getHeaders().getHeaderNames().size();
        Integer stockPosition = inventoryFile.getHeaders().getStockPosition();
        Integer pricePosition = inventoryFile.getHeaders().getPricePosition();

        ManufacturerProgress progress = new MasterPartInventoryUploadProgress(manufacturerId);
        try {
           
            int progressId = manufacturerProgressDao.createManufacturerProgress(progress);
            progress.setId(progressId);
            if (numberOfHeaders != 2) {
                if (stockPosition == null) {
                    savePrice(manufacturerId, inventoryFile);
                } else if (pricePosition == null) {
                    saveStock(manufacturerId, inventoryFile);
                }
            } else {
                inventoryDao.insertInventoryData(inventoryFile, manufacturerId);
                log.info("Inventory file processed. Db operation took {} milliseconds", Instant.now().minusMillis(startDbInsert.toEpochMilli()).toEpochMilli());
                progress.complete();
            }
        } catch (Exception ex) {
            log.error("Failed to Upload Inventory Data for manufacturer id [{}], with error: " + manufacturerId, ex.getMessage());
            progress.error();
        }
        manufacturerProgressDao.updateManufacturerProgress(progress);
    }

    @Async
    public void downloadMasterPartInventory(Integer manufacturerId) throws Exception {
        ManufacturerProgress progress = new MasterPartInventoryDownloadProgress(manufacturerId);
        int progressId = manufacturerProgressDao.createManufacturerProgress(progress);
        progress.setId(progressId);

        StringBuilder csvBuilder = generateHeaders();
        List<MasterPart> masterParts = masterPartDao.getMasterPartsForManufacturerId(manufacturerId);

        for (int i = 0; i < masterParts.size(); i++) {
            csvBuilder.append("\"" + masterParts.get(i).getPartNumber().trim() + "\",");
            csvBuilder.append(((masterParts.get(i).getStock() != null) ? masterParts.get(i).getStock() : "") + ",");
            csvBuilder.append(((masterParts.get(i).getPrice() != null) ? masterParts.get(i).getPrice() : ""));

            csvBuilder.append("\n");
        }

        try {
            String url = s3StorageClient.uploadCSVForMasterPartInventory(csvBuilder.toString(), manufacturerId);
            progress.setS3Url(url);
            progress.complete();
        } catch (Exception ex) {
            progress.error();
        }
        manufacturerProgressDao.updateManufacturerProgress(progress);
    }

    private StringBuilder generateHeaders() {
        StringBuilder csvHeadersBuilder = new StringBuilder();
        csvHeadersBuilder.append("\"" + PART_NUMBER_COLUMN_NAME);

        csvHeadersBuilder.append("\",\"Stock")
                .append("\",\"Price")
                .append("\"")
                .append("\n");
        return csvHeadersBuilder;
    }

    public String downloadMasterPartInventoryTemplate() {

        StringBuilder csvBuilder = generateHeaders();

        return csvBuilder.toString();
    }
}
