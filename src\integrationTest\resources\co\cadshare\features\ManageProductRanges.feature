Feature: Manage ProductRanges

  Scenario Outline: Get All ProductRanges
    Given I am a Manufacturer with email address <emailAddress>
    Then I can view all ProductRanges belonging to my Manufacturer
    Examples:
      | emailAddress                 |  |
      | <EMAIL> |  |

  Scenario Outline: Create new ProductRange as Manufacturer
    Given I am a Manufacturer with email address <emailAddress>
    And a ProductRange with name <ProductRangeName> doesn't exist
    When I create a new ProductRange named <ProductRangeName>
    Then a ProductRange with name <ProductRangeName> exists
    Examples:
      | emailAddress                 | ProductRangeName |
      | <EMAIL> | New ProductRange |

Scenario Outline: Update ProductRange as Manufacturer
    Given I am a Manufacturer with email address <emailAddress>
    And a ProductRange with name <oldProductRangeName> exists
    When I update the ProductRange named <oldProductRangeName> to <newProductRangeName>
    Then a ProductRange with name <newProductRangeName> exists
    And a ProductRange with name <oldProductRangeName> doesn't exist
    Examples:
      | emailAddress                 | newProductRangeName | oldProductRangeName  |
      | <EMAIL> | New ProductRange    | Publication for Caterpillar |

Scenario Outline: Delete ProductRange as Manufacturer
    Given I am a Manufacturer with email address <emailAddress>
    And I create a new ProductRange named <ProductRangeName>
    And a ProductRange with name <ProductRangeName> exists
    When I delete the ProductRange named <ProductRangeName>
    Then a ProductRange with name <ProductRangeName> doesn't exist
    Examples:
      | emailAddress                 | ProductRangeName      |
      | <EMAIL> | deleted Publication Category |

Scenario Outline: Get ProductRange as Manufacturer
    Given I am a Manufacturer with email address <emailAddress>
    And I create a new ProductRange named <ProductRangeName>
    And a ProductRange with name <ProductRangeName> exists
    Then I get the ProductRange named <ProductRangeName>
    Examples:
      | emailAddress                 | ProductRangeName     |
      | <EMAIL> | Single Publication Category |