/*
 * Copyright 2016 Bell.
 */
package co.cadshare.services;

import co.cadshare.domainmodel.machine.Machine;
import co.cadshare.domainmodel.range.Range;
import co.cadshare.shared.core.user.User;
import co.cadshare.persistence.MachineDao;
import co.cadshare.productRanges.adapters.database.RangeDao;
import java.util.ArrayList;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * TODO(dallanmc) Description of class.
 */
@Service
public class RangeService {

    @Autowired
    private RangeDao rangeDao;

    @Autowired
    private MachineDao machineDao;

    public Integer createRange (Range range) {
    if (rangeDao.rangeNameAlreadyExistsForManufacturer(range.getName(), range.getManufacturerId())) {
      throw new IllegalArgumentException("A Range with that name is already associated with this account");
    }
        return rangeDao.createRange(range);
    }

    public List<Machine> getMachinesForRange(int rangeId) {
        return machineDao.getMachinesForRange(rangeId);
    }

    public void assignRangesManufacturerSubEntity(ArrayList<Integer> rangeList, int manufacturerSubEntityId, int userId) throws Exception {
        rangeDao.assignRangesManufacturerSubEntity(rangeList, manufacturerSubEntityId, userId);
    }

    public List<Integer> getAssignedRangeIdsForManufacturerSubEntity(int manufacturerSubEntityId) {
        return rangeDao.getAssignedRangeIdsForManufacturerSubEntity(manufacturerSubEntityId);
    }

    public Boolean updateAssignedRanges(int manufacturerSubEntityId, ArrayList<Integer> assignedManualIds, User user) throws Exception {
        return rangeDao.updateAssignedRangesToManufacturerSubEntity(manufacturerSubEntityId, assignedManualIds, user);
    }
}
