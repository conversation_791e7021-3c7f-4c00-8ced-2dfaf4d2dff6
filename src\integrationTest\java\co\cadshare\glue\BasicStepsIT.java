package co.cadshare.glue;

import co.cadshare.utils.ObjectUtilsExtension;
import io.restassured.http.ContentType;
import io.restassured.response.Response;
import io.restassured.response.ValidatableResponse;
import io.restassured.specification.RequestSpecification;
import lombok.experimental.ExtensionMethod;
import org.hibernate.Session;
import org.hibernate.jdbc.Work;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.web.server.LocalServerPort;

import javax.persistence.EntityManager;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.List;

import static io.restassured.RestAssured.given;

@ExtensionMethod(ObjectUtilsExtension.class)
public abstract class BasicStepsIT {

    @LocalServerPort
    protected String port;
    protected final String baseUrl = "http://localhost:";
    protected static String token;
    @Autowired
    protected EntityManager entityManager;

    protected void executeQuery(String sql) {

        Session session = (Session) entityManager.getDelegate();
        session.doWork(new Work() {
            @Override
            public void execute(Connection connection) throws SQLException {
                //connection, finally!
                assert connection != null;

                ResultSet rs = connection.createStatement().executeQuery(sql);
                while (rs.next()) {
                    System.out.println(rs.getInt(1) + " " + rs.getString(2) + " " + rs.getString(3) + " " + rs.getString(4));
                }
            }
        });
    }

    protected <T> T getResource(String endpoint, Class<T> clazz) {
        return getGetResponse(endpoint).extract().body().as(clazz);
    }

    protected <T> List<T> getResourceList(String endpoint, Class<T> clazz) {
        return getGetResponse(endpoint)
                .extract().body().jsonPath()
                .getList("", clazz);
    }

    private ValidatableResponse getGetResponse(String endpoint) {
        return buildInitialAuthorisedRequest()
                .when()
                .get(endpoint)
                .then()
                .statusCode(200);
    }

    protected <T> Response postResource(String endpoint, T body) {
        return buildInitialAuthorisedRequest()
                .body(body)
                .when()
                .post(endpoint)
                .then()
                .statusCode(200)
                .extract().response();
    }

    protected <T, U> U actionResource(String endpoint, T body, Class<U> clazz) {
        return buildInitialAuthorisedRequest()
                .body(body)
                .when()
                .post(endpoint)
                .then()
                .statusCode(200)
                .extract().body().as(clazz);
    }

    protected <T> Response putResource(String endpoint, T body) {
        return buildInitialAuthorisedRequest()
                .body(body)
                .when()
                .put(endpoint)
                .then()
                .statusCode(200)
                .extract().response();
    }

    protected <T> Response deleteResource(String endpoint) {
        return buildInitialAuthorisedRequest()
                .when()
                .delete(endpoint)
                .then()
                .statusCode(200)
                .extract().response();
    }

    private RequestSpecification buildInitialAuthorisedRequest() {
        return given().header("Site-Url", baseUrl.concat(port))
                .contentType(ContentType.JSON)
                .auth().oauth2(token);
    }
}
