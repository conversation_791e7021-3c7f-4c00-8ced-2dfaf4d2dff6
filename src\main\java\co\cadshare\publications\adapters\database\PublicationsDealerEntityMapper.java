package co.cadshare.publications.adapters.database;

import co.cadshare.shared.core.purchaser.Dealer;
import co.cadshare.shared.core.purchaser.Purchaser;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper(uses = {  PublicationsUserEntityMapper.class })
public interface PublicationsDealerEntityMapper {

    PublicationsDealerEntityMapper Instance = Mappers.getMapper(PublicationsDealerEntityMapper.class);

    PublicationsDealerEntity externalDealerCoreToEntity(Dealer dealer);

    List<PublicationsDealerEntity> externalPurchaserCoresToDealerEntities(List<Purchaser> dealers);

    Dealer entityToExternalPurchaserCore(PublicationsDealerEntity dealer);

    List<Dealer> entitiesToPurchaserCores(List<PublicationsDealerEntity> dealers);


}
