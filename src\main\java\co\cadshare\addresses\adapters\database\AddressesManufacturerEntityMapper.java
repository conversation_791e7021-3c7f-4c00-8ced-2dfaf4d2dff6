package co.cadshare.addresses.adapters.database;

import co.cadshare.addresses.core.*;
import co.cadshare.shared.adapters.database.manufacturer.ManufacturerSettingsEntityMapper;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;
import org.mapstruct.factory.Mappers;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Mapper(uses = ManufacturerSettingsEntityMapper.class)
public interface AddressesManufacturerEntityMapper {

    AddressesManufacturerEntityMapper Instance = Mappers.getMapper(AddressesManufacturerEntityMapper.class);

    @Mapping(source=".", target="externalPurchasers", qualifiedByName="mapAllPurchasers")
    Manufacturer entityToCore(AddressesManufacturerEntity source);

    List<Manufacturer> entitiesToCores(List<AddressesManufacturerEntity> sources);

    @Named("mapAllPurchasers")
    static List<ExternalPurchaser> mapAllPurchasers(AddressesManufacturerEntity manufacturer) {
        List<ExternalPurchaser> purchasers = new ArrayList<>();
        List<ExternalDealer> dealers = AddressesDealerEntityMapper.Instance.entitiesToExternalPurchaserCores(manufacturer.getDealers());
        purchasers.addAll(dealers);
        List<ExternalCustomer> customers = AddressesCustomerEntityMapper.Instance.entitiesToExternalPurchaserCores(manufacturer.getCustomers());
        purchasers.addAll(customers);
        List<ExternalDealerPlus> dealerPluses = AddressesDealerPlusEntityMapper.Instance.entitiesToExternalPurchaserCores(manufacturer.getDealerPluses());
        purchasers.addAll(dealerPluses);
        List<ExternalRegionalOffice> regionalOffices = AddressesRegionalOfficeEntityMapper.Instance.entitiesToExternalPurchaserCores(manufacturer.getRegionalOffices());
        purchasers.addAll(regionalOffices);
        return purchasers;
    }

    @Mapping(source="externalPurchasers", target="dealers", qualifiedByName="mapAllDealers")
    @Mapping(source="externalPurchasers", target="customers", qualifiedByName="mapAllCustomers")
    @Mapping(source="externalPurchasers", target="dealerPluses", qualifiedByName="mapAllDealerPluses")
    @Mapping(source="externalPurchasers", target="regionalOffices", qualifiedByName="mapAllRegionalOffices")
    AddressesManufacturerEntity coreToEntity(Manufacturer source);

    @Named("mapAllDealers")
    public static List<AddressesDealerEntity> mapAllPurchasers(List<ExternalPurchaser> purchasers) {
        List<ExternalPurchaser> dealers = purchasers.stream()
                .filter(purchaser-> purchaser.getClass().equals(ExternalDealer.class))
                .collect(Collectors.toList());

        return AddressesDealerEntityMapper.Instance.externalPurchaserCoresToDealerEntities(dealers);
    }

    @Named("mapAllCustomers")
    public static List<AddressesCustomerEntity> mapAllCustomers(List<ExternalPurchaser> purchasers) {
        List<ExternalPurchaser> customers = purchasers.stream()
                .filter(purchaser-> purchaser.getClass().equals(ExternalCustomer.class))
                .collect(Collectors.toList());

        return AddressesCustomerEntityMapper.Instance.externalPurchaserCoresToCustomerEntities(customers);
    }

    @Named("mapAllDealerPluses")
    public static List<AddressesDealerPlusEntity> mapAllDealerPluses(List<ExternalPurchaser> purchasers) {
        List<ExternalPurchaser> dealerPluses = purchasers.stream()
                .filter(purchaser-> purchaser.getClass().equals(ExternalDealerPlus.class))
                .collect(Collectors.toList());

        return AddressesDealerPlusEntityMapper.Instance.externalPurchaserCoresToDealerPlusEntities(dealerPluses);
    }

    @Named("mapAllRegionalOffices")
    public static List<AddressesRegionalOfficeEntity> mapAllRegionalOffices(List<ExternalPurchaser> purchasers) {
        List<ExternalPurchaser> regionalOffices = purchasers.stream()
                .filter(purchaser-> purchaser.getClass().equals(ExternalRegionalOffice.class))
                .collect(Collectors.toList());

        return AddressesRegionalOfficeEntityMapper.Instance.externalPurchaserCoresToRegionalOfficeEntities(regionalOffices);
    }

}
