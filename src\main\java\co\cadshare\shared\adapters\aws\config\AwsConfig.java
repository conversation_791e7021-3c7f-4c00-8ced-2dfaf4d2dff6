package co.cadshare.shared.adapters.aws.config;

import co.cadshare.shared.adapters.ext.autodesk.CustomOauth2TwoLegged;
import co.cadshare.shared.adapters.ext.autodesk.assetsretriever.CadshareAustodeskAssetsRetrieverClientNonProd;
import co.cadshare.shared.adapters.ext.autodesk.assetsretriever.CadshareAustodeskAssetsRetrieverClientProd;
import co.cadshare.shared.adapters.ext.autodesk.assetsretriever.CadshareAutodeskAssetsRetrieverClient;
import com.amazonaws.internal.StaticCredentialsProvider;
import com.amazonaws.opensdk.config.ConnectionConfiguration;
import com.amazonaws.opensdk.config.TimeoutConfiguration;
import generated.assetsretriever.test.AutodeskAssetsRetriever;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;

@Configuration
public class AwsConfig {

    private StaticCredentialsProvider staticCredentialsProvider;
    private CustomOauth2TwoLegged oAuth2TwoLegged;

    @Autowired
    public AwsConfig(StaticCredentialsProvider staticCredentialsProvider, CustomOauth2TwoLegged oAuth2TwoLegged) {
        this.staticCredentialsProvider = staticCredentialsProvider;
        this.oAuth2TwoLegged = oAuth2TwoLegged;
    }

    @Bean
    @Profile("!prod")
    public CadshareAutodeskAssetsRetrieverClient cadshareAutodeskAssetsRetrieverClientNonProd() {
        return new CadshareAustodeskAssetsRetrieverClientNonProd(nonProdApiClient(), oAuth2TwoLegged.getCredentials());
    }

    private AutodeskAssetsRetriever nonProdApiClient() {
        return AutodeskAssetsRetriever.builder()
            .connectionConfiguration(new ConnectionConfiguration()
            .maxConnections(100)
            .connectionMaxIdleMillis(1000))
            .timeoutConfiguration(new TimeoutConfiguration()
                .httpRequestTimeout(10000)
                .totalExecutionTimeout(10000)
                .socketTimeout(10000))
            .iamCredentials(staticCredentialsProvider)
            .build();
    }

    @Bean
    @Profile("prod")
    public CadshareAutodeskAssetsRetrieverClient cadshareAutodeskAssetsRetrieverClientProd() {
        return new CadshareAustodeskAssetsRetrieverClientProd(prodApiClient(), oAuth2TwoLegged.getCredentials());
    }

    private generated.assetsretriever.prod.AutodeskAssetsRetriever prodApiClient() {
        return generated.assetsretriever.prod.AutodeskAssetsRetriever.builder()
            .connectionConfiguration(new ConnectionConfiguration()
                .maxConnections(100)
                .connectionMaxIdleMillis(1000))
            .timeoutConfiguration(new TimeoutConfiguration()
                .httpRequestTimeout(10000)
                .totalExecutionTimeout(10000)
                .socketTimeout(10000))
            .iamCredentials(staticCredentialsProvider)
            .build();
    }
}
