/*
 * Copyright 2016 Bell.
 */

package co.cadshare.controller;

import co.cadshare.aspects.access.languages.CanUseLanguage;
import co.cadshare.shared.core.Language;
import co.cadshare.models.core.model.softCopy.SoftCopy;
import co.cadshare.models.core.model.viewable.softCopyDetail.SoftCopyDetailParts;
import co.cadshare.shared.core.user.User;
import co.cadshare.services.SoftCopyService;
import java.util.List;

import co.cadshare.utils.ObjectUtilsExtension;
import lombok.experimental.ExtensionMethod;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

@ExtensionMethod(ObjectUtilsExtension.class)
@Slf4j
@Controller
@RequestMapping("/softCopy")
public class SoftCopyController {

  private SoftCopyService softCopyService;

  public SoftCopyController(SoftCopyService softCopyService) {
    this.softCopyService = softCopyService;
  }

  @PostMapping(value = "/model/{modelId}")
  public HttpEntity<Integer> createSoftCopy(@AuthenticationPrincipal User currentUser,
                                            @PathVariable int modelId,
                                            @RequestParam(value = "copySnapshots", required = false) boolean copySnapshots,
                                            @RequestBody SoftCopy softCopy) {

    softCopy.setCreatedByUserId(currentUser.getUserId());
    softCopy.setModifiedByUserId(currentUser.getUserId());
    softCopyService.create(modelId, softCopy, copySnapshots);

    //TODO update this to return correct ID
    return new ResponseEntity<>(modelId, HttpStatus.OK);
  }

  @GetMapping(value = "/model/{modelId}")
  @PreAuthorize("@permissionsDao.hasAccessToModel(#user.getUserId(), #modelId)")
  public HttpEntity<List<SoftCopy>> getModelSoftCopies(@AuthenticationPrincipal User user,
                                                       @PathVariable int modelId) {

    List<SoftCopy> softCopies = softCopyService.getByModelId(modelId);
    return new ResponseEntity<>(softCopies, HttpStatus.OK);
  }

  @DeleteMapping(value = "/{softCopyId}")
  @PreAuthorize("hasRole('ROLE_MANUFACTURER')")
  public HttpEntity<Boolean> deleteSoftCopy(@PathVariable int softCopyId) {
    Boolean isDeleted = softCopyService.delete(softCopyId);

    return new ResponseEntity<>(isDeleted, HttpStatus.OK);
  }

  @PutMapping(value = "/{softCopyId}")
  @PreAuthorize("hasRole('ROLE_MANUFACTURER')")
  public HttpEntity<Boolean> updateSoftCopy(@AuthenticationPrincipal User currentUser,
                                            @RequestBody SoftCopy softCopy,
                                            @PathVariable int softCopyId) {

    softCopy.setId(softCopyId);
    softCopy.setModifiedByUserId(currentUser.getUserId());
    softCopyService.update(softCopy);

    return new ResponseEntity<>(HttpStatus.OK);
  }

  //Used in PDF Export
  @GetMapping("/viewable/{viewableId}")
  @PreAuthorize("hasRole('ROLE_MANUFACTURER')")
  @CanUseLanguage
  public HttpEntity<List<SoftCopyDetailParts>> getSoftCopyDetailsForViewable(@AuthenticationPrincipal User currentUser,
                                                                             @PathVariable int viewableId,
                                                                             @RequestParam(value = "language", required = false) Language language) throws Exception {

	    Integer languageId = language != null ? currentUser.findLanguage(language).getLanguageId() : null;
	    return new ResponseEntity<>(softCopyService.getSoftCopyDetailsForViewable(currentUser, viewableId, languageId, currentUser.obtainDefaultLanguage()), HttpStatus.OK);
  }
}

