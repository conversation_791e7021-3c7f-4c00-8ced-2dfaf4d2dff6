<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                   http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.0.xsd
">

    <!-- masterpart 1 is superseded by masterpart 2 and that is superseeded by masterpart 3
    for testing dealer & manufacturer supersession History

    Cat-part-1 - cat-part-100 for masterParts supersession testing
    Cat-part-100 onwards for orders testing

    -->


    <changeSet author="AndyB" id="2.4-integration-test-data-create-master-parts-1">
        <sql stripComments="true">
            INSERT INTO public.masterpart (id, partnumber, manufacturerid, stocklevel, price, massunit, weight, note, type, supersessionpartnumber, superseded, maxsupersessionpartid, maxsupersessionpartnumber, insupersession, supersessionreverseindex)
            VALUES (
            3,                      --id,
            'Cat-part-3',           --partnumber,
            1,                      --manufacturerid,
            12,                     --stocklevel,
            790,                    --price,
            NULL,                   --massunit,
            NULL,                   --weight,
            NULL,                   --note,
            'PART',                 --type,
            NULL,                   --supersessionpartnumber
            false,                   --superseded
            3,                      --maxsupersessionpartid
            'Cat-part-3',           --maxsupersessionpartnumber
            true,                   --insupersession
            2);                     --supersessionreverseindex
        </sql>
    </changeSet>
    <changeSet author="AndyB" id="2.4-integration-test-data-create-master-parts-2">
        <sql stripComments="true">
            INSERT INTO public.masterpart (id, partnumber, manufacturerid, stocklevel, price, massunit, weight, note, type, supersessionpartnumber, superseded, maxsupersessionpartid, maxsupersessionpartnumber, insupersession, supersessionreverseindex)
            VALUES (
            2,                      --id,
            'Cat-part-2',           --partnumber,
            1,                      --manufacturerid,
            12,                     --stocklevel,
            790,                    --price,
            NULL,                   --massunit,
            NULL,                   --weight,
            NULL,                   --note,
            'PART',                 --type,
            'Cat-part-3',           --supersessionpartnumber
            true,                   --superseded
            3,                      --maxsupersessionpartid
            'Cat-part-3',           --maxsupersessionpartnumber
            true,                   --insupersession
            1);                     --supersessionreverseindex
        </sql>
    </changeSet>
    <changeSet author="AndyB" id="2.4-integration-test-data-create-master-parts-3">
        <sql stripComments="true">
            INSERT INTO public.masterpart (id, partnumber, manufacturerid, stocklevel, price, massunit, weight, note, type, supersessionpartnumber, superseded, maxsupersessionpartid, maxsupersessionpartnumber, insupersession, supersessionreverseindex)
            VALUES (
            1,                      --id,
            'Cat-part-1',           --partnumber,
            1,                      --manufacturerid,
            12,                     --stocklevel,
            790,                    --price,
            NULL,                   --massunit,
            NULL,                   --weight,
            NULL,                   --note,
            'PART',                 --type,
            'Cat-part-2',           --supersessionpartnumber
            true,                   --superseded
            3,                      --maxsupersessionpartid
            'Cat-part-3',           --maxsupersessionpartnumber
            true,                   --insupersession
            0);                     --supersessionreverseindex
        </sql>
    </changeSet>
    <changeSet author="AndyB" id="2.4-integration-test-data-create-master-parts-4">
        <sql stripComments="true">
            INSERT INTO public.masterpart (id, partnumber, manufacturerid, stocklevel, price, massunit, weight, note, type, supersessionpartnumber)
            VALUES (
            4,                      --id,
            'Cat-part-4',           --partnumber,
            1,                      --manufacturerid,
            12,                     --stocklevel,
            790,                    --price,
            NULL,                   --massunit,
            NULL,                   --weight,
            NULL,                   --note,
            'PART',                 --type,
            NULL);          --supersessionpartnumber
        </sql>
    </changeSet>
    <changeSet author="AndyB" id="2.4-integration-test-data-create-master-parts-5">
        <sql stripComments="true">
            INSERT INTO public.masterpart (id, partnumber, manufacturerid, stocklevel, price, massunit, weight, note, type, supersessionpartnumber)
            VALUES (
            5,                      --id,
            'Cat-part-5',           --partnumber,
            1,                      --manufacturerid,
            12,                     --stocklevel,
            790,                    --price,
            NULL,                   --massunit,
            NULL,                   --weight,
            NULL,                   --note,
            'PART',                 --type,
            NULL);          --supersessionpartnumber
        </sql>
    </changeSet>
    <changeSet author="AndyB" id="2.4-integration-test-data-create-master-parts-6">
        <sql stripComments="true">
            INSERT INTO public.masterpart (id, partnumber, manufacturerid, stocklevel, price, massunit, weight, note, type, supersessionpartnumber)
            VALUES (
            6,                      --id,
            'Cat-part-6',           --partnumber,
            1,                      --manufacturerid,
            12,                     --stocklevel,
            790,                    --price,
            NULL,                   --massunit,
            NULL,                   --weight,
            NULL,                   --note,
            'PART',                 --type,
            NULL);                  --supersessionpartnumber
        </sql>
    </changeSet>
    <changeSet author="AndyB" id="2.4-integration-test-data-create-master-parts-7">
        <sql stripComments="true">
            INSERT INTO public.masterpart (id, partnumber, manufacturerid, stocklevel, price, massunit, weight, note, type, supersessionpartnumber, superseded, maxsupersessionpartid, maxsupersessionpartnumber, insupersession, supersessionreverseindex)
            VALUES (
            7,                      --id,
            'Cat-part-7',           --partnumber,
            1,                      --manufacturerid,
            12,                     --stocklevel,
            790,                    --price,
            NULL,                   --massunit,
            NULL,                   --weight,
            NULL,                   --note,
            'PART',                 --type,
            'Cat-part-8',           --supersessionpartnumber
            true,                   --superseded
            11,                     --maxsupersessionpartid
            'Cat-part-11',          --maxsupersessionpartnumber
            true,                   --insupersession
            4);                     --supersessionreverseindex
        </sql>
    </changeSet>
    <changeSet author="AndyB" id="2.4-integration-test-data-create-master-parts-8">
        <sql stripComments="true">
            INSERT INTO public.masterpart (id, partnumber, manufacturerid, stocklevel, price, massunit, weight, note, type, supersessionpartnumber, superseded, maxsupersessionpartid, maxsupersessionpartnumber, insupersession, supersessionreverseindex)
            VALUES (
            8,                      --id,
            'Cat-part-8',           --partnumber,
            1,                      --manufacturerid,
            12,                     --stocklevel,
            790,                    --price,
            NULL,                   --massunit,
            NULL,                   --weight,
            NULL,                   --note,
            'PART',                 --type,
            'Cat-part-9',           --supersessionpartnumber
            true,                   --superseded
            11,                     --maxsupersessionpartid
            'Cat-part-11',          --maxsupersessionpartnumber
            true,                   --insupersession
            3);                     --supersessionreverseindex
        </sql>
    </changeSet>
    <changeSet author="AndyB" id="2.4-integration-test-data-create-master-parts-9">
        <sql stripComments="true">
            INSERT INTO public.masterpart (id, partnumber, manufacturerid, stocklevel, price, massunit, weight, note, type, supersessionpartnumber, superseded, maxsupersessionpartid, maxsupersessionpartnumber, insupersession, supersessionreverseindex)
            VALUES (
            9,                      --id,
            'Cat-part-9',           --partnumber,
            1,                      --manufacturerid,
            12,                     --stocklevel,
            790,                    --price,
            NULL,                   --massunit,
            NULL,                   --weight,
            NULL,                   --note,
            'PART',                 --type,
            'Cat-part-10',           --supersessionpartnumber
            true,                   --superseded
            11,                     --maxsupersessionpartid
            'Cat-part-11',          --maxsupersessionpartnumber
            true,                   --insupersession
            2);                     --supersessionreverseindex
        </sql>
    </changeSet>
    <changeSet author="AndyB" id="2.4-integration-test-data-create-master-parts-10">
        <sql stripComments="true">
            INSERT INTO public.masterpart (id, partnumber, manufacturerid, stocklevel, price, massunit, weight, note, type, supersessionpartnumber, superseded, maxsupersessionpartid, maxsupersessionpartnumber, insupersession, supersessionreverseindex)
            VALUES (
            10,                     --id,
            'Cat-part-10',          --partnumber,
            1,                      --manufacturerid,
            12,                     --stocklevel,
            790,                    --price,
            NULL,                   --massunit,
            NULL,                   --weight,
            NULL,                   --note,
            'PART',                 --type,
            'Cat-part-11',          --supersessionpartnumber
            true,                   --superseded
            11,                     --maxsupersessionpartid
            'Cat-part-11',          --maxsupersessionpartnumber
            true,                   --insupersession
            1);                     --supersessionreverseindex
        </sql>
    </changeSet>
    <changeSet author="AndyB" id="2.4-integration-test-data-create-master-parts-11">
        <sql stripComments="true">
            INSERT INTO public.masterpart (id, partnumber, manufacturerid, stocklevel, price, massunit, weight, note, type, supersessionpartnumber, superseded, maxsupersessionpartid, maxsupersessionpartnumber, insupersession, supersessionreverseindex)
            VALUES (
            11,                     --id,
            'Cat-part-11',          --partnumber,
            1,                      --manufacturerid,
            12,                     --stocklevel,
            790,                    --price,
            NULL,                   --massunit,
            NULL,                   --weight,
            NULL,                   --note,
            'PART',                 --type,
            NULL,                   --supersessionpartnumber
            false,                  --superseded
            11,                     --maxsupersessionpartid
            'Cat-part-11',          --maxsupersessionpartnumber
            true,                   --insupersession
            0);                     --supersessionreverseindex
        </sql>
    </changeSet>
    <changeSet author="AndyB" id="2.4-integration-test-data-create-master-parts-12">
        <sql stripComments="true">
            INSERT INTO public.masterpart (id, partnumber, manufacturerid, stocklevel, price, massunit, weight, note, type, supersessionpartnumber, superseded, maxsupersessionpartid, maxsupersessionpartnumber, insupersession, supersessionreverseindex)
            VALUES (
            12,                     --id,
            'Cat-part-12',          --partnumber,
            1,                      --manufacturerid,
            12,                     --stocklevel,
            790,                    --price,
            NULL,                   --massunit,
            NULL,                   --weight,
            NULL,                   --note,
            'PART',                 --type,
            'Cat-part-13',          --supersessionpartnumber
            true,                   --superseded
            17,                     --maxsupersessionpartid
            'Cat-part-17',          --maxsupersessionpartnumber
            true,                   --insupersession
            5);                     --supersessionreverseindex
        </sql>
    </changeSet>
    <changeSet author="AndyB" id="2.4-integration-test-data-create-master-parts-13">
        <sql stripComments="true">
            INSERT INTO public.masterpart (id, partnumber, manufacturerid, stocklevel, price, massunit, weight, note, type, supersessionpartnumber, superseded, maxsupersessionpartid, maxsupersessionpartnumber, insupersession, supersessionreverseindex)
            VALUES (
            13,                     --id,
            'Cat-part-13',          --partnumber,
            1,                      --manufacturerid,
            12,                     --stocklevel,
            790,                    --price,
            NULL,                   --massunit,
            NULL,                   --weight,
            NULL,                   --note,
            'PART',                 --type,
            'Cat-part-14',          --supersessionpartnumber
            true,                   --superseded
            17,                     --maxsupersessionpartid
            'Cat-part-17',          --maxsupersessionpartnumber
            true,                   --insupersession
            4);                     --supersessionreverseindex
        </sql>
    </changeSet>
    <changeSet author="AndyB" id="2.4-integration-test-data-create-master-parts-14">
        <sql stripComments="true">
            INSERT INTO public.masterpart (id, partnumber, manufacturerid, stocklevel, price, massunit, weight, note, type, supersessionpartnumber, superseded, maxsupersessionpartid, maxsupersessionpartnumber, insupersession, supersessionreverseindex)
            VALUES (
            14,                     --id,
            'Cat-part-14',          --partnumber,
            1,                      --manufacturerid,
            12,                     --stocklevel,
            790,                    --price,
            NULL,                   --massunit,
            NULL,                   --weight,
            NULL,                   --note,
            'PART',                 --type,
            'Cat-part-15',          --supersessionpartnumber
            true,                   --superseded
            17,                     --maxsupersessionpartid
            'Cat-part-17',          --maxsupersessionpartnumber
            true,                   --insupersession
            3);                     --supersessionreverseindex
        </sql>
    </changeSet>
    <changeSet author="AndyB" id="2.4-integration-test-data-create-master-parts-15">
        <sql stripComments="true">
            INSERT INTO public.masterpart (id, partnumber, manufacturerid, stocklevel, price, massunit, weight, note, type, supersessionpartnumber, superseded, maxsupersessionpartid, maxsupersessionpartnumber, insupersession, supersessionreverseindex)
            VALUES (
            15,                     --id,
            'Cat-part-15',          --partnumber,
            1,                      --manufacturerid,
            12,                     --stocklevel,
            790,                    --price,
            NULL,                   --massunit,
            NULL,                   --weight,
            NULL,                   --note,
            'PART',                 --type,
            'Cat-part-16',          --supersessionpartnumber
            true,                   --superseded
            17,                     --maxsupersessionpartid
            'Cat-part-17',          --maxsupersessionpartnumber
            true,                   --insupersession
            2);                     --supersessionreverseindex
        </sql>
    </changeSet>
    <changeSet author="AndyB" id="2.4-integration-test-data-create-master-parts-16">
        <sql stripComments="true">
            INSERT INTO public.masterpart (id, partnumber, manufacturerid, stocklevel, price, massunit, weight, note, type, supersessionpartnumber, superseded, maxsupersessionpartid, maxsupersessionpartnumber, insupersession, supersessionreverseindex)
            VALUES (
            16,                     --id,
            'Cat-part-16',          --partnumber,
            1,                      --manufacturerid,
            12,                     --stocklevel,
            790,                    --price,
            NULL,                   --massunit,
            NULL,                   --weight,
            NULL,                   --note,
            'PART',                 --type,
            'Cat-part-17',          --supersessionpartnumber
            true,                   --superseded
            17,                     --maxsupersessionpartid
            'Cat-part-17',          --maxsupersessionpartnumber
            true,                   --insupersession
            1);                     --supersessionreverseindex
        </sql>
    </changeSet>
    <changeSet author="AndyB" id="2.4-integration-test-data-create-master-parts-17">
        <sql stripComments="true">
            INSERT INTO public.masterpart (id, partnumber, manufacturerid, stocklevel, price, massunit, weight, note, type, supersessionpartnumber, superseded, maxsupersessionpartid, maxsupersessionpartnumber, insupersession, supersessionreverseindex)
            VALUES (
            17,                     --id,
            'Cat-part-17',          --partnumber,
            1,                      --manufacturerid,
            12,                     --stocklevel,
            790,                    --price,
            NULL,                   --massunit,
            NULL,                   --weight,
            NULL,                   --note,
            'PART',                 --type,
            NULL,                   --supersessionpartnumber
            false,                   --superseded
            17,                     --maxsupersessionpartid
            'Cat-part-17',          --maxsupersessionpartnumber
            true,                   --insupersession
            0);                     --supersessionreverseindex
        </sql>
    </changeSet>
    <changeSet author="AndyB" id="2.4-integration-test-data-create-master-parts-18">
        <sql stripComments="true">
            INSERT INTO public.masterpart (id, partnumber, manufacturerid, stocklevel, price, massunit, weight, note, type, supersessionpartnumber)
            VALUES (
            18,                     --id,
            'Cat-part-18',          --partnumber,
            1,                      --manufacturerid,
            12,                     --stocklevel,
            790,                    --price,
            NULL,                   --massunit,
            NULL,                   --weight,
            NULL,                   --note,
            'PART',                 --type,
            NULL);                  --supersessionpartnumber
        </sql>
    </changeSet>
    <!-- masterParts 19 & 20 for testing unsuccessful removal of MasterParts with history of only 2 parts -->
    <changeSet author="AndyB" id="2.4-integration-test-data-create-master-parts-19">
        <sql stripComments="true">
            INSERT INTO public.masterpart (id, partnumber, manufacturerid, stocklevel, price, massunit, weight, note, type, supersessionpartnumber, superseded, maxsupersessionpartid, maxsupersessionpartnumber, insupersession, supersessionreverseindex)
            VALUES (
            19,                     --id,
            'Cat-part-19',          --partnumber,
            1,                      --manufacturerid,
            12,                     --stocklevel,
            790,                    --price,
            NULL,                   --massunit,
            NULL,                   --weight,
            NULL,                   --note,
            'PART',                 --type,
            'Cat-part-20',          --supersessionpartnumber
            true,                   --superseded
            20,                     --maxsupersessionpartid
            'Cat-part-20',          --maxsupersessionpartnumber
            true,                   --insupersession
            1);                     --supersessionreverseindex
            --supersessionpartnumber
        </sql>
    </changeSet>
    <changeSet author="AndyB" id="2.4-integration-test-data-create-master-parts-20">
        <sql stripComments="true">
            INSERT INTO public.masterpart (id, partnumber, manufacturerid, stocklevel, price, massunit, weight, note, type, supersessionpartnumber, superseded, maxsupersessionpartid, maxsupersessionpartnumber, insupersession, supersessionreverseindex)
            VALUES (
            20,                     --id,
            'Cat-part-20',          --partnumber,
            1,                      --manufacturerid,
            12,                     --stocklevel,
            790,                    --price,
            NULL,                   --massunit,
            NULL,                   --weight,
            NULL,                   --note,
            'PART',                 --type,
            NULL,                   --supersessionpartnumber
            false,                  --superseded
            20,                     --maxsupersessionpartid
            'Cat-part-20',          --maxsupersessionpartnumber
            true,                   --insupersession
            0);                     --supersessionreverseindex
        </sql>
    </changeSet>
    <!-- master Parts 21 - 26 for testing removing masterPart from supersessionHistory -->
    <changeSet author="AndyB" id="2.4-integration-test-data-create-master-parts-21">
        <sql stripComments="true">
            INSERT INTO public.masterpart (id, partnumber, manufacturerid, stocklevel, price, massunit, weight, note, type, supersessionpartnumber, superseded, maxsupersessionpartid, maxsupersessionpartnumber, insupersession, supersessionreverseindex)
            VALUES (
            21,                     --id,
            'Cat-part-21',          --partnumber,
            1,                      --manufacturerid,
            12,                     --stocklevel,
            790,                    --price,
            NULL,                   --massunit,
            NULL,                   --weight,
            NULL,                   --note,
            'PART',                 --type,
            'Cat-part-22',          --supersessionpartnumber
            true,                  --superseded
            23,                     --maxsupersessionpartid
            'Cat-part-23',          --maxsupersessionpartnumber
            true,                   --insupersession
            2);                     --supersessionreverseindex
        </sql>
    </changeSet>
    <changeSet author="AndyB" id="2.4-integration-test-data-create-master-parts-22">
        <sql stripComments="true">
            INSERT INTO public.masterpart (id, partnumber, manufacturerid, stocklevel, price, massunit, weight, note, type, supersessionpartnumber, superseded, maxsupersessionpartid, maxsupersessionpartnumber, insupersession, supersessionreverseindex)
            VALUES (
            22,                     --id,
            'Cat-part-22',          --partnumber,
            1,                      --manufacturerid,
            12,                     --stocklevel,
            790,                    --price,
            NULL,                   --massunit,
            NULL,                   --weight,
            NULL,                   --note,
            'PART',                 --type,
            'Cat-part-23',          --supersessionpartnumber
            true,                   --superseded
            23,                     --maxsupersessionpartid
            'Cat-part-23',          --maxsupersessionpartnumber
            true,                   --insupersession
            1);                     --supersessionreverseindex
        </sql>
    </changeSet>
    <changeSet author="AndyB" id="2.4-integration-test-data-create-master-parts-23">
        <sql stripComments="true">
            INSERT INTO public.masterpart (id, partnumber, manufacturerid, stocklevel, price, massunit, weight, note, type, supersessionpartnumber, superseded, maxsupersessionpartid, maxsupersessionpartnumber, insupersession, supersessionreverseindex)
            VALUES (
            23,                     --id,
            'Cat-part-23',          --partnumber,
            1,                      --manufacturerid,
            12,                     --stocklevel,
            790,                    --price,
            NULL,                   --massunit,
            NULL,                   --weight,
            NULL,                   --note,
            'PART',                 --type,
            NULL,                   --supersessionpartnumber
            false,                   --superseded
            23,                     --maxsupersessionpartid
            'Cat-part-23',          --maxsupersessionpartnumber
            true,                   --insupersession
            0);                     --supersessionreverseindex
        </sql>
    </changeSet>
    <changeSet author="AndyB" id="2.4-integration-test-data-create-master-parts-24">
        <sql stripComments="true">
            INSERT INTO public.masterpart (id, partnumber, manufacturerid, stocklevel, price, massunit, weight, note, type, supersessionpartnumber, superseded, maxsupersessionpartid, maxsupersessionpartnumber, insupersession, supersessionreverseindex)
            VALUES (
            24,                     --id,
            'Cat-part-24',          --partnumber,
            1,                      --manufacturerid,
            12,                     --stocklevel,
            790,                    --price,
            NULL,                   --massunit,
            NULL,                   --weight,
            NULL,                   --note,
            'PART',                 --type,
            'Cat-part-25',          --supersessionpartnumber
            true,                   --superseded
            26,                     --maxsupersessionpartid
            'Cat-part-26',          --maxsupersessionpartnumber
            true,                   --insupersession
            2);                     --supersessionreverseindex
        </sql>
    </changeSet>
    <changeSet author="AndyB" id="2.4-integration-test-data-create-master-parts-25">
        <sql stripComments="true">
            INSERT INTO public.masterpart (id, partnumber, manufacturerid, stocklevel, price, massunit, weight, note, type, supersessionpartnumber, superseded, maxsupersessionpartid, maxsupersessionpartnumber, insupersession, supersessionreverseindex)
            VALUES (
            25,                     --id,
            'Cat-part-25',          --partnumber,
            1,                      --manufacturerid,
            12,                     --stocklevel,
            790,                    --price,
            NULL,                   --massunit,
            NULL,                   --weight,
            NULL,                   --note,
            'PART',                 --type,
            'Cat-part-26',          --supersessionpartnumber
            true,                   --superseded
            26,                     --maxsupersessionpartid
            'Cat-part-26',          --maxsupersessionpartnumber
            true,                   --insupersession
            1);                     --supersessionreverseindex
        </sql>
    </changeSet>
    <changeSet author="AndyB" id="2.4-integration-test-data-create-master-parts-26">
        <sql stripComments="true">
            INSERT INTO public.masterpart (id, partnumber, manufacturerid, stocklevel, price, massunit, weight, note, type, supersessionpartnumber, superseded, maxsupersessionpartid, maxsupersessionpartnumber, insupersession, supersessionreverseindex)
            VALUES (
            26,                     --id,
            'Cat-part-26',          --partnumber,
            1,                      --manufacturerid,
            12,                     --stocklevel,
            790,                    --price,
            NULL,                   --massunit,
            NULL,                   --weight,
            NULL,                   --note,
            'PART',                 --type,
            NULL,                   --supersessionpartnumber
            false,                   --superseded
            26,                     --maxsupersessionpartid
            'Cat-part-26',          --maxsupersessionpartnumber
            true,                   --insupersession
            0);                     --supersessionreverseindex
        </sql>
    </changeSet>
    <changeSet author="AndyB" id="2.4-integration-test-data-create-master-parts-27">
        <sql stripComments="true">
            INSERT INTO public.masterpart (id, partnumber, manufacturerid, stocklevel, price, massunit, weight, note, type, supersessionpartnumber, superseded, maxsupersessionpartid, maxsupersessionpartnumber, insupersession, supersessionreverseindex)
            VALUES (
            27,                     --id,
            'Cat-part-27',          --partnumber,
            1,                      --manufacturerid,
            12,                     --stocklevel,
            790,                    --price,
            NULL,                   --massunit,
            NULL,                   --weight,
            NULL,                   --note,
            'PART',                 --type,
            'Cat-part-28',          --supersessionpartnumber
            true,                   --superseded
            28,                     --maxsupersessionpartid
            'Cat-part-28',          --maxsupersessionpartnumber
            true,                   --insupersession
            1);                     --supersessionreverseindex
        </sql>
    </changeSet>
    <changeSet author="AndyB" id="2.4-integration-test-data-create-master-parts-28">
        <sql stripComments="true">
            INSERT INTO public.masterpart (id, partnumber, manufacturerid, stocklevel, price, massunit, weight, note, type, supersessionpartnumber, superseded, maxsupersessionpartid, maxsupersessionpartnumber, insupersession, supersessionreverseindex)
            VALUES (
            28,                     --id,
            'Cat-part-28',          --partnumber,
            1,                      --manufacturerid,
            12,                     --stocklevel,
            790,                    --price,
            NULL,                   --massunit,
            NULL,                   --weight,
            NULL,                   --note,
            'PART',                 --type,
            NULL,                   --supersessionpartnumber
            false,                  --superseded
            28,                     --maxsupersessionpartid
            'Cat-part-28',          --maxsupersessionpartnumber
            true,                   --insupersession
            0);                     --supersessionreverseindex
        </sql>
    </changeSet>
    <changeSet author="AndyB" id="2.4-integration-test-data-create-master-parts-29">
        <sql stripComments="true">
            INSERT INTO public.masterpart (id, partnumber, manufacturerid, stocklevel, price, massunit, weight, note, type, supersessionpartnumber, superseded, maxsupersessionpartid, maxsupersessionpartnumber, insupersession, supersessionreverseindex)
            VALUES (
            29,                     --id,
            'Cat-part-29',          --partnumber,
            1,                      --manufacturerid,
            12,                     --stocklevel,
            790,                    --price,
            NULL,                   --massunit,
            NULL,                   --weight,
            NULL,                   --note,
            'PART',                 --type,
            'Cat-part-30',          --supersessionpartnumber
            true,                   --superseded
            30,                     --maxsupersessionpartid
            'Cat-part-30',          --maxsupersessionpartnumber
            true,                   --insupersession
            1);                     --supersessionreverseindex
        </sql>
    </changeSet>
    <changeSet author="AndyB" id="2.4-integration-test-data-create-master-parts-30">
        <sql stripComments="true">
            INSERT INTO public.masterpart (id, partnumber, manufacturerid, stocklevel, price, massunit, weight, note, type, supersessionpartnumber, superseded, maxsupersessionpartid, maxsupersessionpartnumber, insupersession, supersessionreverseindex)
            VALUES (
            30,                     --id,
            'Cat-part-30',          --partnumber,
            1,                      --manufacturerid,
            12,                     --stocklevel,
            790,                    --price,
            NULL,                   --massunit,
            NULL,                   --weight,
            NULL,                   --note,
            'PART',                 --type,
            NULL,                   --supersessionpartnumber
            false,                  --superseded
            30,                     --maxsupersessionpartid
            'Cat-part-30',          --maxsupersessionpartnumber
            true,                   --insupersession
            0);                     --supersessionreverseindex
        </sql>
    </changeSet>
    <changeSet author="AndyB" id="2.4-integration-test-data-create-master-parts-31">
        <sql stripComments="true">
            INSERT INTO public.masterpart (id, partnumber, manufacturerid, stocklevel, price, massunit, weight, note, type, supersessionpartnumber, superseded, maxsupersessionpartid, maxsupersessionpartnumber, insupersession, supersessionreverseindex)
            VALUES (
            31,                     --id,
            'Cat-part-31',          --partnumber,
            1,                      --manufacturerid,
            12,                     --stocklevel,
            790,                    --price,
            NULL,                   --massunit,
            NULL,                   --weight,
            NULL,                   --note,
            'PART',                 --type,
            'Cat-part-32',          --supersessionpartnumber
            true,                   --superseded
            32,                     --maxsupersessionpartid
            'Cat-part-32',          --maxsupersessionpartnumber
            true,                   --insupersession
            1);                     --supersessionreverseindex
        </sql>
    </changeSet>
    <changeSet author="AndyB" id="2.4-integration-test-data-create-master-parts-32">
    <sql stripComments="true">
        INSERT INTO public.masterpart (id, partnumber, manufacturerid, stocklevel, price, massunit, weight, note, type, supersessionpartnumber, superseded, maxsupersessionpartid, maxsupersessionpartnumber, insupersession, supersessionreverseindex)
        VALUES (
        32,                     --id,
        'Cat-part-32',          --partnumber,
        1,                      --manufacturerid,
        12,                     --stocklevel,
        790,                    --price,
        NULL,                   --massunit,
        NULL,                   --weight,
        NULL,                   --note,
        'PART',                 --type,
        NULL,                   --supersessionpartnumber
        false,                  --superseded
        32,                     --maxsupersessionpartid
        'Cat-part-32',          --maxsupersessionpartnumber
        true,                   --insupersession
        0);                     --supersessionreverseindex
    </sql>
    </changeSet>
    <changeSet author="AndyB" id="2.4-integration-test-data-create-master-parts-33">
        <sql stripComments="true">
            INSERT INTO public.masterpart (id, partnumber, manufacturerid, stocklevel, price, massunit, weight, note, type, supersessionpartnumber, superseded, maxsupersessionpartid, maxsupersessionpartnumber, insupersession, supersessionreverseindex)
            VALUES (
            33,                     --id,
            'Cat-part-33',          --partnumber,
            1,                      --manufacturerid,
            12,                     --stocklevel,
            790,                    --price,
            NULL,                   --massunit,
            NULL,                   --weight,
            NULL,                   --note,
            'PART',                 --type,
            'Cat-part-34',          --supersessionpartnumber
            true,                   --superseded
            34,                     --maxsupersessionpartid
            'Cat-part-34',          --maxsupersessionpartnumber
            true,                   --insupersession
            1);                     --supersessionreverseindex
        </sql>
    </changeSet>
    <changeSet author="AndyB" id="2.4-integration-test-data-create-master-parts-34">
        <sql stripComments="true">
            INSERT INTO public.masterpart (id, partnumber, manufacturerid, stocklevel, price, massunit, weight, note, type, supersessionpartnumber, superseded, maxsupersessionpartid, maxsupersessionpartnumber, insupersession, supersessionreverseindex)
            VALUES (
            34,                     --id,
            'Cat-part-34',          --partnumber,
            1,                      --manufacturerid,
            12,                     --stocklevel,
            790,                    --price,
            NULL,                   --massunit,
            NULL,                   --weight,
            NULL,                   --note,
            'PART',                 --type,
            NULL,                   --supersessionpartnumber
            false,                  --superseded
            34,                     --maxsupersessionpartid
            'Cat-part-34',          --maxsupersessionpartnumber
            true,                   --insupersession
            0);                     --supersessionreverseindex
        </sql>
    </changeSet>

    <changeSet author="AndyB" id="2.4-integration-test-data-create-master-parts-35">
        <sql stripComments="true">
            INSERT INTO public.masterpart (id, partnumber, manufacturerid, stocklevel, price, massunit, weight, note, type, supersessionpartnumber, superseded, maxsupersessionpartid, maxsupersessionpartnumber, insupersession, supersessionreverseindex)
            VALUES (
            35,                      --id,
            'Cat-part-35',           --partnumber,
            1,                      --manufacturerid,
            12,                     --stocklevel,
            790,                    --price,
            NULL,                   --massunit,
            NULL,                   --weight,
            NULL,                   --note,
            'PART',                 --type,
            'Cat-part-36',           --supersessionpartnumber
            true,                   --superseded
            37,                     --maxsupersessionpartid
            'Cat-part-37',          --maxsupersessionpartnumber
            true,                   --insupersession
            2);                     --supersessionreverseindex
        </sql>
    </changeSet>
    <changeSet author="AndyB" id="2.4-integration-test-data-create-master-parts-36">
        <sql stripComments="true">
            INSERT INTO public.masterpart (id, partnumber, manufacturerid, stocklevel, price, massunit, weight, note, type, supersessionpartnumber, superseded, maxsupersessionpartid, maxsupersessionpartnumber, insupersession, supersessionreverseindex)
            VALUES (
            36,                     --id,
            'Cat-part-36',          --partnumber,
            1,                      --manufacturerid,
            12,                     --stocklevel,
            790,                    --price,
            NULL,                   --massunit,
            NULL,                   --weight,
            NULL,                   --note,
            'PART',                 --type,
            'Cat-part-37',          --supersessionpartnumber
            true,                   --superseded
            37,                     --maxsupersessionpartid
            'Cat-part-37',          --maxsupersessionpartnumber
            true,                   --insupersession
            1);                     --supersessionreverseindex
        </sql>
    </changeSet>
    <changeSet author="AndyB" id="2.4-integration-test-data-create-master-parts-37">
        <sql stripComments="true">
            INSERT INTO public.masterpart (id, partnumber, manufacturerid, stocklevel, price, massunit, weight, note, type, supersessionpartnumber, superseded, maxsupersessionpartid, maxsupersessionpartnumber, insupersession, supersessionreverseindex)
            VALUES (
            37,                     --id,
            'Cat-part-37',          --partnumber,
            1,                      --manufacturerid,
            12,                     --stocklevel,
            790,                    --price,
            NULL,                   --massunit,
            NULL,                   --weight,
            NULL,                   --note,
            'PART',                 --type,
            NULL,                   --supersessionpartnumber
            false,                  --superseded
            37,                     --maxsupersessionpartid
            'Cat-part-37',          --maxsupersessionpartnumber
            true,                   --insupersession
            0);                     --supersessionreverseindex
        </sql>
    </changeSet>

    <changeSet author="AndyB" id="2.4-integration-test-data-create-master-parts-38">
        <sql stripComments="true">
            INSERT INTO public.masterpart (id, partnumber, manufacturerid, stocklevel, price, massunit, weight, note, type, supersessionpartnumber, superseded, maxsupersessionpartid, maxsupersessionpartnumber, insupersession, supersessionreverseindex)
            VALUES (
            38,                      --id,
            'Cat-part-38',           --partnumber,
            1,                      --manufacturerid,
            12,                     --stocklevel,
            790,                    --price,
            NULL,                   --massunit,
            NULL,                   --weight,
            NULL,                   --note,
            'PART',                 --type,
            'Cat-part-39',           --supersessionpartnumber
            true,                   --superseded
            41,                     --maxsupersessionpartid
            'Cat-part-41',          --maxsupersessionpartnumber
            true,                   --insupersession
            2);                     --supersessionreverseindex
        </sql>
    </changeSet>
    <changeSet author="AndyB" id="2.4-integration-test-data-create-master-parts-39">
        <sql stripComments="true">
            INSERT INTO public.masterpart (id, partnumber, manufacturerid, stocklevel, price, massunit, weight, note, type, supersessionpartnumber, superseded, maxsupersessionpartid, maxsupersessionpartnumber, insupersession, supersessionreverseindex)
            VALUES (
            39,                     --id,
            'Cat-part-39',          --partnumber,
            1,                      --manufacturerid,
            12,                     --stocklevel,
            790,                    --price,
            NULL,                   --massunit,
            NULL,                   --weight,
            NULL,                   --note,
            'PART',                 --type,
            'Cat-part-40',          --supersessionpartnumber
            true,                   --superseded
            41,                     --maxsupersessionpartid
            'Cat-part-41',          --maxsupersessionpartnumber
            true,                   --insupersession
            1);                     --supersessionreverseindex
        </sql>
    </changeSet>
    <changeSet author="AndyB" id="2.4-integration-test-data-create-master-parts-40">
        <sql stripComments="true">
            INSERT INTO public.masterpart (id, partnumber, manufacturerid, stocklevel, price, massunit, weight, note, type, supersessionpartnumber, superseded, maxsupersessionpartid, maxsupersessionpartnumber, insupersession, supersessionreverseindex)
            VALUES (
            40,                     --id,
            'Cat-part-40',          --partnumber,
            1,                      --manufacturerid,
            12,                     --stocklevel,
            790,                    --price,
            NULL,                   --massunit,
            NULL,                   --weight,
            NULL,                   --note,
            'PART',                 --type,
            'Cat-part-41',          --supersessionpartnumber
            true,                   --superseded
            41,                     --maxsupersessionpartid
            'Cat-part-41',          --maxsupersessionpartnumber
            true,                   --insupersession
            1);                     --supersessionreverseindex
        </sql>
    </changeSet>
    <changeSet author="AndyB" id="2.4-integration-test-data-create-master-parts-41">
        <sql stripComments="true">
            INSERT INTO public.masterpart (id, partnumber, manufacturerid, stocklevel, price, massunit, weight, note, type, supersessionpartnumber, superseded, maxsupersessionpartid, maxsupersessionpartnumber, insupersession, supersessionreverseindex)
            VALUES (
            41,                     --id,
            'Cat-part-41',          --partnumber,
            1,                      --manufacturerid,
            12,                     --stocklevel,
            790,                    --price,
            NULL,                   --massunit,
            NULL,                   --weight,
            NULL,                   --note,
            'PART',                 --type,
            NULL,                   --supersessionpartnumber
            false,                  --superseded
            41,                     --maxsupersessionpartid
            'Cat-part-41',          --maxsupersessionpartnumber
            true,                   --insupersession
            0);                     --supersessionreverseindex
        </sql>
    </changeSet>

    <changeSet author="AndyB" id="2.4-integration-test-data-create-master-parts-42">
        <sql stripComments="true">
            INSERT INTO public.masterpart (id, partnumber, manufacturerid, stocklevel, price, massunit, weight, note, type, supersessionpartnumber, superseded, maxsupersessionpartid, maxsupersessionpartnumber, insupersession, supersessionreverseindex)
            VALUES (
            42,                     --id,
            'Cat-part-42',          --partnumber,
            1,                      --manufacturerid,
            12,                     --stocklevel,
            790,                    --price,
            NULL,                   --massunit,
            NULL,                   --weight,
            NULL,                   --note,
            'PART',                 --type,
            'Cat-part-43',          --supersessionpartnumber
            true,                   --superseded
            44,                     --maxsupersessionpartid
            'Cat-part-44',          --maxsupersessionpartnumber
            true,                   --insupersession
            2);                     --supersessionreverseindex
        </sql>
    </changeSet>
    <changeSet author="AndyB" id="2.4-integration-test-data-create-master-parts-43">
        <sql stripComments="true">
            INSERT INTO public.masterpart (id, partnumber, manufacturerid, stocklevel, price, massunit, weight, note, type, supersessionpartnumber, superseded, maxsupersessionpartid, maxsupersessionpartnumber, insupersession, supersessionreverseindex)
            VALUES (
            43,                     --id,
            'Cat-part-43',          --partnumber,
            1,                      --manufacturerid,
            12,                     --stocklevel,
            790,                    --price,
            NULL,                   --massunit,
            NULL,                   --weight,
            NULL,                   --note,
            'PART',                 --type,
            'Cat-part-44',          --supersessionpartnumber
            true,                   --superseded
            44,                     --maxsupersessionpartid
            'Cat-part-44',          --maxsupersessionpartnumber
            true,                   --insupersession
            1);                     --supersessionreverseindex
        </sql>
    </changeSet>
    <changeSet author="AndyB" id="2.4-integration-test-data-create-master-parts-44">
        <sql stripComments="true">
            INSERT INTO public.masterpart (id, partnumber, manufacturerid, stocklevel, price, massunit, weight, note, type, supersessionpartnumber, superseded, maxsupersessionpartid, maxsupersessionpartnumber, insupersession, supersessionreverseindex)
            VALUES (
            44,                     --id,
            'Cat-part-44',          --partnumber,
            1,                      --manufacturerid,
            12,                     --stocklevel,
            790,                    --price,
            NULL,                   --massunit,
            NULL,                   --weight,
            NULL,                   --note,
            'PART',                 --type,
            NULL,                   --supersessionpartnumber
            false,                  --superseded
            44,                     --maxsupersessionpartid
            'Cat-part-44',          --maxsupersessionpartnumber
            true,                   --insupersession
            0);                     --supersessionreverseindex
        </sql>
    </changeSet>

    <!-- master Parts > 100 for orders testing -->

    <changeSet author="AndyB" id="2.4-integration-test-data-create-master-parts-101">
        <sql stripComments="true">
            INSERT INTO public.masterpart (id, partnumber, manufacturerid, stocklevel, price, massunit, weight, note, type, supersessionpartnumber)
            VALUES (
            103,                      --id,
            'Cat-part-103',           --partnumber,
            1,                      --manufacturerid,
            12,                     --stocklevel,
            790,                    --price,
            NULL,                   --massunit,
            NULL,                   --weight,
            NULL,                   --note,
            'PART',                 --type,
            NULL);                  --supersessionpartnumber
        </sql>
    </changeSet>
    <changeSet author="AndyB" id="2.4-integration-test-data-create-master-parts-102">
        <sql stripComments="true">
            INSERT INTO public.masterpart (id, partnumber, manufacturerid, stocklevel, price, massunit, weight, note, type, supersessionpartnumber)
            VALUES (
            102,                      --id,
            'Cat-part-102',           --partnumber,
            1,                      --manufacturerid,
            12,                     --stocklevel,
            790,                    --price,
            NULL,                   --massunit,
            NULL,                   --weight,
            NULL,                   --note,
            'PART',                 --type,
            NULL);                  --supersessionpartnumber
        </sql>
    </changeSet>
    <changeSet author="AndyB" id="2.4-integration-test-data-create-master-parts-103">
        <sql stripComments="true">
            INSERT INTO public.masterpart (id, partnumber, manufacturerid, stocklevel, price, massunit, weight, note, type, supersessionpartnumber)
            VALUES (
            101,                      --id,
            'Cat-part-101',           --partnumber,
            1,                      --manufacturerid,
            12,                     --stocklevel,
            790,                    --price,
            NULL,                   --massunit,
            NULL,                   --weight,
            NULL,                   --note,
            'PART',                 --type,
            NULL);                  --supersessionpartnumber
        </sql>
    </changeSet>
    <changeSet author="AndyB" id="2.4-integration-test-data-create-master-parts-104">
        <sql stripComments="true">
            INSERT INTO public.masterpart (id, partnumber, manufacturerid, stocklevel, price, massunit, weight, note, type, supersessionpartnumber)
            VALUES (
            104,                      --id,
            'Cat-part-104',           --partnumber,
            1,                      --manufacturerid,
            12,                     --stocklevel,
            790,                    --price,
            NULL,                   --massunit,
            NULL,                   --weight,
            NULL,                   --note,
            'PART',                 --type,
            NULL);                  --supersessionpartnumber
        </sql>
    </changeSet>
    <changeSet author="AndyB" id="2.4-integration-test-data-create-master-parts-105">
        <sql stripComments="true">
            INSERT INTO public.masterpart (id, partnumber, manufacturerid, stocklevel, price, massunit, weight, note, type, supersessionpartnumber)
            VALUES (
            105,                      --id,
            'Cat-part-105',           --partnumber,
            1,                      --manufacturerid,
            12,                     --stocklevel,
            790,                    --price,
            NULL,                   --massunit,
            NULL,                   --weight,
            NULL,                   --note,
            'PART',                 --type,
            NULL);          --supersessionpartnumber
        </sql>
    </changeSet>
    <changeSet author="AndyB" id="2.4-integration-test-data-create-master-parts-106">
        <sql stripComments="true">
            INSERT INTO public.masterpart (id, partnumber, manufacturerid, stocklevel, price, massunit, weight, note, type, supersessionpartnumber)
            VALUES (
            106,                      --id,
            'Cat-part-106',           --partnumber,
            1,                      --manufacturerid,
            12,                     --stocklevel,
            790,                    --price,
            NULL,                   --massunit,
            NULL,                   --weight,
            NULL,                   --note,
            'PART',                 --type,
            NULL);                  --supersessionpartnumber
        </sql>
    </changeSet>
    <changeSet author="AndyB" id="2.4-integration-test-data-create-master-parts-107">
        <sql stripComments="true">
            INSERT INTO public.masterpart (id, partnumber, manufacturerid, stocklevel, price, massunit, weight, note, type, supersessionpartnumber)
            VALUES (
            107,                      --id,
            'Cat-part-107',           --partnumber,
            1,                      --manufacturerid,
            12,                     --stocklevel,
            790,                    --price,
            NULL,                   --massunit,
            NULL,                   --weight,
            NULL,                   --note,
            'PART',                 --type,
            NULL);            --supersessionpartnumber
        </sql>
    </changeSet>

    <changeSet author="AndyB" id="2.4-integration-test-data-create-master-parts-108">
        <sql stripComments="true">
            INSERT INTO public.masterpart (id, partnumber, manufacturerid, stocklevel, price, massunit, weight, note, type, supersessionpartnumber)
            VALUES (
            108,                    --id,
            'Cat-kit-108',          --partnumber,
            1,                      --manufacturerid,
            12,                     --stocklevel,
            790,                    --price,
            NULL,                   --massunit,
            NULL,                   --weight,
            NULL,                   --note,
            'KIT',                  --type,
            NULL);                  --supersessionpartnumber
        </sql>
    </changeSet>

    <!-- master Parts > 200 for searching master parts testing -->

    <changeSet author="AndyB" id="2.4-integration-test-data-create-master-parts-201">
        <sql stripComments="true">
            INSERT INTO public.masterpart (id, partnumber, manufacturerid, stocklevel, price, massunit, weight, note, type, supersessionpartnumber)
            VALUES (
            201,                    --id,
            'Jcb-part-201',     --partnumber,
            2,                      --manufacturerid,
            12,                     --stocklevel,
            790,                    --price,
            NULL,                   --massunit,
            NULL,                   --weight,
            NULL,                   --note,
            'PART',                 --type,
            NULL);                  --supersessionpartnumber
        </sql>
    </changeSet>

    <changeSet author="AndyB" id="2.4-integration-test-data-create-master-parts-202">
        <sql stripComments="true">
            INSERT INTO public.masterpart (id, partnumber, manufacturerid, stocklevel, price, massunit, weight, note, type, supersessionpartnumber)
            VALUES (
            202,                    --id,
            'Lbr-part-202',     --partnumber,
            3,                      --manufacturerid,
            12,                     --stocklevel,
            790,                    --price,
            NULL,                   --massunit,
            NULL,                   --weight,
            NULL,                   --note,
            'PART',                 --type,
            NULL);                  --supersessionpartnumber
        </sql>
    </changeSet>

    <changeSet author="AndyB" id="2.4-integration-test-data-create-master-parts-203">
        <sql stripComments="true">
            INSERT INTO public.masterpart (id, partnumber, manufacturerid, stocklevel, price, massunit, weight, note, type, supersessionpartnumber)
            VALUES (
            203,                    --id,
            'Trx-part-203',     --partnumber,
            4,                      --manufacturerid,
            12,                     --stocklevel,
            790,                    --price,
            NULL,                   --massunit,
            NULL,                   --weight,
            NULL,                   --note,
            'PART',                 --type,
            NULL);                  --supersessionpartnumber
        </sql>
    </changeSet>

    <changeSet author="AndyB" id="2.4-integration-test-data-create-master-parts-211">
        <sql stripComments="true">
            INSERT INTO public.masterpart (id, partnumber, manufacturerid, stocklevel, price, massunit, weight, note, type, supersessionpartnumber)
            VALUES (
            211,                    --id,
            'Cat-kit-part-211',     --partnumber,
            1,                      --manufacturerid,
            12,                     --stocklevel,
            790,                    --price,
            NULL,                   --massunit,
            NULL,                   --weight,
            NULL,                   --note,
            'KIT',                  --type,
            NULL);                  --supersessionpartnumber
        </sql>
    </changeSet>

    <changeSet author="AndyB" id="2.4-integration-test-data-create-master-parts-221">
        <sql stripComments="true">
            INSERT INTO public.masterpart (id, partnumber, manufacturerid, stocklevel, price, massunit, weight, note, type, supersessionpartnumber)
            VALUES (
            221,                    --id,
            'Jcb-kit-part-221',     --partnumber,
            2,                      --manufacturerid,
            12,                     --stocklevel,
            790,                    --price,
            NULL,                   --massunit,
            NULL,                   --weight,
            NULL,                   --note,
            'KIT',                  --type,
            NULL);                  --supersessionpartnumber
        </sql>
    </changeSet>

    <changeSet author="AndyB" id="2.4-integration-test-data-create-master-parts-222">
        <sql stripComments="true">
            INSERT INTO public.masterpart (id, partnumber, manufacturerid, stocklevel, price, massunit, weight, note, type, supersessionpartnumber)
            VALUES (
            222,                    --id,
            'Jcb-kit-part-222',     --partnumber,
            2,                      --manufacturerid,
            12,                     --stocklevel,
            790,                    --price,
            NULL,                   --massunit,
            NULL,                   --weight,
            NULL,                   --note,
            'PART',                 --type,
            NULL);                  --supersessionpartnumber
        </sql>
    </changeSet>

    <changeSet author="AndyB" id="2.4-integration-test-data-create-master-parts-231">
        <sql stripComments="true">
            INSERT INTO public.masterpart (id, partnumber, manufacturerid, stocklevel, price, massunit, weight, note, type, supersessionpartnumber)
            VALUES (
            231,                    --id,
            'Lbr-kit-part-231',     --partnumber,
            3,                      --manufacturerid,
            12,                     --stocklevel,
            790,                    --price,
            NULL,                   --massunit,
            NULL,                   --weight,
            NULL,                   --note,
            'KIT',                  --type,
            NULL);                  --supersessionpartnumber
        </sql>
    </changeSet>

    <changeSet author="AndyB" id="2.4-integration-test-data-create-master-parts-232">
        <sql stripComments="true">
            INSERT INTO public.masterpart (id, partnumber, manufacturerid, stocklevel, price, massunit, weight, note, type, supersessionpartnumber)
            VALUES (
            232,                    --id,
            'Cat-kit-part-232',     --partnumber,
            3,                      --manufacturerid,
            12,                     --stocklevel,
            790,                    --price,
            NULL,                   --massunit,
            NULL,                   --weight,
            NULL,                   --note,
            'PART',                 --type,
            NULL);                  --supersessionpartnumber
        </sql>
    </changeSet>

    <changeSet author="AndyB" id="2.4-integration-test-data-create-master-parts-233">
        <sql stripComments="true">
            INSERT INTO public.masterpart (id, partnumber, manufacturerid, stocklevel, price, massunit, weight, note, type, supersessionpartnumber)
            VALUES (
            233,                    --id,
            'Cat-kit-part-233',     --partnumber,
            3,                      --manufacturerid,
            12,                     --stocklevel,
            790,                    --price,
            NULL,                   --massunit,
            NULL,                   --weight,
            NULL,                   --note,
            'PART',                 --type,
            NULL);                  --supersessionpartnumber
        </sql>
    </changeSet>

    <changeSet author="AndyB" id="2.4-integration-test-data-create-master-parts-241">
        <sql stripComments="true">
            INSERT INTO public.masterpart (id, partnumber, manufacturerid, stocklevel, price, massunit, weight, note, type, supersessionpartnumber)
            VALUES (
            241,                    --id,
            'Trx-kit-part-241',     --partnumber,
            4,                      --manufacturerid,
            12,                     --stocklevel,
            790,                    --price,
            NULL,                   --massunit,
            NULL,                   --weight,
            NULL,                   --note,
            'KIT',                  --type,
            NULL);                  --supersessionpartnumber
        </sql>
    </changeSet>

    <changeSet author="AndyB" id="2.4-integration-test-data-create-master-parts-242">
        <sql stripComments="true">
            INSERT INTO public.masterpart (id, partnumber, manufacturerid, stocklevel, price, massunit, weight, note, type, supersessionpartnumber)
            VALUES (
            242,                    --id,
            'Cat-kit-part-242',     --partnumber,
            4,                      --manufacturerid,
            12,                     --stocklevel,
            790,                    --price,
            NULL,                   --massunit,
            NULL,                   --weight,
            NULL,                   --note,
            'PART',                 --type,
            NULL);                  --supersessionpartnumber
        </sql>
    </changeSet>

    <changeSet author="AndyB" id="2.4-integration-test-data-create-master-parts-243">
        <sql stripComments="true">
            INSERT INTO public.masterpart (id, partnumber, manufacturerid, stocklevel, price, massunit, weight, note, type, supersessionpartnumber)
            VALUES (
            243,                    --id,
            'Cat-kit-part-243',     --partnumber,
            4,                      --manufacturerid,
            12,                     --stocklevel,
            790,                    --price,
            NULL,                   --massunit,
            NULL,                   --weight,
            NULL,                   --note,
            'PART',                 --type,
            NULL);                  --supersessionpartnumber
        </sql>
    </changeSet>

    <changeSet author="AndyB" id="2.4-integration-test-data-create-master-parts-244">
        <sql stripComments="true">
            INSERT INTO public.masterpart (id, partnumber, manufacturerid, stocklevel, price, massunit, weight, note, type, supersessionpartnumber)
            VALUES (
            244,                    --id,
            'Cat-kit-part-244',     --partnumber,
            4,                      --manufacturerid,
            12,                     --stocklevel,
            790,                    --price,
            NULL,                   --massunit,
            NULL,                   --weight,
            NULL,                   --note,
            'PART',                 --type,
            NULL);                  --supersessionpartnumber
        </sql>
    </changeSet>

</databaseChangeLog>
