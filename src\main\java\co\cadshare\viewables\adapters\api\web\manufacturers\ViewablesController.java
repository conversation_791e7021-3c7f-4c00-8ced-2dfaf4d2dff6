package co.cadshare.viewables.adapters.api.web.manufacturers;

import co.cadshare.shared.core.user.User;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;

@RestController
@RequestMapping("/manufacturers/{manufacturer-id}/product-ranges/{product-range-id}/products/{product-id}/viewables")
public class ViewablesController {

    @GetMapping(produces = "application/json")
    @PreAuthorize("hasRole('ROLE_SUPER_USER') or hasRole('ROLE_MANUFACTURER') and #currentUser.manufacturerId == manufacturerId")
    @ResponseStatus(HttpStatus.OK)
    @Operation(summary = "Get the summary details of the list of all Viewables belonging to the specified Product")
    public ResponseEntity<GetViewableListResponseDto> getViewables(@PathVariable("manufacturer-id") int manufacturerId,
                                                                   @PathVariable("product-range-id") int productRangeId,
                                                                   @PathVariable("product-id") int productId,
                                                                   @AuthenticationPrincipal User currentUser) {


        List<ViewableListItemDto> viewables = new ArrayList<>();
        for (Integer i = 1; i < manufacturerId  + 1; i++) {
            Integer finalI = i;
            ViewableListItemDto viewable = new ViewableListItemDto() {{
                setId(finalI);
                setName("Sample Viewable Name".concat(finalI.toString()));
            }};
            viewables.add(viewable);
        }
        GetViewableListResponseDto response = new GetViewableListResponseDto()
        {{ setViewables(viewables);}};
        return new ResponseEntity<>(response, HttpStatus.OK);
    }
}
