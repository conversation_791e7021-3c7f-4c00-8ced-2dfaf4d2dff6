package co.cadshare.addresses.adapters.database;

import com.querydsl.jpa.impl.JPAQueryFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import javax.persistence.EntityManager;
import java.util.List;

@Repository
public class AddressesManufacturerComplexQueryRepo {
    private JPAQueryFactory queryFactory;

    @Autowired
    public AddressesManufacturerComplexQueryRepo(EntityManager entityManager) {
        this.queryFactory = new JPAQueryFactory(entityManager);
    }

    public List<AddressesManufacturerEntity> getAll() {
        QAddressesManufacturerEntity manufacturer = QAddressesManufacturerEntity.addressesManufacturerEntity;
        return this.queryFactory.selectFrom(manufacturer)
                .fetch();
    }

    public AddressesManufacturerEntity getManufacturerBySubdomain(String subdomain) {
        QAddressesManufacturerEntity manufacturer = QAddressesManufacturerEntity.addressesManufacturerEntity;
        return this.queryFactory.selectFrom(manufacturer)
                .where(manufacturer.subdomain.eq(subdomain))
                .fetchOne();
    }

    public List<AddressesManufacturerEntity> getManufacturersWithExternalAddresses() {
        QAddressesManufacturerEntity manufacturer = QAddressesManufacturerEntity.addressesManufacturerEntity;
        return this.queryFactory.selectFrom(manufacturer)
                .where(manufacturer.manufacturerSettings.addressCreationEnabled.eq(false))
                .fetch();
    }
}