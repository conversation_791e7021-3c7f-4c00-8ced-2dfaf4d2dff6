package co.cadshare.modelMgt.publications.adapters.api.web;

import co.cadshare.modelMgt.publications.core.ManualStatus;
import org.mapstruct.Mapper;

@Mapper
public abstract class PublicationStatusMapper {
    public String statusToString(ManualStatus.Status status) {
        return status.toString();
    }

    public ManualStatus.Status dtoStringToStatus(String status) {
        return ManualStatus.Status.valueOf(status);
    }
}
