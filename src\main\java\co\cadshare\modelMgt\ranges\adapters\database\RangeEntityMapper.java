package co.cadshare.modelMgt.ranges.adapters.database;

import co.cadshare.modelMgt.ranges.core.Range;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface RangeEntityMapper {

    RangeEntityMapper Instance = Mappers.getMapper(RangeEntityMapper.class);

    Range entityToCore(RangeEntity entity);

    RangeEntity coreToEntity(Range range);

    List<Range> entitiesToCores(List<RangeEntity> entities);

    List<RangeEntity> coresToEntities(List<Range> ranges);

}
