package co.cadshare.controller;

import co.cadshare.aspects.access.languages.CanUseLanguage;
import co.cadshare.modelMgt.models.boundary.GetModelService;
import co.cadshare.shared.core.Language;
import co.cadshare.domainmodel.machine.Machine;
import co.cadshare.modelMgt.publications.core.Manual;
import co.cadshare.shared.core.manufacturer.Manufacturer;
import co.cadshare.shared.core.manufacturer.ManufacturerDetails;
import co.cadshare.shared.core.manufacturer.subentity.ManufacturerSubEntity;
import co.cadshare.modelMgt.models.core.Model;
import co.cadshare.orders.core.Order;
import co.cadshare.orders.core.OrderStatus;
import co.cadshare.orders.core.OrderUnreadCounts;
import co.cadshare.masterParts.core.extensions.kit.Kit;
import co.cadshare.domainmodel.priceListIdentifier.PriceListIdentifier;
import co.cadshare.domainmodel.range.Range;
import co.cadshare.domainmodel.serialnumber.SerialNumber;
import co.cadshare.shared.core.user.User;
import co.cadshare.shared.core.manufacturer.ManufacturerSettings;
import co.cadshare.services.ManufacturerService;
import java.sql.Timestamp;
import java.util.List;

import co.cadshare.utils.ObjectUtilsExtension;
import lombok.experimental.ExtensionMethod;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

@Controller
@RequestMapping("/manufacturer")
@ExtensionMethod(ObjectUtilsExtension.class)
public class ManufacturerController {

    private final Logger log = LoggerFactory.getLogger(getClass());

    private final ManufacturerService manufacturerService;
	private final GetModelService getModelService;

    @Autowired
    public ManufacturerController(ManufacturerService manufacturerService,
                                  GetModelService getModelService) {
        this.manufacturerService = manufacturerService;
	    this.getModelService = getModelService;
    }

    // TODO need to add role permissions here for creating manufacturers
    @PreAuthorize("hasRole('ROLE_SUPER_USER') and hasRole('Internal')")
    @RequestMapping(method = RequestMethod.POST, consumes = "application/json")
    public HttpEntity<Integer> createManufacturer(@AuthenticationPrincipal User currentUser,
                                                  @RequestBody Manufacturer manufacturer) throws Exception {

        log.info("ACCESS: User [{}], createManufacturer, manufacturer [{}]", currentUser, manufacturer.toString());
        int manufacturerId = manufacturerService.createManufacturer(manufacturer);
        return new ResponseEntity<>(manufacturerId, HttpStatus.OK);
    }

	@PreAuthorize("hasRole('ROLE_SUPER_USER') and hasRole('Internal')")
	@RequestMapping(method = RequestMethod.GET)
	public HttpEntity<List<Manufacturer>> getAllManufacturers(@AuthenticationPrincipal User currentUser) {

		log.info("ACCESS: User [{}], getAllManufacturers", currentUser.accessDetails());
		List<Manufacturer> manufacturers = manufacturerService.getAllManufacturers();
		return new ResponseEntity<>(manufacturers, HttpStatus.OK);
	}

	@PreAuthorize("hasRole('ROLE_MANUFACTURER') and #currentUser.manufacturerId == #manufacturerId and hasRole('Publication')")
	@RequestMapping(value = "/{manufacturerId}/models", method = RequestMethod.GET)
	public HttpEntity<List<Model>> getModelsForManufacturer(@AuthenticationPrincipal User currentUser,
	                                                        @PathVariable int manufacturerId,
	                                                        @RequestParam(value = "uploadedSinceDate", required = false) String uploadedSinceDate,
	                                                        @RequestParam(value = "results", required = false) Integer resultSize) {

		log.info("ACCESS: User [{}], getModelsForManufacturer, manufacturer [{}]", currentUser, manufacturerId);
		Timestamp uploadedSinceDateTimestamp = null;
		if (uploadedSinceDate != null)
		  uploadedSinceDateTimestamp = Timestamp.valueOf(uploadedSinceDate);
		List<Model> modelList = getModelService.getModelsForManufacturer(manufacturerId, uploadedSinceDateTimestamp, resultSize);
		return new ResponseEntity<>(modelList, HttpStatus.OK);
	}

	@PreAuthorize("hasRole('ROLE_MANUFACTURER') and #currentUser.manufacturerId == #manufacturerId")
	@RequestMapping(value = "/{manufacturerId}/manualDetails", method = RequestMethod.GET)
	public HttpEntity<List<SerialNumber>> getManualsForManufacturer(@AuthenticationPrincipal User currentUser,
	                                                                @PathVariable int manufacturerId,
	                                                                @RequestParam(value = "published", required = false) Boolean published) {

		log.info("ACCESS: User [{}], getSerialNumbersForManufacturer, manufacturer [{}]", currentUser, manufacturerId);
		List<SerialNumber> manualList = manufacturerService.getDetailedManualsForManufacturer(manufacturerId, published);
		return new ResponseEntity<List<SerialNumber>>(manualList, HttpStatus.OK);
	}


	@PreAuthorize("hasRole('ROLE_MANUFACTURER') and #currentUser.manufacturerId == #manufacturerId")
	@RequestMapping(value = "/{manufacturerId}/manualDetails/{manualId}", method = RequestMethod.GET)
	public HttpEntity<SerialNumber> getManualForManufacturer(@AuthenticationPrincipal User currentUser,
	                                                             @PathVariable int manufacturerId,
	                                                             @PathVariable int manualId,
	                                                             @RequestParam(value = "published", required = false) Boolean published) {

		SerialNumber manual = manufacturerService.getDetailedManualForManufacturer(manufacturerId, manualId, published);
		return new ResponseEntity<>(manual, HttpStatus.OK);
	}


	@PreAuthorize("hasRole('ROLE_MANUFACTURER') and #currentUser.manufacturerId == #manufacturerId")
	@RequestMapping(value = "/{manufacturerId}/manufacturersubentities", method = RequestMethod.GET)
	public HttpEntity<List<ManufacturerSubEntity>> getManufacturerSubEntitiesForManufacturer(@AuthenticationPrincipal User currentUser,
                                                 @PathVariable int manufacturerId,
                                                 @RequestParam(value = "subEntityType", required = false) ManufacturerSubEntity.ManufacturerSubEntityType subEntityType) throws Exception {

		log.info("ACCESS: User [{}], getManufacturerSubEntitiesForManufacturer, manufacturer [{}], subEntityType [{}]", currentUser, manufacturerId, subEntityType);
		List<ManufacturerSubEntity> manufacturerSubEntityList = manufacturerService.getManufacturerSubEntitiesForManufacturer(manufacturerId, subEntityType);
		return new ResponseEntity<>(manufacturerSubEntityList, HttpStatus.OK);
	}

	@PreAuthorize("hasRole('ROLE_MANUFACTURER') and #currentUser.manufacturerId == #manufacturerId and hasRole('Order')")
	@RequestMapping(value = "/{manufacturerId}/orders", method = RequestMethod.GET)
	public HttpEntity<List<Order>> getOrdersForManufacturer(@AuthenticationPrincipal User currentUser,
	                                                        @PathVariable int manufacturerId,
	                                                        @RequestParam(value = "status", required = false) List<OrderStatus> status,
	                                                        @RequestParam(value = "requestedDeliveryDateBefore", required = false) String requestedDeliveryDateBeforeString) throws Exception {

		log.info("ACCESS: User [{}], getOrdersForManufacturer, manufacturer [{}]", currentUser, manufacturerId);
		Timestamp requestedDeliveryDateBefore = null;
		if (requestedDeliveryDateBeforeString != null)
		  requestedDeliveryDateBefore = Timestamp.valueOf(requestedDeliveryDateBeforeString);
		List<Order> orderList = manufacturerService.getOrdersForManufacturer(manufacturerId,
				status,
				requestedDeliveryDateBefore,
				currentUser.getUserId());
		return new ResponseEntity<>(orderList, HttpStatus.OK);
	}

	@RequestMapping(value = "/{manufacturerId}/orders/counts", method = RequestMethod.GET)
	@PreAuthorize("hasRole('ROLE_MANUFACTURER') and #currentUser.manufacturerId == #manufacturerId and hasRole('Order')")
	public HttpEntity<OrderUnreadCounts> getUnreadOrdersCountForManufacturer(@AuthenticationPrincipal User currentUser,
	                                                                       @PathVariable int manufacturerId) {

		log.info("ACCESS: getUnreadOrdersCountForManufacturer, manufacturer [{}]", manufacturerId);
		OrderUnreadCounts orderCounts = manufacturerService.getOrderCountsForManufacturer(manufacturerId);
		return new ResponseEntity<>(orderCounts, HttpStatus.OK);
	}

	@PreAuthorize("hasRole('ROLE_MANUFACTURER') and #currentUser.manufacturerId == #manufacturerId and hasRole('Publication')")
	@RequestMapping(value = "/{manufacturerId}/manuals", method = RequestMethod.GET)
	public HttpEntity<List<Manual>> getManualsForManufacturer(@AuthenticationPrincipal User currentUser,
	                                                        @PathVariable int manufacturerId,
	                                                        @RequestParam(value = "sort", required = false) String sortBy,
	                                                        @RequestParam(value = "results", required = false) Integer resultSize) {

		log.info("ACCESS: User [{}], getRecentManualsForManufacturer, manufacturer [{}] sorted by [{}] limit [{}] results",
				currentUser.accessDetails(), manufacturerId, sortBy, resultSize);
		if(resultSize == null) resultSize = 10;
		List<Manual> manualList = manufacturerService.getManualsForManufacturer(manufacturerId, sortBy, resultSize);
		return new ResponseEntity<>(manualList, HttpStatus.OK);
	}

	@RequestMapping(value = "/{manufacturerId}", method = RequestMethod.GET)
	@PreAuthorize("hasRole('ROLE_MANUFACTURER') and #currentUser.manufacturerId == #manufacturerId")
	public HttpEntity<Manufacturer> getManufacturerById(@AuthenticationPrincipal User currentUser, @PathVariable int manufacturerId) {

		log.info("ACCESS: Domain [{}], getManufacturerById", manufacturerId);
		Manufacturer manufacturer = manufacturerService.getManufacturer(manufacturerId);
		return new ResponseEntity<>(manufacturer, HttpStatus.OK);
	}

	@RequestMapping(value = "/{manufacturerId}/details", method = RequestMethod.GET)
	@PreAuthorize("hasRole('ROLE_MANUFACTURER') and #currentUser.manufacturerId == #manufacturerId")
	public HttpEntity<ManufacturerDetails> getManufacturerDetails(@AuthenticationPrincipal User currentUser, @PathVariable int manufacturerId) {

		log.info("ACCESS: User [{}], getManufacturerDetails, manufacturer [{}]", currentUser.accessDetails(), manufacturerId);
		ManufacturerDetails manufacturerDetails = manufacturerService.getManufacturerDetails(manufacturerId);
		return new ResponseEntity<>(manufacturerDetails, HttpStatus.OK);
	}

	@PreAuthorize("hasRole('ROLE_MANUFACTURER') and #currentUser.manufacturerId == #manufacturerId and hasRole('Admin')")
	@RequestMapping(value = "/{manufacturerId}/updateDetails", method = RequestMethod.PUT, consumes = "application/json")
	public HttpEntity<Boolean> updateManufacturerDetails(@AuthenticationPrincipal User currentUser,
	                                                     @PathVariable int manufacturerId,
	                                                     @RequestBody ManufacturerDetails manufacturerDetails) {

		log.info("ACCESS: User [{}], updateManufacturerDetails, manufacturer [{}] ", currentUser.accessDetails(), manufacturerId);
		Boolean updated = manufacturerService.updateManufacturerDetails(manufacturerId, manufacturerDetails, currentUser.getUserId());
		return new ResponseEntity<>(updated, HttpStatus.OK);
	}

	@RequestMapping(value = "/subdomain/{subdomain}", method = RequestMethod.GET)
	public HttpEntity<Manufacturer> getManufacturerByDomain(@PathVariable String subdomain) {

		log.info("ACCESS: Domain [{}], getManufacturerByDomain", subdomain);
		Manufacturer manufacturer = manufacturerService.getManufacturerByDomain(subdomain);
		return new ResponseEntity<>(manufacturer, HttpStatus.OK);
	}


	@PreAuthorize("hasRole('ROLE_MANUFACTURER') and #currentUser.manufacturerId == #manufacturerId and hasRole('Admin')")
	@GetMapping(value = "/{manufacturerId}/kits")
	@CanUseLanguage
	public HttpEntity<List<Kit>> getMasterPartKitsByManufacturerId(@AuthenticationPrincipal User currentUser,
	                                                             @PathVariable int manufacturerId,
	                                                             @RequestParam(value = "language", required = false) Language language) throws Exception {

		log.info("ACCESS: User [{}], getMasterPartKitsByManufacturerId, manufacturerId id [{}]",
				currentUser.accessDetails(), manufacturerId);
		Integer languageId = language != null ? currentUser.findLanguage(language).getLanguageId() : null;
		List<Kit> kitList = manufacturerService.getMasterPartKitsByManufacturerId(manufacturerId,
		        languageId,
		        currentUser.obtainDefaultLanguage());
		return new ResponseEntity<>(kitList, HttpStatus.OK);
	}

	@PreAuthorize("hasRole('ROLE_MANUFACTURER')")
	@RequestMapping(value = "/settings", method = RequestMethod.GET)
	public HttpEntity<ManufacturerSettings> getManufacturerSettings(@AuthenticationPrincipal User currentUser) throws Exception {

		log.info("ACCESS: User [{}], getManufacturerSettings, manufacturer [{}]", currentUser, currentUser.getManufacturerId());
		ManufacturerSettings settings = manufacturerService.getManufacturerSettings(currentUser.getManufacturerId());
		return new ResponseEntity<>(settings, HttpStatus.OK);
	}

	@PreAuthorize("hasRole('ROLE_MANUFACTURER') and hasRole('Admin')")
	@RequestMapping(value = "/settings", method = RequestMethod.PUT, consumes = "application/json")
	public HttpEntity<Boolean> updateManufacturerSettings(@AuthenticationPrincipal User currentUser, @RequestBody ManufacturerSettings settings) {

		log.info("ACCESS: User [{}], updateManufacturerSettings, manufacturer [{}] ", currentUser.accessDetails(), currentUser.getManufacturerId());
		Boolean updated = false;
		try {
		  if (settings.getManufacturerId() != 0 && (currentUser.getManufacturerId() == settings.getManufacturerId()))
		    updated = manufacturerService.updateManufacturerSettings(settings);
		} catch (Exception ex) {
		    return new ResponseEntity<>(HttpStatus.CONFLICT);
		}
		return new ResponseEntity<>(updated, HttpStatus.OK);
	}

	@RequestMapping(value = "/{manufacturerId}/priceListIdentifiers", method = RequestMethod.GET)
	@PreAuthorize("hasRole('ROLE_MANUFACTURER') and #currentUser.manufacturerId == #manufacturerId")
	public HttpEntity<List<PriceListIdentifier>> getPriceListIdentifiersForManufacturer(@AuthenticationPrincipal User currentUser,
	                                                                                    @PathVariable int manufacturerId) {

		log.info("ACCESS: getPriceListIdentifiersForManufacturer, manufacturer [{}]", manufacturerId);
		List<PriceListIdentifier> priceLists = manufacturerService.getPriceListIdentifiersForManufacturer(manufacturerId);
		return new ResponseEntity<>(priceLists, HttpStatus.OK);
	}
}
