package co.cadshare.models.adapters.database;

import co.cadshare.publications.adapters.database.CoverImageEntity;
import co.cadshare.publications.adapters.database.PublicationsDealerEntity;
import co.cadshare.publications.adapters.database.PublicationsModelEntity;
import co.cadshare.publications.core.ManualStatus;
import lombok.Data;

import javax.persistence.*;
import java.util.List;

@Entity
@Table(name="Manual")
@Data
public class ModelsPublicationEntity {
    @Id
    @GeneratedValue
    @Column(name="manualid")
    private Integer id;

    @Column(name="manualname")
    private String name;

    @Column(name="manufacturerid")
    private Integer manufacturerId;

    private boolean archived;

    @ManyToOne
    @JoinColumn(name = "mediaid")
    private CoverImageEntity coverImage;

    @ManyToMany(cascade = { CascadeType.ALL })
    @JoinTable(
            name = "manufacturersubentitymanualmap",
            joinColumns = { @JoinColumn(name = "manualid") },
            inverseJoinColumns = { @JoinColumn(name = "manufacturersubentityid") }
    )
    private List<PublicationsDealerEntity> dealers;

    @Enumerated(EnumType.STRING)
    private ManualStatus.Status status;

    @ManyToMany(mappedBy = "publications")
    private List<PublicationsModelEntity> models;

}


