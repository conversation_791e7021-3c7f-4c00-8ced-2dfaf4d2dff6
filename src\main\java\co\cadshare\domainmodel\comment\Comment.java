/*
 * Copyright 2016 Bell.
 */
package co.cadshare.domainmodel.comment;

import java.sql.Timestamp;

import com.fasterxml.jackson.annotation.JsonInclude;

import lombok.Data;

@JsonInclude(JsonInclude.Include.NON_NULL)
@Data
public class Comment {

    private Integer id;
    private int commentThreadId;
    private String message;
    private int createdByUserId;
    private String createdByUserName;
    private Timestamp createdDate;
}
