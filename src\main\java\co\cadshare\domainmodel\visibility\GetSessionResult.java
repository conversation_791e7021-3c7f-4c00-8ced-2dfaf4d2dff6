package co.cadshare.domainmodel.visibility;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;

@XmlRootElement(name = "GetSessionResult", namespace = "http://visibility.com/")
@XmlAccessorType(XmlAccessType.FIELD)
public class GetSessionResult {

    @XmlElement(name = "sReturnMessage", namespace = "http://visibility.com/")
    private String message;

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }
}
