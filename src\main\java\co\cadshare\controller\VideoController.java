/*
 * Copyright 2016 Bell.
 */
package co.cadshare.controller;

import co.cadshare.shared.core.user.User;
import co.cadshare.modelMgt.publications.core.Video;
import co.cadshare.modelMgt.publications.boundary.VideoService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Slf4j
@Controller
@RequestMapping("/video")
public class VideoController {

  private VideoService videoService;

  public VideoController(VideoService videoService) {
    this.videoService = videoService;
  }

  @GetMapping
  public HttpEntity<List<Video>> getVideoByManufacturerId(@AuthenticationPrincipal User currentUser) {
    log.info("ACCESS: User [{}], getVideoByManufacturerId", currentUser.accessDetails());

    List<Video> videos = videoService.getVideoByManufacturerId(currentUser.getManufacturerId());
    return new ResponseEntity<>(videos, HttpStatus.OK);
  }


  @PostMapping(consumes = "application/json")
  public HttpEntity<Integer> createVideo(@AuthenticationPrincipal User currentUser, @RequestBody Video video) throws Exception {
    log.info("ACCESS: User [{}], createVideo", currentUser.accessDetails());

    video.setManufacturerId(currentUser.getManufacturerId());

    int videoId = videoService.createVideo(video);

    return new ResponseEntity<>(videoId, HttpStatus.OK);
  }

  @PutMapping(value = "/{videoId}")
  public HttpEntity<Boolean> updateVideo(@AuthenticationPrincipal User currentUser, @PathVariable int videoId, @RequestBody Video video) {
    log.info("ACCESS: User [{}], updateVideo, videoId [{}]", currentUser.accessDetails(), videoId);
    Boolean updated = false;
    Video existingVideo = videoService.getVideoById(videoId);

    if (existingVideo != null) {
      //Check user manufacturer has access to that tech doc
      if (existingVideo.getManufacturerId() == currentUser.getManufacturerId()) {
        video.setId(videoId);
        updated = videoService.updateVideo(video);
      } else {
        return new ResponseEntity<>(HttpStatus.FORBIDDEN);
      }
    }
    return new ResponseEntity<>(updated, HttpStatus.OK);
  }

  @DeleteMapping(value = "/{videoId}")
  public HttpEntity<Boolean>deleteVideo(@AuthenticationPrincipal User currentUser, @PathVariable int videoId) {
    log.info("ACCESS: User [{}], deleteVideo, techDocId [{}]", currentUser.accessDetails(), videoId);
    Boolean deleted = false;
    Video existingVideo = videoService.getVideoById(videoId);

    if (existingVideo != null) {
      //Check user manufacturer has access to that tech doc
      if (existingVideo.getManufacturerId() == currentUser.getManufacturerId()) {
        deleted = videoService.deleteVideo(videoId);
      } else {
        return new ResponseEntity<>(HttpStatus.FORBIDDEN);
      }
    }
    return new ResponseEntity<>(deleted, HttpStatus.OK);
  }
}
