package co.cadshare.modelMgt.models.boundary;

import co.cadshare.modelMgt.models.core.Model;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.dao.EmptyResultDataAccessException;

import co.cadshare.exceptions.NotFoundException;
import co.cadshare.shared.core.user.User;
import co.cadshare.aspects.logging.Log;

@Service
public class UpdateModelService {

    private static final Logger logger = LoggerFactory.getLogger(UpdateModelService.class);
    private final ModelCommandPort modelCommand;
    private final ModelQueryPort modelQuery;

    @Autowired
    public UpdateModelService(ModelCommandPort modelCommand,
                              ModelQueryPort modelQuery) {
        this.modelCommand = modelCommand;
        this.modelQuery = modelQuery;
    }

    @Log
    public void update(User user, Model model) throws Exception {
        try {
            this.modelQuery.get(model.getModelId());
            this.modelCommand.update(user, model);
        } catch (EmptyResultDataAccessException ex) {
            throw new NotFoundException("Model does not exist");
        }
    }
}
