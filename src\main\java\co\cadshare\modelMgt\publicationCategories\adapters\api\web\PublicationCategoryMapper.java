package co.cadshare.modelMgt.publicationCategories.adapters.api.web;

import co.cadshare.modelMgt.publicationCategories.core.PublicationCategory;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import java.util.List;

@Mapper
public interface PublicationCategoryMapper {

    PublicationCategoryMapper Instance = Mappers.getMapper(PublicationCategoryMapper.class);

    PublicationCategory toPublicationCategory(PostPublicationCategoryRequestDto postRequestDto);

    PublicationCategory toPublicationCategory(PutPublicationCategoryRequestDto putRequestDto);

    GetPublicationCategoryResponseDto toGetResponseDto(PublicationCategory publicationCategory);

    List<GetPublicationCategoryListItemResponseDto> toGetListResponseDto(List<PublicationCategory> publicationCategories);
}
