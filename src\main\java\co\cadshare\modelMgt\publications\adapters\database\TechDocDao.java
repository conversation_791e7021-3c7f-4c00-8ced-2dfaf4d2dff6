/*
 * Copyright 2016 Bell.
 */
package co.cadshare.modelMgt.publications.adapters.database;

import co.cadshare.modelMgt.publications.core.TechDoc;
import lombok.extern.slf4j.Slf4j;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.namedparam.MapSqlParameterSource;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.jdbc.support.GeneratedKeyHolder;
import org.springframework.jdbc.support.KeyHolder;
import org.springframework.stereotype.Repository;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Repository
public class TechDocDao {

  private NamedParameterJdbcTemplate namedParamJdbcTemplate;

  public TechDocDao(NamedParameterJdbcTemplate namedParamJdbcTemplate) {
    this.namedParamJdbcTemplate = namedParamJdbcTemplate;
  }

  private static final String TECH_DOC_BY_MANUFACTURER = "SELECT * FROM technical_document WHERE manufacturerid = :manufacturerId";

  public List<TechDoc> getTechDocByManufacturerId(int manufacturerId) {
    Map<String, Object> parameters = new HashMap<String, Object>();
    parameters.put("manufacturerId", manufacturerId);

    // Check if user has a record for reading the latest comment in the thread.
    List<TechDoc> techDocs =  namedParamJdbcTemplate.query(TECH_DOC_BY_MANUFACTURER, parameters, new BeanPropertyRowMapper<>(TechDoc.class));
    return techDocs;
  }

  private static final String TECH_DOC_BY_ID = "SELECT * FROM technical_document WHERE id = :id";

  public TechDoc getTechDocById(int techDocId) {
    Map<String, Object> parameters = new HashMap<String, Object>();
    parameters.put("id", techDocId);

    // Check if user has a record for reading the latest comment in the thread.
    TechDoc techDoc =  namedParamJdbcTemplate.queryForObject(TECH_DOC_BY_ID, parameters, new BeanPropertyRowMapper<>(TechDoc.class));
    return techDoc;
  }

  private final static String CREATE_TECH_DOC = "INSERT INTO technical_document (name, description, url, filename, manufacturerid, pagecount, createdbyuserid, createddate) "
          + "VALUES( :name, :description, :url, :filename, :manufacturerId, :pageCount, :createdByUserId, :createdDate)";

  public int createTechDoc(TechDoc techDoc) {
    KeyHolder keyHolder = new GeneratedKeyHolder();

    MapSqlParameterSource parameters = new MapSqlParameterSource();
    parameters.addValue("name", techDoc.getName());
    parameters.addValue("description", techDoc.getDescription());
    parameters.addValue("url", techDoc.getUrl());
    parameters.addValue("filename", techDoc.getFilename());
    parameters.addValue("manufacturerId", techDoc.getManufacturerId());
    parameters.addValue("pageCount", techDoc.getPageCount());
    parameters.addValue("createdByUserId", techDoc.getCreatedByUserId());
    parameters.addValue("createdDate", techDoc.getCreatedDate());

    int result = namedParamJdbcTemplate.update(CREATE_TECH_DOC, parameters, keyHolder, new String[] { "id" });
    return keyHolder.getKey().intValue();
  }

  private final static String UPDATE_TECH_DOC = "UPDATE technical_document SET name = :name, description = :description," +
          "url = :url, filename = :filename " +
          "WHERE id = :id";

  public boolean updateTechDoc(TechDoc techDoc) {
    Map<String, Object> parameters = new HashMap<String, Object>();
    parameters.put("name", techDoc.getName());
    parameters.put("description", techDoc.getDescription());
    parameters.put("url", techDoc.getUrl());
    parameters.put("filename", techDoc.getFilename());
    parameters.put("id", techDoc.getId());

    int result = namedParamJdbcTemplate.update(UPDATE_TECH_DOC, parameters);
    return result > 0;
  }

  private static final String DELETE_TECH_DOC = "DELETE FROM technical_document WHERE id = :techDocId";

  public boolean deleteTechDoc(int techDocId) {
    Map<String, Integer> namedParameters = new HashMap<String, Integer>();
    namedParameters.put("techDocId", techDocId);

    namedParamJdbcTemplate.update(DELETE_TECH_DOC, namedParameters);

    log.info("Deleted Tech Doc Set with ID [{}]", techDocId);
    return true;
  }
}
