package co.cadshare.controller;

import co.cadshare.domainmodel.GenericMessageResponse;
import co.cadshare.shared.core.Currency;
import co.cadshare.shared.core.manufacturer.subentity.ManufacturerSubEntity;
import co.cadshare.orders.core.Order;
import co.cadshare.orders.core.OrderStatus;
import co.cadshare.orders.core.OrderUnreadCounts;
import co.cadshare.shared.core.user.User;
import co.cadshare.shared.core.user.settings.ManufacturerSubEntitySettings;
import co.cadshare.response.CustomerManual;
import co.cadshare.modelMgt.publications.boundary.ManualService;
import co.cadshare.response.PurchaserOrderResponse;
import co.cadshare.services.ManufacturerSubEntityService;
import co.cadshare.services.PermissionsService;
import co.cadshare.modelMgt.ranges.boundary.RangeService;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.security.oauth2.common.exceptions.UnauthorizedUserException;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

@Slf4j
@Controller
@RequestMapping("/manufacturersubentity")
@Tag(name = "Purchaser API")
public class ManufacturerSubEntityController {

  private ManufacturerSubEntityService manufacturerSubEntityService;
  private ManualService manualService;
  private RangeService rangeService;
  private PermissionsService permissionsService;

  public ManufacturerSubEntityController(ManufacturerSubEntityService manufacturerSubEntityService,
      ManualService manualService, RangeService rangeService, PermissionsService permissionsService) {
    this.manufacturerSubEntityService = manufacturerSubEntityService;
    this.manualService = manualService;
    this.rangeService = rangeService;
    this.permissionsService = permissionsService;
  }

  @RequestMapping(method = RequestMethod.POST, consumes = "application/json")
  public ResponseEntity<?> createManufacturerSubEntity(@AuthenticationPrincipal User currentUser,
      @RequestBody ManufacturerSubEntity manufacturerSubEntity) throws Exception {

    log.info("ACCESS: User [{}], createManufacturerSubEntity, manufacturerSubEntity: [{}]", currentUser.accessDetails(),
        manufacturerSubEntity.toString());

    try {
      int manufacturerSubEntityId = manufacturerSubEntityService.createManufacturerSubEntity(currentUser,
              manufacturerSubEntity);
      manufacturerSubEntity.setManufacturerSubEntityId(manufacturerSubEntityId);
      log.info("manufacturerSubEntity [{}] successfully created", manufacturerSubEntity);

      return new ResponseEntity<>(manufacturerSubEntityId, HttpStatus.OK);
    } catch (IllegalArgumentException ex) {
      return new ResponseEntity<>(new GenericMessageResponse(ex.getMessage()), HttpStatus.CONFLICT);
    }
  }

  @RequestMapping(method = RequestMethod.PUT, consumes = "application/json")
  public HttpEntity<GenericMessageResponse> updateManufacturerSubEntity(@AuthenticationPrincipal User currentUser, @RequestBody ManufacturerSubEntity manufacturerSubEntity) throws Exception {

    try {
      hasAdminPermissionsForSubEntity(currentUser, manufacturerSubEntity.getManufacturerSubEntityId());
    } catch (Exception ex) {
      return new ResponseEntity<>(null, HttpStatus.FORBIDDEN);
    }
    try {
      int result = manufacturerSubEntityService.updateManufacturerSubEntity(currentUser, manufacturerSubEntity);
      return new ResponseEntity<>(new GenericMessageResponse("Successfully updated subentity"), HttpStatus.OK);
    } catch (IllegalArgumentException ex) {
      return new ResponseEntity<>(new GenericMessageResponse(ex.getMessage()), HttpStatus.CONFLICT);
    }
  }

  @DeleteMapping("/{manufacturerSubEntityId}")
  @PreAuthorize("hasRole('ROLE_MANUFACTURER') and @manufacturerSubEntityService.getManufacturerSubEntity(#manufacturerSubEntityId).getManufacturerId() == #currentUser.getManufacturerId()")
  public HttpEntity<GenericMessageResponse> deleteManufacturerSubEntity(@AuthenticationPrincipal User currentUser, @PathVariable int manufacturerSubEntityId) throws Exception {
    try {
      hasAdminPermissionsForSubEntity(currentUser, manufacturerSubEntityId);
    } catch (Exception ex) {
      return new ResponseEntity<>(null, HttpStatus.FORBIDDEN);
    }

    if(manufacturerSubEntityService.manufacturerSubEntityCanBeDeleted(manufacturerSubEntityId)) {
      manufacturerSubEntityService.deleteManufacturerSubEntity(manufacturerSubEntityId);
    } else {
      return new ResponseEntity<>(new GenericMessageResponse("Company cannot be deleted due to associated (or previously associated) users, orders or manuals."), HttpStatus.BAD_REQUEST);
    }

    return new ResponseEntity<>(new GenericMessageResponse("Successfully deleted subentity"), HttpStatus.OK);
  }

  @GetMapping(value = "/{manufacturerSubEntityId}")
  @PreAuthorize("hasRole('ROLE_MANUFACTURER') or hasRole('ROLE_MANUFACTURER_SUB_ENTITY_DEALER') or hasRole('ROLE_MANUFACTURER_SUB_ENTITY_CUSTOMER') or hasRole('ROLE_MANUFACTURER_SUB_ENTITY_DEALER_PLUS')")
  public HttpEntity<ManufacturerSubEntity> getManufacturerSubEntity(@AuthenticationPrincipal User currentUser,
                                                       @PathVariable int manufacturerSubEntityId) throws IOException {
    log.info("ACCESS: User [{}], getManufacturerSubEntity", currentUser.accessDetails());

    ManufacturerSubEntity subEntity = manufacturerSubEntityService.getManufacturerSubEntity(manufacturerSubEntityId);

    if((currentUser.getManufacturerId() != null && (subEntity.getManufacturerId() != currentUser.getManufacturerId())) ||
       (currentUser.getManufacturerSubEntityId() != null && (subEntity.getManufacturerSubEntityId() != currentUser.getManufacturerSubEntityId())))
      throw new UnauthorizedUserException("The ManufacturerId or ManufacturerSubEntityId does not match");

    return new ResponseEntity<>(subEntity, HttpStatus.OK);
  }

  @PreAuthorize("(hasRole('ROLE_MANUFACTURER_SUB_ENTITY_DEALER') or hasRole('ROLE_MANUFACTURER_SUB_ENTITY_CUSTOMER') or hasRole('ROLE_MANUFACTURER_SUB_ENTITY_DEALER_PLUS') and #currentUser.manufacturerSubEntityId == #manufacturerSubEntityId) OR hasRole('ROLE_MANUFACTURER')")
  @GetMapping(value = "/{manufacturerSubEntityId}/currency")
  public HttpEntity<Currency> getManufacturerSubEntityCurrency(@AuthenticationPrincipal User currentUser, @PathVariable int manufacturerSubEntityId) throws IOException {
    log.info("ACCESS: User [{}], getManufacturerSubEntityCurrency", currentUser.accessDetails());

    try {
      hasAdminPermissionsForSubEntity(currentUser, manufacturerSubEntityId);
    } catch (Exception ex) {
      return new ResponseEntity<>(null, HttpStatus.FORBIDDEN);
    }
    Currency currency = manufacturerSubEntityService.getCurrencyBySubEntityId(currentUser, manufacturerSubEntityId);

    return new ResponseEntity<>(currency, HttpStatus.OK);
  }

  @RequestMapping(value = "/{manufacturerSubEntityId}/orders", method = RequestMethod.GET)
  public HttpEntity<PurchaserOrderResponse> getOrdersForManufacturerSubEntityId(@AuthenticationPrincipal User currentUser, @PathVariable int manufacturerSubEntityId, @RequestParam(value = "status", required = false) List<OrderStatus> status) throws Exception {

    List<Order> orderList = manufacturerSubEntityService.getOrdersForManufacturerSubEntity(manufacturerSubEntityId, status, currentUser.getUserId());
    PurchaserOrderResponse response = new PurchaserOrderResponse(){{
      setOrders(orderList);
    }};
    return new ResponseEntity<PurchaserOrderResponse>(response, HttpStatus.OK);
  }

  @RequestMapping(value = "/{manufacturerSubEntityId}/orders/counts", method = RequestMethod.GET)
  public HttpEntity<OrderUnreadCounts> getUnreadOrdersCountForManufacturerSubEntityId(@PathVariable int manufacturerSubEntityId) throws Exception {

    OrderUnreadCounts orderCounts = manufacturerSubEntityService.getOrderCountsForManufacturerSubEntity(manufacturerSubEntityId);

    return new ResponseEntity<OrderUnreadCounts>(orderCounts, HttpStatus.OK);
  }

  @PreAuthorize("(hasAnyRole('ROLE_MANUFACTURER_SUB_ENTITY_DEALER', 'ROLE_MANUFACTURER_SUB_ENTITY_CUSTOMER', 'ROLE_MANUFACTURER_SUB_ENTITY_DEALER_PLUS') and #currentUser.manufacturerSubEntityId == #manufacturerSubEntityId) " +
          " or (hasRole('ROLE_MANUFACTURER') and @manufacturerSubEntityService.getManufacturerSubEntity(#manufacturerSubEntityId).manufacturerId == #currentUser.getManufacturerId())" +
          " or (hasRole('ROLE_MANUFACTURER_SUB_ENTITY_DEALER_PLUS') and @manufacturerSubEntityService.getManufacturerSubEntity(#manufacturerSubEntityId).parentSubEntityId == #currentUser.manufacturerSubEntityId)")
  @RequestMapping(value = "/{manufacturerSubEntityId}/machines", method = RequestMethod.GET)
  public HttpEntity<List<CustomerManual>> getMachinesForManufacturerSubEntityId(
      @AuthenticationPrincipal User currentUser,
      @PathVariable int manufacturerSubEntityId) throws Exception {

    log.info("ACCESS: User [{}], getMachinesForManufacturerSubEntityId, manufacturerSubEntityId [{}]",
        currentUser.accessDetails());
    List<CustomerManual> manualList = manualService.getManualsForManufacturerSubEntity(manufacturerSubEntityId);
    return new ResponseEntity<>(manualList, HttpStatus.OK);
  }

  @PreAuthorize("(hasAnyRole('ROLE_MANUFACTURER_SUB_ENTITY_DEALER', 'ROLE_MANUFACTURER_SUB_ENTITY_CUSTOMER', 'ROLE_MANUFACTURER_SUB_ENTITY_DEALER_PLUS') and #currentUser.manufacturerSubEntityId == #manufacturerSubEntityId)" +
          " or (hasRole('ROLE_MANUFACTURER') and @manufacturerSubEntityService.getManufacturerSubEntity(#manufacturerSubEntityId).manufacturerId == #currentUser.getManufacturerId())" +
          " or (hasRole('ROLE_MANUFACTURER_SUB_ENTITY_DEALER_PLUS') and @manufacturerSubEntityService.getManufacturerSubEntity(#manufacturerSubEntityId).parentSubEntityId == #currentUser.manufacturerSubEntityId())")
  @RequestMapping(value = "/{manufacturerSubEntityId}/settings", method = RequestMethod.GET)
  public HttpEntity<ManufacturerSubEntitySettings> getManufacturerSubEntitySettings(@AuthenticationPrincipal User currentUser, @PathVariable int manufacturerSubEntityId) throws Exception {
    log.info("ACCESS: User [{}], getManufacturerSubEntitySettings, manufacturer [{}]", currentUser, currentUser.getManufacturerSubEntityId());

    ManufacturerSubEntitySettings settings = manufacturerSubEntityService.getManufacturerSubEntitySettings(manufacturerSubEntityId);

    return new ResponseEntity<ManufacturerSubEntitySettings>(settings, HttpStatus.OK);
  }

  @PreAuthorize("(hasRole('ROLE_MANUFACTURER') and @manufacturerSubEntityService.getManufacturerSubEntity(#manufacturerSubEntityId).manufacturerId == #currentUser.getManufacturerId())")
  @RequestMapping(value = "/{manufacturerSubEntityId}/settings", method = RequestMethod.PUT, consumes = "application/json")
  public HttpEntity<Boolean> updateManufacturerSubEntitySettings(@AuthenticationPrincipal User currentUser, @PathVariable int manufacturerSubEntityId, @RequestBody ManufacturerSubEntitySettings settings) throws Exception {

    log.info("ACCESS: User [{}], updateManufacturerSubEntitySettings, manufacturer [{}] ", currentUser.accessDetails(), currentUser.getManufacturerSubEntityId());
    Boolean updated = false;
    try {
      if (settings.getManufacturerSubEntityId() != 0 ) {
        updated = manufacturerSubEntityService.updateManufacturerSubEntitySettings(settings);
      }
    } catch (Exception ex) {
      return new ResponseEntity<>(HttpStatus.CONFLICT);
    }

    return new ResponseEntity<Boolean>(updated, HttpStatus.OK);
  }

  @PreAuthorize("(hasRole('ROLE_MANUFACTURER') and @manufacturerSubEntityService.getManufacturerSubEntity(#manufacturerSubEntityId).manufacturerId == #currentUser.getManufacturerId())")
  @RequestMapping(value = "/{manufacturerSubEntityId}/assignedManualIds", method = RequestMethod.GET)
  public HttpEntity<List<Integer>> getAssignedManualIds (
          @AuthenticationPrincipal User currentUser,
          @PathVariable int manufacturerSubEntityId) throws Exception {

    log.info("ACCESS: User [{}], getAssignedManuals, manufacturerSubEntityId [{}]",
            currentUser.accessDetails(), manufacturerSubEntityId);

    List<Integer> manualIDList = manualService.getAssignedManualIdsForManufacturerSubEntity(manufacturerSubEntityId);
    return new ResponseEntity<>(manualIDList, HttpStatus.OK);
  }


  @PreAuthorize("(hasRole('ROLE_MANUFACTURER') and @manufacturerSubEntityService.getManufacturerSubEntity(#manufacturerSubEntityId).manufacturerId == #currentUser.getManufacturerId())")
  @RequestMapping(value = "/{manufacturerSubEntityId}/assignedRangeIds", method = RequestMethod.GET)
  public HttpEntity<List<Integer>> getAssignedRangeIds (
          @AuthenticationPrincipal User currentUser,
          @PathVariable int manufacturerSubEntityId) throws Exception {

    log.info("ACCESS: User [{}], getAssignedRangeIds, manufacturerSubEntityId [{}]",
            currentUser.accessDetails(), manufacturerSubEntityId);

    List<Integer> manualIDList = rangeService.getAssignedRangeIdsForManufacturerSubEntity(manufacturerSubEntityId);
    return new ResponseEntity<>(manualIDList, HttpStatus.OK);
  }


  private boolean hasAdminPermissionsForSubEntity(User currentUser, int manufacturerSubEntityId) throws Exception {
    return permissionsService.hasAdminPermissionsForSubEntity(currentUser, manufacturerSubEntityId);
  }


}
