package co.cadshare.addresses.adapters.database;

import co.cadshare.addresses.core.ExternalDealer;
import co.cadshare.addresses.core.ExternalPurchaser;
import co.cadshare.addresses.core.User;
import co.cadshare.utils.CycleAvoidingMappingContext;
import org.mapstruct.Mapper;
import org.mapstruct.Named;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper(uses = { ExternalAddressEntityMapper.class, AddressesUserEntityMapper.class})
public interface AddressesDealerEntityMapper {

    AddressesDealerEntityMapper Instance = Mappers.getMapper(AddressesDealerEntityMapper.class);

    //CoreToEntity

    @Mapping(source="users", target="users", qualifiedByName="userCoreToEntity")
    AddressesDealerEntity externalDealerCoreToEntity(ExternalDealer dealer);

    List<AddressesDealerEntity> externalPurchaserCoresToDealerEntities(List<ExternalPurchaser> dealers);


    //Entity To Core

    @Mapping(source="users", target="users", qualifiedByName="userEntityToCore")
    ExternalDealer entityToExternalPurchaserCore(AddressesDealerEntity dealer);

    List<ExternalDealer> entitiesToExternalPurchaserCores(List<AddressesDealerEntity> dealers);


    //Named

    @Named("userEntityToCore")
    public static User userEntityToCore(AddressesUserEntity entity) {
        return AddressesUserEntityMapper.Instance.entityToCore(entity, new CycleAvoidingMappingContext());
    }

    @Named("userCoreToEntity")
    public static AddressesUserEntity userCoreToEntity(User core) {
        return AddressesUserEntityMapper.Instance.coreToEntity(core, new CycleAvoidingMappingContext());
    }

}
