package co.cadshare.modelMgt.publicationCategories.boundary;

import co.cadshare.exceptions.UnprocessableEntityException;
import co.cadshare.modelMgt.publicationCategories.core.PublicationCategory;
import co.cadshare.modelMgt.shared.core.Publication;
import co.cadshare.modelMgt.shared.boundary.PublicationQueryPort;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.dao.EmptyResultDataAccessException;

import co.cadshare.exceptions.NotFoundException;
import co.cadshare.shared.core.user.User;
import co.cadshare.aspects.logging.Log;

import java.util.List;

@Service
public class DeletePublicationCategoryService {

    private final PublicationCategoryCommandPort commandPort;
    private final PublicationCategoryQueryPort queryPort;
    private final PublicationQueryPort publicationQueryPort;

    @Autowired
    public DeletePublicationCategoryService(PublicationCategoryCommandPort commandPort,
                                            PublicationCategoryQueryPort queryPort,
                                            PublicationQueryPort publicationQueryPort) {
        this.commandPort = commandPort;
        this.queryPort = queryPort;
        this.publicationQueryPort = publicationQueryPort;
    }

    @Log
    public void delete(User user, Integer publicationCategoryId) throws Exception {
        try {
            PublicationCategory publicationCategory = this.queryPort.get(publicationCategoryId);
            List<Publication> publications = publicationQueryPort.getPublicationsForPublicationCategory(publicationCategoryId);
            if (publications.isEmpty()) {
                publicationCategory.setDeleted(true);
                this.commandPort.delete(user, publicationCategory);
            } else {
                throw new UnprocessableEntityException("PublicationCategory cannot be deleted because it is used in a publication");
            }
        } catch (EmptyResultDataAccessException ex) {
            throw new NotFoundException("PublicationCategory does not exist");
        }
    }
}
