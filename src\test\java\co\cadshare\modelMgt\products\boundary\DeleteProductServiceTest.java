package co.cadshare.modelMgt.products.boundary;

import co.cadshare.aspects.logging.ServiceLoggingAspect;
import co.cadshare.modelMgt.products.boundary.DeleteProductService;
import co.cadshare.modelMgt.products.boundary.ProductCommandPort;
import co.cadshare.modelMgt.products.boundary.ProductQueryPort;
import co.cadshare.shared.core.user.User;
import co.cadshare.modelMgt.products.core.Product;
import org.junit.Before;
import org.junit.Test;
import org.mockito.MockitoAnnotations;
import org.junit.runner.RunWith;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import static org.mockito.Mockito.*;

@EnableAspectJAutoProxy
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(classes = {DeleteProductService.class, ServiceLoggingAspect.class})
public class DeleteProductServiceTest {

    @MockBean
    private ProductCommandPort commandPort;
    @MockBean
    private ProductQueryPort queryPort;
    @Autowired
    private DeleteProductService out;
    private User user;
    private Product product;
    private Product errorProduct = new Product();

    @Before
    public void Before() {
        MockitoAnnotations.initMocks(this);
        product = buildProduct();
        user = new User() {{
            setUserId(1);
            setFirstName("firstName");
            setLastName("lastName");
        }};
    }

    @Test
    public void DeleteProductSuccess() throws Exception {
        Integer returnVal = Integer.valueOf(1);
        when(queryPort.get(returnVal)).thenReturn(product);
        doNothing().when(commandPort).delete(user, product);
        out.delete(user, returnVal);
    }

    @Test(expected = RuntimeException.class)
    public void DeleteProductFailureException() throws Exception {
        Integer returnVal = Integer.valueOf(2);
        when(queryPort.get(returnVal)).thenReturn(errorProduct);
        doThrow(new RuntimeException("terrible")).when(commandPort).delete(user, errorProduct);
        out.delete(user, returnVal);
    }

    private Product buildProduct() {
        Product product = new Product();
        return product;
    }
}
