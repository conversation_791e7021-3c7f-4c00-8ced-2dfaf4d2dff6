<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.0.xsd
        http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd">

  <changeSet author="AndyB" id="57.5-manualmodelmap-constraints">
    <sql>
      UPDATE manualmodelmap
      SET archived = FALSE
      WHERE archived is NULL;
    </sql>

    <addUniqueConstraint
            tableName="manualmodelmap"
            columnNames="manualid, modelid"
            constraintName="uq_manualmodelmap_manualid_modelid"/>

    <addDefaultValue
            tableName="manualmodelmap"
            columnName="archived"
            defaultValueBoolean="false"/>

    <addNotNullConstraint
            tableName="manualmodelmap"
            columnName="archived"
            columnDataType="boolean"/>
  </changeSet>

</databaseChangeLog>
