package co.cadshare.publications.adapters.database;

import lombok.Data;

import javax.persistence.*;

@Data
@Entity
@Table(name="statedetail")
public class PublicationsSnapshotEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name="id")
    private Integer id;

    @ManyToOne
    @JoinColumn(name = "viewableid")
    private PublicationsViewableConfigEntity viewableConfig;

    @Column(name="stateid")
    private String stateId;

    @Column(name="imgurl")
    private String imgUrl;

}
