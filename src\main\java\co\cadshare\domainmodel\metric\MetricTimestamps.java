/*
 * Copyright 2016 Bell.
 */
package co.cadshare.domainmodel.metric;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.sql.Timestamp;


@JsonInclude(JsonInclude.Include.NON_NULL)
@Data
public class MetricTimestamps {

    private Timestamp currentPeriodStartTimestamp;
    private Timestamp currentPeriodEndTimestamp;

    private Timestamp previousPeriodStartTimestamp;
    private Timestamp previousPeriodEndTimestamp;

    public void setCurrentPeriodStartTimestamp(Timestamp ts) {
        this.currentPeriodStartTimestamp = ts;
        this.previousPeriodEndTimestamp = ts;
    }

    public void setPreviousPeriodEndTimestamp(Timestamp ts) {
        this.previousPeriodEndTimestamp = ts;
        this.currentPeriodStartTimestamp = ts;
    }
}
