package co.cadshare.media.adapters.api.web;

import co.cadshare.media.core.Image;
import org.junit.Test;

import static org.junit.Assert.assertEquals;

public class ImageMapperTest {


	@Test
	public void testToDto() {
		Image image = new Image();
		image.setId(1);
		image.setDescription("desc");
		image.setLocationUrl("url");

		GetImageResponseDto dto = ImageMapper.Instance.toDto(image);

		assertEquals(image.getId(), dto.getId().intValue());
		assertEquals(image.getDescription(), dto.getDescription());
		assertEquals(image.getLocationUrl(), dto.getLocationUrl());
	}
}
