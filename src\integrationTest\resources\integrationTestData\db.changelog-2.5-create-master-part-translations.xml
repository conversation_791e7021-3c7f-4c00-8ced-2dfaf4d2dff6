<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                   http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.0.xsd
">

    <changeSet author="AndyB" id="2.5-integration-test-data-create-master-part-translations-1">
        <sql stripComments="true">
            INSERT INTO public.parttranslation(
            description, languageid, masterpartid)
            VALUES ('description for English', 1, 1);
            INSERT INTO public.parttranslation(
            description, languageid, masterpartid)
            VALUES ('description for French', 2, 1);
            INSERT INTO public.parttranslation(
            description, languageid, masterpartid)
            VALUES ('description for Russian', 4, 1);
        </sql>
    </changeSet>
    <changeSet author="AndyB" id="2.5-integration-test-data-create-master-part-translations-2">
        <sql stripComments="true">
            INSERT INTO public.parttranslation(
            description, languageid, masterpartid)
            VALUES ('description for English', 1, 2);
            INSERT INTO public.parttranslation(
            description, languageid, masterpartid)
            VALUES ('description for German', 3, 2);
            INSERT INTO public.parttranslation(
            description, languageid, masterpartid)
            VALUES ('description for Spanish', 5, 2);
        </sql>
    </changeSet>
    <changeSet author="AndyB" id="2.5-integration-test-data-create-master-part-translations-3">
        <sql stripComments="true">
            INSERT INTO public.parttranslation(
            description, languageid, masterpartid)
            VALUES ('Cat-part-3 desc in English', 1, 3);
            INSERT INTO public.parttranslation(
            description, languageid, masterpartid)
            VALUES ('Cat-part-3 desc en Français', 2, 3);
            INSERT INTO public.parttranslation(
            description, languageid, masterpartid)
            VALUES ('Cat-part-3 desc auf Englisch', 3, 3);
        </sql>
    </changeSet>
    <changeSet author="AndyB" id="2.5-integration-test-data-create-master-part-translations-4">
        <sql stripComments="true">
            INSERT INTO public.parttranslation(
            description, languageid, masterpartid)
            VALUES ('description for English', 1, 4);
            INSERT INTO public.parttranslation(
            description, languageid, masterpartid)
            VALUES ('description for French', 2, 4);
            INSERT INTO public.parttranslation(
            description, languageid, masterpartid)
            VALUES ('description for German', 3, 4);
        </sql>
    </changeSet>
    <changeSet author="AndyB" id="2.5-integration-test-data-create-master-part-translations-5">
        <sql stripComments="true">
            INSERT INTO public.parttranslation(
            description, languageid, masterpartid)
            VALUES ('description for English', 1, 5);
            INSERT INTO public.parttranslation(
            description, languageid, masterpartid)
            VALUES ('description for French', 2, 5);
            INSERT INTO public.parttranslation(
            description, languageid, masterpartid)
            VALUES ('description for German', 3, 5);
        </sql>
    </changeSet>
    <changeSet author="AndyB" id="2.5-integration-test-data-create-master-part-translations-6">
        <sql stripComments="true">
            INSERT INTO public.parttranslation(
            description, languageid, masterpartid)
            VALUES ('description for English', 1, 6);
            INSERT INTO public.parttranslation(
            description, languageid, masterpartid)
            VALUES ('description for French', 2, 6);
            INSERT INTO public.parttranslation(
            description, languageid, masterpartid)
            VALUES ('description for German', 3, 6);
        </sql>
    </changeSet>



    <changeSet author="AndyB" id="2.5-integration-test-data-create-master-part-translations-101">
        <sql stripComments="true">
            INSERT INTO public.parttranslation(
            description, languageid, masterpartid)
            VALUES ('description for English', 1, 101);
            INSERT INTO public.parttranslation(
            description, languageid, masterpartid)
            VALUES ('description for French', 2, 101);
            INSERT INTO public.parttranslation(
            description, languageid, masterpartid)
            VALUES ('description for Russian', 4, 101);
        </sql>
    </changeSet>
    <changeSet author="AndyB" id="2.5-integration-test-data-create-master-part-translations-102">
        <sql stripComments="true">
            INSERT INTO public.parttranslation(
            description, languageid, masterpartid)
            VALUES ('description for English', 1, 102);
            INSERT INTO public.parttranslation(
            description, languageid, masterpartid)
            VALUES ('description for German', 3, 102);
            INSERT INTO public.parttranslation(
            description, languageid, masterpartid)
            VALUES ('description for Spanish', 5, 102);
        </sql>
    </changeSet>
    <changeSet author="AndyB" id="2.5-integration-test-data-create-master-part-translations-103">
        <sql stripComments="true">
            INSERT INTO public.parttranslation(
            description, languageid, masterpartid)
            VALUES ('description for English', 1, 103);
            INSERT INTO public.parttranslation(
            description, languageid, masterpartid)
            VALUES ('description for French', 2, 103);
            INSERT INTO public.parttranslation(
            description, languageid, masterpartid)
            VALUES ('description for German', 3, 103);
        </sql>
    </changeSet>
    <changeSet author="AndyB" id="2.5-integration-test-data-create-master-part-translations-104">
        <sql stripComments="true">
            INSERT INTO public.parttranslation(
            description, languageid, masterpartid)
            VALUES ('description for English', 1, 104);
            INSERT INTO public.parttranslation(
            description, languageid, masterpartid)
            VALUES ('description for French', 2, 104);
            INSERT INTO public.parttranslation(
            description, languageid, masterpartid)
            VALUES ('description for German', 3, 104);
        </sql>
    </changeSet>
    <changeSet author="AndyB" id="2.5-integration-test-data-create-master-part-translations-105">
        <sql stripComments="true">
            INSERT INTO public.parttranslation(
            description, languageid, masterpartid)
            VALUES ('description for English', 1, 105);
            INSERT INTO public.parttranslation(
            description, languageid, masterpartid)
            VALUES ('description for French', 2, 105);
            INSERT INTO public.parttranslation(
            description, languageid, masterpartid)
            VALUES ('description for German', 3, 105);
        </sql>
    </changeSet>
    <changeSet author="AndyB" id="2.5-integration-test-data-create-master-part-translations-106">
        <sql stripComments="true">
            INSERT INTO public.parttranslation(
            description, languageid, masterpartid)
            VALUES ('description for English', 1, 106);
            INSERT INTO public.parttranslation(
            description, languageid, masterpartid)
            VALUES ('description for French', 2, 106);
            INSERT INTO public.parttranslation(
            description, languageid, masterpartid)
            VALUES ('description for German', 3, 106);
        </sql>
    </changeSet>

    <changeSet author="AndyB" id="2.5-integration-test-data-create-master-part-translations-107">
        <sql stripComments="true">
            INSERT INTO public.parttranslation(
            description, languageid, masterpartid)
            VALUES ('description for English', 1, 107);
            INSERT INTO public.parttranslation(
            description, languageid, masterpartid)
            VALUES ('description for French', 2, 107);
            INSERT INTO public.parttranslation(
            description, languageid, masterpartid)
            VALUES ('description for German', 3, 107);
        </sql>
    </changeSet>

    <changeSet author="AndyB" id="2.5-integration-test-data-create-master-part-translations-108">
        <sql stripComments="true">
            INSERT INTO public.parttranslation(
            description, languageid, masterpartid)
            VALUES ('description for English', 1, 108);
            INSERT INTO public.parttranslation(
            description, languageid, masterpartid)
            VALUES ('description for French', 2, 108);
            INSERT INTO public.parttranslation(
            description, languageid, masterpartid)
            VALUES ('description for German', 3, 108);
        </sql>
    </changeSet>

    <changeSet author="AndyB" id="2.5-integration-test-data-create-master-part-translations-201">
        <sql stripComments="true">
            INSERT INTO public.parttranslation(
            description, languageid, masterpartid)
            VALUES ('Jcb-part-201 desc in English', 1, 201);
            INSERT INTO public.parttranslation(
            description, languageid, masterpartid)
            VALUES ('Jcb-part-201 desc en Français', 2, 201);
            INSERT INTO public.parttranslation(
            description, languageid, masterpartid)
            VALUES ('Jcb-part-201 desc auf Englisch', 3, 201);
        </sql>
    </changeSet>

    <changeSet author="AndyB" id="2.5-integration-test-data-create-master-part-translations-202">
        <sql stripComments="true">
            INSERT INTO public.parttranslation(
            description, languageid, masterpartid)
            VALUES ('Lbr-part-202 desc in English', 1, 202);
            INSERT INTO public.parttranslation(
            description, languageid, masterpartid)
            VALUES ('Lbr-part-202 desc en Français', 2, 202);
            INSERT INTO public.parttranslation(
            description, languageid, masterpartid)
            VALUES ('Lbr-part-202 desc auf Englisch', 3, 202);
        </sql>
    </changeSet>

    <changeSet author="AndyB" id="2.5-integration-test-data-create-master-part-translations-203">
        <sql stripComments="true">
            INSERT INTO public.parttranslation(
            description, languageid, masterpartid)
            VALUES ('Trx-part-203 desc in English', 1, 203);
            INSERT INTO public.parttranslation(
            description, languageid, masterpartid)
            VALUES ('Trx-part-203 desc en Français', 2, 203);
            INSERT INTO public.parttranslation(
            description, languageid, masterpartid)
            VALUES ('Trx-part-203 desc auf Englisch', 3, 203);
        </sql>
    </changeSet>

    <changeSet author="AndyB" id="2.5-integration-test-data-create-master-part-translations-211">
        <sql stripComments="true">
            INSERT INTO public.parttranslation(
            description, languageid, masterpartid)
            VALUES ('Cat-part-211 desc in English', 1, 211);
            INSERT INTO public.parttranslation(
            description, languageid, masterpartid)
            VALUES ('Cat-part-211 desc en Français', 2, 211);
            INSERT INTO public.parttranslation(
            description, languageid, masterpartid)
            VALUES ('Cat-part-211 desc auf Englisch', 3, 211);
        </sql>
    </changeSet>

    <changeSet author="AndyB" id="2.5-integration-test-data-create-master-part-translations-221">
        <sql stripComments="true">
            INSERT INTO public.parttranslation(
            description, languageid, masterpartid)
            VALUES ('Jcb-part-221 desc in English', 1, 221);
            INSERT INTO public.parttranslation(
            description, languageid, masterpartid)
            VALUES ('Jcb-part-221 desc en Français', 2, 221);
            INSERT INTO public.parttranslation(
            description, languageid, masterpartid)
            VALUES ('Jcb-part-221 desc auf Englisch', 3, 221);
        </sql>
    </changeSet>

    <changeSet author="AndyB" id="2.5-integration-test-data-create-master-part-translations-231">
        <sql stripComments="true">
            INSERT INTO public.parttranslation(
            description, languageid, masterpartid)
            VALUES ('Lbr-part-231 desc in English', 1, 231);
            INSERT INTO public.parttranslation(
            description, languageid, masterpartid)
            VALUES ('Lbr-part-231 desc en Français', 2, 231);
            INSERT INTO public.parttranslation(
            description, languageid, masterpartid)
            VALUES ('Lbr-part-231 desc auf Englisch', 3, 231);
        </sql>
    </changeSet>

    <changeSet author="AndyB" id="2.5-integration-test-data-create-master-part-translations-241">
        <sql stripComments="true">
            INSERT INTO public.parttranslation(
            description, languageid, masterpartid)
            VALUES ('Trx-part-241 desc in English', 1, 241);
            INSERT INTO public.parttranslation(
            description, languageid, masterpartid)
            VALUES ('Trx-part-241 desc en Français', 2, 241);
            INSERT INTO public.parttranslation(
            description, languageid, masterpartid)
            VALUES ('Trx-part-241 desc auf Englisch', 3, 241);
        </sql>
    </changeSet>

</databaseChangeLog>
