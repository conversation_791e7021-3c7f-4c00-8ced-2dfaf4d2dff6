package co.cadshare.modelMgt.publications.adapters.database;

import co.cadshare.modelMgt.models.core.model.viewable.Viewable;

import co.cadshare.modelMgt.products.core.Product;
import co.cadshare.shared.adapters.database.ImageEntityMapper;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper(uses = { PublicationsRangeEntityMapper.class })
public interface PublicationsProductEntityMapper {

    PublicationsProductEntityMapper Instance = Mappers.getMapper(PublicationsProductEntityMapper.class);

	@Mapping(source = "range.id", target = "rangeId")
	@Mapping(source = "range.name", target = "rangeName")
	Product entityToCore(PublicationsProductEntity entity);

    List<Product> entitiesToCores(List<PublicationsProductEntity> publication);

}
