package co.cadshare.modelMgt.products.adapters.api.ext;

import co.cadshare.config.ApplicationProperties;
import co.cadshare.modelMgt.products.adapters.api.GetProductListResponseDto;
import co.cadshare.modelMgt.products.adapters.api.GetProductResponseDto;
import co.cadshare.modelMgt.products.adapters.api.ProductMapper;
import co.cadshare.modelMgt.products.boundary.CreateProductService;
import co.cadshare.modelMgt.products.boundary.DeleteProductService;
import co.cadshare.shared.core.user.User;
import co.cadshare.modelMgt.products.boundary.UpdateProductService;
import co.cadshare.modelMgt.products.boundary.GetProductService;
import co.cadshare.modelMgt.products.core.Product;
import co.cadshare.users.boundary.UsersService;
import co.cadshare.shared.adapters.api.ext.ExternalApiController;
import io.swagger.v3.oas.annotations.Operation;
import org.apache.commons.lang3.NotImplementedException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.*;

import java.util.List;


@RestController("ProductsExternalController")
@RequestMapping(value = "/api/manufacturers/{manufacturer-id}/ranges/{range-id}/products", headers = "X-API-Version=1")
public class ProductsController extends ExternalApiController {

    private final CreateProductService createProductService;
    private final UpdateProductService updateProductService;
    private final DeleteProductService deleteProductService;
    private final GetProductService getProductService;

    @Autowired
    public ProductsController(CreateProductService createProductService,
                              UpdateProductService updateProductService,
                              DeleteProductService deleteProductService,
                              GetProductService getProductService,
                              UsersService userService,
                              ApplicationProperties properties){

        super(userService, properties);
        this.createProductService = createProductService;
        this.updateProductService = updateProductService;
        this.deleteProductService = deleteProductService;
        this.getProductService = getProductService;
    }

    @PostMapping(consumes = "application/json", produces = "application/json")
    @ResponseStatus(HttpStatus.OK)
    @Operation(summary = "Create a Product")
    public ResponseEntity<PostProductResponseDto> postProduct(@PathVariable("manufacturer-id") int manufacturerId,
                                                              @PathVariable("range-id") int rangeId,
                                                                @AuthenticationPrincipal User currentUser,
                                                                @RequestBody PostProductRequestDto postProduct) throws Exception {

        checkManufacturerPermissions(currentUser, manufacturerId);
        Product product = ProductMapper.Instance.postRequestDtoToProduct(postProduct);
        product.setRangeId(rangeId);
        Integer createdId = this.createProductService.create(product, manufacturerId);
        PostProductResponseDto response = new PostProductResponseDto() {{setId(createdId);}};
        return new ResponseEntity<>(response, HttpStatus.OK);
    }

    @PutMapping(path = "/{product-id}", consumes = "application/json")
    @ResponseStatus(HttpStatus.OK)
    @Operation(summary = "Maintain the details of a Product")
    public ResponseEntity putProduct(@PathVariable("manufacturer-id") int manufacturerId,
                                        @PathVariable("range-id") int rangeId,
                                         @PathVariable("product-id") Integer productId,
                                         @AuthenticationPrincipal User currentUser,
                                         @RequestBody PutProductRequestDto putProduct) throws Exception {

        throw new NotImplementedException("This action is not yet supported.");
        /*
        Product product = ProductMapper.Instance.putRequestDtoToProduct(putProduct);
        product.setRangeId(rangeId);
        product.setId(productId);
        this.updateProductService.update(currentUser, product);
        return new ResponseEntity<>(HttpStatus.OK);
         */
    }

    @DeleteMapping(path = "/{product-id}")
    @ResponseStatus(HttpStatus.OK)
    @Operation(summary = "Delete a Product")
    public ResponseEntity deleteProduct(@PathVariable("manufacturer-id") int manufacturerId,
                                        @PathVariable("range-id") int rangeId,
                                            @PathVariable("product-id") Integer productId,
                                            @AuthenticationPrincipal User currentUser) {

        throw new NotImplementedException("This action is not yet supported.");
        //this.deleteProductService.delete(currentUser, productId);
        //return new ResponseEntity<>(HttpStatus.OK);
    }

    @GetMapping(path = "/{product-id}", produces = "application/json")
    @ResponseStatus(HttpStatus.OK)
    @Operation(summary = "Get the details of a specific Product")
    public ResponseEntity<GetProductResponseDto> getProduct(@PathVariable("manufacturer-id") int manufacturerId,
                                                            @PathVariable("range-id") int rangeId,
                                                            @PathVariable("product-id") Integer productId,
                                                            @AuthenticationPrincipal User currentUser) {

        checkManufacturerPermissions(currentUser, manufacturerId);
        Product product = this.getProductService.get(productId);
        GetProductResponseDto getResponseDto = ProductMapper.Instance.productToGetResponseDto(product);
        return new ResponseEntity<>(getResponseDto, HttpStatus.OK);
    }

    @GetMapping(produces = "application/json")
    @ResponseStatus(HttpStatus.OK)
    @Operation(summary = "Get the summary details of the list of all Products belonging to the Manufacturer")
    public ResponseEntity<GetProductListResponseDto> getProducts(@PathVariable("manufacturer-id") int manufacturerId,
                                                                 @PathVariable("range-id") int rangeId,
                                                                 @AuthenticationPrincipal User currentUser) {

        checkManufacturerPermissions(currentUser, manufacturerId);
        List<Product> products = this.getProductService.getProductsForRange(rangeId);
        List<GetProductResponseDto> productResponses = ProductMapper.Instance.productToGetListResponseDto(products);
        GetProductListResponseDto getListResponseDto = new GetProductListResponseDto(){{
            setProducts(productResponses);
        }};
        return new ResponseEntity<>(getListResponseDto, HttpStatus.OK);
    }
}
