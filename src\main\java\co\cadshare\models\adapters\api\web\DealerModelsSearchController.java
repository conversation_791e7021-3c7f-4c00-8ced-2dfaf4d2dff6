package co.cadshare.models.adapters.api.web;

import co.cadshare.shared.core.user.User;
import co.cadshare.models.boundary.SearchModelsService;
import co.cadshare.models.core.Model;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("dealers/{dealer-id}")
public class DealerModelsSearchController {

  private SearchModelsService searchModelService;

  @Autowired
  public DealerModelsSearchController(SearchModelsService searchModelService) {
    this.searchModelService = searchModelService;
  }

  @PreAuthorize("(hasRole('ROLE_MANUFACTURER') and hasRole('Parts')) or "+
          " (hasRole('ROLE_MANUFACTURER_SUB_ENTITY_DEALER') or " +
          " hasRole('ROLE_MANUFACTURER_SUB_ENTITY_CUSTOMER') or " +
          " hasRole('ROLE_MANUFACTURER_SUB_ENTITY_DEALER_PLUS'))")
  @PostMapping("model-search")
  public ResponseEntity<PostModelSearchForPartNumberResponseDto> PostModelSearchForPartNumber(@PathVariable("dealer-id") int dealerId,
                                                                                         @AuthenticationPrincipal User currentUser,
                                                                                          @RequestBody PostModelSearchForPartNumberDto searchDto) {

    List<Model> models = this.searchModelService.getPublishedModelsForPart(dealerId, searchDto.getPartNumber());
    List<ModelForPartNumberResponseDto> modelResponses = ModelMapper.Instance.ModelsToModelForPartNumberResponsesDto(models);
    PostModelSearchForPartNumberResponseDto response = new PostModelSearchForPartNumberResponseDto() {{
      setModels(modelResponses);
    }};
    return new ResponseEntity<>(response, HttpStatus.OK);
  }


}
