/*
 * Copyright 2016 Bell.
 */
package co.cadshare.controller;

import co.cadshare.modelMgt.models.core.model.viewable.Viewable;
import co.cadshare.shared.core.user.User;
import co.cadshare.services.SoftCopyViewableService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PostAuthorize;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequestMapping("/softcopy/viewer/")
public class SoftCopyViewableController {


    private SoftCopyViewableService softCopyViewableService;

    public SoftCopyViewableController( SoftCopyViewableService softCopyViewableService) {
        this.softCopyViewableService = softCopyViewableService;
    }

    @GetMapping(value = "/viewable/{viewableId}")
    @PostAuthorize("@permissionsDao.hasAccessToModel(#currentUser.getUserId(), returnObject.getModelId())")
    public Viewable getSoftCopyViewerByViewableId(@AuthenticationPrincipal User currentUser, @PathVariable int viewableId) {
        log.info("ACCESS: User [{}], getViewableByModelId, model ID: [{}]", currentUser.accessDetails(), viewableId);
        return softCopyViewableService.getStatesForViewable(viewableId);
    }
}
