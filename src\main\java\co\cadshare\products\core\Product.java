package co.cadshare.products.core;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import java.sql.Timestamp;

@Data
public class Product {

    private Integer id;
    private String name;
    private Integer rangeId;
    private boolean deleted;
    private Integer createdByUserId;
    private Integer modifiedByUserId;
    private Timestamp createdDate;
    private Timestamp modifiedDate;

}