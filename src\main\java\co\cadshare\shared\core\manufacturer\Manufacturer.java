package co.cadshare.shared.core.manufacturer;

import co.cadshare.addresses.core.Address;
import co.cadshare.shared.core.purchaser.Purchaser;
import co.cadshare.shared.core.user.User;
import co.cadshare.utils.ObjectUtilsExtension;
import io.swagger.annotations.ApiModelProperty;
import java.sql.Timestamp;
import java.util.List;

import lombok.AccessLevel;
import lombok.Data;
import lombok.Getter;
import lombok.experimental.ExtensionMethod;

@Data
@ExtensionMethod(ObjectUtilsExtension.class)
public class Manufacturer {

    @ApiModelProperty(hidden = true)
    private int manufacturerId;
    private String name;
    private String subdomain;
    
    private String emailSignature;
    private String logoUrl;
    @Getter(AccessLevel.NONE)
    private String supportEmail;
    private String phone;
    
    private Address address;

    private boolean paid;

    private Timestamp createdDate;
    private int createdByUserId;
    private Timestamp modifiedDate;
    private Integer modifiedByUserId;

    protected ManufacturerSettings manufacturerSettings;

    private List<AdditionalEmail> additionalEmails;

    public enum Currency {
        GBP,
        USD,
        EUR
    }

    public boolean usesExternalAddresses() {
        return manufacturerSettings.isNotNull() && !manufacturerSettings.isAddressCreationEnabled();
    }

    public boolean hasTermsAndConditions() {
        return manufacturerSettings.isNotNull()  &&
                manufacturerSettings.getTermsAndConditionsUrl().isNotNull() &&
                !manufacturerSettings.getTermsAndConditionsUrl().isEmpty();
    }

    public boolean hasEmailSignature() {
        return emailSignature.isNotNull() && !getEmailSignature().isEmpty();
    }

    public String getSupportEmail() {
        return supportEmail.isNotNull() ? supportEmail : "<EMAIL>";
    }

}

