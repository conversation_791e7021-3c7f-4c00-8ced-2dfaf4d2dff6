package co.cadshare.modelMgt.publications.adapters.database;

import co.cadshare.modelMgt.publications.boundary.TechDocQueryPort;
import co.cadshare.modelMgt.publications.core.TechDoc;
import org.springframework.stereotype.Service;

@Service
public class TechDocDataFacade implements TechDocQueryPort {

	private final TechDocDao techDocDao;

	public TechDocDataFacade(TechDocDao techDocDao) {
		this.techDocDao = techDocDao;
	}

	@Override
	public TechDoc getTechDocById(int techDocId) {
		return techDocDao.getTechDocById(techDocId);
	}
}
