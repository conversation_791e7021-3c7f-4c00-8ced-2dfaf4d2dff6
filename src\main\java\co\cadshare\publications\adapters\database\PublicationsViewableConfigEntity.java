package co.cadshare.publications.adapters.database;

import lombok.Data;

import javax.persistence.*;
import java.util.List;


@Data
@Entity
@Table(name="viewable")
public class PublicationsViewableConfigEntity {

    @Id
    @GeneratedValue (strategy = GenerationType.IDENTITY)
    @Column(name="id")
    private Integer id;

    @ManyToOne
    @JoinColumn(name = "modelid")
    private PublicationsModelEntity viewable;

    @OneToMany(mappedBy="viewableConfig")
    private List<PublicationsSnapshotEntity> snapshots;

}

