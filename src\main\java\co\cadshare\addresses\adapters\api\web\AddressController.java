package co.cadshare.addresses.adapters.api.web;

import co.cadshare.addresses.boundary.CreateAddressService;
import co.cadshare.addresses.boundary.MaintainAddressService;
import co.cadshare.addresses.core.Address;
import co.cadshare.addresses.core.ContactName;
import co.cadshare.shared.core.user.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Controller
@RequestMapping("/address")
@Slf4j
public class AddressController {

  private final MaintainAddressService maintainAddressService;
  private final CreateAddressService createAddressService;

  @Autowired
  public AddressController(MaintainAddressService maintainAddressService,
                           CreateAddressService createAddressService) {
      this.maintainAddressService = maintainAddressService;
      this.createAddressService = createAddressService;
  }

  @CanMaintainAddress
  @GetMapping
  public HttpEntity<List<Address>> getAddressesForUser(@AuthenticationPrincipal User currentUser,
                                                       int userId) {
    log.info("ACCESS: User [{}]", currentUser.accessDetails());
    List<Address> addressList = maintainAddressService.getAllAddressesForUser(userId);
    return new ResponseEntity<>(addressList, HttpStatus.OK);
  }

    @CanMaintainAddress
    @GetMapping("/{addressId}/contacts")
	public ResponseEntity<ContactsListDto> getContactsForAddressForUser(@AuthenticationPrincipal User currentUser,
                                                                        @PathVariable int addressId,
                                                                        int userId) {

	    List<ContactName> contacts = maintainAddressService.getAddressContactsForUser(addressId, userId);
	    List<ContactDto> contactDtos = ContactMapper.Instance.coresToDtos(contacts);
	    return new ResponseEntity<>(new ContactsListDto(contactDtos), HttpStatus.OK);
    }


  @CanMaintainAddress
  @PostMapping(consumes = "application/json")
  public HttpEntity<Integer> createUserAddress(@AuthenticationPrincipal User currentUser,
                                               @RequestBody Address address,
                                               int userId) {

    log.info("ACCESS: User [{}], createUserAddress", currentUser.accessDetails());
    int addressId = createAddressService.createAddressForUser(currentUser, address, userId);
    return new ResponseEntity<>(addressId, HttpStatus.OK);
  }

  @PutMapping(value = "/{addressId}", consumes = "application/json")
  public HttpEntity<Integer> updateAddress(@AuthenticationPrincipal User currentUser,
                                           @RequestBody Address address,
                                           @PathVariable int addressId) {

    log.info("ACCESS: User [{}], updateAddress, addressId [{}]", currentUser.accessDetails(), address.getId());

    maintainAddressService.updateUserAddress(currentUser.getUserId(), address);
    return new ResponseEntity<>(HttpStatus.OK);
  }

  @DeleteMapping("/{addressId}")
  public HttpEntity<Boolean> deleteAddress(@AuthenticationPrincipal User currentUser,
                                           @PathVariable int addressId) {

    log.info("ACCESS: User [{}], deleteAddress, address id [{}]", currentUser.accessDetails(), addressId);
    Boolean isDeleted = maintainAddressService.deleteUserAddress(currentUser.getUserId(), addressId);
    return new ResponseEntity<>(isDeleted, HttpStatus.OK);
  }
}
