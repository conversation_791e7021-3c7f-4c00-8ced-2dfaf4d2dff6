package co.cadshare.modelMgt.models.core.processor.fileproperties;

import co.cadshare.modelMgt.models.core.MetadataObjectExtended;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.LinkedHashMap;
import java.util.List;



@Component
public class NexatSparePartPropertyProcessor extends SparePartPropertyProcessor implements FilePropertiesProcessor, InitializingBean {


    @Override
    public void setProperties(LinkedHashMap properties, MetadataObjectExtended object) {

        // & set sparePart to true
        String sparePartIdentifier = getPropertyValue(properties);
        object.setSparePartIdentifier(sparePartIdentifier);
        boolean isSparePart = Arrays.asList("A", "B", "C", "D", "Z").contains(sparePartIdentifier);
        object.setSparePart(isSparePart);
    }
    
    //}
    @Override
    public List<String> getSynonyms() {
        return sparePartSynonyms;
    }

    @Override
    public void setSynonyms(List<String> synonyms) {
        sparePartSynonyms = synonyms;
    }

    @Override
    public void afterPropertiesSet() throws Exception {
     //not checking properties as synonyms won't be set at this point.
    }
}

