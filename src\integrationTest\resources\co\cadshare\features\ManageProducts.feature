Feature: Manage Products

  Scenario Outline: Get All Products for Range
    Given I am a Manufacturer with email address <emailAddress>
    Then I can view all Products belonging to my Manufacturer and <range>
    Examples:
      | emailAddress                 | range             |
      | <EMAIL> | Caterpillar Range 1|

  Scenario Outline: Get All Products
    Given I am a Manufacturer with email address <emailAddress>
    Then I can view all Products belonging to my Manufacturer
    Examples:
      | emailAddress                 |  |
      | <EMAIL> |  |

  Scenario Outline: Create new Product as Manufacturer
    Given I am a Manufacturer with email address <emailAddress>
    And a Product with name <ProductName> doesn't exist
    When I create a new Product named <ProductName>
    Then a Product with name <ProductName> exists
    Examples:
      | emailAddress                 | ProductName |
      | <EMAIL> | New Product |

Scenario Outline: Update Product as Manufacturer
    Given I am a Manufacturer with email address <emailAddress>
    And a Product with name <oldProductName> exists
    When I update the Product named <oldProductName> to <newProductName>
    Then a Product with name <newProductName> exists
    And a Product with name <oldProductName> doesn't exist
    Examples:
      | emailAddress                 | newProductName | oldProductName  |
      | <EMAIL> | New Product    | Publication for Caterpillar |

  Scenario Outline: Delete Product as Manufacturer
    Given I am a Manufacturer with email address <emailAddress>
    And I create a new Product named <ProductName>
    And a Product with name <ProductName> exists
    When I delete the Product named <ProductName>
    Then a Product with name <ProductName> doesn't exist
    Examples:
      | emailAddress                 | ProductName      |
      | <EMAIL> | deleted Publication Category |

    Scenario Outline: Get Product as Manufacturer
    Given I am a Manufacturer with email address <emailAddress>
    And I create a new Product named <ProductName>
    And a Product with name <ProductName> exists
    Then I get the Product named <ProductName>
    Examples:
      | emailAddress                 | ProductName     |
      | <EMAIL> | Single Publication Category |