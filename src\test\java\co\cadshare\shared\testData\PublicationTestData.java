package co.cadshare.shared.testData;

import co.cadshare.modelMgt.publicationCategories.adapters.database.PublicationCategoryEntity;
import co.cadshare.modelMgt.publications.adapters.database.*;
import co.cadshare.modelMgt.publications.core.ManualStatus;
import co.cadshare.shared.adapters.database.ImageEntity;

import java.util.List;
import java.util.Set;

public abstract class PublicationTestData {

	public static PublicationsPublicationEntity getPublicationEntity(PublicationCategoryEntity publicationCategoryEntity,
	                                                                 ImageEntity coverImage,
	                                                                 ImageEntity featuredViewableImage,
	                                                                 Set<PublicationsModelEntity> modelEntities,
	                                                                 Set<PublicationsTechDocEntity > techDocEntities,
	                                                                 Set<PublicationsVideoEntity> videoEntities,
	                                                                 Set<PublicationsKitEntity> kitEntities,
	                                                                 Set<PublicationsPurchaserEntity> purchaserEntities) {
		PublicationsPublicationEntity publicationEntity = new PublicationsPublicationEntity();
		publicationEntity.setName("Publication name");
		publicationEntity.setDeleted(true);
		publicationEntity.setManufacturerId(1);
		publicationEntity.setFeaturedViewableId(5);
		publicationEntity.setPublicationCategory(publicationCategoryEntity);
		publicationEntity.setFeaturedViewableImage(featuredViewableImage);
		publicationEntity.setCoverImage(coverImage);
		publicationEntity.setModels(modelEntities);
		publicationEntity.setTechDocs(techDocEntities);
		publicationEntity.setVideos(videoEntities);
		publicationEntity.setKits(kitEntities);
		publicationEntity.setPurchasers(purchaserEntities);
		publicationEntity.setStatus(ManualStatus.Status.PUBLISHED);
		return publicationEntity;
	}

	public static PublicationsRangeEntity getRangeEntity() {
		PublicationsRangeEntity rangeEntity = new PublicationsRangeEntity();
		rangeEntity.setName("Range name");
		return rangeEntity;
	}

	public static PublicationsProductEntity getProductEntity() {
		PublicationsProductEntity productEntity = new PublicationsProductEntity();
		productEntity.setName("Product Name");
		return productEntity;
	}

	public static PublicationsModelEntity getModelEntity() {
		PublicationsModelEntity modelEntity = new PublicationsModelEntity();
		modelEntity.setModelName("Model name");
		modelEntity.setSetupComplete(true);
		return modelEntity;
	}

	public static ImageEntity getCoverImage() {
		ImageEntity coverImage = new ImageEntity();
		coverImage.setLocationUrl("https://cover.image.url");
		coverImage.setDescription("cover image");
		return coverImage;
	}

	public static ImageEntity getFeaturedViewableImage() {
		ImageEntity featuredViewableImage = new ImageEntity();
		featuredViewableImage.setLocationUrl("https://featured.viewable.image.url");
		featuredViewableImage.setDescription("featured viewable image");
		return featuredViewableImage;
	}

	public static PublicationsTechDocEntity getTechDocEntity() {
	    PublicationsTechDocEntity techDocEntity = new PublicationsTechDocEntity();
	    techDocEntity.setName("Tech Document Title");
	    techDocEntity.setUrl("https://techdoc.url");
		techDocEntity.setManufacturerId(1);
	    return techDocEntity;
	}

	public static PublicationsVideoEntity getVideoEntity() {
		PublicationsVideoEntity videoEntity = new PublicationsVideoEntity();
		videoEntity.setName("Video title");
		videoEntity.setUrl("https://video.url");
		videoEntity.setManufacturerId(1);
		return videoEntity;
	}

	public static PublicationsKitEntity getKitEntity() {
		PublicationsKitEntity kitEntity = new PublicationsKitEntity();
		kitEntity.setTitle("Kit title");
		kitEntity.setDescription("Kit description");
		return kitEntity;
	}

	public static PublicationsPurchaserEntity getDealerEntity() {
		PublicationsPurchaserEntity purchaserEntity = new PublicationsDealerEntity();
		purchaserEntity.setName("customer 1");
		return purchaserEntity;
	}

	public static PublicationsPurchaserEntity getCustomerEntity() {
		PublicationsPurchaserEntity purchaserEntity = new PublicationsCustomerEntity();
		purchaserEntity.setName("customer 2");
		return purchaserEntity;
	}

	public static PublicationCategoryEntity getPublicationCategoryEntity() {
		PublicationCategoryEntity publicationCategoryEntity = new PublicationCategoryEntity();
		publicationCategoryEntity.setName("Category name");
		return publicationCategoryEntity;
	}
}
