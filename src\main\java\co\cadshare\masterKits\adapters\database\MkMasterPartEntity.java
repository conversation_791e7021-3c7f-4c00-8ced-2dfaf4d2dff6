package co.cadshare.masterKits.adapters.database;

import co.cadshare.masterParts.core.MasterPartType;
import lombok.Data;

import javax.persistence.*;
import java.util.List;

@Data
@Entity
@Table(name="masterpart")
public class MkMasterPartEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name="id")
    private Integer id;

    @Column(name="partnumber")
    private String partNumber;

    @ManyToOne(cascade=CascadeType.DETACH, fetch = FetchType.LAZY)
    @JoinColumn(name = "manufacturerid")
    private MkManufacturerEntity manufacturer;

    private Float price;

    @OneToMany(cascade=CascadeType.ALL, fetch=FetchType.LAZY)
    @JoinColumn(name="masterpartid", nullable = false)
    private List<MkMasterPartPriceEntity> prices;

    @OneToMany(cascade=CascadeType.ALL, fetch=FetchType.LAZY)
    @JoinColumn(name="masterpartid", nullable = false)
    private List<MkMasterPartTranslationEntity> translations;

    @Enumerated(EnumType.STRING)
    private MasterPartType type;
}
