package co.cadshare.masterParts.adapters.database;

import co.cadshare.masterParts.core.Translation;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface MasterPartTranslationEntityViewMapper {

    MasterPartTranslationEntityViewMapper Instance = Mappers.getMapper(MasterPartTranslationEntityViewMapper.class);

    @Mapping(source = "id", target = "masterPartId")
    @Mapping(source = "description", target = "description")
    @Mapping(source = "code", target = "languageCode")
    Translation entityToCore(MasterPartTranslationEntityView entity);

    List<Translation> entitiesToCores(List<MasterPartTranslationEntityView> entities);
}
