package co.cadshare.modelMgt.ranges.boundary;

import co.cadshare.modelMgt.ranges.core.Range;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.dao.EmptyResultDataAccessException;

import co.cadshare.exceptions.NotFoundException;
import co.cadshare.shared.core.user.User;
import co.cadshare.aspects.logging.Log;

@Service
public class DeleteRangeService {

    private final RangeCommandPort commandPort;
    private final RangeQueryPort queryPort;

    @Autowired
    public DeleteRangeService(RangeCommandPort commandPort,
                                    RangeQueryPort queryPort) {
        this.commandPort = commandPort;
        this.queryPort = queryPort;
    }

    @Log
    public void delete(User user, Integer rangeId) throws Exception {
        try {
            Range range = this.queryPort.get(rangeId);
            this.commandPort.delete(user, range);
        } catch (EmptyResultDataAccessException ex) {
            throw new NotFoundException("Range does not exist");
        }
    }
}
