package co.cadshare.domainmodel.visibility;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;

@XmlRootElement(name = "GetPartAvailQtyListResult", namespace = "http://visibility.com/")
@XmlAccessorType(XmlAccessType.FIELD)
public class GetPartAvailQtyListResult {

    @XmlElement(name = "ENTITY_CODE", namespace = "http://visibility.com/")
    private String ENTITY_CODE;

    @XmlElement(name = "PART_X", namespace = "http://visibility.com/")
    private String PART_X;

    @XmlElement(name = "WAREHOUSE_X", namespace = "http://visibility.com/")
    private String WAREHOUSE_X;
    @XmlElement(name = "PART_X", namespace = "http://visibility.com/")
    private String ErrorMsg;
    @XmlElement(name = "ErrorC", namespace = "http://visibility.com/")
    private String ErrorC;
    @XmlElement(name = "List", namespace = "http://visibility.com/")
    private PartAvailListDetailList partAvailListDetailList;

    public String getENTITY_CODE() {
        return ENTITY_CODE;
    }

    public void setENTITY_CODE(String ENTITY_CODE) {
        this.ENTITY_CODE = ENTITY_CODE;
    }

    public String getErrorMsg() {
        return ErrorMsg;
    }

    public void setErrorMsg(String errorMsg) {
        ErrorMsg = errorMsg;
    }

    public String getErrorC() {
        return ErrorC;
    }

    public void setErrorC(String errorC) {
        ErrorC = errorC;
    }

    public String getPART_X() {
        return PART_X;
    }

    public void setPART_X(String PART_X) {
        this.PART_X = PART_X;
    }

    public String getWAREHOUSE_X() {
        return WAREHOUSE_X;
    }

    public void setWAREHOUSE_X(String WAREHOUSE_X) {
        this.WAREHOUSE_X = WAREHOUSE_X;
    }

    public PartAvailListDetailList getPartAvailListDetailList() {
        return partAvailListDetailList;
    }

    public void setPartAvailListDetailList(PartAvailListDetailList partAvailListDetailList) {
        this.partAvailListDetailList = partAvailListDetailList;
    }
}
