/*
 * Copyright 2016 Bell.
 */
package co.cadshare.controller;

import co.cadshare.orders.boundary.OrderItemService;
import co.cadshare.shared.core.user.User;
import co.cadshare.services.PermissionsService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

@Controller
@PreAuthorize("hasRole('Order')")
@RequestMapping("/orderItem")
@Slf4j
public class OrderItemController {

  private final OrderItemService orderItemService;
  private final PermissionsService permissionsService;

  @Autowired
  public OrderItemController(OrderItemService orderItemService, PermissionsService permissionsService) {
    this.orderItemService = orderItemService;
    this.permissionsService = permissionsService;
  }

  @PreAuthorize("hasRole('ROLE_MANUFACTURER')")
  @DeleteMapping(value = "/{orderItemId}")
  public HttpEntity<Boolean> deleteOrderItem(@RequestHeader(value = "Site-Url") String siteUrl, @AuthenticationPrincipal User currentUser, @PathVariable int orderItemId) throws Exception {

    log.info("ACCESS: User [{}], deleteOrderItem, orderItemId [{}]", currentUser.accessDetails(), orderItemId);

    try {
      userHasPermissionsToDeleteOrderItem(currentUser.getUserId(), orderItemId);
    } catch (Exception ex) {
      return new ResponseEntity<>(null, HttpStatus.FORBIDDEN);
    }
    boolean deleteSuccess = orderItemService.deleteOrderItem(orderItemId, currentUser.getUserId(), siteUrl);

    log.info("OrderItemId [{}] deleted ", orderItemId);
    return new ResponseEntity<Boolean>(deleteSuccess, HttpStatus.OK);
  }

  private boolean userHasPermissionsToDeleteOrderItem(int userId, int orderItemId) throws Exception {
    boolean permissions = permissionsService.userHasPermissionsToDeleteOrderItem(userId, orderItemId);
    return permissions;
  }
}
