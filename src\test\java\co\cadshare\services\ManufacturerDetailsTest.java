package co.cadshare.services;

import co.cadshare.shared.adapters.database.manufacturer.ManufacturerDao;
import co.cadshare.shared.core.manufacturer.AdditionalEmail;
import co.cadshare.shared.core.manufacturer.Manufacturer;
import co.cadshare.shared.core.manufacturer.ManufacturerDetails;
import co.cadshare.shared.core.manufacturer.ManufacturerSettings;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.sql.Timestamp;
import java.util.Arrays;
import java.util.List;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

public class ManufacturerDetailsTest {

    private ManufacturerService manufacturerService;

    @Mock
    private ManufacturerDao manufacturerDao;

    private Manufacturer testManufacturer;
    private ManufacturerSettings testSettings;
    private List<AdditionalEmail> testAdditionalEmails;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        manufacturerService = new ManufacturerService();
        manufacturerService.manufacturerDao = manufacturerDao;

        // Set up test data
        testSettings = new ManufacturerSettings();
        testSettings.setViewerColour("#FF0000");
        testSettings.setEdgingEnabledDefault(true);
        testSettings.setContactUsPageEnabled(true);

        testManufacturer = new Manufacturer();
        testManufacturer.setManufacturerId(1);
        testManufacturer.setEmailSignature("Test Signature");
        testManufacturer.setLogoUrl("http://example.com/logo.png");
        testManufacturer.setPhone("************");
        testManufacturer.setModifiedDate(new Timestamp(System.currentTimeMillis()));
        testManufacturer.setModifiedByUserId(100);
        testManufacturer.setManufacturerSettings(testSettings);

        AdditionalEmail email1 = new AdditionalEmail();
        email1.setId(1);
        email1.setLabel("Europe");
        email1.setEmail("<EMAIL>");
        email1.setManufacturerId(1);

        AdditionalEmail email2 = new AdditionalEmail();
        email2.setId(2);
        email2.setLabel("North America");
        email2.setEmail("<EMAIL>");
        email2.setManufacturerId(1);

        testAdditionalEmails = Arrays.asList(email1, email2);
    }

    @Test
    public void testGetManufacturerDetails() {
        // Arrange
        when(manufacturerDao.getManufacturer(1)).thenReturn(testManufacturer);
        when(manufacturerDao.getAdditionalEmailsForManufacturer(1)).thenReturn(testAdditionalEmails);

        // Act
        ManufacturerDetails result = manufacturerService.getManufacturerDetails(1);

        // Assert
        assertNotNull(result);
        assertEquals(1, result.getManufacturerId());
        assertEquals("Test Signature", result.getEmailSignature());
        assertEquals("http://example.com/logo.png", result.getLogoUrl());
        assertEquals("<EMAIL>", result.getSupportEmail()); // Default value
        assertEquals("************", result.getPhone());
        assertEquals("#FF0000", result.getViewerColour());
        assertTrue(result.isEdgingEnabledDefault());
        assertTrue(result.isContactUsPageEnabled());

        assertNotNull(result.getAdditionalEmails());
        assertEquals(2, result.getAdditionalEmails().size());
        assertEquals("Europe", result.getAdditionalEmails().get(0).getLabel());
        assertEquals("<EMAIL>", result.getAdditionalEmails().get(0).getEmail());
        assertEquals("North America", result.getAdditionalEmails().get(1).getLabel());
        assertEquals("<EMAIL>", result.getAdditionalEmails().get(1).getEmail());

        verify(manufacturerDao).getManufacturer(1);
        verify(manufacturerDao).getAdditionalEmailsForManufacturer(1);
    }

    @Test
    public void testUpdateManufacturerDetailsWithAdditionalEmails() {
        // Arrange
        ManufacturerDetails details = new ManufacturerDetails();
        details.setManufacturerId(1);
        details.setAdditionalEmails(testAdditionalEmails);

        when(manufacturerDao.getManufacturer(1)).thenReturn(testManufacturer);
        when(manufacturerDao.updateManufacturer(any(ManufacturerDetails.class))).thenReturn(true);
        when(manufacturerDao.getManufacturerSettingsById(1)).thenReturn(testSettings);

        // Act
        boolean result = manufacturerService.updateManufacturerDetails(1, details, 100);

        // Assert
        assertTrue(result);
        verify(manufacturerDao).saveAdditionalEmailsForManufacturer(1, testAdditionalEmails);
    }
}
