package co.cadshare.modelMgt.publications.boundary;

import co.cadshare.modelMgt.publications.core.Manual;
import co.cadshare.modelMgt.publications.core.ManualStatus;
import co.cadshare.masterParts.adapters.database.MasterPartExtensionsDao;
import co.cadshare.modelMgt.ranges.adapters.database.RangeDao;
import co.cadshare.response.CustomerManual;
import co.cadshare.shared.boundary.ManufacturerSubEntityQueryPort;
import com.amazonaws.util.CollectionUtils;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.sql.Timestamp;
import java.util.*;

@Slf4j
@Service
public class ManualService {

  private ManualQueryPort manualQuery;
  private ManualCommandPort manualCommand;
  private RangeDao rangeDao;
  private MasterPartExtensionsDao masterPartExtensionsDao;
  private ManufacturerSubEntityQueryPort manufacturerSubEntityQueryPort;

  @Autowired
  public ManualService(ManualQueryPort manualQuery,
                       ManualCommandPort manualCommand,
                       RangeDao rangeDao,
                       MasterPartExtensionsDao masterPartExtensionsDao,
                       ManufacturerSubEntityQueryPort manufacturerSubEntityQueryPort) {
    this.manualQuery = manualQuery;
    this.manualCommand = manualCommand;
    this.rangeDao = rangeDao;
    this.masterPartExtensionsDao = masterPartExtensionsDao;
    this.manufacturerSubEntityQueryPort = manufacturerSubEntityQueryPort;
  }

  public int createManual(Manual manual) {

    return manualCommand.createManual(manual);
  }

  public int updateManual(Manual manual) {
    Manual existingManual = manualQuery.getManual(manual.getManualId());

    Manual newManual = manual;
    newManual.setCreatedByUserId(existingManual.getCreatedByUserId());
    newManual.setCreatedDate(existingManual.getCreatedDate());

    // Empty List to assign if null for allowing intersection checks to still run
    List<Integer> emptyList = new ArrayList<>();
    // If lists null assign empty list
    if (manual.getModelId() == null) {
      manual.setModelId(emptyList);
    }
    if (manual.getTechDocId() == null) {
      manual.setTechDocId(emptyList);
    }
    if (manual.getVideoId() == null) {
      manual.setVideoId(emptyList);
    }
    if (manual.getKitId() == null) {
      manual.setKitId(emptyList);
    }

    // Check if models assigned to manual have updated so mapping table can be
    // updated accordingly
    if (!compareIdLists(existingManual.getModelId(), manual.getModelId())) {

      List<Integer> modelsInCommon = intersection(existingManual.getModelId(), manual.getModelId());

      List<Integer> modelMapToArchive = new ArrayList<>();
      List<Integer> modelMapToCreate = new ArrayList<>();

      // Determine new models
      for (int modelId : manual.getModelId()) {
        if (!modelsInCommon.contains(modelId)) {
          modelMapToCreate.add(modelId);
        }
      }

      for (int modelId : existingManual.getModelId()) {
        if (!modelsInCommon.contains(modelId)) {
          modelMapToArchive.add(modelId);
        }
      }

      if (!CollectionUtils.isNullOrEmpty(modelMapToArchive)) {
        manualCommand.deleteManualModelMap(manual.getManualId(), modelMapToArchive);
      }

      modelMapToCreate.forEach((modelId) -> {
        manualCommand.createManualModelMap(manual.getManualId(), modelId);
      });
    }

    // Check if technical documents assigned to manual have updated so mapping table can be
    // updated accordingly
    if (!compareIdLists(existingManual.getTechDocId(), manual.getTechDocId())) {

      List<Integer> techDocsInCommon = intersection(existingManual.getTechDocId(), manual.getTechDocId());

      List<Integer> techDocMappingsToDelete = new ArrayList<>();
      List<Integer> techDocMappingsToCreate = new ArrayList<>();

      // Determine new technical documents
      for (int techDocId : manual.getTechDocId()) {
        if (!techDocsInCommon.contains(techDocId)) {
          techDocMappingsToCreate.add(techDocId);
        }
      }

      for (int techDocId : existingManual.getTechDocId()) {
        if (!techDocsInCommon.contains(techDocId)) {
          techDocMappingsToDelete.add(techDocId);
        }
      }

      if (!CollectionUtils.isNullOrEmpty(techDocMappingsToDelete)) {
        manualCommand.deleteManualTechDocMap(manual.getManualId(), techDocMappingsToDelete);
      }

      techDocMappingsToCreate.forEach((modelId) -> {
        manualCommand.createManualTechDocMap(manual.getManualId(), modelId);
      });
    }

    // Check if videos assigned to manual have updated so mapping table can be
    // updated accordingly
    if (!compareIdLists(existingManual.getVideoId(), manual.getVideoId())) {

      List<Integer> videosInCommon = intersection(existingManual.getVideoId(), manual.getVideoId());

      List<Integer> videoMappingsToDelete = new ArrayList<>();
      List<Integer> videoMappingsToCreate = new ArrayList<>();

      // Determine new Videos
      for (int techDocId : manual.getVideoId()) {
        if (!videosInCommon.contains(techDocId)) {
          videoMappingsToCreate.add(techDocId);
        }
      }

      for (int videoId : existingManual.getVideoId()) {
        if (!videosInCommon.contains(videoId)) {
          videoMappingsToDelete.add(videoId);
        }
      }

      if (!CollectionUtils.isNullOrEmpty(videoMappingsToDelete)) {
        manualCommand.deleteManualVideoMap(manual.getManualId(), videoMappingsToDelete);
      }

      videoMappingsToCreate.forEach((modelId) -> {
        manualCommand.createManualVideoMap(manual.getManualId(), modelId);
      });
    }

    // Check if Kits assigned to manual have updated so mapping table can be
    // updated accordingly
    if (!compareIdLists(existingManual.getKitId(), manual.getKitId())) {

      List<Integer> kitsInCommon = intersection(existingManual.getKitId(), manual.getKitId());

      List<Integer> kitMappingsToDelete = new ArrayList<>();
      List<Integer> kitMappingsToCreate = new ArrayList<>();

      // Determine new Videos
      for (int kitId : manual.getKitId()) {
        if (!kitsInCommon.contains(kitId)) {
          kitMappingsToCreate.add(kitId);
        }
      }

      for (int kitId : existingManual.getKitId()) {
        if (!kitsInCommon.contains(kitId)) {
          kitMappingsToDelete.add(kitId);
        }
      }

      if (!CollectionUtils.isNullOrEmpty(kitMappingsToDelete)) {
        manualCommand.deleteManualKitMap(manual.getManualId(), kitMappingsToDelete);
      }

      kitMappingsToCreate.forEach((id) -> {
        manualCommand.createManualKitMap(manual.getManualId(), id);
      });
    }
    return manualCommand.updateManual(newManual);
  }

  private <T> List<T> intersection(List<T> list1, List<T> list2) {
    List<T> list = new ArrayList<T>();

    for (T t : list1) {
      if (list2.contains(t)) {
        list.add(t);
      }
    }

    return list;
  }

  public static <T> boolean compareIdLists(List<T> l1, List<T> l2) {
    final Set<T> s1 = new HashSet<>(l1);
    final Set<T> s2 = new HashSet<>(l2);

    return s1.equals(s2);
  }

  public List<Manual> getManualsForModel(int modelId) {
    return manualQuery.getManualsForModel(modelId);
  }

  public Manual getManual(int manualId) {
    return manualQuery.getManual(manualId);
  }

  public void updateManualStatus(int manualId, ManualStatus manualStatus) {
    manualCommand.updateManualStatus(manualId, manualStatus);
  }

  public Boolean deleteManual(int manualId) {
    return manualCommand.deleteManual(manualId);
  }

  public List<CustomerManual> getManualsForManufacturerSubEntity(int manufacturerSubEntityId) {
    List<CustomerManual> manualList = manualQuery.getManualsForManufacturerSubEntityId(manufacturerSubEntityId);
    return manualList;
  }

  public List<Integer> getAssignedManualIdsForManufacturerSubEntity(int manufacturerSubEntityId) {
    List<Integer> manualList = manualQuery.getAssignedPublicationsForPurchaser(manufacturerSubEntityId);
    return manualList;
  }

  public void updateAssignedManuals(int manufacturerSubEntityId, List<Integer> assignedManualIds) {
    manualCommand.assignPurchaserToPublications(manufacturerSubEntityId, assignedManualIds);
  }

  public Boolean deleteModelFromManualModelMap(int modelId) {
    return manualCommand.deleteModelFromManualModelMap(modelId);
  }

  public Boolean deleteTechDocFromManualTechDocMap(int techDocId) {
    return manualCommand.deleteTechDocFromManualTechDocMap(techDocId);
  }

  public Timestamp getManualLatestUpdateTimestamp(int manualId) {
    return manualQuery.getManual(manualId).getModifiedDate();
  }

  public void assignManualToManufacturerSubEntities(int manualId, ArrayList<Integer> subEntitiesToBeAssigned) {

    ArrayList<Integer> unassignList = new ArrayList<>();
    ArrayList<Integer> assignList = new ArrayList<>();

    //get existing manufacturerSubEntities assigned to Manual
    val subEntitiesAlreadyAssignedToManual =
            manufacturerSubEntityQueryPort.getManufacturerSubEntitiesForManual(manualId);

    Manual.determinePurchasersForManual(subEntitiesToBeAssigned,
            subEntitiesAlreadyAssignedToManual,
            unassignList,
            assignList);

    //unassign
    if(unassignList.size() > 0)
      manualCommand.clearMappingsForManual(manualId, unassignList);
    //assign
    if(assignList.size() > 0)
      manualCommand.assignManualToManufacturerSubEntities(manualId, assignList);

  }


  public Boolean deleteVideoFromManualVideoMap(int videoId) {
    return manualCommand.deleteVideoFromManualVideoMap(videoId);
  }

  public Boolean deleteKitFromManualKitMap(int kitId) {
    return manualCommand.deleteKitFromManualKitMap(kitId);
  }

  public List<Manual> getManualsForRange(int rangeId) {
    return manualQuery.getManualsForRange(rangeId);
  }
}
