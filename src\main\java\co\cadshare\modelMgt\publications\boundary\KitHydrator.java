package co.cadshare.modelMgt.publications.boundary;

import co.cadshare.modelMgt.publications.core.Kit;
import co.cadshare.modelMgt.shared.core.Publication;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service
public class KitHydrator implements PublicationAttributeHydrator {

	private final KitQueryPort kitQueryPort;

	@Autowired
	public KitHydrator(KitQueryPort kitQueryPort) {
		this.kitQueryPort = kitQueryPort;
	}

	@Override
	public void hydrate(PublicationCommand command, Publication publication) {
		List<Kit> kits = new ArrayList<>();
		if(command.hasKits())
			command.getKits().forEach(v -> kits.add(kitQueryPort.get(v)));
		publication.setKits(kits);
	}
}
