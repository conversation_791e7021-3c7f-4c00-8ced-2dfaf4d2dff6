package co.cadshare.models.core.processor.fileproperties;

import co.cadshare.models.core.MetadataObjectExtended;

import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Component;

import java.util.LinkedHashMap;
import java.util.List;


@Component
public class NexatPartNumberPropertyProcessor extends PartNumberPropertyProcessor implements FilePropertiesProcessor, InitializingBean {


    @Override
    public void setProperties(LinkedHashMap properties, MetadataObjectExtended object) {

        // & set sparePart to true
        String partNumber = getPropertyValue(properties);
        if(partNumber != null && !partNumber.startsWith("01") &&
                partNumber.chars().filter(ch -> ch == '-').count() > 1)
            partNumber = partNumber.substring(0, partNumber.lastIndexOf("-"));
        object.setPartNumber(partNumber);
    }
    
    //}
    @Override
    public List<String> getSynonyms() {
        return partNumberSynonyms;
    }

    @Override
    public void setSynonyms(List<String> synonyms) {
        partNumberSynonyms = synonyms;
    }

    @Override
    public void afterPropertiesSet() throws Exception {
     //not checking properties as synonyms won't be set at this point.
    }
}

