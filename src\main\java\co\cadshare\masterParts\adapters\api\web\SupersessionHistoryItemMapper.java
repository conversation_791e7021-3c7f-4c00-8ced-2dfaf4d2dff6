package co.cadshare.masterParts.adapters.api.web;

import co.cadshare.masterParts.core.SupersessionHistoryItem;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper(uses = {TranslationMapper.class})
public interface SupersessionHistoryItemMapper {

    SupersessionHistoryItemMapper Instance = Mappers.getMapper(SupersessionHistoryItemMapper.class);

    SupersessionHistoryItemDto coreToDto(SupersessionHistoryItem core);

    List<SupersessionHistoryItemDto> coresToDtos(List<SupersessionHistoryItem> cores);
}
