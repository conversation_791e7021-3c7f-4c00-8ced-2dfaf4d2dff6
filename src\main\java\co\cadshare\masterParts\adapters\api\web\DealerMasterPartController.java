/*
 * Copyright 2016 Bell.
 */
package co.cadshare.masterParts.adapters.api.web;

import co.cadshare.aspects.access.languages.CanUseLanguage;
import co.cadshare.masterParts.core.MasterPartDetails;
import co.cadshare.shared.core.Language;
import co.cadshare.modelMgt.models.core.Model;
import co.cadshare.domainmodel.part.Part;
import co.cadshare.masterParts.core.Translation;
import co.cadshare.masterParts.core.extensions.kit.Kit;
import co.cadshare.masterParts.core.extensions.nonModelled.NonModelled;
import co.cadshare.masterParts.core.extensions.optionsSet.OptionsSet;
import co.cadshare.masterParts.core.extensions.partModelLink.PartModelLink;
import co.cadshare.masterParts.core.extensions.partModelLink.LegacySupersededModelLink;
import co.cadshare.modelMgt.publications.core.TechDoc;
import co.cadshare.shared.core.user.User;
import co.cadshare.masterParts.boundary.DealerMasterPartService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Slf4j
@Controller
@PreAuthorize("hasRole('ROLE_MANUFACTURER_SUB_ENTITY_DEALER_PLUS')")
@RequestMapping("/dealerplus/masterPart")
public class DealerMasterPartController {

	private final DealerMasterPartService dealerMasterPartService;

	@Autowired
	public DealerMasterPartController(DealerMasterPartService dealerMasterPartService) {
		this.dealerMasterPartService = dealerMasterPartService;
	}

	//Master Part Details Services
	@GetMapping(value = "/{masterPartId}")
	@CanUseLanguage
	public HttpEntity<MasterPartDetails> getMasterPartDetails(@AuthenticationPrincipal User currentUser,
	                                                          @PathVariable int masterPartId,
	                                                          @RequestParam(value = "language", required = false) Language language) throws Exception {

		log.info("ACCESS: User [{}], dealerplus - getMasterPartDetails, masterpartid [{}]", currentUser.accessDetails(), masterPartId);
		Integer languageId = language != null ? currentUser.findLanguage(language).getLanguageId() : null;
		MasterPartDetails partViewerDetails = dealerMasterPartService.getPartViewerDetails(masterPartId,
				languageId,
				currentUser.obtainDefaultLanguage(),
				currentUser);
		return new ResponseEntity<>(partViewerDetails, HttpStatus.OK);
	}

  //Inventory Management Services
	@GetMapping(value = "/{masterPartId}/price")
	public HttpEntity<Float> getMasterPartPrice(@AuthenticationPrincipal User currentUser,
	                                          @PathVariable int masterPartId) {

		log.info("ACCESS: User [{}], dealerplus - getMasterPartPrice, masterPartId [{}]", currentUser.accessDetails(), masterPartId);
		Float price = dealerMasterPartService.getMasterPartPrice(masterPartId, currentUser.getManufacturerSubEntityId());
		return new ResponseEntity<>(price, HttpStatus.OK);
	}

	@PutMapping(value = "/{masterPartId}/price")
	public HttpEntity<Boolean> updateMasterPartPrice(@AuthenticationPrincipal User currentUser,
	                                               @PathVariable int masterPartId,
	                                               @RequestBody Part partInventory) {

		log.info("ACCESS: User [{}], dealerplus - getMasterPartInventory, masterpartid [{}]", currentUser.accessDetails(), masterPartId);
		partInventory.setMasterPartId(masterPartId);
		Boolean updated = dealerMasterPartService.updateDealerMasterPartPrice(currentUser.getManufacturerSubEntityId(), partInventory);
		return new ResponseEntity<>(updated, HttpStatus.OK);
	}

	//Translations Services
	@GetMapping(value = "/{masterPartId}/translation")
	public HttpEntity<List<Translation>> getMasterPartTranslations(@AuthenticationPrincipal User currentUser,
	                                                               @PathVariable int masterPartId) {

		log.info("ACCESS: User [{}], dealerplus - getMasterPartDetails, masterpartid [{}]", currentUser.accessDetails(), masterPartId);
		List<Translation> translations = dealerMasterPartService.getPartTranslations(masterPartId);
		return new ResponseEntity<>(translations, HttpStatus.OK);
	}

	//Option Set Services
	@RequestMapping(value = "/{masterPartId}/optionSet", method = RequestMethod.GET)
	@CanUseLanguage
	public HttpEntity<List<OptionsSet>> getOptionsSetsForPart(@AuthenticationPrincipal User currentUser,
	                                                          @PathVariable int masterPartId,
	                                                          @RequestParam(value = "language", required = false) Language language) throws Exception {

		log.info("ACCESS: User [{}], dealerplus - getOptionsSetsForPart, partId [{}]", currentUser.accessDetails(), masterPartId);
		Integer languageId = language != null ? currentUser.findLanguage(language).getLanguageId() : null;
		List<OptionsSet> optionsSets = dealerMasterPartService.getOptionsSetsForPart(masterPartId, languageId, currentUser.obtainDefaultLanguage());
		return new ResponseEntity<>(optionsSets, HttpStatus.OK);
	}

	@RequestMapping(value = "/optionSet/{optionSetId}", method = RequestMethod.GET)
	@CanUseLanguage
	public HttpEntity<OptionsSet> getOptionsSetForPartById(@AuthenticationPrincipal User currentUser,
	                                                       @PathVariable int optionSetId,
	                                                       @RequestParam(value = "language", required = false) Language language) throws Exception {

		log.info("ACCESS: User [{}], dealerplus - getOptionsSetForPartById, partId [{}]", currentUser.accessDetails(), optionSetId);
		Integer languageId = language != null ? currentUser.findLanguage(language).getLanguageId() : null;
		OptionsSet optionsSets = dealerMasterPartService.getOptionsSetForPartById(optionSetId, languageId, currentUser.obtainDefaultLanguage());
		return new ResponseEntity<>(optionsSets, HttpStatus.OK);
	}

	//Linked Parts Services
	@RequestMapping(value = "/{masterPartId}/linkedPart", method = RequestMethod.GET)
	public HttpEntity<PartModelLink> getLinkedPartsForPart(@AuthenticationPrincipal User currentUser,
	                                                       @PathVariable int masterPartId) {

		log.info("ACCESS: User [{}], dealerplus - getlinkedPartsForPart, partId [{}]", currentUser.accessDetails(), masterPartId);
		PartModelLink linkedViewable = dealerMasterPartService.getLinkedPartsForPart(masterPartId);
		return new ResponseEntity<>(linkedViewable, HttpStatus.OK);
	}

	//Kits Services
	@RequestMapping(value = "/{masterPartId}/kit", method = RequestMethod.GET)
	public HttpEntity<List<Kit>> getKitsForPart(@AuthenticationPrincipal User currentUser,
	                                            @PathVariable int masterPartId) {

		log.info("ACCESS: User [{}], dealerplus - getKitsForPart, partId [{}]", currentUser.accessDetails(), masterPartId);
		List<Kit> kits = dealerMasterPartService.getKitsForPart(masterPartId);
		return new ResponseEntity<>(kits, HttpStatus.OK);
	}

	@RequestMapping(value = "/kit/{kitId}", method = RequestMethod.GET)
	@CanUseLanguage
	public HttpEntity<Kit> getKitForPartById(@AuthenticationPrincipal User currentUser,
	                                         @PathVariable int kitId,
	                                         @RequestParam(value = "language", required = false) Language language) throws Exception {

		log.info("ACCESS: User [{}], dealerplus - getKitForPartById, kitId [{}]", currentUser.accessDetails(), kitId);
		Integer languageId = language != null ? currentUser.findLanguage(language).getLanguageId() : null;
		Kit kit = dealerMasterPartService.getKitForPartById(kitId, languageId, currentUser.obtainDefaultLanguage());
		return new ResponseEntity<>(kit, HttpStatus.OK);
	}

    //Nonmodelled Parts Services
	@RequestMapping(value = "/{masterPartId}/nonModelled", method = RequestMethod.GET)
	@CanUseLanguage
	public HttpEntity<NonModelled> getNonModelledForPartById(@AuthenticationPrincipal User currentUser,
	                                                         @PathVariable int masterPartId,
	                                                         @RequestParam(value = "language", required = false) Language language) throws Exception {

		log.info("ACCESS: User [{}], dealerplus - getNonModelledForPartById, partId [{}]", currentUser.accessDetails(), masterPartId);
		Integer languageId = language != null ? currentUser.findLanguage(language).getLanguageId() : null;
	    NonModelled nonModelled = dealerMasterPartService.getNonModelledForPartById(masterPartId, languageId, currentUser.obtainDefaultLanguage());
	    return new ResponseEntity<>(nonModelled, HttpStatus.OK);
	}

	//Legacy Superseded Parts Services
	@RequestMapping(value = "/{masterPartId}/superseded", method = RequestMethod.GET)
	public HttpEntity<LegacySupersededModelLink> getLegacySupersededPartsForPart(@AuthenticationPrincipal User currentUser,
	                                                                             @PathVariable int masterPartId) {

		log.info("ACCESS: User [{}], dealerplus - getLegacySupersededPartsForPart, partId [{}]", currentUser.accessDetails(), masterPartId);
		LegacySupersededModelLink superseded = dealerMasterPartService.getLegacySupersededPartsForPart(masterPartId);
		return new ResponseEntity<>(superseded, HttpStatus.OK);
	}

	//Models containing
	@GetMapping(value = "/{masterPartId}/models")
	public HttpEntity<List<Model>> getModelsContainingMasterPart(@AuthenticationPrincipal User currentUser,
	                                                             @PathVariable int masterPartId) {

		log.info("ACCESS: User [{}], dealerplus - getModelsContainingMasterPart, masterpartid [{}]", currentUser.accessDetails(), masterPartId);
		List<Model> models = dealerMasterPartService.getModelsForDealerMasterPart(currentUser.getManufacturerSubEntityId(), masterPartId);
		return new ResponseEntity<>(models, HttpStatus.OK);
	}

	//Link Tech docs
	@RequestMapping(value = "/{masterPartId}/techDoc", method = RequestMethod.GET)
	public HttpEntity<List<TechDoc>> getLinkedTechDocsForPart(@AuthenticationPrincipal User currentUser,
	                                                          @PathVariable int masterPartId) throws Exception {

		log.info("ACCESS: User [{}], dealerplus - getLinkedTechDocsForPart, partId [{}]", currentUser.accessDetails(), masterPartId);
		List<TechDoc> techDocs = dealerMasterPartService.getLinkedTechDocsForPart(masterPartId);
		return new ResponseEntity<>(techDocs, HttpStatus.OK);
	}
}
