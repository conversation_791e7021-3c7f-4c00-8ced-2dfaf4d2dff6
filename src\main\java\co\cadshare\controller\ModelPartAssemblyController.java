/*
 * Copyright 2016 Bell.
 */
package co.cadshare.controller;

import co.cadshare.aspects.access.languages.CanUseLanguage;
import co.cadshare.shared.core.Language;
import java.util.List;

import co.cadshare.utils.ObjectUtilsExtension;
import lombok.experimental.ExtensionMethod;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestMapping;

import co.cadshare.domainmodel.assembly.Assembly;
import co.cadshare.shared.core.user.User;
import co.cadshare.services.ModelPartAssemblyService;
import org.springframework.web.bind.annotation.RequestParam;

@ExtensionMethod(ObjectUtilsExtension.class)
@Controller
@RequestMapping("/modelAssembly")
public class ModelPartAssemblyController {

	  private static final Logger logger = LoggerFactory.getLogger(ModelPartAssemblyController.class);
	  private final ModelPartAssemblyService modelPartAssemblyService;

	  @Autowired
	  ModelPartAssemblyController(ModelPartAssemblyService modelPartAssemblyService) {
	    this.modelPartAssemblyService = modelPartAssemblyService;
	  }

	  @PreAuthorize("(hasRole('ROLE_MANUFACTURER') and hasRole('Products')) || (hasRole('PublishedProducts'))")
	  @GetMapping(value = "/model/{modelId}", produces = "application/json")
	  @CanUseLanguage
	  public HttpEntity<List<Assembly>> getAssembliesPartIdByModelId(@AuthenticationPrincipal User currentUser,
	                                                                 @PathVariable int modelId,
	                                                                 @RequestParam(value = "language", required = false) Language language) {

		logger.info("ACCESS: User [{}], getAssembliesByModelId, modelId [{}]", currentUser.accessDetails(), modelId);
		Integer languageId = language != null ? currentUser.findLanguage(language).getLanguageId() : null;
		List<Assembly> partIds = modelPartAssemblyService.getAssembliesByModelId(modelId, languageId);
		return new ResponseEntity<>(partIds, HttpStatus.OK);
	  }

	  @PreAuthorize("hasRole('ROLE_MANUFACTURER') and hasRole('Products')")
	  @PutMapping(value = "/model/{modelId}/part/{partId}", produces = "application/json")
	  public HttpEntity<Integer> createAssembly(@AuthenticationPrincipal User currentUser,
	                                            @PathVariable int modelId,
	                                            @PathVariable int partId) {

	    logger.info("ACCESS: User [{}], getModel, modelId [{}]", currentUser.accessDetails(), modelId);
	    int assemblyId = modelPartAssemblyService.createAssembly(modelId, partId);
	    return new ResponseEntity<>(assemblyId, HttpStatus.OK);
	  }

	  @PreAuthorize("hasRole('ROLE_MANUFACTURER') and hasRole('Products')")
	  @DeleteMapping(value = "/{assemblyId}", produces = "application/json")
	  public HttpEntity<Boolean> deleteAssembly(@AuthenticationPrincipal User currentUser,
	                                            @PathVariable int assemblyId) {

	    logger.info("ACCESS: User [{}], deleteAssembly, assemblyId [{}]", currentUser.accessDetails(), assemblyId);
	    boolean success = modelPartAssemblyService.deleteAssembly(assemblyId);
	    return new ResponseEntity<>(success, HttpStatus.OK);
	  }

}
