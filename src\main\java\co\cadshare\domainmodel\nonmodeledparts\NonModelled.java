/*
 * Copyright 2016 Bell.
 */
package co.cadshare.domainmodel.nonmodeledparts;

import co.cadshare.shared.core.PartElementWithDescription;
import lombok.Builder;
import lombok.Data;

import java.sql.Timestamp;
import java.util.List;

import co.cadshare.domainmodel.part.Part;
import lombok.NoArgsConstructor;


@Data
@NoArgsConstructor
public class NonModelled implements PartElementWithDescription {

    private int id;
    private int modelId;
    private Integer partId;
    private int objectId;
    
    private String partNumber;
    private String partDescription;
    
    private List<Part> parts;
    private boolean masterPartFound;
    private boolean masterPartDescriptionFound;

    private Integer createdByUserId;
    private Timestamp createdDate;

    private Integer modifiedByUserId;
    private Timestamp modifiedDate;

    public static NonModelled createFrom(NonModelled nonModelled) {
        return NonModelled.builder()
            .objectId(nonModelled.getObjectId())
            .partNumber(nonModelled.getPartNumber())
            .partDescription(nonModelled.getPartDescription())
            .masterPartFound(nonModelled.isMasterPartFound())
            .masterPartDescriptionFound(nonModelled.isMasterPartDescriptionFound())
            .build();
    }

    @Builder
    public NonModelled(int id, int modelId, int partId, int objectId, String partNumber, String partDescription,
        List<Part> parts, boolean masterPartFound, boolean masterPartDescriptionFound, Integer createdByUserId, Timestamp createdDate, Integer modifiedByUserId, Timestamp modifiedDate) {
        this.id = id;
        this.modelId = modelId;
        this.partId = partId;
        this.objectId = objectId;
        this.partNumber = partNumber;
        this.partDescription = partDescription;
        this.parts = parts;
        this.masterPartFound = masterPartFound;
        this.masterPartDescriptionFound = masterPartDescriptionFound;
        this.createdByUserId = createdByUserId;
        this.createdDate = createdDate;
        this.modifiedByUserId = modifiedByUserId;
        this.modifiedDate = modifiedDate;
    }
}
