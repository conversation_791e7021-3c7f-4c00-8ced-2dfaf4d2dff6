package co.cadshare.shared.adapters.database;

import co.cadshare.modelMgt.publications.core.CoverImage;
import co.cadshare.modelMgt.shared.core.Publication;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface ImageEntityMapper {
    ImageEntityMapper Instance = Mappers.getMapper(ImageEntityMapper.class);

    CoverImage entityToCore(ImageEntity entity);

    ImageEntity coreToEntity(CoverImage core);
    List<CoverImage> entitiesToCores(List<ImageEntity> entities);
    List<ImageEntity> coresToEntities(List<Publication> cores);
}
