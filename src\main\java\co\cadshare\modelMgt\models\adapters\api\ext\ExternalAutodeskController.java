package co.cadshare.modelMgt.models.adapters.api.ext;

import co.cadshare.config.ApplicationProperties;
import co.cadshare.shared.adapters.api.ext.ExternalApiController;
import co.cadshare.shared.core.user.User;
import co.cadshare.users.boundary.UsersService;
import co.cadshare.modelMgt.models.boundary.AutodeskPort;
import com.autodesk.client.auth.Credentials;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping(value = "/api/manufacturers/{manufacturer-id}/autodesk-token", headers = "X-API-Version=1")
public class ExternalAutodeskController extends ExternalApiController {

    private AutodeskPort autodesk;

    @Autowired
    public ExternalAutodeskController(AutodeskPort autodesk,
                                      UsersService userService,
                                      ApplicationProperties properties) {

        super(userService, properties);
        this.autodesk = autodesk;
    }

    @GetMapping()
    @ResponseBody
    @ResponseStatus(HttpStatus.OK)
    @Operation(summary = "Returns the access (bearer) token used to authenticate with Autodesk Platform Services API.")
    public HttpEntity<Credentials> getAutodeskToken(@PathVariable("manufacturer-id") int manufacturerId,
                                                    @AuthenticationPrincipal User currentUser) throws Exception {

        checkManufacturerPermissions(currentUser, manufacturerId);
        Credentials credentials = autodesk.getAutodeskToken();
        return new ResponseEntity<>(credentials, HttpStatus.OK);
    }
}
