package co.cadshare.models.core.model;

public enum AutodeskStatus {
  PENDING, UPLOADED, PROPERTIES_PROCESSED, PROPERTIES_PROCESSED_WITH_WARNINGS, INPROGRESS, FAILED, TIMEOUT;

  public String customerReadable() {
    String readable = "";
    switch (this) {
    case PENDING:
    case UPLOADED:
      readable = "PREPARING";
      break;
    case PROPERTIES_PROCESSED:
      readable = "VIEWABLE";
      break;
    case PROPERTIES_PROCESSED_WITH_WARNINGS:
      readable = "VIEWABLE WITH ERRORS";
      break;
    case INPROGRESS:
      readable = "TRANSLATING";
      break;
    case FAILED:
    case TIMEOUT:
      readable = "TRANSLATION ERROR";
      break;
    default:
      break;
    }

    return readable;
  }
}
