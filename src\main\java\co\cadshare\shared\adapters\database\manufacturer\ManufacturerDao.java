package co.cadshare.shared.adapters.database.manufacturer;

import co.cadshare.domainmodel.priceListIdentifier.PriceListIdentifier;
import co.cadshare.persistence.CurrencyDao;
import co.cadshare.shared.core.manufacturer.Manufacturer;
import co.cadshare.shared.core.manufacturer.ManufacturerDetails;
import co.cadshare.shared.core.user.User;
import co.cadshare.shared.core.manufacturer.ManufacturerSettings;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.BatchPreparedStatementSetter;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.namedparam.BeanPropertySqlParameterSource;
import org.springframework.jdbc.core.namedparam.MapSqlParameterSource;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.jdbc.support.GeneratedKeyHolder;
import org.springframework.jdbc.support.KeyHolder;
import org.springframework.stereotype.Repository;

import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Repository
public class ManufacturerDao {

    @Autowired
    CurrencyDao currencyDao;

    private static final Logger logger = LoggerFactory.getLogger(ManufacturerDao.class);

    private static final String GET_MANUFACTURER_FOR_MANUFACTURER_ID = "SELECT m.* FROM manufacturer m WHERE manufacturerId = :manufacturerId";

    private static final String GET_MANUFACTURER_FOR_SUBDOMAIN = "SELECT m.* FROM manufacturer m WHERE subdomain = :subdomain";

    private static final String GET_ALL_MANUFACTURERS = "SELECT m.* FROM manufacturer m";

    private static final String ASSIGN_USER_ID_TO_MANUFACTURER = "INSERT INTO ManufacturerUsers (manufacturerid, userid) VALUES (?, ?)";

    private static final String UPDATE_MANUFACTURER_DETAILS = "UPDATE manufacturer SET emailSignature = :emailSignature, logoUrl = :logoUrl, supportEmail = :supportEmail, phone = :phone, modifiedDate = :modifiedDate, modifiedByUserId = :modifiedByUserId WHERE manufacturerid = :manufacturerId";

    private static final String GET_MANUFACTURERS_FOR_CUSTOMER_USER = "SELECT m.* FROM manufacturer m "
        + "INNER JOIN manufacturersubentity mse ON mse.manufacturerid = m.manufacturerid "
        + "INNER JOIN manufacturersubentityusers mseu ON mseu.manufacturersubentityid = mse.manufacturersubentityid "
        + "WHERE mseu.userid = :userId";

    private static final String GET_MANUFACTURER_FOR_MODEL = "SELECT man.* FROM manufacturer man "
            + "INNER JOIN range r ON man.manufacturerid = r.manufacturerid "
            + "INNER JOIN machine mac ON r.rangeid = mac.rangeid "
            + "INNER JOIN model mod ON mac.machineid = mod.machineid "
            + "WHERE mod.modelid=:modelId";

    private static final String GET_MANUFACTURER_FOR_ORDER = "SELECT man.* FROM manufacturer man "
            + "INNER JOIN manufacturersubentity mse ON man.manufacturerid = mse.manufacturerid "
            + "INNER JOIN orders o ON o.manufacturersubentityid = mse.manufacturersubentityid "
            + "WHERE o.orderid=:orderId";

    private static final String GET_MANUFACTURER_ID_FOR_CUSTOMER_USER_ID = "SELECT mse.manufacturerid FROM manufacturersubentityusers mseu "
            + "INNER JOIN manufacturersubentity mse ON mseu.manufacturersubentityid = mse.manufacturersubentityid "
            + "WHERE mseu.userid = :userId";

    private static final String GET_MANUFACTURER_SETTINGS_BY_ID = "SELECT * FROM manufacturersettings m "
            + "WHERE m.manufacturerid = :manufacturerId";

    private static final String UPDATE_MANUFACTURER_SETTINGS = "UPDATE manufacturersettings " +
            "SET " +
            "previewstocklevelenabled = :previewStockLevelEnabled, " +
            "previewpricingenabled = :previewPricingEnabled, " +
            "hideisolateenabled = :hideIsolateEnabled, " +
            "partsearchenabled = :partSearchEnabled, " +
            "viewercolour = :viewerColour, " +
            "edgingenableddefault = :edgingEnabledDefault, " +
            "addresscreationenabled = :addressCreationEnabled, " +
            "contactuspageenabled = :contactUsPageEnabled, " +
            "hideunpublishedparts = :hideUnpublishedParts, " +
            "requiredserialnumber = :requiredSerialNumber ," +
            "overridefordirectorders = :overrideForDirectOrders " +
            "WHERE manufacturerid = :manufacturerId";

    @Autowired
    NamedParameterJdbcTemplate namedParamJdbcTemplate;

    @Autowired
    private JdbcTemplate jdbcTemplate;

  private static final String CREATE_MANUFACTURER = "INSERT INTO manufacturer (name, description, createddate, createdbyuserid) VALUES (:name, :description, :createdDate, :createdByUserId)";

    public Integer createManufacturer(Manufacturer manufacturer) throws Exception {

        BeanPropertySqlParameterSource namedParameters = new BeanPropertySqlParameterSource(manufacturer);
        KeyHolder keyHolder = new GeneratedKeyHolder();

        namedParamJdbcTemplate.update(CREATE_MANUFACTURER, namedParameters, keyHolder, new String[]{"manufacturerid"});

        return keyHolder.getKey().intValue();
    }

    public void assignUserIdsToManufacturer(ArrayList<Integer> userIdList, int manufacturerId) {

        int[] results = jdbcTemplate.batchUpdate(ASSIGN_USER_ID_TO_MANUFACTURER, new BatchPreparedStatementSetter() {

            @Override
            public void setValues(PreparedStatement ps, int i) throws SQLException {
                int userId = userIdList.get(i);
                ps.setInt(1, manufacturerId);
                ps.setInt(2, userId);
            }

            @Override
            public int getBatchSize() {
                return userIdList.size();
            }
        });

        logger.info("Result of db call for assignUserIdsToManufacturer: [{}]", results);
    }

    public Manufacturer getManufacturer(int manufacturerId) {
        Map<String, Object> parameters = new HashMap<String, Object>();
        parameters.put("manufacturerId", manufacturerId);

        Manufacturer manufacturer = namedParamJdbcTemplate.queryForObject(GET_MANUFACTURER_FOR_MANUFACTURER_ID,
                parameters, new BeanPropertyRowMapper<>(Manufacturer.class));

        manufacturer.setManufacturerSettings(getManufacturerSettingsById(manufacturer.getManufacturerId()));

        return manufacturer;
    }

    public List<Manufacturer> getAllManufacturers() {
        Map<String, Object> parameters = new HashMap<String, Object>();

        List<Manufacturer> manufacturer = namedParamJdbcTemplate.query(GET_ALL_MANUFACTURERS,
                parameters, new BeanPropertyRowMapper<>(Manufacturer.class));

        return manufacturer;
    }

  public boolean updateManufacturer(ManufacturerDetails manufacturerDetails) {
    BeanPropertySqlParameterSource namedParameters = new BeanPropertySqlParameterSource(manufacturerDetails);
    namedParamJdbcTemplate.update(UPDATE_MANUFACTURER_DETAILS, namedParameters);
    return true;
  }

  public Manufacturer getManufacturerByDomain(String subdomain) {
    Map<String, Object> parameters = new HashMap<String, Object>();
    parameters.put("subdomain", subdomain);

    Manufacturer manufacturer = namedParamJdbcTemplate.queryForObject(GET_MANUFACTURER_FOR_SUBDOMAIN,
            parameters, new BeanPropertyRowMapper<>(Manufacturer.class));

    manufacturer.setManufacturerSettings(getManufacturerSettingsById(manufacturer.getManufacturerId()));
    return manufacturer;
  }

    public Manufacturer getManufacturerForCustomer(User user) {
        MapSqlParameterSource parameters = new MapSqlParameterSource();
        parameters.addValue("userId", user.getUserId());

        Manufacturer manufacturer = namedParamJdbcTemplate.queryForObject(GET_MANUFACTURERS_FOR_CUSTOMER_USER,
            parameters, new BeanPropertyRowMapper<>(Manufacturer.class));

        manufacturer.setManufacturerSettings(getManufacturerSettingsById(manufacturer.getManufacturerId()));
        return manufacturer;
    }

    public Manufacturer getManufacturerForModel(int modelId) {
        MapSqlParameterSource parameters = new MapSqlParameterSource();
        parameters.addValue("modelId", modelId);

        Manufacturer manufacturer = namedParamJdbcTemplate.queryForObject(GET_MANUFACTURER_FOR_MODEL,
                parameters, new BeanPropertyRowMapper<>(Manufacturer.class));

        manufacturer.setManufacturerSettings(getManufacturerSettingsById(manufacturer.getManufacturerId()));
        return manufacturer;
    }

    public Manufacturer getManufacturerForOrder(int orderId) {
        MapSqlParameterSource parameters = new MapSqlParameterSource();
        parameters.addValue("orderId", orderId);

        Manufacturer manufacturer = namedParamJdbcTemplate.queryForObject(GET_MANUFACTURER_FOR_ORDER,
                parameters, new BeanPropertyRowMapper<>(Manufacturer.class));

        manufacturer.setManufacturerSettings(getManufacturerSettingsById(manufacturer.getManufacturerId()));
        return manufacturer;
    }

    public Integer getManufacturerIdForCustomerUserId(int userId) {
        MapSqlParameterSource mapSqlParameterSource = new MapSqlParameterSource();
        mapSqlParameterSource.addValue("userId", userId);

        return namedParamJdbcTemplate.queryForObject(GET_MANUFACTURER_ID_FOR_CUSTOMER_USER_ID, mapSqlParameterSource, Integer.class);
    }

    public ManufacturerSettings getManufacturerSettingsById(int manufacturerId) {
        MapSqlParameterSource parameters = new MapSqlParameterSource();
        parameters.addValue("manufacturerId", manufacturerId);
        ManufacturerSettings settings = namedParamJdbcTemplate.queryForObject(GET_MANUFACTURER_SETTINGS_BY_ID,
                parameters, new BeanPropertyRowMapper<>(ManufacturerSettings.class));
        settings.setDefaultCurrency(currencyDao.getCurrencyById(settings.getDefaultCurrencyId()));
        return settings;
    }

    public boolean updateManufacturerSettings(ManufacturerSettings settings) {
        BeanPropertySqlParameterSource namedParameters = new BeanPropertySqlParameterSource(settings);
        namedParamJdbcTemplate.update(UPDATE_MANUFACTURER_SETTINGS, namedParameters);
        return true;
    }

    public List<PriceListIdentifier> getPriceListIdentifiersForManufacturer(int manufacturerId) {
        final String GET_PRICE_LISTS_FOR_MANUFACTURER_ID = "SELECT * FROM pricelistidentifier WHERE manufacturerId = :manufacturerId";
        Map<String, Object> parameters = new HashMap<String, Object>();
        parameters.put("manufacturerId", manufacturerId);
        return namedParamJdbcTemplate.query(GET_PRICE_LISTS_FOR_MANUFACTURER_ID, parameters, new BeanPropertyRowMapper<>(PriceListIdentifier.class));
    }
    private static final String GET_PRICE_LIST_FOR_IDENTIFIER = "SELECT * FROM pricelistidentifier WHERE identifier = :identifier AND manufacturerId = :manufacturerId LIMIT 1";

    public PriceListIdentifier getPriceListForIdentifier(int manufacturerId, String identifier) {
        Map<String, Object> parameters = new HashMap<String, Object>();
        parameters.put("manufacturerId", manufacturerId);
        parameters.put("identifier", identifier);
        return namedParamJdbcTemplate.queryForObject(GET_PRICE_LIST_FOR_IDENTIFIER, parameters,  new BeanPropertyRowMapper<>(PriceListIdentifier.class));
    }

    public List<AdditionalEmail> getAdditionalEmailsForManufacturer(int manufacturerId) {
        String sql = "SELECT id, manufacturerid, label, email FROM additionalemail WHERE manufacturerid = ?";
        return jdbcTemplate.query(sql, new Object[]{manufacturerId}, (rs, rowNum) -> {
            AdditionalEmail email = new AdditionalEmail();
            email.setId(rs.getInt("id"));
            email.setManufacturerId(rs.getInt("manufacturerid"));
            email.setLabel(rs.getString("label"));
            email.setEmail(rs.getString("email"));
            return email;
        });
    }

    public void saveAdditionalEmailsForManufacturer(int manufacturerId, List<AdditionalEmail> emails) {
        jdbcTemplate.update("DELETE FROM additionalemail WHERE manufacturerid = ?", manufacturerId);
        
        if (emails != null && !emails.isEmpty()) {
            String sql = "INSERT INTO additionalemail (manufacturerid, label, email) VALUES (?, ?, ?)";
            for (AdditionalEmail email : emails) {
                jdbcTemplate.update(sql, manufacturerId, email.getLabel(), email.getEmail());
            }
        }
    }
}
