package co.cadshare.addresses.adapters.database;

import co.cadshare.shared.adapters.database.ManufacturerSettingsEntity;

import lombok.Data;

import javax.persistence.*;
import java.sql.Timestamp;
import java.util.List;

@Data
@Entity
@Table(name = "manufacturer")
public class AddressesManufacturerEntity {

    @Id
    @GeneratedValue
    @Column(name = "manufacturerid")
    private int manufacturerId;
    private String name;
    private String subdomain;
    @Column(name = "emailsignature")
    private String emailSignature;
    @Column(name = "logourl")
    private String logoUrl;
    @Column(name = "supportemail")
    private String supportEmail;
    private String phone;
    private boolean paid;
    @Column(name = "createddate")
    private Timestamp createdDate;
    @Column(name = "createdbyuserid")
    private int createdByUserId;
    @Column(name = "modifieddate")
    private Timestamp modifiedDate;
    @Column(name = "modifiedbyuserid")
    private Integer modifiedByUserId;

    @OneToOne(mappedBy = "manufacturer", fetch = FetchType.LAZY)
    private ManufacturerSettingsEntity manufacturerSettings;

    @OneToMany(cascade = CascadeType.ALL,
            fetch = FetchType.LAZY, mappedBy="manufacturer")
    private List<AddressesDealerEntity> dealers;

    @OneToMany(cascade = CascadeType.ALL,
            fetch = FetchType.LAZY, mappedBy="manufacturer")
    private List<AddressesDealerPlusEntity> dealerPluses;

    @OneToMany(cascade = CascadeType.ALL,
            fetch = FetchType.LAZY, mappedBy="manufacturer")
    private List<AddressesCustomerEntity> customers;

    @OneToMany(cascade = CascadeType.ALL,
            fetch = FetchType.LAZY, mappedBy="manufacturer")
    private List<AddressesRegionalOfficeEntity> regionalOffices;
}
