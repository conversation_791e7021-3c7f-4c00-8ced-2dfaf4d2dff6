package co.cadshare.modelMgt.products.boundary;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.dao.EmptyResultDataAccessException;

import co.cadshare.exceptions.NotFoundException;
import co.cadshare.modelMgt.products.core.Product;
import co.cadshare.shared.core.user.User;
import co.cadshare.aspects.logging.Log;

@Service
public class DeleteProductService {

    private final ProductCommandPort commandPort;
    private final ProductQueryPort queryPort;

    @Autowired
    public DeleteProductService(ProductCommandPort commandPort,
                                    ProductQueryPort queryPort) {
        this.commandPort = commandPort;
        this.queryPort = queryPort;
    }

    @Log
    public void delete(User user, Integer productId) throws Exception {
        try {
            Product product = this.queryPort.get(productId);
            this.commandPort.delete(user, product);
        } catch (EmptyResultDataAccessException ex) {
            throw new NotFoundException("Product does not exist");
        }
    }
}
