# @name cadshareAuthResponse

POST {{CADSHARE_URL}}/oauth/token
Content-Type: application/x-www-form-urlencoded
Authorization: Basic Y2xpZW50YXBwOnNlY3JldA==

username={{MANUFACTURER_USER_ID}}
&password={{STANDARD_PWD}}
&grant_type=password


###

@cadshareToken = Bearer {{cadshareAuthResponse.response.body.access_token}}

PUT {{CADSHARE_URL}}/manufacturersubentity/
Content-Type: application/json
Authorization: {{cadshareToken}}
Site-Url: {{CADSHARE_URL}}

{
    "name":"<PERSON><PERSON> Dealer",
    "defaultDiscount":1,
    "manufacturerSubEntityId":472,
    "visCustomerCode":"23",
    "warehouseId":1,
    "priceListIdentifierId":null
}