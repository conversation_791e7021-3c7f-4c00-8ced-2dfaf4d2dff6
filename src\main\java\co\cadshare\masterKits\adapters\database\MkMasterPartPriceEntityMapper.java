package co.cadshare.masterKits.adapters.database;

import co.cadshare.masterKits.core.MasterPartPriceListItem;
import co.cadshare.shared.adapters.database.CurrencyEntityMapper;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

@Mapper(uses= CurrencyEntityMapper.class)
public interface MkMasterPartPriceEntityMapper {

    MkMasterPartPriceEntityMapper Instance = Mappers.getMapper(MkMasterPartPriceEntityMapper.class);

    @Mapping(source="id", target="id")
    @Mapping(source="priceListIdentifier.id", target="identifierId")
    @Mapping(source="priceListIdentifier.identifier", target="identifier")
    @Mapping(source="priceListIdentifier.currency", target="currency")
    MasterPartPriceListItem entityToCore(MkMasterPartPriceEntity entity);

    @Mapping(source="id", target="id")
    @Mapping(source="identifierId", target="priceListIdentifier.id")
    @Mapping(source="identifier", target="priceListIdentifier.identifier")
    @Mapping(source="currency", target="priceListIdentifier.currency")
    MkMasterPartPriceEntity coreToEntity(MasterPartPriceListItem core);

}
