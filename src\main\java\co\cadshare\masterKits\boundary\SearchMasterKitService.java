package co.cadshare.masterKits.boundary;

import co.cadshare.masterKits.core.MasterKitSearchResult;
import co.cadshare.shared.core.manufacturer.Manufacturer;
import co.cadshare.shared.core.user.User;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class SearchMasterKitService {

    private final MasterKitQueryPort masterKitQueryPort;

    @Autowired
    public SearchMasterKitService(MasterKitQueryPort masterKitQueryPort) {
        this.masterKitQueryPort = masterKitQueryPort;
    }

    public List<MasterKitSearchResult> searchForPurchaser(User user, PurchaserMasterKitSearchRequest searchRequest) {
        return this.masterKitQueryPort.searchForMasterKitsForPurchaser(user, searchRequest);
    }

	public List<MasterKitSearchResult> searchForManufacturer(User user, ManufacturerMasterKitSearchRequest searchRequest) {
		return this.masterKitQueryPort.searchForMasterKitsForManufacturer(user, searchRequest);
	}
}
