package co.cadshare.masterKits.boundary;

import co.cadshare.masterKits.core.KitMasterPart;
import co.cadshare.masterKits.core.MasterKitSearchResult;
import co.cadshare.masterKits.core.PartMasterPart;
import co.cadshare.shared.boundary.QueryPort;
import co.cadshare.masterKits.core.MasterKit;
import co.cadshare.shared.core.user.User;

import java.util.List;

public interface MasterKitQueryPort extends QueryPort<MasterKit, Integer> {

    List<MasterKit> getListForManufacturer(Integer manufacturerId);
    List<PartMasterPart> getMasterPartsForKit(List<Integer> ids);
    KitMasterPart getKitMasterPartForKit(Integer id);

    List<MasterKitSearchResult> searchForMasterKitsForManufacturer(User user, ManufacturerMasterKitSearchRequest searchRequest);
	List<MasterKitSearchResult> searchForMasterKitsForPurchaser(User user, PurchaserMasterKitSearchRequest searchRequest);
}
