package co.cadshare.masterParts.adapters.database;

import co.cadshare.shared.adapters.database.ManufacturerEntity;
import lombok.Data;
import org.hibernate.annotations.NaturalId;

import javax.persistence.*;
import java.io.Serializable;

@Data
@Entity
@Table(name = "masterpart")
public class MpMasterPartEntity implements Serializable {

    @Id
    @GeneratedValue
    @Column(name="id")
    private Integer masterPartId;

    @Column(name="partnumber")
    private String partNumber;

    private Float price;

    @Column(name="supersessionpartnumber")
    private String supersessionPartNumber;

    @Column(name="superseded")
    private boolean superseded;

    @Column(name="maxsupersessionpartid")
    private Integer maxSupersessionPartId;

    @Column(name="maxsupersessionpartnumber")
    private String maxSupersessionPartNumber;

    @Column(name="insupersession")
    private boolean inSupersession;

    @Column(name="supersessionreverseindex")
    private Integer supersessionReverseIndex;
}
