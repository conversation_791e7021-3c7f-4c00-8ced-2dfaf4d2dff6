package co.cadshare.modelMgt.products.boundary;

import co.cadshare.domainmodel.machine.Machine;
import co.cadshare.modelMgt.products.core.Product;
import co.cadshare.shared.boundary.GetService;
import co.cadshare.shared.core.user.User;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.util.List;

@Service
public class GetProductService extends GetService<Product, Integer> {

    private ProductQueryPort complexQueryPort;

    @Autowired
    public GetProductService(ProductQueryPort queryPort) {
        super(queryPort);
        this.complexQueryPort = queryPort;
    }

    public List<Product> getProductsForRange(int rangeId){
        return this.complexQueryPort.getListForRange(rangeId);
    }

	public List<Product> getProductsForManufacturer(User currentUser) {
		return this.complexQueryPort.getListForManufacturer(currentUser.getManufacturerId());
	}
}
