package co.cadshare.controller;


import co.cadshare.domainmodel.s3.PresignedUrlRequest;
import co.cadshare.domainmodel.s3.S3Url;
import co.cadshare.shared.boundary.S3StoragePort;
import co.cadshare.shared.core.user.User;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import java.net.URL;

@Controller
public class AwsS3Controller {

    private static final Logger logger = LoggerFactory.getLogger(AwsS3Controller.class);

    @Autowired
    S3StoragePort s3StorageClient;

    private final Logger log = LoggerFactory.getLogger(getClass());

    @PostMapping(value = "/aws/s3/getpresignedurl", consumes = "application/json")
    @PreAuthorize("hasRole('ROLE_MANUFACTURER')")
    @ResponseBody
    public HttpEntity<String> getManufacturerPresignedUrl(@AuthenticationPrincipal User currentUser,
                                                          @RequestBody PresignedUrlRequest presignedUrlRequest) throws Exception {

        log.info("ACCESS: User [{}], getPresignedUrl - Manufacturer", currentUser.accessDetails());

        URL presignedUrl = s3StorageClient.getFileURLForManufacturerUpload(presignedUrlRequest.getObjectKey(), currentUser.getManufacturerId());
        String jsonURL = "{\"URL\": \"" + presignedUrl.toString() + "\"}";
        return new ResponseEntity<>(jsonURL, HttpStatus.OK);
    }

    @PostMapping(value = "/aws/s3/getpresignedurl/machine", consumes = "application/json")
    @PreAuthorize("hasRole('ROLE_MANUFACTURER')")
    @ResponseBody
    public HttpEntity<String> getMachinePresignedUrl(@AuthenticationPrincipal User currentUser,
                                                          @RequestBody PresignedUrlRequest presignedUrlRequest) throws Exception {


        log.info("ACCESS: User [{}], getPresignedUrl - Machine image", currentUser.accessDetails());

        URL presignedUrl = s3StorageClient.getFileURLForMachineUpload(presignedUrlRequest.getObjectKey(), currentUser.getManufacturerId());
        String jsonURL = "{\"URL\": \"" + presignedUrl.toString() + "\"}";
        return new ResponseEntity<>(jsonURL, HttpStatus.OK);
    }

    @PostMapping(value = "/aws/s3/getpresignedurl/machine/{machineId}/model/{modelId}", consumes = "application/json")
    @PreAuthorize("hasRole('ROLE_MANUFACTURER')")
    @ResponseBody
    public HttpEntity<String> getModelPresignedUrl(@AuthenticationPrincipal User currentUser,
                                                   @PathVariable int machineId, @PathVariable int modelId,
                                                          @RequestBody PresignedUrlRequest presignedUrlRequest) throws Exception {


        log.info("ACCESS: User [{}], getPresignedUrl - Model Snapshot", currentUser.accessDetails());

        URL presignedUrl = s3StorageClient.getFileURLForModelUpload(presignedUrlRequest.getObjectKey(), currentUser.getManufacturerId(), machineId, modelId);
        String jsonURL = "{\"URL\": \"" + presignedUrl.toString() + "\"}";
        return new ResponseEntity<>(jsonURL, HttpStatus.OK);
    }

    @PostMapping(value = "/aws/s3/getpresignedurl/order", consumes = "application/json")
    @PreAuthorize("hasRole('ROLE_MANUFACTURER')")
    @ResponseBody
    public HttpEntity<String> getOrderInvoicePresignedUrl(@AuthenticationPrincipal User currentUser,
                                                   @RequestBody PresignedUrlRequest presignedUrlRequest) throws Exception {


        log.info("ACCESS: User [{}], getPresignedUrl - Order", currentUser.accessDetails());

        URL presignedUrl = s3StorageClient.getFileURLForOrderInvoiceUpload(presignedUrlRequest.getObjectKey(), currentUser.getManufacturerId());
        String jsonURL = "{\"URL\": \"" + presignedUrl.toString() + "\"}";
        return new ResponseEntity<>(jsonURL, HttpStatus.OK);
    }

    @PostMapping(value = "/aws/s3/getpresignedurl/techDoc", consumes = "application/json")
    @PreAuthorize("hasRole('ROLE_MANUFACTURER')")
    @ResponseBody
    public HttpEntity<String> getTechDocPresignedUrl(@AuthenticationPrincipal User currentUser,
                                                     @RequestBody PresignedUrlRequest presignedUrlRequest) throws Exception {

        log.info("ACCESS: User [{}], getPresignedUrl - techDoc", currentUser.accessDetails());

        String fileType = presignedUrlRequest.getFileType(); // Get the file type from the request

        URL presignedUrl = s3StorageClient.getFileURLForTechDocUpload(presignedUrlRequest.getObjectKey(), fileType, currentUser.getManufacturerId());
        String jsonURL = "{\"URL\": \"" + presignedUrl.toString() + "\"}";
        return new ResponseEntity<>(jsonURL, HttpStatus.OK);
    }

    @PostMapping(value = "/aws/s3/deleteS3File", consumes = "application/json")
    @PreAuthorize("hasRole('ROLE_MANUFACTURER')")
    @ResponseBody
    public HttpEntity<String> deleteS3File(@AuthenticationPrincipal User currentUser, @RequestBody S3Url s3Url) throws Exception {

        //https://s3.amazonaws.com/cadshare-dev-bucket/manufacturer/3/ba24-87c4-61e6-6c43-cc36-bdfc-aa01-cc8b.png
        try {
            checkHasPermissonsForAWSBucket(s3StorageClient.getBucketName(), s3Url.getUrl(), currentUser.getManufacturerId()) ;
        } catch (Exception ex) {
            return new ResponseEntity<>(ex.getMessage(), HttpStatus.FORBIDDEN);
        }

        log.info("ACCESS: User [{}], deleteS3File", currentUser.accessDetails());
        boolean deleted = s3StorageClient.deleteS3File(s3Url.getUrl());
        return new ResponseEntity<>(String.valueOf(deleted), HttpStatus.OK);
        }

    private boolean checkHasPermissonsForAWSBucket(String bucketName, String url, int manufacturerId) throws Exception {
        String[] splitUrl = url.split(bucketName+"/manufacturer/");
        //Check manufacturerIds Match
        boolean manufacturerMatch = splitUrl[1].startsWith(String.valueOf(manufacturerId));

        //If they don't match
        if (!manufacturerMatch) {
            throw new Exception("Manufacturer Id not found in URL to be deleted OR did not match logged in manufacturer");
        }
        return true;
    }
}
