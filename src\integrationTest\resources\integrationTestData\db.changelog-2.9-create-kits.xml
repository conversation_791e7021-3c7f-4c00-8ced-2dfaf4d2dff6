<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                   http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.0.xsd
">
    <changeSet author="AndyB" id="2.9-integration-test-data-create-kits-1x">
        <sql stripComments="true">
            INSERT INTO public.mp_kit(
            id, title, description, deleted)
            VALUES (108, 'Caterpillar Kit 1 Title', 'Caterpillar Kit Description', FALSE);

            -- KIT masterPart
            INSERT INTO public.masterpart_kit_map(
            kitid, masterpartid, quantity, id)
            VALUES (108, 108, 8, 1);

            INSERT INTO public.masterpart_kit_map(
            kitid, masterpartid, quantity, id)
            VALUES (108, 104, 4, 2);

            INSERT INTO public.masterpart_kit_map(
            kitid, masterpartid, quantity, id)
            VALUES (108, 105, 5, 3);

            --ensures KIT is published to purchaser
            INSERT INTO public.manual_kit_map(
            manualid, kitid)
            VALUES ((select manualid
            from manual
            where manualname like '%Publication for Caterpillar%'), 108);
        </sql>
    </changeSet>

    <changeSet author="AndyB" id="2.9-integration-test-data-create-kits-21x">
        <sql stripComments="true">
            INSERT INTO public.mp_kit(
            id, title, description, deleted)
            VALUES (211, 'Caterpillar Kit 1 Title', 'Caterpillar Kit Description', FALSE);

            -- KIT masterPart
            INSERT INTO public.masterpart_kit_map(
            kitid, masterpartid, quantity, id)
            VALUES (211, 211, 8, 211);

            --ensures KIT is published to purchaser
            INSERT INTO public.manual_kit_map(
            manualid, kitid)
            VALUES ((select manualid
            from manual
            where manualname like '%Publication for Caterpillar%'), 211);
        </sql>
    </changeSet>

    <changeSet author="AndyB" id="2.9-integration-test-data-create-kits-22x">
        <sql stripComments="true">
            INSERT INTO public.mp_kit(
            id, title, description, deleted)
            VALUES (221, 'JCB Kit 1 Title', 'JCB Kit Description', FALSE);

            -- KIT masterPart
            INSERT INTO public.masterpart_kit_map(
            kitid, masterpartid, quantity, id)
            VALUES (221, 221, 8, 221);

            INSERT INTO public.masterpart_kit_map(
            kitid, masterpartid, quantity, id)
            VALUES (221, 222, 4, 222);

            --ensures KIT is published to purchaser
            INSERT INTO public.manual_kit_map(
            manualid, kitid)
            VALUES ((select manualid
            from manual
            where manualname like '%Publication for JCB%'), 221);
        </sql>
    </changeSet>

    <changeSet author="AndyB" id="2.9-integration-test-data-create-kits-23x">
        <sql stripComments="true">
            INSERT INTO public.mp_kit(
            id, title, description, deleted)
            VALUES (231, 'Liebherr Kit 1 Title', 'Liebherr Kit Description', FALSE);

            -- KIT masterPart
            INSERT INTO public.masterpart_kit_map(
            kitid, masterpartid, quantity, id)
            VALUES (231, 231, 8, 231);

            INSERT INTO public.masterpart_kit_map(
            kitid, masterpartid, quantity, id)
            VALUES (231, 232, 4, 232);

            INSERT INTO public.masterpart_kit_map(
            kitid, masterpartid, quantity, id)
            VALUES (231, 233, 5, 233);

            --ensures KIT is published to purchaser
            INSERT INTO public.manual_kit_map(
            manualid, kitid)
            VALUES ((select manualid
            from manual
            where manualname like '%Publication for Liebherr%'), 231);
        </sql>
    </changeSet>

    <changeSet author="AndyB" id="2.9-integration-test-data-create-kits-24x">
        <sql stripComments="true">
            INSERT INTO public.mp_kit(
            id, title, description, deleted)
            VALUES (241, 'Terex Kit 1 Title', 'Terex Kit Description', FALSE);

            -- KIT masterPart
            INSERT INTO public.masterpart_kit_map(
            kitid, masterpartid, quantity, id)
            VALUES (241, 241, 8, 241);

            INSERT INTO public.masterpart_kit_map(
            kitid, masterpartid, quantity, id)
            VALUES (241, 242, 4, 242);

            INSERT INTO public.masterpart_kit_map(
            kitid, masterpartid, quantity, id)
            VALUES (241, 243, 5, 243);

            INSERT INTO public.masterpart_kit_map(
            kitid, masterpartid, quantity, id)
            VALUES (241, 244, 5, 244);

            --ensures KIT is published to purchaser
            INSERT INTO public.manual_kit_map(
            manualid, kitid)
            VALUES ((select manualid
            from manual
            where manualname like '%Publication for Terex%'), 241);
        </sql>
    </changeSet>


</databaseChangeLog>
