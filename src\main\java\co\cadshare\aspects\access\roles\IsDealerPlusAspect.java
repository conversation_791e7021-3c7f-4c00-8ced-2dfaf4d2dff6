package co.cadshare.aspects.access.roles;

import co.cadshare.aspects.BaseAspect;
import co.cadshare.exceptions.ForbiddenException;
import co.cadshare.utils.ObjectUtilsExtension;
import lombok.experimental.ExtensionMethod;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.stereotype.Component;

@Aspect
@Component
@ExtensionMethod(ObjectUtilsExtension.class)
public class IsDealerPlusAspect extends BaseAspect {

    @Pointcut("@annotation(IsDealerPlus)")
    public void serviceLogPointcut(){}

    @Before("serviceLogPointcut()")
    public void checkUser(JoinPoint joinPoint) throws ForbiddenException {
	    configureInterception(joinPoint);
		    if (!user.isDealerPlusUser())
			    throw new ForbiddenException("User does not have permission to access this data.");
    }
}
