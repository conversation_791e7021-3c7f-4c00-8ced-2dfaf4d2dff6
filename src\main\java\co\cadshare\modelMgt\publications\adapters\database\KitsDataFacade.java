package co.cadshare.modelMgt.publications.adapters.database;

import co.cadshare.modelMgt.publications.boundary.KitQueryPort;
import co.cadshare.modelMgt.publications.core.Kit;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

@Service
public class KitsDataFacade implements KitQueryPort {

	@Autowired
	private final PublicationsKitRepo repo;

	public KitsDataFacade(PublicationsKitRepo repo) {
		this.repo = repo;
	}

	@Override
	public Kit get(Integer id) {
		PublicationsKitEntity entity = repo.getOne(id);
		return PublicationsKitEntityMapper.Instance.entityToCore(entity);
	}

	@Override
	public List<Kit> getList() {
		return Collections.emptyList();
	}
}
