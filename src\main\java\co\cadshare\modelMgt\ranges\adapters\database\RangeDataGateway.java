package co.cadshare.modelMgt.ranges.adapters.database;

import co.cadshare.exceptions.NotFoundException;
import co.cadshare.modelMgt.ranges.boundary.RangeCommandPort;
import co.cadshare.modelMgt.ranges.boundary.RangeQueryPort;
import co.cadshare.modelMgt.ranges.core.Range;
import co.cadshare.aspects.logging.Log;
import co.cadshare.shared.core.user.User;

import java.sql.Timestamp;
import java.util.Calendar;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class RangeDataGateway implements RangeQueryPort, RangeCommandPort {

    private RangeRepo rangeRepo;
    private RangeComplexQueryRepo rangeQueryRepo;

    @Autowired
    public RangeDataGateway(RangeRepo rangeRepo,
                                  RangeComplexQueryRepo rangeQueryRepo) {
        this.rangeRepo = rangeRepo;
        this.rangeQueryRepo = rangeQueryRepo;
    }

    @Override
    public Range get(Integer rangeId) {
         RangeEntity entity = this.rangeRepo.getOne(rangeId);
         if(entity.isDeleted())
             throw new NotFoundException("Range does not exist");
         return RangeEntityMapper.Instance.entityToCore(entity);
    }

    public List<Range> getListForManufacturer(Integer manufacturerId) {
         List<RangeEntity> entities = this.rangeQueryRepo.getRangesForManufacturer(manufacturerId);
         return RangeEntityMapper.Instance.entitiesToCores(entities);
    }

    @Override
    public List<Range> getList() {
        List<RangeEntity> entities = this.rangeRepo.findAll();
        return RangeEntityMapper.Instance.entitiesToCores(entities);
    }

    @Override
    @Log
    public Integer create(User user, Range range) {
        range.setCreatedByUserId(user.getUserId());
        range.setCreatedDate(new Timestamp(Calendar.getInstance().getTime().getTime()));
        RangeEntity rangeEntity = RangeEntityMapper.Instance.coreToEntity(range);
        RangeEntity savedEntity = this.rangeRepo.save(rangeEntity);
        return savedEntity.getId();
    }

    @Override
    @Log
    public void update(User user, Range range) throws Exception {
        range.setModifiedByUserId(user.getUserId());
        range.setModifiedDate(new Timestamp(Calendar.getInstance().getTime().getTime()));
        this.rangeRepo.save(RangeEntityMapper.Instance.coreToEntity(range));
    }

    @Override
    @Log
    public void delete(User user, Range range) throws Exception {
        range.setModifiedByUserId(user.getUserId());
        range.setModifiedDate(new Timestamp(Calendar.getInstance().getTime().getTime()));
        this.rangeRepo.save(RangeEntityMapper.Instance.coreToEntity(range));
    }
}