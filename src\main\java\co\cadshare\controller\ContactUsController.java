package co.cadshare.controller;

import co.cadshare.domainmodel.ContactUs.ContactUsRequest;
import co.cadshare.shared.core.user.User;
import co.cadshare.services.ContactUsService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

@Controller
@RequestMapping("/contactUs")
@Slf4j
public class ContactUsController {

  private ContactUsService contactUsService;

  public ContactUsController(ContactUsService contactUsService) {
    this.contactUsService = contactUsService;
  }

  @PreAuthorize("(hasRole('ROLE_MANUFACTURER_SUB_ENTITY_DEALER') or hasRole('ROLE_MANUFACTURER_SUB_ENTITY_CUSTOMER') or hasRole('ROLE_MANUFACTURER_SUB_ENTITY_DEALER_PLUS'))")
  @PostMapping(consumes = "application/json")
  public HttpEntity<Boolean> createContactusRequest(@AuthenticationPrincipal User currentUser, @RequestBody ContactUsRequest contact) throws Exception {

    log.info("ACCESS: User [{}], createContactusRequest", currentUser.accessDetails());
    contact.setCreatedByUserId(currentUser.getUserId());

    boolean success = contactUsService.sendContactRequest(contact, currentUser);

    return new ResponseEntity<>(success, HttpStatus.OK);
  }

}
