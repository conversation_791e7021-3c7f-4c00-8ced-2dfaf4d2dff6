package co.cadshare.oauth.domainmodel;

import co.cadshare.shared.core.user.User;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface OAuthUserMapper {
    OAuthUserMapper Instance = Mappers.getMapper(OAuthUserMapper.class);

    OAuthUser buildFromUser(User user);

    @AfterMapping
    static void calledAfterMapping(@MappingTarget OAuthUser target) {
        target.buildAuthRoles();
    }

}
