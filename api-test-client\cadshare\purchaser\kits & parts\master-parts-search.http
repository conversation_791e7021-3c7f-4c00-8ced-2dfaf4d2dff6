// log in

# @name cadshareAuthResponse
POST {{CADSHARE_URL}}/oauth/token
Content-Type: application/x-www-form-urlencoded
Authorization: Basic Y2xpZW50YXBwOnNlY3JldA==

username=2
&password=Passw0rd
&grant_type=password


###


// search for Master Parts with Part Number

@cadshareToken = Bearer {{cadshareAuthResponse.response.body.access_token}}

POST {{CADSHARE_URL}}/manufacturer-sub-entities/1/master-part-search?language=EN
Content-Type: application/json
Authorization: {{cadshareToken}}

{
	"partNumber": "123"
}