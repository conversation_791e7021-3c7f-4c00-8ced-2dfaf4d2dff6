package co.cadshare.modelMgt.publications.boundary;

import co.cadshare.exceptions.UnprocessableEntityException;
import co.cadshare.modelMgt.shared.core.Viewable;
import org.junit.Test;

import java.util.Arrays;
import java.util.Collections;

public class UpdatePublicationCommandTest {


	@Test(expected = UnprocessableEntityException.class)
	public void validatePublicationCategoryIdIsEmptyThrowUnprocessableEntityException() {
		UpdatePublicationCommand command = new UpdatePublicationCommand();
		command.validate();
	}

	@Test(expected = UnprocessableEntityException.class)
	public void validateViewablesIsEmptyThrowUnprocessableEntityException() {
		UpdatePublicationCommand command = new UpdatePublicationCommand();
		command.setPublicationCategoryId(1);
		command.validate();
	}

	@Test
	public void validateViewableExistsNoException() {
		UpdatePublicationCommand command = new UpdatePublicationCommand();
		command.setPublicationCategoryId(1);
		command.setName("publication name");
		Viewable viewable = new Viewable();
		viewable.setId(1);
		viewable.setName("viewable name");
		command.setViewables(Collections.singletonList(viewable));
		command.validate();
	}
}
