package co.cadshare.modelMgt.publications.adapters.api.web;

import co.cadshare.aspects.access.languages.CanUseLanguage;
import co.cadshare.modelMgt.publications.boundary.TechDocService;
import co.cadshare.modelMgt.publications.boundary.VideoService;
import co.cadshare.modelMgt.publications.core.Manual;
import co.cadshare.shared.core.Language;
import co.cadshare.masterParts.boundary.MasterPartKitService;
import co.cadshare.modelMgt.models.boundary.ModelService;
import co.cadshare.modelMgt.publications.boundary.LatestChangesService;
import co.cadshare.modelMgt.publications.core.ManualLatestUpdateResponse;
import co.cadshare.modelMgt.publications.core.ManualStatus;
import co.cadshare.masterParts.core.extensions.kit.Kit;
import co.cadshare.domainmodel.serialnumber.SerialNumber;
import co.cadshare.modelMgt.publications.core.TechDoc;
import co.cadshare.shared.core.user.User;
import co.cadshare.modelMgt.publications.core.Video;
import co.cadshare.modelMgt.publications.boundary.ManualService;
import co.cadshare.response.CustomerModel;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.List;

@Controller
@RequestMapping("/manual")
public class ManualController {

	private final ManualService manualService;
	private final MasterPartKitService masterPartKitService;
	private final ModelService modelService;
	private final TechDocService techDocService;
	private final VideoService videoService;
	private final LatestChangesService latestChangesService;

	@Autowired
	public ManualController(ManualService manualService,
	                      MasterPartKitService masterPartKitService,
	                      ModelService modelService,
	                      TechDocService techDocService,
	                      VideoService videoService,
	                      LatestChangesService latestChangesService) {
		this.manualService = manualService;
		this.masterPartKitService = masterPartKitService;
		this.modelService = modelService;
		this.techDocService = techDocService;
		this.videoService = videoService;
		this.latestChangesService = latestChangesService;
	}

  private static final Logger logger = LoggerFactory.getLogger(ManualController.class);

	@PostMapping(consumes = "application/json")
	@PreAuthorize("hasRole('ROLE_MANUFACTURER')")
	public HttpEntity<Integer> createManual(@AuthenticationPrincipal User currentUser,
	                                      @RequestBody Manual manual) {

		logger.info("ACCESS: User [{}], createManual, manual [{}]", currentUser.accessDetails(), manual.toString());
		manual.setCreatedByUserId(currentUser.getUserId());
		manual.setModifiedByUserId(currentUser.getUserId());
		manual.setManufacturerId(currentUser.getManufacturerId());
		int manualId = manualService.createManual(manual);
		logger.info("Manual with id [{}] created", manualId);
		return new ResponseEntity<>(manualId, HttpStatus.OK);
	}

	@PutMapping(consumes = "application/json")
	@PreAuthorize("hasRole('ROLE_MANUFACTURER') and @manualService.getManual(#manual.getManualId()).getManufacturerId() == #currentUser.getManufacturerId()")
	public HttpEntity<Integer> updateManual(@AuthenticationPrincipal User currentUser,
	                                        @RequestBody Manual manual) {

		logger.info("ACCESS: User [{}], updateManual, manual [{}]", currentUser.accessDetails(), manual.toString());
		Timestamp now = new java.sql.Timestamp(Calendar.getInstance().getTime().getTime());
		manual.setModifiedByUserId(currentUser.getUserId());
		manual.setModifiedDate(now);
		int manualId = manualService.updateManual(manual);
		logger.info("Manual with id [{}] updated", manualId);
		return new ResponseEntity<>(manualId, HttpStatus.OK);
	}

	@GetMapping("/{manualId}")
	public HttpEntity<Manual> getManual(@AuthenticationPrincipal User currentUser,
	                                    @PathVariable int manualId) {

		logger.info("ACCESS: User [{}], getManual, manualId [{}]", currentUser.accessDetails(), manualId);
		Manual manual = manualService. getManual(manualId);
		return new ResponseEntity<>(manual, HttpStatus.OK);
	}

	@GetMapping("/{manualId}/models")
	@PreAuthorize("@permissionsService.userHasPermissionsToViewManual(#currentUser.getUserId(), #manualId) or (hasRole('ROLE_MANUFACTURER') and @manualService.getManual(#manualId).getManufacturerId() == #currentUser.getManufacturerId())")
	public HttpEntity<List<CustomerModel>> getModelsForManual(@AuthenticationPrincipal User currentUser,
	                                                        @PathVariable int manualId) {

		logger.info("ACCESS: User [{}], getModelsForManual, manualId [{}]", currentUser.accessDetails(), manualId);
		List<CustomerModel> modelList = modelService.getModelsByManualId(manualId);
		return new ResponseEntity<>(modelList, HttpStatus.OK);
	}

	@GetMapping("/{manualId}/techDocs")
	@PreAuthorize("@permissionsService.userHasPermissionsToViewManual(#currentUser.getUserId(), #manualId) or (hasRole('ROLE_MANUFACTURER') and @manualService.getManual(#manualId).getManufacturerId() == #currentUser.getManufacturerId())")
	public HttpEntity<List<TechDoc>> getTechDocForManual(@AuthenticationPrincipal User currentUser,
	                                                     @PathVariable int manualId) {

		logger.info("ACCESS: User [{}], getTechDocForManual, manualId [{}]", currentUser.accessDetails(), manualId);
		List<TechDoc> techDocList = techDocService.getTechDocByManualId(manualId);
		return new ResponseEntity<>(techDocList, HttpStatus.OK);
	}

	@GetMapping("/{manualId}/video")
	@PreAuthorize("@permissionsService.userHasPermissionsToViewManual(#currentUser.getUserId(), #manualId) or (hasRole('ROLE_MANUFACTURER') and @manualService.getManual(#manualId).getManufacturerId() == #currentUser.getManufacturerId())")
	public HttpEntity<List<Video>> getVideoForManual(@AuthenticationPrincipal User currentUser,
	                                                 @PathVariable int manualId) {

		logger.info("ACCESS: User [{}], getVideoForManual, manualId [{}]", currentUser.accessDetails(), manualId);
		List<Video> videoList = videoService.getVideoByManualId(manualId);
		return new ResponseEntity<>(videoList, HttpStatus.OK);
	}

	@PutMapping("/{manualId}/status")
	public HttpEntity<List<SerialNumber>> updateManualStatus(@AuthenticationPrincipal User currentUser,
	                                                         @PathVariable int manualId,
	                                                         @RequestBody ManualStatus manualStatus) {

		logger.info("ACCESS: User [{}], publishManual, manualId [{}]", currentUser.accessDetails(), manualId);
		manualService.updateManualStatus(manualId, manualStatus);
		return new ResponseEntity<>(HttpStatus.OK);
	}

	@DeleteMapping("/{manualId}")
	@PreAuthorize("hasRole('ROLE_MANUFACTURER') and @manualService.getManual(#manualId).getManufacturerId() == #currentUser.getManufacturerId()")
	public HttpEntity<Boolean> deleteManual(@AuthenticationPrincipal User currentUser,
	                                        @PathVariable int manualId) {

		logger.info("ACCESS: User [{}], deleteManual, manual id [{}]", currentUser.accessDetails(), manualId);
		Boolean isDeleted = manualService.deleteManual(manualId);
		return new ResponseEntity<>(isDeleted, HttpStatus.OK);
	}

	@GetMapping("/{manualId}/latestUpdateTime")
	public HttpEntity<Timestamp> getManualLatestUpdateTimestamp(@AuthenticationPrincipal User currentUser,
	                                                            @PathVariable int manualId) {

		logger.info("ACCESS: User [{}], getManualLatestUpdateTimestamp, manual id [{}]", currentUser.accessDetails(), manualId);
		Timestamp latestUpdate = manualService.getManualLatestUpdateTimestamp(manualId);
		return new ResponseEntity<>(latestUpdate, HttpStatus.OK);
	}

	@GetMapping("/{manualId}/latestChanges")
	public HttpEntity<ManualLatestUpdateResponse> getManualLatestChanges(@AuthenticationPrincipal User currentUser,
	                                                                     @PathVariable int manualId) {

		logger.info("ACCESS: User [{}], getManualLatestChanges, manual id [{}]", currentUser.accessDetails(), manualId);
		ManualLatestUpdateResponse response = latestChangesService.getManualLatestChanges(manualId);
		return new ResponseEntity<>(response, HttpStatus.OK);
	}

	@PreAuthorize("hasRole('ROLE_MANUFACTURER')")
	@RequestMapping(value = "/{manualId}/assign", method = RequestMethod.PUT, consumes = "application/json")
	public HttpEntity<Integer> assignManufacturerSubEntityToManual(@AuthenticationPrincipal User currentUser,
	                                                              @PathVariable int manualId,
	                                                               @RequestBody ArrayList<Integer> manufacturerSubEntityIds) {

		logger.info("ACCESS: User [{}], assignManufacturerSubEntityToManual, manualIds [{}]",
		        currentUser.accessDetails(), manufacturerSubEntityIds.toString());
		manualService.assignManualToManufacturerSubEntities(manualId, manufacturerSubEntityIds);
		logger.info("Manual [{}] successfully assigned to manufacturerSubEntityIds [{}]",  manualId, manufacturerSubEntityIds);
		return new ResponseEntity<>(HttpStatus.OK);
	}

	@GetMapping("/{manualId}/kits")
	@CanUseLanguage
	public HttpEntity<List<Kit>> getMasterPartKitsByManualId(@AuthenticationPrincipal User currentUser,
	                                                         @PathVariable int manualId,
	                                                         @RequestParam(value = "language", required = false) Language language,
	                                                         Integer warehouseId) throws Exception {

		logger.info("ACCESS: User [{}], getMasterPartKitsByManualId, manual id [{}]", currentUser.accessDetails(), manualId);
		Integer languageId = language != null ? currentUser.findLanguage(language).getLanguageId() : null;
		List<Kit> kitList = masterPartKitService.getKitsByManualId(manualId,
				languageId,
				currentUser.obtainDefaultLanguage(),
				warehouseId);
		return new ResponseEntity<>(kitList, HttpStatus.OK);
	}
}
