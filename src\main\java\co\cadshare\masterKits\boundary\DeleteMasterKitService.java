package co.cadshare.masterKits.boundary;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.dao.EmptyResultDataAccessException;

import co.cadshare.exceptions.NotFoundException;
import co.cadshare.masterKits.core.MasterKit;
import co.cadshare.shared.core.user.User;
import co.cadshare.aspects.logging.Log;

import javax.transaction.Transactional;

@Service
public class DeleteMasterKitService {

    private final MasterKitCommandPort commandPort;
    private final MasterKitQueryPort queryPort;

    @Autowired
    public DeleteMasterKitService(MasterKitCommandPort commandPort,
                                    MasterKitQueryPort queryPort) {
        this.commandPort = commandPort;
        this.queryPort = queryPort;
    }

    @Log
    @Transactional
    public void delete(User user, Integer masterKitId) throws Exception {
        try {
            MasterKit masterKit = this.queryPort.get(masterKitId);
            masterKit.Delete();

            this.commandPort.delete(user, masterKit);
            if(masterKit.isNotLegacy())
                this.commandPort.update(user, masterKit.getFormerKitMasterPart());
        } catch (EmptyResultDataAccessException ex) {
            throw new NotFoundException("MasterKit does not exist");
        }
    }
}
