package co.cadshare.masterKits.boundary;

import co.cadshare.masterKits.core.KitMasterPart;
import co.cadshare.masterKits.core.MasterKit;
import co.cadshare.masterKits.core.PartMasterPart;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Data
public class CreateMasterKitCommand {

    private MasterKitQueryPort queryPort;
    private int kitMasterPartId;
    private String kitMasterPartDescription;
    private Map<Integer, Integer> parts;
    private int manufacturerId;

}
