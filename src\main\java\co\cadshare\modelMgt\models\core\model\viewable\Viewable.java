/*
 * Copyright 2016 Bell.
 */
package co.cadshare.modelMgt.models.core.model.viewable;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.sql.Timestamp;
import java.util.List;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
public class Viewable {

    private int id;
    private int modelId;
    private ViewableType type;
    @JsonProperty("stateData")
    @JsonInclude(Include.NON_NULL)
    private List<StateDetail> stateDetails;
    private Boolean lineDrawingEnabled;
    private Boolean viewLocked;
    private Boolean edgingEnabled;
    private String background;
    private List<Integer> backgroundRGB;
    private Timestamp createdDate;
    private int createdByUserId;
    private Timestamp modifiedDate;
    private int modifiedByUserId;

    public static Viewable createFrom(Viewable viewable) {
        return Viewable.builder()
            .modelId(viewable.getModelId())
            .lineDrawingEnabled(viewable.getLineDrawingEnabled())
            .viewLocked(viewable.getViewLocked())
            .edgingEnabled(viewable.getEdgingEnabled())
            .background(viewable.getBackground())
            .build();
    }

    @Builder
    public Viewable(int id, int modelId, ViewableType viewableType, List<StateDetail> stateDetails, Boolean lineDrawingEnabled, Boolean viewLocked, Boolean edgingEnabled, String background, Timestamp createdDate, int createdByUserId,
                    Timestamp modifiedDate, int modifiedByUserId) {
        this.id = id;
        this.modelId = modelId;
        this.type = viewableType;
        this.stateDetails = stateDetails;
        this.lineDrawingEnabled = lineDrawingEnabled;
        this.viewLocked = viewLocked;
        this.edgingEnabled = edgingEnabled;
        this.background = background;
        this.createdDate = createdDate;
        this.createdByUserId = createdByUserId;
        this.modifiedDate = modifiedDate;
        this.modifiedByUserId = modifiedByUserId;
    }
    
}
