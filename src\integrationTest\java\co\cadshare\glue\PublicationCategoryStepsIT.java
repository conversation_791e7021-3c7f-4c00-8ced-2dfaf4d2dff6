package co.cadshare.glue;

import co.cadshare.modelMgt.publicationCategories.adapters.api.web.*;
import io.cucumber.java.en.And;
import io.cucumber.java.en.Then;
import io.cucumber.java.en.When;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Arrays;
import java.util.List;
import java.util.Optional;

import static org.junit.Assert.*;

public class PublicationCategoryStepsIT {

	private final CadshareIT cadshare;

	@Autowired
	public PublicationCategoryStepsIT(CadshareIT cadshare) {
		this.cadshare = cadshare;
	}


	@Then("I can view all PublicationCategories belonging to my Manufacturer")
	public void iCanViewAllPublicationCategoriesBelongingToMyManufacturer() {
		GetPublicationCategoryListResponseDto publicationCategories = cadshare.loggedInUser().getPublicationCategoriesList();
		assertNotNull(publicationCategories);
		assertFalse(publicationCategories.getPublicationCategories().isEmpty());
	}

	@And("a publicationCategory with name {} doesn't exist")
	public void aPublicationCategoryWithNameDoesnTExist(String name) {
		GetPublicationCategoryListResponseDto publicationCategories = cadshare.loggedInUser().getPublicationCategoriesList();
		boolean found = publicationCategories.getPublicationCategories().stream()
				.anyMatch(pc -> pc.getName().equals(name));
		assertFalse(found);
	}

	@When("I create a new PublicationCategory named {}")
	public void iCreateANewPublicationCategoryNamed(String name) {
		cadshare.loggedInUser().createPublicationCategory(name);
	}

	@Then("a PublicationCategory with name {} exists")
	public void aNewPublicationCategoryWithNameNowExists(String name) {
		cadshare.loggedInUser().verifyPublicationCategoryExists(name);
	}

	@When("I delete the PublicationCategory named {}")
	public void iDeleteThePublicationCategoryNamed(String name) {
		GetPublicationCategoryListResponseDto publicationCategories = cadshare.loggedInUser().getPublicationCategoriesList();
		Optional<GetPublicationCategoryListItemResponseDto> found = publicationCategories.getPublicationCategories().stream()
				.filter(pc -> pc.getName().equals(name)).findFirst();
		assertTrue(found.isPresent());
		cadshare.loggedInUser().deletePublicationCategory(found.get().getId().toString());
	}

	@When("I update the PublicationCategory named {} to {}")
	public void iUpdateThePublicationCategoryNamed(String oldName, String newName) {
		GetPublicationCategoryListResponseDto publicationCategories = cadshare.loggedInUser().getPublicationCategoriesList();
		Optional<GetPublicationCategoryListItemResponseDto> found = publicationCategories.getPublicationCategories().stream()
				.filter(pc -> pc.getName().equals(oldName)).findFirst();
		assertTrue(found.isPresent());
		cadshare.loggedInUser().updatePublicationCategory(newName, found.get().getId().toString());
	}

	@When("I get the PublicationCategory named {}")
	public void iGetThePublicationCategoryNamed(String name) {
		GetPublicationCategoryListResponseDto publicationCategories = cadshare.loggedInUser().getPublicationCategoriesList();
		Optional<GetPublicationCategoryListItemResponseDto> found = publicationCategories.getPublicationCategories().stream()
				.filter(pc -> pc.getName().equals(name)).findFirst();
		assertTrue(found.isPresent());
		GetPublicationCategoryResponseDto publicationCategory = cadshare.loggedInUser().getPublicationCategory(found.get().getId().toString());
		assertNotNull(publicationCategory);
		assertEquals(name, publicationCategory.getName());
	}

	@And("I create PublicationCategories named {}")
	public void iCreatePublicationCategoriesNamed(String publicationCategoriesArray) {
		List<String> publicationCategories = Arrays.asList(publicationCategoriesArray.split(","));
		publicationCategories.forEach(cadshare.loggedInUser()::createPublicationCategory);
	}

	@When("I assign PublicationCategories named {} to Dealer named {}")
	public void iAssignPublicationCategoriesNamedToDealerNamed(String publicationCategoriesArray, String dealerName) {
		cadshare.loggedInUser().assignPublicationCategoriesToDealer(publicationCategoriesArray, dealerName);
	}

    @Then("I can't delete the PublicationCategory named {}")
    public void iCanTDeleteThePublicationCategoryNamed(String name) {
		GetPublicationCategoryListResponseDto publicationCategories = cadshare.loggedInUser().getPublicationCategoriesList();
		Optional<GetPublicationCategoryListItemResponseDto> found = publicationCategories.getPublicationCategories().stream()
				.filter(pc -> pc.getName().equals(name)).findFirst();
		assertTrue(found.isPresent());
		cadshare.loggedInUser().cantDeletePublicationCategory(found.get().getId().toString());
    }
}
