/*
 * Copyright 2016 Bell.
 */
package co.cadshare.exceptions;

import com.fasterxml.jackson.annotation.JsonProperty;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.dao.DataAccessException;
import org.springframework.dao.EmptyResultDataAccessException;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.authentication.InternalAuthenticationServiceException;
import org.springframework.security.oauth2.common.exceptions.OAuth2Exception;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;

import java.sql.SQLException;

/**
 * TODO(dallanmc) Description of class.
 */
@ControllerAdvice
public class GlobalExceptionHandler {

    private static final Logger logger = LoggerFactory.getLogger(GlobalExceptionHandler.class);

    private static final String GENERIC_ERROR = "A server error has occured, please contact cadshare support for assistance";
    private static final String GENERIC_LOG_ERROR = "Exception caught in GlobalExceptionHandler";
    private static final String NOT_FOUND_ERROR = "The requested resource was not found";


    //400
    @ExceptionHandler({IllegalArgumentException.class, BadClientRequestException.class})
    @ResponseBody
    public ResponseEntity<?> handleIllegalArgumentException(Exception e) {
        logger.error(GENERIC_LOG_ERROR, e);
        return response(new ExceptionMessage(e.getMessage()), HttpStatus.BAD_REQUEST);
    }

    //401
    @ExceptionHandler({OAuth2Exception.class, InternalAuthenticationServiceException.class})
    @ResponseBody
    public ResponseEntity<?> handleOAuth2Exception(Exception e) {
        logger.error(GENERIC_LOG_ERROR, e);
        return response(new ExceptionMessage(e.getMessage()), HttpStatus.UNAUTHORIZED);
    }

    //403
    @ExceptionHandler({ForbiddenException.class})
    @ResponseBody
    public ResponseEntity<?> handleForbiddenException(Exception e) {
        logger.error(GENERIC_LOG_ERROR, e);
        return response(new ExceptionMessage(e.getMessage()), HttpStatus.FORBIDDEN);
    }

    //404
    @ExceptionHandler({EmptyResultDataAccessException.class, NotFoundException.class})
    @ResponseBody
    public ResponseEntity<?> handleNotFoundException(Exception e) {
        logger.error(GENERIC_LOG_ERROR, e);
        return response(new ExceptionMessage(NOT_FOUND_ERROR), HttpStatus.NOT_FOUND);
    }

    //500
    @ExceptionHandler({ SQLException.class, DataAccessException.class})
    @ResponseBody
    public ResponseEntity<?> handleSQLException(Exception e) {

        logger.error(GENERIC_LOG_ERROR, e);
        return response(new ExceptionMessage(GENERIC_ERROR), HttpStatus.INTERNAL_SERVER_ERROR);
    }

    //502
    @ExceptionHandler({ExternalDataException.class, ExternalServiceException.class})
    @ResponseBody
    public ResponseEntity<?> handleBadGatewayException(Exception e) {
        logger.error(GENERIC_LOG_ERROR, e);
        return response(new ExceptionMessage(e.getMessage()), HttpStatus.BAD_GATEWAY);
    }

    protected <T> ResponseEntity<T> response(T body, HttpStatus status) {
        return new ResponseEntity<T>(body, new HttpHeaders(), status);
    }

    public class ExceptionMessage {

        private final String errorMessage;

        public ExceptionMessage(String errorMessage ) {
            this.errorMessage = errorMessage;
        }

        @JsonProperty("error")
        public String getError() {
            return errorMessage;
        }

    }
}
