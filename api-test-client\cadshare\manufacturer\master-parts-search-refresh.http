// log in

# @name cadshareAuthResponse
POST {{CADSHARE_URL}}/oauth/token
Content-Type: application/x-www-form-urlencoded
Authorization: Basic Y2xpZW50YXBwOnNlY3JldA==

username={{MANUFACTURER_USER_ID}}
&password={{STANDARD_PWD}}
&grant_type=password

###


// search for Master Parts with Part Number

@cadshareToken = Bearer {{cadshareAuthResponse.response.body.access_token}}

POST {{CADSHARE_URL}}/master-part-search/refresh
Content-Type: application/json
Authorization: {{cadshareToken}}