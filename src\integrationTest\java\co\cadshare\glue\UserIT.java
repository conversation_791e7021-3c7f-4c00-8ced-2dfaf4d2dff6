package co.cadshare.glue;

import co.cadshare.addresses.core.Address;
import co.cadshare.domainmodel.part.Part;
import co.cadshare.domainmodel.part.PartViewerDetails;
import co.cadshare.masterKits.adapters.api.web.PostMasterKitsSearchResponseDto;
import co.cadshare.masterParts.adapters.api.web.PostPurchaserSearchMasterPartsDto;
import co.cadshare.masterParts.adapters.api.web.SupersessionHistoryDto;
import co.cadshare.masterParts.boundary.MasterPartSearchResult;
import co.cadshare.masterParts.core.MasterPartDetails;
import co.cadshare.media.adapters.api.web.GetImageResponseDto;
import co.cadshare.modelMgt.models.core.Model;
import co.cadshare.modelMgt.products.adapters.api.GetProductListResponseDto;
import co.cadshare.modelMgt.products.adapters.api.web.GetManufacturerProductListResponseDto;
import co.cadshare.modelMgt.publicationCategories.adapters.api.web.GetPublicationCategoryListItemResponseDto;
import co.cadshare.modelMgt.publicationCategories.adapters.api.web.GetPublicationCategoryListResponseDto;
import co.cadshare.modelMgt.publicationCategories.adapters.api.web.GetPublicationCategoryResponseDto;
import co.cadshare.modelMgt.publicationCategories.adapters.api.web.PostPublicationCategoriesDto;
import co.cadshare.modelMgt.publications.adapters.api.web.*;
import co.cadshare.modelMgt.ranges.adapters.api.web.GetProductRangeListResponseDto;
import co.cadshare.modelMgt.viewables.adapters.api.web.GetViewableListResponseDto;
import co.cadshare.modelMgt.viewables.adapters.api.web.PostFilterViewablesResponseDto;
import co.cadshare.modelMgt.viewables.adapters.api.web.PostSearchViewablesResponseDto;
import co.cadshare.orders.adapters.api.web.GetOrderListResponseDto;
import co.cadshare.orders.adapters.api.web.PostOrderItemRequestDto;
import co.cadshare.orders.adapters.api.web.PostOrderItemResponseDto;
import co.cadshare.orders.core.Order;
import co.cadshare.response.CustomerModel;
import co.cadshare.response.UserManufacturer;
import co.cadshare.shared.core.manufacturer.subentity.ManufacturerSubEntity;
import co.cadshare.shared.core.user.User;
import io.cucumber.spring.ScenarioScope;
import io.restassured.RestAssured;
import io.restassured.http.ContentType;
import io.restassured.path.json.JsonPath;
import io.restassured.response.Response;
import io.restassured.response.ValidatableResponse;
import io.restassured.specification.RequestSpecification;
import org.apache.commons.lang3.NotImplementedException;
import org.hibernate.Session;
import org.hibernate.jdbc.Work;
import org.springframework.stereotype.Component;

import javax.persistence.EntityManager;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

import static io.restassured.RestAssured.given;
import static org.junit.Assert.*;
import static org.junit.Assert.assertTrue;

@Component
@ScenarioScope
public abstract class UserIT {

	private final String notImplementedExceptionMsg = "%s is not supported for this user type";
    protected Integer manufacturerId;
    protected User user;

    public String CLIENT_SECRET;
    public String CLIENT_ID;

    protected String port;
    protected final String baseUrl = "http://localhost:";
    protected static String token;
	protected EntityManager entityManager;

	public UserIT(String port, String clientId, String secret, EntityManager entityManager) {
		this.port = port;
		this.CLIENT_ID = clientId;
		this.CLIENT_SECRET = secret;
		this.entityManager = entityManager;
	}

    protected User logInUsingEmailAddress(String emailAddress) {
        RestAssured.baseURI = baseUrl.concat(port);

        String json = String.format("{\"email\":\"%s\"}", emailAddress);
        List<UserManufacturer> manufacturerUsers = given().header("Site-Url", baseUrl.concat(port))
                .auth().basic(CLIENT_ID, CLIENT_SECRET)
                .contentType(ContentType.JSON)
                .body(json)
                .when()
                .post("/user/manufacturers")
                .then()
                .statusCode(200)
                .extract().body().jsonPath()
                .getList("", UserManufacturer.class);

        assertFalse(manufacturerUsers.isEmpty());

        Response response = given().header("Site-Url", baseUrl.concat(port))
                .auth().basic(CLIENT_ID, CLIENT_SECRET)
                .contentType(ContentType.URLENC)
                .body(String.format("username=%s&password=Passw0rd&grant_type=password", manufacturerUsers.get(0).getUserId()))
                .when()
                .post("/oauth/token")
                .then()
                .statusCode(200)
                .extract().response();

        String jsonString = response.asString();
        token = JsonPath.from(jsonString).get("access_token");
        assertNotNull(token);

	    User user = getResource("/user", User.class);
        assertNotNull(user);
        manufacturerId = user.getManufacturerId();
        return user;
    }

    public void tryGetAllAddressesFor(int userId) {
        String sql = String.format("/address?userId=%s", userId);
        getForbidden(sql);
    }

    public List<Address> getAllAddresses() {
        String sql = String.format("/address?userId=%s", user.getUserId());
        return getResourceList(sql, Address.class);
    }

    public Part getPart(int partId) {
        String sql = String.format("/part/%s", partId);
        return getResource(sql, Part.class);
    }

    public MasterPartDetails getMasterPart(int partId) {
        String sql = String.format("/masterPart/%s?language=EN", partId);
        return getResource(sql, MasterPartDetails.class);
    }

    public Order getOrder(int orderId) {
        String sql = String.format("/order/%s?language=EN", orderId);
        return getResource(sql, Order.class);
    }

    public void addMasterPartToOrder(int orderId, String masterPartNumber, int quantity) {
        String url = String.format("/orders/%s/order-items?language=EN", orderId);
        PostOrderItemRequestDto request = new PostOrderItemRequestDto();
        int masterPartId = searchForMasterPartByNumber(masterPartNumber).getMasterParts().get(0).getMasterPartId();
        request.setMasterPartId(masterPartId);
        request.setQuantity(quantity);
        Response response = postResource(url, request);
        PostOrderItemResponseDto dto = response.body().as(PostOrderItemResponseDto.class);
        assertTrue(dto.getOrderItemId() > 0);
    }

    public void addMasterKitToOrder(int orderId, String masterPartNumber, int quantity) {
        String url = String.format("/orders/%s/order-items?language=EN", orderId);
        PostOrderItemRequestDto request = new PostOrderItemRequestDto();
        int masterKitId = searchForMasterKit(masterPartNumber);
        request.setMasterKitId(masterKitId);
        request.setQuantity(quantity);
        Response response = postResource(url, request);
        PostOrderItemResponseDto dto = response.body().as(PostOrderItemResponseDto.class);
        assertTrue(dto.getOrderItemId() > 0);
    }

    protected void failToGetPartViewerDetailsOnBehalfOf(MasterPartDetails masterPart, int userId, String userType) {
        Part part = getPart(masterPart.getId());
        String sql = String.format("/model/%s/part/%s/viewerDetails??userType=%s&userId=%s",
                part.getModelId(), part.getObjectId(), userType, userId);
        getForbidden(sql);
    }

    protected PartViewerDetails getPartViewerDetailsOnBehalfOf(MasterPartDetails masterPart, int userId, String userType) {
        Part part = getPart(masterPart.getId());
        String sql = String.format("/model/%s/part/%s/viewerDetails??userType=%s&userId=%s",
                part.getModelId(), part.getObjectId(), userType, userId);
        return getResource(sql, PartViewerDetails.class);
    }

    protected void executeQuery(String sql) {

        Session session = (Session) entityManager.getDelegate();
        session.doWork(new Work() {
            @Override
            public void execute(Connection connection) throws SQLException {
                //connection, finally!
                assert connection != null;

                ResultSet rs = connection.createStatement().executeQuery(sql);
                while (rs.next()) {
                    System.out.println(rs.getInt(1) + " " + rs.getString(2) + " " + rs.getString(3) + " " + rs.getString(4));
                }
            }
        });
    }

    protected <T> T getResource(String endpoint, Class<T> clazz) {
        return getGetResponse(endpoint).extract().body().as(clazz);
    }

    protected void getNotFound(String endpoint) {
        getNotFoundResponse(endpoint);
    }

    protected void getForbidden(String endpoint) {
        getForbiddenResponse(endpoint);
    }

    protected <T> List<T> getResourceList(String endpoint, Class<T> clazz) {
        return getGetResponse(endpoint)
                .extract().body().jsonPath()
                .getList("", clazz);
    }

    private ValidatableResponse getGetResponse(String endpoint) {
        return buildInitialAuthorisedRequest()
                .when()
                .get(endpoint)
                .then()
                .statusCode(200);
    }

    private void getNotFoundResponse(String endpoint) {
         buildInitialAuthorisedRequest()
                .when()
                .get(endpoint)
                .then()
                .statusCode(404);
    }

    private void getForbiddenResponse(String endpoint) {
        buildInitialAuthorisedRequest()
                .when()
                .get(endpoint)
                .then()
                .statusCode(403);
    }

    protected <T> Response postResource(String endpoint, T body) {
        return buildInitialAuthorisedRequest()
                .body(body)
                .when()
                .post(endpoint)
                .then()
                .statusCode(200)
                .extract().response();
    }

    protected <T, U> U actionResource(String endpoint, T body, Class<U> clazz) {
        return buildInitialAuthorisedRequest()
                .body(body)
                .when()
                .post(endpoint)
                .then()
                .statusCode(200)
                .extract().body().as(clazz);
    }

    protected Response actionResource(String endpoint) {
        return buildInitialAuthorisedRequest()
                .when()
                .post(endpoint)
                .then()
                .statusCode(200)
                .extract().response();
    }

    protected Response actionResourceWithHttpErrorCode(String endpoint, int httpCode) {
        return buildInitialAuthorisedRequest()
                .when()
                .post(endpoint)
                .then()
                .statusCode(httpCode)
                .extract().response();
    }

    protected <T> Response actionResourceWithHttpErrorCode(String endpoint, T body, int httpCode) {
        return buildInitialAuthorisedRequest()
                .body(body)
                .when()
                .post(endpoint)
                .then()
                .statusCode(httpCode)
                .extract().response();
    }

    protected <T> Response putResource(String endpoint, T body) {
        return buildInitialAuthorisedRequest()
                .body(body)
                .when()
                .put(endpoint)
                .then()
                .statusCode(200)
                .extract().response();
    }

    protected <T> Response deleteResource(String endpoint) {
        return buildInitialAuthorisedRequest()
                .when()
                .delete(endpoint)
                .then()
                .statusCode(200)
                .extract().response();
    }

	protected Response deleteResourceWithHttpErrorCode(String endpoint, int httpCode) {
		return buildInitialAuthorisedRequest()
				.when()
				.delete(endpoint)
				.then()
				.statusCode(httpCode)
				.extract().response();
	}

    private RequestSpecification buildInitialAuthorisedRequest() {
        return given().header("Site-Url", baseUrl.concat(port))
                .contentType(ContentType.JSON)
                .auth().oauth2(token);
    }

    public static ManufacturerSubEntity buildPurchaser(String name, ManufacturerSubEntity.ManufacturerSubEntityType type) {
        ManufacturerSubEntity purchaser = new ManufacturerSubEntity();
        purchaser.setDefaultDiscount(0);
        purchaser.setDescription(name.concat(" description"));
        purchaser.setManufacturerId(1);
        purchaser.setManufacturerSubEntityType(type);
        purchaser.setName(name);
        return purchaser;
    }

	// MasterParts Supersession History

	public Response reviseSupersessionHistory(String masterPartNumber) {
		throw new NotImplementedException(String.format(notImplementedExceptionMsg, "reviseSupersessionHistory"));
	}

	public Response splitSupersessionHistory(String masterPartNumber) {
		throw new NotImplementedException(String.format(notImplementedExceptionMsg, "splitSupersessionHistory"));
	}

	public void doNotGetSupersessionHistoryForPart(String masterPartNumber) {
		throw new NotImplementedException(String.format(notImplementedExceptionMsg, "doNotGetSupersessionHistoryForPart"));
	}

	public Response attemptRevisionSupersessionHistory(String masterPartNumber, int i) {
		throw new NotImplementedException(String.format(notImplementedExceptionMsg, "attemptRevisionSupersessionHistory"));
	}

	public void failToSupersedeMasterPart(String supersededMasterPart, String supersessionMasterPart) {
		throw new NotImplementedException(String.format(notImplementedExceptionMsg, "failToSupersedeMasterPart"));
	}


	// Manage Purchasers

	public List<ManufacturerSubEntity> getPurchasersList() {
		throw new NotImplementedException(String.format(notImplementedExceptionMsg, "getPurchasersList"));
	}

	public String createPurchaser(String name, ManufacturerSubEntity.ManufacturerSubEntityType manufacturerSubEntityType) {
		throw new NotImplementedException(String.format(notImplementedExceptionMsg, "createPurchaser"));
	}

	public ManufacturerSubEntity getPurchaser(String purchaserId) {
		throw new NotImplementedException(String.format(notImplementedExceptionMsg, "getPurchaser"));
	}

	public void updatePurchaser(String newName, String purchaserId) {
		throw new NotImplementedException(String.format(notImplementedExceptionMsg, "updatePurchaser"));
	}

	public void deletePurchaser(String purchaserId) {
		throw new NotImplementedException(String.format(notImplementedExceptionMsg, "deletePurchaser"));
	}

	// Manage PublicationCategories

	public GetPublicationCategoryListResponseDto getPublicationCategoriesList() {
		throw new NotImplementedException(String.format(notImplementedExceptionMsg, "getPublicationCategoriesList"));
	}

	public String createPublicationCategory(String name) {
		throw new NotImplementedException(String.format(notImplementedExceptionMsg, "createPublicationCategory"));
	}

	public GetPublicationCategoryResponseDto getPublicationCategory(String publicationCategoryId) {
		throw new NotImplementedException(String.format(notImplementedExceptionMsg, "getPublicationCategory"));
	}

	public void updatePublicationCategory(String newName, String publicationCategoryId) {
		throw new NotImplementedException(String.format(notImplementedExceptionMsg, "updatePublicationCategory"));
	}

	public void deletePublicationCategory(String publicationCategoryId) {
		throw new NotImplementedException(String.format(notImplementedExceptionMsg, "deletePulicationCategory"));
	}
	public void cantDeletePublicationCategory(String publicationCategoryId) {
		throw new NotImplementedException(String.format(notImplementedExceptionMsg, "deletePublicationCategory"));
	}

	// Orders

	public GetOrderListResponseDto getEnquiries() {
		throw new NotImplementedException(String.format(notImplementedExceptionMsg, "getEnquiries"));
	}

	// Models

	public Model getModel(String modelName) {
		throw new NotImplementedException(String.format(notImplementedExceptionMsg, "getModel"));
	}

	public void autoPublishViewable(Model model, String viewableName) {
		throw new NotImplementedException(String.format(notImplementedExceptionMsg, "autoPublishViewable"));
	}

	public void verifyPublicationExists(String viewableName) {
		throw new NotImplementedException(String.format(notImplementedExceptionMsg, "verifyPublicationExists"));
	}

	public void verifyPublicationDoesntExist(String publicationName) {
		throw new NotImplementedException(String.format(notImplementedExceptionMsg, "verifyPublicationDoesntExist"));
	}

	public SupersessionHistoryDto getSupersessionHistoryForPart(String supersessionMasterPart) {
		throw new NotImplementedException(String.format(notImplementedExceptionMsg, "getSupersessionHistoryForPart"));
	}

	public Response supersedeMasterPart(String supersededMasterPart, String supersessionMasterPart) {
		throw new NotImplementedException(String.format(notImplementedExceptionMsg, "getSupersessionHistoryForPart"));
	}


	// Users

	public User getUser(String userId) {
		throw new NotImplementedException(String.format(notImplementedExceptionMsg, "getUser"));
	}

	public void logIn(String emailAddress) {
		throw new NotImplementedException(String.format(notImplementedExceptionMsg, "logIn"));
	}

	public MasterPartSearchResult searchForMasterPartByNumber(String identifiedPartNumber) {
		throw new NotImplementedException(String.format(notImplementedExceptionMsg, "searchForMasterPartByNumber"));
	}

	public MasterPartSearchResult exactSearchForMasterPartByNumber(String identifiedPartNumber) {
		throw new NotImplementedException(String.format(notImplementedExceptionMsg, "searchForMasterPartByNumber"));
	}

	public MasterPartSearchResult searchForMasterPartByNumberUsingLanguage(String identifiedPartNumber, String languageCode) {
		throw new NotImplementedException(String.format(notImplementedExceptionMsg, "searchForMasterPartByNumberUsingLanguage"));
	}

	public MasterPartSearchResult searchForMasterPartByDescriptionUsingLanguage(String identifiedPartDescription, String languageCode) {
		throw new NotImplementedException(String.format(notImplementedExceptionMsg, "searchForMasterPartByDescription"));
	}

	public int searchForMasterKit(String identifiedPartNumber) {
		throw new NotImplementedException(String.format(notImplementedExceptionMsg, "searchForMasterKit"));
	}

	public PartViewerDetails getPartViewerDetails(MasterPartDetails masterPart) {
		throw new NotImplementedException(String.format(notImplementedExceptionMsg, "getPartViewerDetails"));
	}

	public PartViewerDetails getPartViewerDetailsOnBehalfOf(MasterPartDetails masterPart, int userId) {
		throw new NotImplementedException(String.format(notImplementedExceptionMsg, "getPartViewerDetailsOnBehalfOf"));
	}

	public void failToGetPartViewerDetailsOnBehalfOf(MasterPartDetails masterPart, int userId) {
		throw new NotImplementedException(String.format(notImplementedExceptionMsg, "failToGetPartViewerDetailsOnBehalfOf"));
	}

	public int getManufacturerId() {
		throw new NotImplementedException(String.format(notImplementedExceptionMsg, "getManufacturerId"));
	}

	public void notAuthorisedToGetUser(String userId) {
		throw new NotImplementedException(String.format(notImplementedExceptionMsg, "notAuthorisedToGetUser"));
	}

	public void publicationHasDefaultImage(Model model, String publicationName) {
		throw new NotImplementedException(String.format(notImplementedExceptionMsg, "publicationHasDefaultImage"));
	}

	public List<CustomerModel> getViewablesForPublication(String publicationName) {
		int publicationId = getPublicationIdFromName(publicationName);
		String url = String.format("/manual/%s/models?language=EN", publicationId);
		return getResourceList(url, CustomerModel.class);
	}

	public GetPublicationResponseDto getPublicationFromName(String publicationName) {
		throw new NotImplementedException(String.format(notImplementedExceptionMsg, "getPublicationFromName"));
	}

	protected int getPublicationIdFromName(String publicationName) {
		throw new NotImplementedException(String.format(notImplementedExceptionMsg, "getPublicationIdFromName"));
	}

	public PostMasterKitsSearchResponseDto searchForMasterKitByNumber(String masterKitNumberSearch) {
		throw new NotImplementedException(String.format(notImplementedExceptionMsg, "searchForMasterKitByNumber"));
	}

	public PostMasterKitsSearchResponseDto searchForMasterKitByDescriptionUsingLanguage(String masterKitDescSearch, String languageCode) {
		throw new NotImplementedException(String.format(notImplementedExceptionMsg, "searchForMasterKitByDescriptionUsingLanguage"));
	}

	public GetProductRangeListResponseDto getProductRanges() {
		throw new NotImplementedException(String.format(notImplementedExceptionMsg, "getProductRanges"));
	}

	public GetProductListResponseDto getProductsForRange(String productRangeId) {
		throw new NotImplementedException(String.format(notImplementedExceptionMsg, "getProductsForRange"));
	}

	public GetManufacturerProductListResponseDto getAllProducts() {
		throw new NotImplementedException(String.format(notImplementedExceptionMsg, "getAllProducts"));
	}

	public GetViewableListResponseDto getAllViewables() {
		throw new NotImplementedException(String.format(notImplementedExceptionMsg, "getAllViewables"));
	}

	public PostSearchViewablesResponseDto searchViewablesByName(String viewableName) {
		throw new NotImplementedException(String.format(notImplementedExceptionMsg, "searchViewablesByName"));
	}

	protected PostMasterKitsSearchResponseDto searchForMasterKitByNumber(String role, String identifiedPartNumber, String languageCode, Integer purchaserId){
		String sql = String.format("/%s/%s/master-part-kits/search?language=%s", role, purchaserId, languageCode);
		PostPurchaserSearchMasterPartsDto request = new PostPurchaserSearchMasterPartsDto();
		request.setPartNumber(identifiedPartNumber);
		return actionResource(sql, request, PostMasterKitsSearchResponseDto.class);
	}

	protected PostMasterKitsSearchResponseDto searchForMasterKitByDesc(String role, String identifiedPartDesc, String languageCode, Integer purchaserId){
		String sql = String.format("/%s/%s/master-part-kits/search?language=%s", role, purchaserId, languageCode);
		PostPurchaserSearchMasterPartsDto request = new PostPurchaserSearchMasterPartsDto();
		request.setPartDescription(identifiedPartDesc);
		return actionResource(sql, request, PostMasterKitsSearchResponseDto.class);
	}


	public PostFilterViewablesResponseDto filterViewablesByProduct(int productId) {
		throw new NotImplementedException(String.format(notImplementedExceptionMsg, "filterViewablesByProduct"));
	}

	public GetPublicationsListResponseDto getPublicationsForManufacturer() {
		throw new NotImplementedException(String.format(notImplementedExceptionMsg, "getPublicationsForManufacturer"));
	}

	public void createPublication(PostPublicationRequestDto request) {
		throw new NotImplementedException(String.format(notImplementedExceptionMsg, "createPublication"));
	}

	public PostPublicationRequestDto getNewPublication() {
		return new PostPublicationRequestDto();
	}

	public void verifyPublicationCategoryExists(String name) {
		throw new NotImplementedException(String.format(notImplementedExceptionMsg, "doesPublicationCategoryExist"));
	}

	public GetPublicationCategoryResponseDto searchForPublicationCategoryByName(String name) {
		throw new NotImplementedException(String.format(notImplementedExceptionMsg, "searchForPublicationCategoryByName"));
	}

	public GetPublicationListItemResponseDto getPublicationFromListByName(String publicationName) {
		throw new NotImplementedException(String.format(notImplementedExceptionMsg, "getPublicationFromListByName"));
	}

	public void updatePublication(PutPublicationRequestDto updatedPublication, int publicationId) {
		throw new NotImplementedException(String.format(notImplementedExceptionMsg, "updatePublication"));
	}

	public void deletePublication(String publicationId) {
		throw new NotImplementedException(String.format(notImplementedExceptionMsg, "deletePublication"));
	}

	public void publishPublication(String publicationId) {
		throw new NotImplementedException(String.format(notImplementedExceptionMsg, "publishPublication"));
	}

	public void unpublishPublication(String publicationId) {
		throw new NotImplementedException(String.format(notImplementedExceptionMsg, "unpublishPublication"));
	}

	public void assignPublicationCategoriesToDealer(String publicationCategoriesArray, String emailAddress) {
		throw new NotImplementedException(String.format(notImplementedExceptionMsg, "assignPublicationCategoriesToDealer"));
	}

	public GetImageResponseDto getImage(int imageId) {
		throw new NotImplementedException(String.format(notImplementedExceptionMsg, "getImage"));
	}

	public void assignPublicationsToDealer(String publicationNames, String dealerName) {
		throw new NotImplementedException(String.format(notImplementedExceptionMsg, "assignPublicationsToDealer"));
	}
}
