package co.cadshare.modelMgt.models.core.model;

import co.cadshare.shared.core.user.User;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@JsonInclude(Include.NON_NULL)
@NoArgsConstructor
public class CloneSettings {
    private User user;
    @JsonProperty
    private boolean is2d;
    private String viewableName;
    private boolean includeSnapshots;
    private boolean include2dNonModeledParts;

    @Builder
    public CloneSettings(User user, boolean is2d, String viewableName, boolean includeSnapshots, boolean include2dNonModeledParts) {
        this.user = user;
        this.is2d = is2d;
        this.viewableName = viewableName;
        this.includeSnapshots = includeSnapshots;
        this.include2dNonModeledParts = include2dNonModeledParts;
    }
}
