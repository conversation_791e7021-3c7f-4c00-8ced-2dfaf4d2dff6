package co.cadshare.modelMgt.publications.adapters.database;

import co.cadshare.main.Application;
import co.cadshare.modelMgt.publicationCategories.adapters.database.PublicationCategoryEntity;
import co.cadshare.modelMgt.shared.core.Product;
import co.cadshare.shared.adapters.database.ImageEntity;
import co.cadshare.shared.adapters.database.UserEntity;
import co.cadshare.shared.core.user.User;
import co.cadshare.shared.testData.PublicationTestData;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.EntityManager;
import javax.persistence.TemporalType;

import java.math.BigInteger;
import java.sql.Timestamp;
import java.util.Arrays;
import java.util.HashSet;
import java.util.Optional;

import static org.junit.Assert.*;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class)
@Transactional
public class PublicationsRepoTest {


	@Autowired
	private EntityManager entityManager;

	@Autowired
	private PublicationsRepo sut;

	private PublicationsPublicationEntity publicationEntity;

	@Before
	public void setup() {
		PublicationCategoryEntity publicationCategory = PublicationTestData.getPublicationCategoryEntity();
		entityManager.persist(publicationCategory);
		ImageEntity coverImage = PublicationTestData.getCoverImage();
		entityManager.persist(coverImage);
		ImageEntity featuredViewableImage = PublicationTestData.getFeaturedViewableImage();
		entityManager.persist(featuredViewableImage);
		PublicationsRangeEntity range = PublicationTestData.getRangeEntity();
		entityManager.persist(range);
		PublicationsProductEntity product = PublicationTestData.getProductEntity();
		entityManager.persist(product);
		PublicationsModelEntity model = PublicationTestData.getModelEntity();
		product.setRange(range);
		model.setProduct(product);entityManager.persist(product);
		PublicationsTechDocEntity techDoc1 = PublicationTestData.getTechDocEntity();
		entityManager.persist(techDoc1);
		PublicationsTechDocEntity techDoc2 = PublicationTestData.getTechDocEntity();
		entityManager.persist(techDoc2);
		PublicationsVideoEntity video1 = PublicationTestData.getVideoEntity();
		entityManager.persist(video1);
		PublicationsVideoEntity video2 = PublicationTestData.getVideoEntity();
		entityManager.persist(video2);
		PublicationsKitEntity kit1 = PublicationTestData.getKitEntity();
		entityManager.persist(kit1);
		PublicationsKitEntity kit2 = PublicationTestData.getKitEntity();
		entityManager.persist(kit2);
		PublicationsPurchaserEntity dealer = PublicationTestData.getDealerEntity();
		entityManager.persist(dealer);
		PublicationsPurchaserEntity customer = PublicationTestData.getCustomerEntity();
		entityManager.persist(customer);
		publicationEntity = PublicationTestData.getPublicationEntity(publicationCategory,
				coverImage,
				featuredViewableImage,
				new HashSet<>(Arrays.asList(model)),
				new HashSet<>(Arrays.asList(techDoc1, techDoc2)),
				new HashSet<>(Arrays.asList(video1, video2)),
				new HashSet<>(Arrays.asList(kit1, kit2)),
				new HashSet<>(Arrays.asList(dealer, customer)));
		entityManager.persist(publicationEntity);

	}

	@Test
	public void shouldReturnNoMatches() {
		Optional<PublicationsPublicationEntity> found = sut.findById(999);
		assertFalse(found.isPresent());
	}

	@Test
	public void shouldReturnMatch() {
		Optional<PublicationsPublicationEntity> found = sut.findById(publicationEntity.getId());
		assertTrue(found.isPresent());
	}

}
