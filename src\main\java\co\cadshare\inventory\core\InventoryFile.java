package co.cadshare.inventory.core;

import lombok.Data;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

@Data
public class InventoryFile {

    private String PART_NUMBER_COLUMN_NAME = "Part Number";
    InventoryFileHeaders headers;
    List<String []> dataRows;

    public InventoryFile(List<String[]> input) throws UnparseableInventoryFileException {

        this.headers = new InventoryFileHeaders(input.get(0));
        this.dataRows = input.subList(1, input.size());
    }

    @Data
    public class InventoryFileHeaders {

        private List<String> headerNames = new ArrayList<>();
        private Integer stockPosition;
        private Integer pricePosition;

        public InventoryFileHeaders(String[] headers) throws UnparseableInventoryFileException {
            if (headers.length < 2) {
                throw new UnparseableInventoryFileException("CSV must contain at least 2 columns");
            }
            if (headers.length > 3) {
                throw new UnparseableInventoryFileException("CSV must contain max 3 columns");
            }
            if (!PART_NUMBER_COLUMN_NAME.equals(headers[0])) {
                throw new UnparseableInventoryFileException("Header of first column should be " + PART_NUMBER_COLUMN_NAME);
            }
            if (hasInvalidHeaders(headers)) {
                throw new UnparseableInventoryFileException("Header contains invalid column header(s). Header [" + Arrays.toString(headers) + "].");
            }

            for (int i = 1; i < headers.length; i++) {
                if (headers[i].equalsIgnoreCase("Stock")) {
                    stockPosition = i;
                }
                if (headers[i].equalsIgnoreCase("Price")) {
                    pricePosition = i;
                }
                headerNames.add(headers[i]);
            }
        }

        private boolean hasInvalidHeaders(String[] headers) {

            for (int i = 2; i < headers.length; i++) {
              if (!headers[i].trim().equalsIgnoreCase("Stock") && !headers[i].trim().equalsIgnoreCase("Price")) {
                  return true;
                }
            }
            return false;
        }
    }

    public class UnparseableInventoryFileException extends Exception {
        public UnparseableInventoryFileException(String message){
            super(message);
        }
    }
}
