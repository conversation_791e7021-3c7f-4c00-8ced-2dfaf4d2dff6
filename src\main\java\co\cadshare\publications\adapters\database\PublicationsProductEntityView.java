package co.cadshare.publications.adapters.database;

import com.blazebit.persistence.view.EntityView;
import com.blazebit.persistence.view.IdMapping;
import com.blazebit.persistence.view.Mapping;

@EntityView(PublicationsProductEntity.class)
public interface PublicationsProductEntityView {

    @IdMapping
    int getId();

    String getName();

    PublicationsRangeEntityView getRange();

    String getThumbnailUrl();
}
