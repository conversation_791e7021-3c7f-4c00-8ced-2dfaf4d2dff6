package co.cadshare.addresses.adapters.ext.erp.vis;

import co.cadshare.shared.adapters.ext.erp.vis.BaseVisibilityRequest;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlRootElement;
import lombok.AccessLevel;
import lombok.Data;
import lombok.Setter;

@JacksonXmlRootElement(localName = "GetSHIP_TO_ADDRESSS")
@Data
public class GetShipToAddressListRequest extends BaseVisibilityRequest {
    @JacksonXmlProperty(localName = "strTP_NAME_X")
    @Setter(AccessLevel.NONE)
    private String tradingPartnerName;

    public GetShipToAddressListRequest(String strSecureID, String tradingPartnerName) {
        super(strSecureID);
        this.tradingPartnerName = tradingPartnerName;
    }


}
