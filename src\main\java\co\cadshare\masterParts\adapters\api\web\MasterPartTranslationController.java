package co.cadshare.masterParts.adapters.api.web;

import co.cadshare.masterParts.core.Translation;
import co.cadshare.masterParts.core.TranslationFile;
import co.cadshare.masterParts.core.TranslationFile.UnparseableTranslationFileException;
import co.cadshare.shared.core.user.User;
import co.cadshare.masterParts.boundary.MasterPartTranslationService;
import com.google.gson.JsonObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;

@RestController
@Slf4j
public class MasterPartTranslationController {


    private final MasterPartTranslationService translationService;
    private final TranslationCsvFileProcessor csvProcessor;

    @Autowired
    public MasterPartTranslationController(MasterPartTranslationService translationUploadService,
                                           TranslationCsvFileProcessor csvProcessor) {
        this.translationService = translationUploadService;
        this.csvProcessor = csvProcessor;
    }

    @PostMapping("/translation")
    public ResponseEntity<String> translationFileUpload(@AuthenticationPrincipal User user, @RequestParam("file") MultipartFile file) {
        log.info("Received translation file [{}] for processing for manufacturer id", file.getName(), user.getManufacturerId());
        // We will do some quick validation and then hand back to the client. In the background
        // we'll process the file and commit to database
        try {
            TranslationFile translationFile = csvProcessor.convert(file);
            translationService.save(user.getManufacturerId(), translationFile);
        } catch (UnparseableTranslationFileException | IOException e) {
            JsonObject response = new JsonObject();
            response.addProperty("message", "Error processing file: " + e.getMessage());
            return new ResponseEntity<>(response.toString(), HttpStatus.BAD_REQUEST);
        }
        return new ResponseEntity<>( HttpStatus.ACCEPTED);
    }

    @GetMapping(value = "/masterPart/{masterpartId}/translation")
    public HttpEntity<List<Translation>> getMasterPartTranslations(@AuthenticationPrincipal User currentUser, @PathVariable int masterpartId) {

        log.info("ACCESS: User [{}], getMasterPartTranslations, masterpartid [{}]", currentUser.accessDetails(), masterpartId);

        List<Translation> translations = translationService.getPartTranslations(masterpartId);
        return new ResponseEntity<>(translations, HttpStatus.OK);
    }

    @PostMapping(value = "/masterPart/{masterpartId}/translation/language/{languageId}")
    public HttpEntity<Boolean> createMasterPartTranslation(@AuthenticationPrincipal User currentUser, @PathVariable int masterpartId, @PathVariable int languageId, @RequestBody Translation translation) {

        log.info("ACCESS: User [{}], createMasterPartTranslation, masterpartid [{}]", currentUser.accessDetails(), masterpartId);
        translation.setMasterPartId(masterpartId);
        translation.setLanguageId(languageId);
        Boolean result = translationService.createPartTranslation(translation);
        return new ResponseEntity<>(result, HttpStatus.OK);
    }

    @PutMapping(value = "/masterPart/{masterpartId}/translation/language/{languageId}")
    public HttpEntity<Boolean> updateMasterPartTranslation(@AuthenticationPrincipal User currentUser, @PathVariable int masterpartId, @PathVariable int languageId, @RequestBody Translation translation) {

        log.info("ACCESS: User [{}], updateMasterPartTranslation, masterpartid [{}]", currentUser.accessDetails(), masterpartId);
        translation.setMasterPartId(masterpartId);
        translation.setLanguageId(languageId);
        Boolean result = translationService.updatePartTranslation(translation);
        return new ResponseEntity<>(result, HttpStatus.OK);
    }

    @DeleteMapping(value = "/masterPart/{masterpartId}/translation/language/{languageId}")
    public HttpEntity<Boolean> deleteMasterPartTranslation(@AuthenticationPrincipal User currentUser, @PathVariable int masterpartId, @PathVariable int languageId) {

        log.info("ACCESS: User [{}], deleteMasterPartTranslation, masterpartid [{}]", currentUser.accessDetails(), masterpartId);

        boolean response =  translationService.deletePartTranslation(masterpartId, languageId);
        return new ResponseEntity<>(response, HttpStatus.OK);
    }


}
