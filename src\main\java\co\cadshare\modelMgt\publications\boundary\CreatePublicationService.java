package co.cadshare.modelMgt.publications.boundary;

import co.cadshare.exceptions.NoCadshareDataFoundException;
import co.cadshare.modelMgt.shared.core.Publication;
import co.cadshare.shared.core.user.User;
import co.cadshare.utils.ObjectUtilsExtension;
import lombok.experimental.ExtensionMethod;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.EmptyResultDataAccessException;
import org.springframework.stereotype.Service;

@Service
@ExtensionMethod(ObjectUtilsExtension.class)
public class CreatePublicationService {

    private final PublicationCommandPort publicationCommandPort;
	private final PublicationHydrator publicationHydrator;

    @Autowired
    public CreatePublicationService(PublicationCommandPort publicationCommandPort,
                                    PublicationHydrator publicationHydrator) {
        this.publicationCommandPort = publicationCommandPort;
	    this.publicationHydrator = publicationHydrator;
    }

    public Integer create(User user, CreatePublicationCommand createPublication) {

		try {
			createPublication.validate();
			Publication publication = createPublication.build();
			publicationHydrator.hydrate(createPublication, publication);
			return this.publicationCommandPort.create(user, publication);
		} catch (EmptyResultDataAccessException e) {
			throw new NoCadshareDataFoundException(e.getMessage());
		}
    }
}
