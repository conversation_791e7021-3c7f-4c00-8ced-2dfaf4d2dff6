package co.cadshare.modelMgt.publications.adapters.database;

import co.cadshare.modelMgt.publications.core.ManualStatus;
import com.querydsl.jpa.impl.JPAQueryFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import javax.persistence.EntityManager;
import java.util.List;

@Repository
public class PublicationsComplexQueryRepo {

    private final JPAQueryFactory queryFactory;

    @Autowired
    public PublicationsComplexQueryRepo(EntityManager entityManager) {
        this.queryFactory = new JPAQueryFactory(entityManager);
    }

    public List<PublicationsPublicationEntity> getPublicationsForManufacturer(Integer manufacturerId) {
        QPublicationsPublicationEntity publication = QPublicationsPublicationEntity.publicationsPublicationEntity;
        return queryFactory.selectFrom(publication)
                .where(publication.manufacturerId.eq(manufacturerId)
                        .and(publication.deleted.eq(false)))
                .fetch();
    }

	public List<PublicationsPublicationEntity> getPublicationsForPurchaser(int purchaserId) {
		QPublicationsPublicationEntity publication = QPublicationsPublicationEntity.publicationsPublicationEntity;
		return queryFactory.selectFrom(publication)
				.where(((publication.purchasers.any().id.eq(purchaserId)
                                .and(publication.deleted.eq(false)))
						.or(publication.publicationCategory.purchasers.any().id.eq(purchaserId))
                        ).and(publication.status.eq(ManualStatus.Status.PUBLISHED))
				).fetch();
	}

    public List<PublicationsPublicationEntity> getPublicationsForPublicationCategory(Integer publicationCategoryId) {
        QPublicationsPublicationEntity publication = QPublicationsPublicationEntity.publicationsPublicationEntity;
        return queryFactory.selectFrom(publication)
                .where(publication.publicationCategory.id.eq(publicationCategoryId)
                        .and(publication.deleted.eq(false)))
                .fetch();
    }
}
