package co.cadshare.exceptions;

import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ResponseStatus;

@ResponseStatus(value = HttpStatus.BAD_REQUEST)
public class BadClientRequestException extends RuntimeException {

    private static final long serialVersionUID = 1L;

    public BadClientRequestException(String message) {
        super(message);
    }

    public BadClientRequestException(String message, Throwable throwable) {
        super(message, throwable);
    }
}
