Feature: Manage Addresses

  Scenario: Get All Addresses for Dealer User
    Given I am a Dealer with <NAME_EMAIL>
    When I attempt to get all addresses mapped to me
    Then all addresses mapped to me should be returned

  Scenario: Get All Addresses for Manufacturer User
    Given I am a Manufacturer with <NAME_EMAIL>
    When I attempt to get all addresses mapped to me
    Then all addresses mapped to me should be returned

  Scenario: Do Not Get Addresses for Different Manufacturer User
    Given I am a Manufacturer with <NAME_EMAIL>
    When I attempt to get all addresses mapped to another manufacturer user
    Then no addresses mapped to me should be returned
