package co.cadshare.masterParts.adapters.database;

import co.cadshare.shared.adapters.database.BlazeQueryRepo;
import co.cadshare.shared.adapters.database.QMasterPartEntity;
import com.blazebit.persistence.CriteriaBuilderFactory;
import com.blazebit.persistence.querydsl.BlazeJPAQuery;
import com.blazebit.persistence.view.EntityViewManager;
import com.querydsl.core.types.dsl.Expressions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.EntityManager;
import java.util.ArrayList;
import java.util.List;

@Repository
public class MasterPartSupersessionQueryRepo extends BlazeQueryRepo {

    @Autowired
    public MasterPartSupersessionQueryRepo(EntityManager entityManager,
                                           EntityViewManager entityViewManager,
                                           CriteriaBuilderFactory factory) {
        super(entityManager, entityViewManager, factory);
    }

    public List<SupersededMasterPartEntityCte> getSupersedingMasterParts(String partNumber, int manufacturerId) {
        return getMasterPartSupersessionEntityCte()
                .select(QSupersededMasterPartEntityCte.supersededMasterPartEntityCte)
                .from(QSupersededMasterPartEntityCte.supersededMasterPartEntityCte)
                .where(QSupersededMasterPartEntityCte.supersededMasterPartEntityCte.maxSupersessionPartNumber.eq(partNumber))
                .where(QSupersededMasterPartEntityCte.supersededMasterPartEntityCte.manufacturerId.eq(manufacturerId))
                .orderBy(QSupersededMasterPartEntityCte.supersededMasterPartEntityCte.reverseIndex.desc())
                .fetch();
    }

    public SupersededMasterPartEntityCte getMaxSupersedingMasterPart(String partNumber, int manufacturerId) {

        return getMasterPartSupersessionEntityCte()
                .select(QSupersededMasterPartEntityCte.supersededMasterPartEntityCte)
                .from(QSupersededMasterPartEntityCte.supersededMasterPartEntityCte)
                .where(QSupersededMasterPartEntityCte.supersededMasterPartEntityCte.partNumber.eq(partNumber))
                .where(QSupersededMasterPartEntityCte.supersededMasterPartEntityCte.manufacturerId.eq(manufacturerId))
                .fetchOne();
    }

    public List<SupersededMasterPartEntityCte> getMaxSupersedingMasterParts(List<String> partNumbers, int manufacturerId) {

        return getMasterPartSupersessionEntityCte()
                .select(QSupersededMasterPartEntityCte.supersededMasterPartEntityCte)
                .from(QSupersededMasterPartEntityCte.supersededMasterPartEntityCte)
                .where(QSupersededMasterPartEntityCte.supersededMasterPartEntityCte.partNumber.in(partNumbers))
                .where(QSupersededMasterPartEntityCte.supersededMasterPartEntityCte.manufacturerId.eq(manufacturerId))
                .fetch();
    }

    private BlazeJPAQuery<SupersededMasterPartEntityCte> getMasterPartSupersessionEntityCte() {

        QSupersededMasterPartEntityCte parentMasterPart = new QSupersededMasterPartEntityCte("parentMasterPart");

        return new BlazeJPAQuery<SupersededMasterPartEntityCte>(entityManager, factory)
                .withRecursive(QSupersededMasterPartEntityCte.supersededMasterPartEntityCte, new BlazeJPAQuery<>().unionAll(
                        new BlazeJPAQuery<>()
                                .from(QMasterPartEntity.masterPartEntity)
                                .bind(QSupersededMasterPartEntityCte.supersededMasterPartEntityCte.masterPartId, QMasterPartEntity.masterPartEntity.id)
                                .bind(QSupersededMasterPartEntityCte.supersededMasterPartEntityCte.partNumber, QMasterPartEntity.masterPartEntity.partNumber)
                                .bind(QSupersededMasterPartEntityCte.supersededMasterPartEntityCte.maxSupersessionPartId, QMasterPartEntity.masterPartEntity.id)
                                .bind(QSupersededMasterPartEntityCte.supersededMasterPartEntityCte.supersessionPartNumber, QMasterPartEntity.masterPartEntity.supersessionPartNumber)
                                .bind(QSupersededMasterPartEntityCte.supersededMasterPartEntityCte.maxSupersessionPartNumber, QMasterPartEntity.masterPartEntity.partNumber)
                                .bind(QSupersededMasterPartEntityCte.supersededMasterPartEntityCte.manufacturerId, QMasterPartEntity.masterPartEntity.manufacturer.id)
                                .bind(QSupersededMasterPartEntityCte.supersededMasterPartEntityCte.reverseIndex, Expressions.constant(0))
                                .where(QMasterPartEntity.masterPartEntity.supersessionPartNumber.isNull()),
                        new BlazeJPAQuery<>()
                                .from(QMasterPartEntity.masterPartEntity)
                                .from(parentMasterPart)
                                .bind(QSupersededMasterPartEntityCte.supersededMasterPartEntityCte.masterPartId, QMasterPartEntity.masterPartEntity.id)
                                .bind(QSupersededMasterPartEntityCte.supersededMasterPartEntityCte.partNumber, QMasterPartEntity.masterPartEntity.partNumber)
                                .bind(QSupersededMasterPartEntityCte.supersededMasterPartEntityCte.maxSupersessionPartId, parentMasterPart.maxSupersessionPartId)
                                .bind(QSupersededMasterPartEntityCte.supersededMasterPartEntityCte.supersessionPartNumber, QMasterPartEntity.masterPartEntity.supersessionPartNumber)
                                .bind(QSupersededMasterPartEntityCte.supersededMasterPartEntityCte.maxSupersessionPartNumber, parentMasterPart.maxSupersessionPartNumber)
                                .bind(QSupersededMasterPartEntityCte.supersededMasterPartEntityCte.manufacturerId, QMasterPartEntity.masterPartEntity.manufacturer.id)
                                .bind(QSupersededMasterPartEntityCte.supersededMasterPartEntityCte.reverseIndex, parentMasterPart.reverseIndex.add(1))
                                .where(QMasterPartEntity.masterPartEntity.supersessionPartNumber.eq(parentMasterPart.partNumber)
                                        .and(QMasterPartEntity.masterPartEntity.manufacturer.id.eq(parentMasterPart.manufacturerId)))
                ));
    }
}
