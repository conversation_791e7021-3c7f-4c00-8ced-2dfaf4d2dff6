package co.cadshare.inventory.adapters.api.web;

import co.cadshare.inventory.boundary.PriceListService;
import co.cadshare.shared.core.manufacturer.ManufacturerConfig;
import co.cadshare.inventory.core.PriceListUploadFile;
import co.cadshare.shared.core.user.User;
import co.cadshare.response.PriceListUploadAudit;
import co.cadshare.services.*;
import com.google.gson.JsonObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;

@RestController
@RequestMapping("/priceList")
@Slf4j
public class PriceListUploadController {

    @Autowired
    private PriceListService priceListService;

    @Autowired
    private ManufacturerService manufacturerService;
    @Autowired
    private PriceListFileUploadProcessor csvProcessor;
    @Autowired
    private VisibilityPriceListServiceGateway visibilityPriceListService;
    @Autowired
    private VisibilityPartsServiceGateway visibilityPartsService;

    @PostMapping
    public ResponseEntity<String> priceListFileUpload(@AuthenticationPrincipal User user, @RequestParam("file") MultipartFile file) {
        log.info("Received price list file [{}] for processing for manufacturer id", file.getName(), user.getManufacturerId());
        // We will do some quick validation and then hand back to the client. In the background
        // we'll process the file and commit to database
        try {
            ManufacturerConfig config = manufacturerService.getManufacturerConfigById(user.getManufacturerId());
            String priceListPartNumber = (config != null && config.getPriceListPartNumber() != null) ? config.getPriceListPartNumber() : "Part Number";
            PriceListUploadFile priceListFile = csvProcessor.convert(file, priceListPartNumber, user.getManufacturerId());
            priceListService.save(user, priceListFile);
        } catch (PriceListUploadFile.UnparseablePriceListFileException | IOException e) {
            JsonObject response = new JsonObject();
            response.addProperty("message", "Error processing file: " + e.getMessage());
            return new ResponseEntity<>(response.toString(), HttpStatus.BAD_REQUEST);
        }
        return new ResponseEntity<>( HttpStatus.ACCEPTED);
    }

    @GetMapping
    @PreAuthorize("hasRole('ROLE_MANUFACTURER')")
    public HttpEntity<PriceListUploadAudit> getSoftCopyDetailsForViewable(@AuthenticationPrincipal User currentUser) {
        return new ResponseEntity<>(priceListService.getPriceListUploadHistory(currentUser), HttpStatus.OK);
    }


    @GetMapping(value = "/getSupremePriceLists")
    public HttpEntity<Boolean> getSupremePriceLists(@AuthenticationPrincipal User currentUser) {
        visibilityPriceListService.getPriceLists();
        return new ResponseEntity<>(true, HttpStatus.OK);
    }

    @GetMapping(value = "/getSupremePartData")
    public HttpEntity<Boolean> getSupremePartData(@AuthenticationPrincipal User currentUser) {
        visibilityPartsService.getParts();
        return new ResponseEntity<>(true, HttpStatus.OK);
    }
}
