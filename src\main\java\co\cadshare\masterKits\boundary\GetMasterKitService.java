package co.cadshare.masterKits.boundary;

import co.cadshare.masterKits.core.MasterKit;
import co.cadshare.shared.boundary.GetService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.util.List;

@Service
public class GetMasterKitService extends GetService<MasterKit, Integer> {

    private MasterKitQueryPort complexQueryPort;

    @Autowired
    public GetMasterKitService(MasterKitQueryPort queryPort) {
        super(queryPort);
        this.complexQueryPort = queryPort;
    }

    public List<MasterKit> getMasterKitsForManufacturer(int manufacturerId){
        return this.complexQueryPort.getListForManufacturer(manufacturerId);
    }

}
