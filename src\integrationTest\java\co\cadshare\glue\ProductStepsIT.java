package co.cadshare.glue;

import co.cadshare.modelMgt.products.adapters.api.GetProductListResponseDto;
import co.cadshare.modelMgt.products.adapters.api.web.GetManufacturerProductListResponseDto;
import io.cucumber.java.en.And;
import io.cucumber.java.en.Then;
import io.cucumber.java.en.When;
import org.springframework.beans.factory.annotation.Autowired;

import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertNotNull;

public class ProductStepsIT {

	private final CadshareIT cadshare;

	@Autowired
	public ProductStepsIT(CadshareIT cadshare) {
		this.cadshare = cadshare;
	}


	@Then("I can view all Products belonging to my Manufacturer and {}")
	public void iCanViewAllProductsBelongingToMyManufacturer(String productRangeName) {
		GetProductListResponseDto Products = cadshare.loggedInUser().getProductsForRange(productRangeName);
		assertNotNull(Products);
		assertFalse(Products.getProducts().isEmpty());
	}

	@And("a Product with name {} doesn't exist")
	public void aProductWithNameDoesnTExist(String name) {
		/*
		GetProductListResponseDto publicationCategories = auth.getLoggedInUser().getPublicationCategoriesList();
		boolean found = publicationCategories.getPublicationCategories().stream()
				.anyMatch(pc -> pc.getName().equals(name));
		assertFalse(found);
		 */
	}

	@When("I create a new Product named {}")
	public void iCreateANewProductNamed(String name) {
		//auth.getLoggedInUser().createProduct(name);
	}

	@Then("a Product with name {} exists")
	public void aNewProductWithNameNowExists(String name) {
		/*
		GetProductListResponseDto publicationCategories = auth.getLoggedInUser().getPublicationCategoriesList();
		boolean found = publicationCategories.getPublicationCategories().stream()
				.anyMatch(pc -> pc.getName().equals(name));
		assertTrue(found);
		 */
	}

	@When("I delete the Product named {}")
	public void iDeleteTheProductNamed(String name) {
		/*
		GetProductListResponseDto publicationCategories = auth.getLoggedInUser().getPublicationCategoriesList();
		Optional<GetProductListItemResponseDto> found = publicationCategories.getPublicationCategories().stream()
				.filter(pc -> pc.getName().equals(name)).findFirst();
		assertTrue(found.isPresent());
		auth.getLoggedInUser().deleteProduct(found.get().getId().toString());

		 */
	}

	@When("I update the Product named {} to {}")
	public void iUpdateTheProductNamed(String oldName, String newName) {
		/*
		GetProductListResponseDto publicationCategories = auth.getLoggedInUser().getPublicationCategoriesList();
		Optional<GetProductListItemResponseDto> found = publicationCategories.getPublicationCategories().stream()
				.filter(pc -> pc.getName().equals(oldName)).findFirst();
		assertTrue(found.isPresent());
		auth.getLoggedInUser().updateProduct(newName, found.get().getId().toString());

		 */
	}

	@When("I get the Product named {}")
	public void iGetTheProductNamed(String name) {
		/*
		GetProductListResponseDto publicationCategories = auth.getLoggedInUser().getPublicationCategoriesList();
		Optional<GetProductListItemResponseDto> found = publicationCategories.getPublicationCategories().stream()
				.filter(pc -> pc.getName().equals(name)).findFirst();
		assertTrue(found.isPresent());
		GetProductResponseDto Product = auth.getLoggedInUser().getProduct(found.get().getId().toString());
		assertNotNull(Product);
		assertEquals(name, Product.getName());

		 */
	}

	@Then("I can view all Products belonging to my Manufacturer")
	public void iCanViewAllProductsBelongingToMyManufacturer() {
		GetManufacturerProductListResponseDto Products = cadshare.loggedInUser().getAllProducts();
		assertNotNull(Products);
		assertFalse(Products.getProducts().isEmpty());
	}
}
