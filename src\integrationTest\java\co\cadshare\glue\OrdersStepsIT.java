package co.cadshare.glue;

import co.cadshare.orders.adapters.api.web.GetOrderListResponseDto;
import co.cadshare.orders.core.Order;
import co.cadshare.orders.core.OrderItem;
import co.cadshare.utils.ObjectUtilsExtension;
import io.cucumber.java.en.And;
import io.cucumber.java.en.Given;
import io.cucumber.java.en.Then;
import io.cucumber.java.en.When;
import lombok.experimental.ExtensionMethod;
import org.springframework.beans.factory.annotation.Autowired;

import javax.persistence.EntityManager;
import java.math.BigInteger;

import static org.junit.Assert.*;

@ExtensionMethod(ObjectUtilsExtension.class)
public class OrdersStepsIT {

	@Autowired
	protected EntityManager entityManager;
	protected UserIT loggedInUser;
	private final CadshareIT cadshare;

	@Autowired
	public OrdersStepsIT(CadshareIT cadshare) {
		this.cadshare = cadshare;
	}


    private int manufacturerOrderCount = 0;
    private String manufacturerAndDealerPlusSql = "SELECT COUNT(*)\n" +
            "FROM orders o,\n" +
            "manufacturersubentity mse\n" +
            "WHERE o.manufacturersubentityid = mse.manufacturersubentityid\n" +
            "AND mse.manufacturerid = %s \n" +
            "AND o.orderstatus IN ('SUBMITTED', 'PREPARING')\n";

    private Order order;

    @Given("I shouldn't see Orders placed with the DealerPlus")
    public void iShouldntSeeOrdersPlacedWithTheDealerPlus() {
    }

    @When("I place an enquiry for this {listOfStrings}")
    public void iPlaceAnEnquiryForThisListOfParts(String listOfParts) {
        //todo
    }

    @Then("an enquiry should be created with this {listOfStrings} as line items")
    public void anEnquiryShouldBeCreatedWithThisListOfPartsAsLineItems(String listOfParts) {
        //todo
    }

    @When("a number of Enquiries have been placed with this Manufacturer and DealerPlus")
    public void aNumberOfEnquiriesHaveBeenPlacedWithThisManufacturerAndDealerPlus() {
        manufacturerAndDealerPlusSql = String.format(manufacturerAndDealerPlusSql, cadshare.loggedInUser().getManufacturerId());
        int totalCount = ((BigInteger)entityManager.createNativeQuery(manufacturerAndDealerPlusSql).getSingleResult()).intValue();

        String manufacturerOnlySql = manufacturerAndDealerPlusSql.concat("AND mse.parentsubentityid IS NULL");
        manufacturerOrderCount = ((BigInteger)entityManager.createNativeQuery(manufacturerOnlySql).getSingleResult()).intValue();

        assertTrue(manufacturerOrderCount > 0);
        assertTrue(totalCount > manufacturerOrderCount);
    }

    @Then("I can only list the Enquiries for this Manufacturer")
    public void iCanOnlyListTheEnquiriesForThisManufacturer() {

        GetOrderListResponseDto ordersResponse = cadshare.loggedInUser().getEnquiries();
        assertEquals(manufacturerOrderCount, ordersResponse.getOrders().size());
    }

    @And("I have already placed an enquiry with {} for this {}")
    public void iHaveAlreadyPlacedAnEnquiryWithForThis(int orderId, String listOfParts) {
        getOrderAndVerifyOrderItems(orderId, listOfParts.split(","));
    }

    @Then("the enquiry with {} should have this {}")
    public void theEnquiryShouldHaveThis(int orderId, String listOfParts) {
        getOrderAndVerifyOrderItems(orderId, listOfParts.split(","));
    }

    @When("I add {} of part with number {} to the enquiry with {}")
    public void iAddOfToTheEnquiryWith(int quantity, String partToBeAdded, int orderId) {
        cadshare.loggedInUser().addMasterPartToOrder(orderId, partToBeAdded, quantity);
    }


    @When("I add {} of kit with number {} to the enquiry with {}")
    public void iAddOfKitWithNumberToTheEnquiryWith(int quantity, String partToBeAdded, int orderId) {
	    cadshare.loggedInUser().addMasterKitToOrder(orderId, partToBeAdded, quantity);
    }

    @And("the enquiry with {} with this {} should have {}")
    public void enquiryShouldHave(int orderId, String masterPart, int quantity) {
        order = cadshare.loggedInUser().getOrder(orderId);
        OrderItem item = order.getOrderItems()
                .stream().
                filter(orderItem -> orderItem.getPartNumber().equals(masterPart))
                .findFirst().get();
        assertEquals(quantity, item.getQuantity(), 0);
    }


    private void getOrderAndVerifyOrderItems(int orderId, String[] listOfParts) {
        order = cadshare.loggedInUser().getOrder(orderId);
        assertEquals(listOfParts.length, order.getOrderItems().size());
        for (String part : listOfParts) {
            assertTrue(order.getOrderItems()
                    .stream()
                    .anyMatch(orderItem -> orderItem.getPartNumber().equals(part)));
        }
    }

}