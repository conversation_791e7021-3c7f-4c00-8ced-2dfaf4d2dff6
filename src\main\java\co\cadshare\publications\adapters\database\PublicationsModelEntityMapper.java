package co.cadshare.publications.adapters.database;

import co.cadshare.models.adapters.database.ModelCommandEntity;
import co.cadshare.models.core.Model;
import co.cadshare.shared.adapters.database.ProductEntityMapper;
import org.mapstruct.AfterMapping;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper(uses = {ProductEntityMapper.class, PublicationEntityMapper.class, PublicationsPartEntityMapper.class})
public interface PublicationsModelEntityMapper {

    PublicationsModelEntityMapper Instance = Mappers.getMapper(PublicationsModelEntityMapper.class);

    Model clone(Model model);

    @Mapping(source="product.id", target="machineId")
    @Mapping(source="product.name", target="machineName")
    @Mapping(source="createdBy.lastName", target="createdByUserLastName")
    @Mapping(source="createdBy.firstName", target="createdByUserFirstName")
    @Mapping(source="createdBy.id", target="createdByUserId")
    Model entityToCore(PublicationsModelEntity entity);

    ModelCommandEntity coreToEntity(Model model);

    @AfterMapping
    default void setFullName(@MappingTarget PublicationsModelEntity entity, Model model) {
        model.setCreatedByUserFullName(entity.getCreatedBy().getFirstName() + " " + entity.getCreatedBy().getLastName());
    }

    List<Model> entitiesToCores(List<PublicationsModelEntity> entities);

    List<PublicationsModelEntity> coresToEntities(List<Model> models);

}
