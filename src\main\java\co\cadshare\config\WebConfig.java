package co.cadshare.config;

import org.springframework.boot.web.client.RestTemplateBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import java.time.Duration;

@Configuration
public class WebConfig implements WebMvcConfigurer {

//    private final PermissionsService permissionsService;

//    public WebConfig(PermissionsService permissionsService) {
//        this.permissionsService = permissionsService;
//    }

//    @Override
//    public void addInterceptors(final InterceptorRegistry registry) {
//        registry
//            .addInterceptor(new ModelPermissionsInterceptor(permissionsService))
//            .addPathPatterns(
//                "/model/*/statedetails/*",
//                "/model/*/statedetails",
//                "/model/*/autodeskresources");
//    }

    @Bean
    public RestTemplate restTemplate() {
        return new RestTemplateBuilder()
                .defaultHeader(HttpHeaders.ACCEPT, MediaType.APPLICATION_JSON_VALUE)
                .setConnectTimeout(Duration.ofMillis(5000))
                .setReadTimeout(Duration.ofMillis(20000))
                .build();
    }
}
