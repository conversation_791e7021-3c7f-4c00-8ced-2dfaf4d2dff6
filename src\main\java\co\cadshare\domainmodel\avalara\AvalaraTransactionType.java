package co.cadshare.domainmodel.avalara;

public enum AvalaraTransactionType {
    SALES_ORDER("SalesOrder"),
    SALES_INVOICE("SalesInvoice"),
    PURCHASE_ORDER("PurchaseOrder"),
    PURCHASE_INVOICE("PurchaseInvoice"),
    RETURN_ORDER("ReturnOrder"),
    RETURN_INVOICE("ReturnInvoice"),
    INVENTORY_TRANSFER_ORDER("InventoryTransferOrder"),
    INVENTORY_TRANSFER_INVOICE("InventoryTransferInvoice"),
    REVERSE_CHARGE_ORDER("ReverseChargeOrder"),
    REVERSE_CHARGE_INVOICE("ReverseChargeInvoice"),
    CUSTOMS_ORDER("CustomsOrder"),
    CUSTOMS_INVOICE("CustomsInvoice"),
    ANY("Any");


    private String keyName;
    AvalaraTransactionType(String keyName) {
        this.keyName = keyName;
    }

    public String getKeyName() {
        return keyName;
    }
}
