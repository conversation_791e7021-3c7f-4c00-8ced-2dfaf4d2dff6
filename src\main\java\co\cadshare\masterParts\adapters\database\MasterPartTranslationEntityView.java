package co.cadshare.masterParts.adapters.database;

import co.cadshare.shared.adapters.database.MasterPartTranslationEntity;
import com.blazebit.persistence.view.EntityView;
import com.blazebit.persistence.view.IdMapping;
import com.blazebit.persistence.view.Mapping;

@EntityView(MasterPartTranslationEntity.class)
public interface MasterPartTranslationEntityView {

    @IdMapping
    Integer getId();

    @Mapping
    String getDescription();

    @Mapping("language.code")
    String getCode();
}
