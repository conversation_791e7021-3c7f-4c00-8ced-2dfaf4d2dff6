package co.cadshare.modelMgt.ranges.boundary;

import co.cadshare.modelMgt.ranges.core.Range;
import co.cadshare.shared.boundary.GetService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.util.List;

@Service
public class GetRangeService extends GetService<Range, Integer> {

    private RangeQueryPort complexQueryPort;

    @Autowired
    public GetRangeService(RangeQueryPort queryPort) {
        super(queryPort);
        this.complexQueryPort = queryPort;
    }

    public List<Range> getRangesForManufacturer(int manufacturerId){
        return this.complexQueryPort.getListForManufacturer(manufacturerId);
    }

}
