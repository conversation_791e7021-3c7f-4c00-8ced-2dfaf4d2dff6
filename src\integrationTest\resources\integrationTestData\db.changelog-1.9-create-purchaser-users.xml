<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                   http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.0.xsd
">

    <!-- Caterpillar -->

    <changeSet author="AndyB" id="1.9-integration-test-data-create-purchaser-users-1">
        <sql>
            INSERT INTO manufacturersubentityusers(userid, manufacturersubentityid)
            VALUES ((select userid
            FROM users
            WHERE emailaddress = '<EMAIL>'),
            (select manufacturersubentityid
            from manufacturersubentity
            where name like '%Caterpillar Dealer %'));
        </sql>
    </changeSet>

    <changeSet author="AndyB" id="1.9-integration-test-data-create-purchaser-users-2">
        <sql>
            INSERT INTO manufacturersubentityusers(userid, manufacturersubentityid)
            VALUES ((select userid
            FROM users
            WHERE emailaddress = '<EMAIL>'),
            (select manufacturersubentityid
            from manufacturersubentity
            where name = 'Caterpillar DealerPlus 1'));
        </sql>
    </changeSet>

    <changeSet author="AndyB" id="1.9-integration-test-data-create-purchaser-users-3">
        <sql>
            INSERT INTO manufacturersubentityusers(userid, manufacturersubentityid)
            VALUES ((select userid
            FROM users
            WHERE emailaddress = '<EMAIL>'),
            (select manufacturersubentityid
            from manufacturersubentity
            where name like '%Caterpillar Customer for DealerPlus 1%'));
        </sql>
    </changeSet>

    <!-- JCB -->

    <changeSet author="AndyB" id="1.9-integration-test-data-create-purchaser-users-4">
        <sql>
            INSERT INTO manufacturersubentityusers(userid, manufacturersubentityid)
            VALUES ((select userid
            FROM users
            WHERE emailaddress = '<EMAIL>'),
            (select manufacturersubentityid
            from manufacturersubentity
            where name like '%JCB Dealer %'));
        </sql>
    </changeSet>

    <changeSet author="AndyB" id="1.9-integration-test-data-create-purchaser-users-5">
        <sql>
            INSERT INTO manufacturersubentityusers(userid, manufacturersubentityid)
            VALUES ((select userid
            FROM users
            WHERE emailaddress = '<EMAIL>'),
            (select manufacturersubentityid
            from manufacturersubentity
            where name like '%JCB DealerPlus %'));
        </sql>
    </changeSet>

    <changeSet author="AndyB" id="1.9-integration-test-data-create-purchaser-users-6">
        <sql>
            INSERT INTO manufacturersubentityusers(userid, manufacturersubentityid)
            VALUES ((select userid
            FROM users
            WHERE emailaddress = '<EMAIL>'),
            (select manufacturersubentityid
            from manufacturersubentity
            where name like '%JCB Customer for DealerPlus %'));
        </sql>
    </changeSet>


    <!-- Liebherr -->

    <changeSet author="AndyB" id="1.9-integration-test-data-create-purchaser-users-7">
        <sql>
            INSERT INTO manufacturersubentityusers(userid, manufacturersubentityid)
            VALUES ((select userid
            FROM users
            WHERE emailaddress = '<EMAIL>'),
            (select manufacturersubentityid
            from manufacturersubentity
            where name like '%Liebherr Dealer %'));
        </sql>
    </changeSet>

    <changeSet author="AndyB" id="1.9-integration-test-data-create-purchaser-users-8">
        <sql>
            INSERT INTO manufacturersubentityusers(userid, manufacturersubentityid)
            VALUES ((select userid
            FROM users
            WHERE emailaddress = '<EMAIL>'),
            (select manufacturersubentityid
            from manufacturersubentity
            where name like '%Liebherr DealerPlus %'));
        </sql>
    </changeSet>

    <changeSet author="AndyB" id="1.9-integration-test-data-create-purchaser-users-9">
        <sql>
            INSERT INTO manufacturersubentityusers(userid, manufacturersubentityid)
            VALUES ((select userid
            FROM users
            WHERE emailaddress = '<EMAIL>'),
            (select manufacturersubentityid
            from manufacturersubentity
            where name like '%Liebherr Customer for DealerPlus %'));
        </sql>
    </changeSet>


    <!-- Terex -->

    <changeSet author="AndyB" id="1.9-integration-test-data-create-purchaser-users-10">
        <sql>
            INSERT INTO manufacturersubentityusers(userid, manufacturersubentityid)
            VALUES ((select userid
            FROM users
            WHERE emailaddress = '<EMAIL>'),
            (select manufacturersubentityid
            from manufacturersubentity
            where name like '%Terex Dealer %'));
        </sql>
    </changeSet>

    <changeSet author="AndyB" id="1.9-integration-test-data-create-purchaser-users-11">
        <sql>
            INSERT INTO manufacturersubentityusers(userid, manufacturersubentityid)
            VALUES ((select userid
            FROM users
            WHERE emailaddress = '<EMAIL>'),
            (select manufacturersubentityid
            from manufacturersubentity
            where name like '%Terex DealerPlus %'));
        </sql>
    </changeSet>

    <changeSet author="AndyB" id="1.9-integration-test-data-create-purchaser-users-12">
        <sql>
            INSERT INTO manufacturersubentityusers(userid, manufacturersubentityid)
            VALUES ((select userid
            FROM users
            WHERE emailaddress = '<EMAIL>'),
            (select manufacturersubentityid
            from manufacturersubentity
            where name like '%Terex Customer for DealerPlus %'));
        </sql>
    </changeSet>

    <changeSet author="AndyB" id="1.9-integration-test-data-create-purchaser-users-13">
        <sql>
            INSERT INTO manufacturersubentityusers(userid, manufacturersubentityid)
            VALUES ((select userid
            FROM users
            WHERE emailaddress = '<EMAIL>'),
            (select manufacturersubentityid
            from manufacturersubentity
            where name like '%Caterpillar Dealer-2%'));
        </sql>
    </changeSet>

    <changeSet author="AndyB" id="1.9-integration-test-data-create-purchaser-users-14">
        <sql>
            INSERT INTO manufacturersubentityusers(userid, manufacturersubentityid)
            VALUES ((select userid
            FROM users
            WHERE emailaddress = '<EMAIL>'),
            (select manufacturersubentityid
            from manufacturersubentity
            where name like '%Caterpillar DealerPlus-2%'));
        </sql>
    </changeSet>

    <changeSet author="AndyB" id="1.9-integration-test-data-create-purchaser-users-15">
        <sql>
            INSERT INTO manufacturersubentityusers(userid, manufacturersubentityid)
            VALUES ((select userid
            FROM users
            WHERE emailaddress = '<EMAIL>'),
            (select manufacturersubentityid
            from manufacturersubentity
            where name like '%Caterpillar Customer for DealerPlus-2%'));
        </sql>
    </changeSet>
</databaseChangeLog>
