package co.cadshare.models.boundary;

import co.cadshare.models.core.model.AutodeskStatus;
import co.cadshare.shared.boundary.QueryPort;
import co.cadshare.models.core.Model;
import java.util.List;

public interface ModelQueryPort extends QueryPort<Model, Integer> {

    List<Model> getModelsListForProduct(Integer id);
    List<Model> getModelsListForProduct(Integer id, AutodeskStatus status, boolean setupComplete);

    List<Model> getPublishedModelsForMasterPart(PublishedModelsForMasterPartSearchCriteria searchCriteria);

}
