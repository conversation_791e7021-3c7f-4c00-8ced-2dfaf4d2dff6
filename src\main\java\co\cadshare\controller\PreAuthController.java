/*
package co.cadshare.controller;

import co.cadshare.domainmodel.auth.PreAuthResponseType;
import co.cadshare.preauth.CadshareTokenCreator;
import co.cadshare.preauth.PreAuthRequest;
import co.cadshare.preauth.PreAuthResponse;
import co.cadshare.preauth.ThirdPartyAuthenticationValidator;
import co.cadshare.preauth.ThirdPartyAuthenticationValidatorFactory;
import com.auth0.jwt.exceptions.TokenExpiredException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.security.oauth2.common.OAuth2AccessToken;
import org.springframework.web.bind.annotation.*;

@RestController
@Slf4j
public class PreAuthController {

    private ThirdPartyAuthenticationValidatorFactory thirdPartyAuthenticationValidatorFactory;
    private CadshareTokenCreator cadshareTokenCreator;

    public PreAuthController(ThirdPartyAuthenticationValidatorFactory thirdPartyAuthenticationValidatorFactory, CadshareTokenCreator cadshareTokenCreator) {
        this.thirdPartyAuthenticationValidatorFactory = thirdPartyAuthenticationValidatorFactory;
        this.cadshareTokenCreator = cadshareTokenCreator;
    }

    @PostMapping({"/preAuth", "/api/preAuth"})
    public ResponseEntity<PreAuthResponse> handlePreAuth(@RequestBody PreAuthRequest request) {
        ThirdPartyAuthenticationValidator validator = thirdPartyAuthenticationValidatorFactory.getValidatorByClient(request.getClient());

        try {
            validator.validate(request.getToken());
        } catch (TokenExpiredException e) {
            log.error("User's third party token has expired!", e);
            return new ResponseEntity<>(PreAuthResponse.builder()
                .responseType(PreAuthResponseType.EXPIRED)
                .redirect(validator.getRedirectUrl())
                .build(), HttpStatus.UNAUTHORIZED);
        }

        String username = validator.getUsername(request.getToken());
        int manufacturerId = validator.getManufacturerId();
        OAuth2AccessToken jwtAccessToken;
        try {
            jwtAccessToken = cadshareTokenCreator.createTokenForUsername(username, manufacturerId);
        } catch(UsernameNotFoundException e) {
            log.error("PreAuth login failed - user: " + username + " not found.", e);
            return new ResponseEntity<>(PreAuthResponse.builder()
                .responseType(PreAuthResponseType.NOT_FOUND)
                .build(), HttpStatus.NOT_FOUND);
        }

        return new ResponseEntity<>(PreAuthResponse.builder()
            .responseType(PreAuthResponseType.SUCCESS)
            .cadshareToken(jwtAccessToken)
            .build(), HttpStatus.OK);
    }

    @ExceptionHandler(RuntimeException.class)
    public ResponseEntity<PreAuthResponse> handleGenericFailure() {
        return new ResponseEntity<>(PreAuthResponse.builder()
            .responseType(PreAuthResponseType.UNKNOWN_FAILURE)
            .build(), HttpStatus.INTERNAL_SERVER_ERROR);
    }
}*/
