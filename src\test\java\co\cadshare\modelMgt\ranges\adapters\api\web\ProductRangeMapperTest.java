package co.cadshare.modelMgt.ranges.adapters.api.web;

import co.cadshare.domainmodel.range.Range;
import org.junit.Test;

import static org.junit.Assert.assertEquals;

public class ProductRangeMapperTest {

	@Test
	public void testSingleRangeMapping() {
		Range range = new Range();
		range.setRangeId(1);
		range.setName("test range");
		range.setDescription("test range description");
		ProductRangeListItemDto dto = ProductRangeMapper.Instance.RangeToDto(range);
		assertEquals(range.getRangeId(), dto.getId().intValue());
		assertEquals(range.getName(), dto.getName());
	}
}
