package co.cadshare.models.core.processor.fileproperties;

import co.cadshare.models.core.MetadataObjectExtended;
import co.cadshare.utils.ObjectExtension;
import lombok.experimental.ExtensionMethod;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Component;

import java.util.LinkedHashMap;
import java.util.List;

@Component
@ExtensionMethod(ObjectExtension.class)
public class AlmabSparePartPropertyProcessor extends SparePartPropertyProcessor implements FilePropertiesProcessor, InitializingBean {
    @Override
    public void setProperties(LinkedHashMap properties, MetadataObjectExtended object) {

        // & set sparePart to true
        String sparePartIdentifier = getPropertyValue(properties);
        object.setSparePartIdentifier(sparePartIdentifier);
        if(sparePartIdentifier.isNull()) {
            object.setSparePart(true);
            object.setSellablePart(false);
        }
        else if(sparePartIdentifier.equalsIgnoreCase("yes")) {
            object.setSparePart(true);
            object.setSellablePart(true);
        } else if(sparePartIdentifier.equalsIgnoreCase("no")) {
            object.setSparePart(false);
            object.setSellablePart(false);
        }

    }

    @Override
    public List<String> getSynonyms() {
        return sparePartSynonyms;
    }

    @Override
    public void setSynonyms(List<String> synonyms) {
        sparePartSynonyms = synonyms;
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        //not checking properties as synonyms won't be set at this point.
    }
}
