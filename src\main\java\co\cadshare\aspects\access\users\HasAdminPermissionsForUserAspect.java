package co.cadshare.aspects.access.users;

import co.cadshare.aspects.BaseAspect;
import co.cadshare.exceptions.ForbiddenException;
import co.cadshare.persistence.PermissionsDao;
import co.cadshare.services.PermissionsService;
import co.cadshare.shared.core.user.User;
import co.cadshare.shared.core.user.UserType;
import co.cadshare.users.boundary.UsersService;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Aspect
@Component
public class HasAdminPermissionsForUserAspect extends BaseAspect {

    private final UsersService usersService;
    private final PermissionsDao permissionsDao;

    @Autowired
    public HasAdminPermissionsForUserAspect(UsersService usersService,
                                            PermissionsDao permissionsDao) {
        this.usersService = usersService;
        this.permissionsDao = permissionsDao;
    }

    @Pointcut("@annotation(HasAdminPermissionsForUserId)")
    public void permissionsCheckForUserId(){}

    @Before("permissionsCheckForUserId()")
    public void checkAbacPermissionsForUserId(JoinPoint joinPoint) throws ForbiddenException {
        configureInterception(joinPoint);
        Object arg = getArgumentByName(joinPoint, "userId");
        if(arg!= null) {
            int userId = (int)arg;
            User nominatedUser = usersService.findDetailsByUserid(userId);
            boolean permission;
            if (user.getUserId() == nominatedUser.getUserId())
                permission = false; //cannot edit your own account
            else if (user.isManufacturerUser() &&
		            nominatedUser.isManufacturerUser() &&
		            user.getManufacturerId().equals(nominatedUser.getManufacturerId()))
                permission = true;
	        else if (user.isManufacturerUser() && nominatedUser.isNotManufacturerUser())
	            permission = permissionsDao.hasAccessToManufacturerSubEntity(user.getManufacturerId(),
			            nominatedUser.getManufacturerSubEntityId());
			else if (user.isDealerPlusUser() &&
		            nominatedUser.isCustomerUser() || nominatedUser.isDealerUser())
		        //If user is a customer check it is in a sub entity linked to that manufacturer
		        permission = permissionsDao.hasDealerAccessToManufacturerSubEntity(user.getManufacturerSubEntityId(),
				        nominatedUser.getManufacturerSubEntityId());
			else
				permission = false;

            if (!permission) {
                logger.error(String.format("User id: %s attempted to access User id: %s", user.getUserId(), nominatedUser.getUserId()));
                throw new ForbiddenException("User does not have permission to edit nominated user");
            }
        }
    }

    @Pointcut("@annotation(HasAdminPermissionsForUser)")
    public void permissionsCheckForUser(){}

    @Before("permissionsCheckForUser()")
    public void checkAbacPermissionsForUser(JoinPoint joinPoint) throws ForbiddenException {
        configureInterception(joinPoint);
    }
}
