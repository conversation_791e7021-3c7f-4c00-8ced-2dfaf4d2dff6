package co.cadshare.modelMgt.publicationCategories.adapters.api.web;

import co.cadshare.aspects.access.roles.CanAccessPurchaser;
import co.cadshare.aspects.access.roles.CanAccessPurchaserAsManufacturerOrPurchaser;
import co.cadshare.modelMgt.publicationCategories.boundary.*;
import co.cadshare.modelMgt.publicationCategories.core.PublicationCategory;
import co.cadshare.shared.core.user.User;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * REST API for managing Publication Categories
 */
@RestController
@RequestMapping("/purchasers/{purchaser-id}/publication-categories")
public class PurchaserPublicationCategoriesController {

    private final GetPublicationCategoryService getPublicationCategoryService;

    @Autowired
    public PurchaserPublicationCategoriesController(GetPublicationCategoryService getPublicationCategoryService,
                                                    AssignPublicationCategoryService assignPublicationCategoryService){

        this.getPublicationCategoryService = getPublicationCategoryService;
    }

    @GetMapping(path = "/{publication-category-id}", produces = "application/json")
    @CanAccessPurchaser
    @ResponseStatus(HttpStatus.OK)
    @Operation(summary = "Get the details of a specific PublicationCategory")
    public ResponseEntity<GetPublicationCategoryResponseDto> getPublicationCategory(@PathVariable("purchaser-id") int purchaserId,
                                            @PathVariable("publication-category-id") Integer publicationCategoryId,
                                            @AuthenticationPrincipal User currentUser) {

        PublicationCategory publicationCategory = this.getPublicationCategoryService.get(publicationCategoryId);
        GetPublicationCategoryResponseDto getResponseDto = PublicationCategoryMapper.Instance.toGetResponseDto(publicationCategory);
        return new ResponseEntity<>(getResponseDto, HttpStatus.OK);
    }

    @GetMapping(produces = "application/json")
    @CanAccessPurchaserAsManufacturerOrPurchaser
    @ResponseStatus(HttpStatus.OK)
    @Operation(summary = "Get the summary details of the list of all PublicationCategories belonging to the Purchaser")
    public ResponseEntity<GetPublicationCategoryListResponseDto> getPublicationCategories(@PathVariable("purchaser-id") int purchaserId,
                                                                          @AuthenticationPrincipal User currentUser) {

        List<PublicationCategory> publicationCategories = this.getPublicationCategoryService.getPublicationCategoriesForPurchaser(purchaserId);
        List<GetPublicationCategoryListItemResponseDto> publicationCategoryResponses = PublicationCategoryMapper.Instance.toGetListResponseDto(publicationCategories);
        GetPublicationCategoryListResponseDto getListResponseDto = new GetPublicationCategoryListResponseDto(){{
            setPublicationCategories(publicationCategoryResponses);
        }};
        return new ResponseEntity<>(getListResponseDto, HttpStatus.OK);
    }

}
