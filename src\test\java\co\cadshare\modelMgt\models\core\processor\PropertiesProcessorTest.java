package co.cadshare.modelMgt.models.core.processor;

import co.cadshare.main.Application;
import co.cadshare.modelMgt.models.core.processor.PropertiesProcessor;
import co.cadshare.modelMgt.models.core.processor.fileproperties.*;
import co.cadshare.shared.core.manufacturer.PartNumberProcessorStrategy;
import co.cadshare.shared.core.manufacturer.ManufacturerConfig;
import co.cadshare.shared.core.manufacturer.SparePartProcessorStrategy;
import co.cadshare.modelMgt.models.core.MetadataDataExtended;
import co.cadshare.modelMgt.models.core.MetadataWrapper;
import co.cadshare.modelMgt.models.core.MetadataObjectExtended;
import com.autodesk.client.model.MetadataCollection;
import com.autodesk.client.model.MetadataData;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Answers;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.transaction.annotation.Transactional;

import java.io.IOException;
import java.io.InputStream;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class)
@Transactional
public class PropertiesProcessorTest {

    PropertiesProcessor out;
    ObjectMapper mapper = new ObjectMapper();

    @Mock
    PartNumberPropertyProcessor partNumberPropertyProcessor;
    @Mock
    DescriptionPropertyProcessor descPropertyProcessor;
    @Mock
    WeightPropertyProcessor weightPropertyProcessor;
    @Mock
    MassUnitPropertyProcessor massUnitPropertyProcessor;
    @Mock
    AlternatePartNumberPropertyProcessor alternatePartNumberPropertyProcessor;
    @Mock(answer = Answers.RETURNS_DEEP_STUBS)
    MetadataWrapper propertiesForGuid;
    @Mock(answer = Answers.RETURNS_DEEP_STUBS)
    MetadataWrapper metadataForGuid;

    ManufacturerConfig manufacturerConfig;

    @Before
    public void Before() {
        MockitoAnnotations.initMocks(this);
        out = new PropertiesProcessor(partNumberPropertyProcessor,
                descPropertyProcessor,
                weightPropertyProcessor,
                massUnitPropertyProcessor,
                alternatePartNumberPropertyProcessor,
                "File Properties",
                "<root>");
    }

    @Test
    public void processAlternatePartNumberForFrima() throws IOException {

        MetadataData metadataData = buildMetadataFor("Frima");
        List<MetadataCollection> metadataCollections = buildMetadataCollectionFor("Frima");
        manufacturerConfig = new ManufacturerConfig() {{
            setSparePartProcessorStrategy(SparePartProcessorStrategy.OTHER);
            setAlternatePartNumberSynonyms("TEILENUMMER,Artikelnummer,Part Number");
            setPartNumberNode("File Properties");
            setAlternatePartNumberSynonyms("Component Name");
            setAlternatePartNumberNode("<root>");
            setPartNumberProcessorStrategy(PartNumberProcessorStrategy.OTHER);
        }};
        when(propertiesForGuid.getMetadata().getData().getCollection()).thenReturn(metadataCollections);
        when(metadataForGuid.getMetadata().getData()).thenReturn(metadataData);

        MetadataDataExtended combinedProperties = out.getCombinedProperties(propertiesForGuid, metadataForGuid, manufacturerConfig);
        Assert.assertNotNull(combinedProperties);
        Assert.assertEquals(1, combinedProperties.getObjects().size());

        List<MetadataObjectExtended> flattenedPartsList = out.toFlatList(combinedProperties.getObjects());
        Assert.assertNotNull(flattenedPartsList);
        verify(alternatePartNumberPropertyProcessor, atLeast(1)).setProperties(any(), any());
        verify(alternatePartNumberPropertyProcessor, atMost(flattenedPartsList.size())).setProperties(any(), any());
    }

    @Test
    public void ProcessSparePartsForNotNexat() throws IOException {

        MetadataData metadataData = buildMetadataFor("Nexat");
        List<MetadataCollection> metadataCollections = buildMetadataCollectionFor("Nexat");
        manufacturerConfig = new ManufacturerConfig() {{
            setSparePartProcessorStrategy(SparePartProcessorStrategy.OTHER);
            setSparePartIdentifierNode("File Properties");
            setSparePartIdentifierSynonyms("DB_SPARE_PART_CHAR");

            setPartNumberProcessorStrategy(PartNumberProcessorStrategy.OTHER);
        }};
        when(propertiesForGuid.getMetadata().getData().getCollection()).thenReturn(metadataCollections);
        when(metadataForGuid.getMetadata().getData()).thenReturn(metadataData);

        MetadataDataExtended combinedProperties = out.getCombinedProperties(propertiesForGuid, metadataForGuid, manufacturerConfig);
        Assert.assertNotNull(combinedProperties);
        Assert.assertEquals(1, combinedProperties.getObjects().size());

        List<MetadataObjectExtended> flattenedPartsList = out.toFlatList(combinedProperties.getObjects());
        Assert.assertNotNull(flattenedPartsList);
        Assert.assertEquals(317, flattenedPartsList.size());
        long numberOfSpareParts = flattenedPartsList.stream().filter(p -> p.isSparePart()).count();
        MetadataObjectExtended outlier = flattenedPartsList.stream().filter(p -> !p.isSparePart()).findFirst().get();
        Assert.assertEquals(316, numberOfSpareParts);
    }

    @Test
    public void ProcessSparePartsForNexat() throws IOException {

        MetadataData metadataData = buildMetadataFor("Nexat");
        List<MetadataCollection> metadataCollections = buildMetadataCollectionFor("Nexat");
        manufacturerConfig = new ManufacturerConfig() {{
            setSparePartProcessorStrategy(SparePartProcessorStrategy.NEXAT);
            setSparePartIdentifierNode("File Properties");
            setSparePartIdentifierSynonyms("DB_SPARE_PART_CHAR");
            setPartNumberProcessorStrategy(PartNumberProcessorStrategy.OTHER);
        }};
        when(propertiesForGuid.getMetadata().getData().getCollection()).thenReturn(metadataCollections);
        when(metadataForGuid.getMetadata().getData()).thenReturn(metadataData);

        MetadataDataExtended combinedProperties = out.getCombinedProperties(propertiesForGuid, metadataForGuid, manufacturerConfig);
        Assert.assertNotNull(combinedProperties);
        Assert.assertEquals(1, combinedProperties.getObjects().size());

        List<MetadataObjectExtended> flattenedPartsList = out.toFlatList(combinedProperties.getObjects());
        Assert.assertNotNull(flattenedPartsList);
        Assert.assertEquals(317, flattenedPartsList.size());
        long numberOfSpareParts = flattenedPartsList.stream().filter(p -> p.isSparePart()).count();
        Assert.assertEquals(143, numberOfSpareParts);
    }

    private MetadataData buildMetadataFor(String manufacturerLabel) throws IOException {

        InputStream input = getClass().getResourceAsStream(String.format("/modelMetadata/%sMetadataExtended.json", manufacturerLabel));
        MetadataData metadataData = mapper.readValue(input, MetadataData.class);
        return metadataData;
    }

    private List<MetadataCollection> buildMetadataCollectionFor(String manufacturerLabel) throws IOException {
        InputStream input = getClass().getResourceAsStream(String.format("/modelMetadata/%sMetadataCollections.json", manufacturerLabel));
        List<MetadataCollection> metadataCollections = mapper.readValue(input, new TypeReference<List<MetadataCollection>>() {
        });
        return metadataCollections;
    }
}