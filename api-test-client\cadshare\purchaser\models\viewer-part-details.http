
# @name cadshareAuthResponse

POST {{CADSHARE_URL}}/oauth/token
Content-Type: application/x-www-form-urlencoded
Authorization: Basic Y2xpZW50YXBwOnNlY3JldA==

username={{DEALER_USER_ID}}
&password={{STANDARD_PWD}}
&grant_type=password


###

// view Part Details

@cadshareToken = Bearer {{cadshareAuthResponse.response.body.access_token}}

# @name modelSearchResponse
GET {{CADSHARE_URL}}/model/526/part/5866/viewerDetails?language=EN
Content-Type: application/json
Authorization: {{cadshareToken}}
Site-Url: {{CADSHARE_URL}}
