package co.cadshare.masterParts.boundary;

import co.cadshare.shared.core.manufacturer.subentity.ManufacturerSubEntity;
import co.cadshare.shared.core.user.User;
import co.cadshare.shared.core.manufacturer.ManufacturerSettings;
import co.cadshare.masterParts.adapters.database.MasterPartDao;
import co.cadshare.masterParts.core.MasterPart;
import co.cadshare.shared.adapters.database.manufacturer.ManufacturerDao;
import co.cadshare.persistence.ManufacturerSubEntityDao;
import co.cadshare.masterParts.adapters.database.MasterPartExtensionsDao;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import co.cadshare.persistence.WarehouseDao;

import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
public class SearchMasterPartService {

    private static final Logger logger = LoggerFactory.getLogger(SearchMasterPartService.class);

    private final MasterPartDao masterPartDao;
    private final MasterPartExtensionsDao masterPartExtensionsDao;
    private final ManufacturerSubEntityDao manufacturerSubEntityDao;
    private final ManufacturerDao manufacturerDao;
    private final MasterPartQueryPort masterPartQuery;
    private final WarehouseDao warehouseDao;

    @Autowired
    public SearchMasterPartService(MasterPartDao masterPartDao,
                                   MasterPartExtensionsDao masterPartExtensionsDao,
                                   ManufacturerSubEntityDao manufacturerSubEntityDao,
                                   ManufacturerDao manufacturerDao,
                                   MasterPartQueryPort masterPartQuery,
                                   WarehouseDao warehouseDao) {
        this.masterPartDao = masterPartDao;
        this.masterPartExtensionsDao = masterPartExtensionsDao;
        this.manufacturerSubEntityDao = manufacturerSubEntityDao;
        this.manufacturerDao = manufacturerDao;
        this.masterPartQuery = masterPartQuery;
        this.warehouseDao = warehouseDao;
    }

    public MasterPartSearchResult findForManufacturer(User currentUser,
                                                      MasterPartSearchRequest request,
                                                      int manufacturerId) {

        MasterPartSearchResult results = new MasterPartSearchResult(request.getPage(), request.getSize());
        ManufacturerSettings settings = manufacturerDao.getManufacturerSettingsById(manufacturerId);
        boolean warehousesEnabled = settings.isStockWarehousesEnabled();
        Integer warehouseId = warehousesEnabled ? warehouseDao.getWarehouseIdForManufacturer(manufacturerId) : null;
        List<MasterPart> masterParts = masterPartQuery.searchMasterPartsForManufacturer(request, manufacturerId);
	    masterParts = applyExactSearchMatch(request, masterParts);
        setAdditionalAttributes(currentUser, results, masterParts, warehouseId);
        return results;
    }

    public MasterPartSearchResult findForPurchaser(User currentUser,
                                                   MasterPartSearchRequest searchRequest,
                                                   Integer manufacturerSubEntityId,
                                                   Integer onBehalfOfUserId) {

        MasterPartSearchResult results = new MasterPartSearchResult(searchRequest.getPage(), searchRequest.getSize());

        Integer userId = (onBehalfOfUserId != null) ? onBehalfOfUserId : currentUser.getUserId();
        ManufacturerSubEntity manufacturerSubEntity = manufacturerSubEntityDao.getManufacturerSubEntity(manufacturerSubEntityId);
        Integer manufacturerId = manufacturerSubEntity.getManufacturerId();
        ManufacturerSettings settings = manufacturerDao.getManufacturerSettingsById(manufacturerId);
        boolean priceListsEnabled = settings.isPriceListsEnabled();
        boolean warehousesEnabled = settings.isStockWarehousesEnabled();
        Integer priceListIdentifierId = priceListsEnabled ? manufacturerSubEntity.getPriceListIdentifierId() : null;
        Integer warehouseId = warehousesEnabled ? manufacturerSubEntity.getWarehouseId() : null;
        manufacturerSubEntityId = manufacturerSubEntity.getParentSubEntityId() != null ?
		        manufacturerSubEntity.getParentSubEntityId() :
		        manufacturerSubEntityId;

        List<MasterPart> masterParts = masterPartQuery.searchMasterPartsForPurchaser(searchRequest,
                manufacturerId,
                manufacturerSubEntity,
                priceListsEnabled,
                priceListIdentifierId,
                warehousesEnabled,
                warehouseId);

	    masterParts = applyExactSearchMatch(searchRequest, masterParts);

	    setAdditionalAttributes(currentUser, results, masterParts, warehouseId);
        return results;
    }


	public MasterPartSearchResult findForDealerPlusPrices(User currentUser, MasterPartSearchRequest searchRequest, int purchaserId) {
		MasterPartSearchResult results = new MasterPartSearchResult(searchRequest.getPage(), searchRequest.getSize());
		ManufacturerSubEntity purchaser = manufacturerSubEntityDao.getManufacturerSubEntity(purchaserId);
		List<MasterPart> masterParts = masterPartQuery.searchMasterPartsForDealerPlusPrice(searchRequest, purchaser);
		masterParts = applyExactSearchMatch(searchRequest, masterParts);
		setAdditionalAttributes(currentUser, results, masterParts, null);
		return results;
	}

	private static List<MasterPart> applyExactSearchMatch(MasterPartSearchRequest searchRequest, List<MasterPart> masterParts) {
		if(searchRequest.isExactMatch())
			masterParts = masterParts.stream()
					.filter(masterPart -> searchRequest.isPartNumberSearch()
							? masterPart.getPartNumber().equals(searchRequest.getPartNumber())
							: masterPart.getDescription().equals(searchRequest.getDescription()))
					.collect(Collectors.toList());
		return masterParts;
	}

	private void setAdditionalAttributes(User currentUser,
                                         MasterPartSearchResult results,
                                         List<MasterPart> masterParts,
                                         Integer warehouseId) {

	    for (MasterPart masterpart : masterParts) {
	        boolean hasLinkedTechDocs = masterPartExtensionsDao.hasLinkedTechDocs(masterpart.getMasterPartId());
	        masterpart.setHasLinkedTechDocs(hasLinkedTechDocs);
	        if (hasLinkedTechDocs)
	            masterpart.setLinkedTechDocs(masterPartExtensionsDao.getLinkedTechDocsForPart(masterpart.getMasterPartId()));

	        if (!currentUser.canViewPrices())
	            masterpart.setPrice(null);

	        if (warehouseId != null) {
	            Double warehouseStock = masterPartDao.getWarehouseStockForMasterPart(masterpart.getMasterPartId(), warehouseId);
	            masterpart.setStock(warehouseStock);
	        }
	    }
	    results.setTotalResults(masterParts.size());
	    results.setMasterParts(masterParts);
	}
}
