package co.cadshare.modelMgt.publications.adapters.database;

import co.cadshare.modelMgt.publicationCategories.adapters.database.PublicationCategoryEntityMapper;
import co.cadshare.modelMgt.shared.core.Publication;
import co.cadshare.modelMgt.shared.core.Purchaser;
import co.cadshare.shared.adapters.database.ImageEntityMapper;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;
import org.mapstruct.SubclassExhaustiveStrategy;
import org.mapstruct.factory.Mappers;

import java.util.List;
import java.util.Set;

@Mapper(uses = {
		ImageEntityMapper.class,
		PublicationCategoryEntityMapper.class,
		PublicationsViewableEntityMapper.class,
		PublicationsRangeEntityMapper.class}, subclassExhaustiveStrategy = SubclassExhaustiveStrategy.RUNTIME_EXCEPTION)
public interface PublicationEntityMapper {

    PublicationEntityMapper Instance = Mappers.getMapper(PublicationEntityMapper.class);

	@Mapping(source="models", target="viewables")
	@Mapping(source="purchasers", target="purchasers", qualifiedByName="purchasersEntitiesToCores" )
	Publication entityToCore(PublicationsPublicationEntity publication);

	@Named("purchasersEntitiesToCores")
	default List<Purchaser> purchasersEntitiesToCores(Set<PublicationsPurchaserEntity> purchasers) {
		return PublicationsPurchaserEntityMapper.Instance.entitiesToPurchaserCores(purchasers);
	}

	@Mapping(source="viewables", target="models")
	@Mapping(source="purchasers", target="purchasers", qualifiedByName="purchasersCoresToEntities" )
    PublicationsPublicationEntity coreToEntity(Publication publication);

	@Named("purchasersCoresToEntities")
	default Set<PublicationsPurchaserEntity> purchasersCoresToEntities(List<Purchaser> purchasers) {
		return PublicationsPurchaserEntityMapper.Instance.purchaserCoresToEntities(purchasers);
	}


	List<Publication> entitiesToCores(List<PublicationsPublicationEntity> publication);
    List<PublicationsPublicationEntity> coresToEntities(List<Publication> publications);


}
