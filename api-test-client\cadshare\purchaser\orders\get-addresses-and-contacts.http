
# @name cadshareAuthResponse

POST {{CADSHARE_URL}}/oauth/token
Content-Type: application/x-www-form-urlencoded
Authorization: Basic Y2xpZW50YXBwOnNlY3JldA==

username={{DEALER_USER_ID}}
&password={{STANDARD_PWD}}
&grant_type=password


###

@cadshareToken = Bearer {{cadshareAuthResponse.response.body.access_token}}

GET {{CADSHARE_URL}}/address/1715/contacts?userId={{DEALER_USER_ID}}
Content-Type: application/json
Authorization: {{cadshareToken}}
Site-Url: {{CADSHARE_URL}}


###


# @name cadshareAuthResponse

POST {{CADSHARE_URL}}/oauth/token
Content-Type: application/x-www-form-urlencoded
Authorization: Basic Y2xpZW50YXBwOnNlY3JldA==

username=1671
&password={{STANDARD_PWD}}
&grant_type=password


###

@cadshareToken = Bearer {{cadshareAuthResponse.response.body.access_token}}

GET {{CADSHARE_URL}}/address/1279/contacts?userId=1671
Content-Type: application/json
Authorization: {{cadshareToken}}
Site-Url: {{CADSHARE_URL}}




