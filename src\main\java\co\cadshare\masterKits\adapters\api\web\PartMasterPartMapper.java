package co.cadshare.masterKits.adapters.api.web;

import co.cadshare.masterKits.core.PartMasterPart;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

@Mapper(uses=TranslationMapper.class)
public interface PartMasterPartMapper {

    PartMasterPartMapper Instance = Mappers.getMapper(PartMasterPartMapper.class);

    @Mapping(source="translations", target="descriptions")
    @Mapping(source="id", target="masterPartId")
    GetMasterKitMasterPartResponseDto coreToGetResponseDto(PartMasterPart masterPart);

    @Mapping(source="quantity", target="quantity")
    @Mapping(source="masterPartId", target="id")
    PartMasterPart postRequestToMasterPart(MasterKitMasterPartsRequestDto request);
}
