/*
 * Copyright 2016 Bell.
 */
package co.cadshare.persistence.dealer;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.namedparam.MapSqlParameterSource;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.stereotype.Repository;

import java.sql.Types;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Repository
public class DealerPlusPublicationDao {

    private final NamedParameterJdbcTemplate namedParamJdbcTemplate;

    @Autowired
	public DealerPlusPublicationDao(NamedParameterJdbcTemplate namedParamJdbcTemplate) {
		this.namedParamJdbcTemplate = namedParamJdbcTemplate;
	}

	public void unassignPublicationsFromDealerPlusCustomers(int purchaserId, List<Integer> publicationsToBeUnassigned) {

		String DELETE_PUBLICATIONS_FOR_PURCHASERS = "DELETE FROM manufacturersubentitymanualmap " +
				"WHERE manualid IN (:publicationIds) " +
				"AND manufacturersubentityid IN (SELECT manufacturersubentityid " +
				"FROM manufacturersubentity "+
				"WHERE parentsubentityid = :purchaserId) ";

	    MapSqlParameterSource namedParameters = new MapSqlParameterSource();
	    namedParameters.addValue("purchaserId", purchaserId, Types.INTEGER);
	    namedParameters.addValue("publicationIds", publicationsToBeUnassigned, Types.INTEGER);

	    namedParamJdbcTemplate.update(DELETE_PUBLICATIONS_FOR_PURCHASERS, namedParameters);
	}

	public List<Integer> getAssignedPublicationCategoriesForPurchaser(int purchaserId) {

	    String GET_ASSIGNED_PUBLICATION_CATEGORIES = "SELECT DISTINCT(publicationcategoryid) "
			  + "FROM purchaserpublicationcategorymap  "
			  + "WHERE purchaserid = :purchaserId ";

		Map<String, Object> parameters = new HashMap<String, Object>();
		parameters.put("purchaserId", purchaserId);

		return namedParamJdbcTemplate.queryForList(GET_ASSIGNED_PUBLICATION_CATEGORIES,
		        parameters, Integer.class);
	}

  private static final String GET_PUBLICATIONS_FOR_PUBLICATION_CATEGORIES = "SELECT DISTINCT man.manualid "
          + "FROM publicationcategory pc "
          + "LEFT JOIN pc.publicationcategoryid = man.publicationcategoryid "
          + "WHERE man.publicationcategoryid IN (:publicationCategoryIds) "
          + "AND man.manualid IS NOT NULL ";

  public List<Integer> getPublicationsForPublicationCategories(List<Integer> publicationCategoryIds) {
    Map<String, Object> parameters = new HashMap<String, Object>();
    parameters.put("publicationCategoryIds", publicationCategoryIds);

    List<Integer> manualIds = namedParamJdbcTemplate.queryForList(GET_PUBLICATIONS_FOR_PUBLICATION_CATEGORIES, parameters, Integer.class);

    return manualIds;
  }
}