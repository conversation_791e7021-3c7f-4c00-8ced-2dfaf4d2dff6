package co.cadshare.addresses.adapters.ext.erp.vis;

import co.cadshare.addresses.core.ExternalAddressContact;
import co.cadshare.addresses.core.ExternalContact;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface ExternalContactMapper {

	ExternalContactMapper Instance = Mappers.getMapper(ExternalContactMapper.class);

	@Mapping(source="contactId", target="externalRefId")
	@Mapping(source="contactName", target="name")
	@Mapping(target="id", ignore=true)
	ExternalContact externalToCore(ContactDetail source);

	List<ExternalContact> externalToCores(List<ContactDetail> source);
}
