package co.cadshare.masterParts.adapters.api.web;

import co.cadshare.aspects.access.languages.CanUseLanguage;
import co.cadshare.shared.core.Language;
import co.cadshare.masterParts.core.SupersessionHistoryItem;
import co.cadshare.shared.core.user.User;
import co.cadshare.masterParts.boundary.MasterPartSupersessionService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Slf4j
@RestController
@PreAuthorize("hasRole('ROLE_MANUFACTURER_SUB_ENTITY_DEALER') or hasRole('ROLE_MANUFACTURER_SUB_ENTITY_DEALER_PLUS') or hasRole('ROLE_MANUFACTURER_SUB_ENTITY_CUSTOMER')")
@RequestMapping("purchasers/{purchaser-id}")
public class PurchaserMasterPartController {

	private final MasterPartSupersessionService supersessionService;

	@Autowired
	public PurchaserMasterPartController(MasterPartSupersessionService supersessionService) {
	    this.supersessionService = supersessionService;
	}


    @GetMapping(value = "/master-parts/{master-part-number}/supersession-history", produces = "application/json")
    @CanUseLanguage
    public ResponseEntity<SupersessionHistoryDto> getMasterPartSupersessionHistory(@AuthenticationPrincipal User currentUser,
                                                                             @PathVariable("master-part-number") String masterPartNumber,
                                                                             @RequestParam(value = "language", required = false) Language language) {

        log.info("ACCESS: User [{}], getMasterPartSupersessionHistory, masterPartNumber [{}]", currentUser.accessDetails(), masterPartNumber);
        List<SupersessionHistoryItem> historyItems = supersessionService.getMasterPartSupersessionHistoryForPurchaser(
                masterPartNumber,
                currentUser);
        List<SupersessionHistoryItemDto> history = SupersessionHistoryItemMapper.Instance.coresToDtos(historyItems);
        SupersessionHistoryDto supersessionHistoryDto = new SupersessionHistoryDto();
        supersessionHistoryDto.setSupersessionHistory(history);
        return new ResponseEntity<>(supersessionHistoryDto, HttpStatus.OK);
    }
}
