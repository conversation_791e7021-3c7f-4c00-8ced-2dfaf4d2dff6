<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.0.xsd
        http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd">

<changeSet author="Andrew" id="60-AdditionalSupportEmails">
    <createTable schemaName="public" tableName="additionalemail">
        <column name="id" type="serial">
            <constraints nullable="false" primaryKey="true"/>
        </column>
        <column name="manufacturerid" type="integer">
            <constraints nullable="false"/>
        </column>
        <column name="label" type="varchar(255)">
            <constraints nullable="false"/>
        </column>
        <column name="email" type="varchar(255)">
            <constraints nullable="false"/>
        </column>
    </createTable>
    
    <addForeignKeyConstraint baseColumnNames="manufacturerid"
                             baseTableName="additionalemail"
                             constraintName="fk_additionalemail_manufacturer"
                             onDelete="CASCADE"
                             onUpdate="RESTRICT"
                             referencedColumnNames="manufacturerid"
                             referencedTableName="manufacturer"/>
</changeSet>

</databaseChangeLog>
