/*
package co.cadshare.modelMgt.publications.adapters.database;

import co.cadshare.modelMgt.models.adapters.database.ModelCommandEntity;
import co.cadshare.modelMgt.models.core.Model;
import co.cadshare.shared.adapters.database.ProductEntityMapper;
import com.blazebit.persistence.view.MappingInverse;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper(uses = {
		ProductEntityMapper.class,
		PublicationEntityMapper.class,
		PublicationsPartEntityMapper.class},
		subclassExhaustiveStrategy = SubclassExhaustiveStrategy.RUNTIME_EXCEPTION)
public interface PublicationsModelEntityMapper {

    PublicationsModelEntityMapper Instance = Mappers.getMapper(PublicationsModelEntityMapper.class);

    Model clone(Model model);

	 @Mapping(source="product.id", target="machineId")
	 @Mapping(source="product.name", target="machineName")
    Model entityToCore(PublicationsModelEntity entity);

	@InheritInverseConfiguration
    ModelCommandEntity coreToEntity(Model model);

    List<Model> entitiesToCores(List<PublicationsModelEntity> entities);

    List<PublicationsModelEntity> coresToEntities(List<Model> models);

}


 */