package co.cadshare.masterKits.adapters.database;

import co.cadshare.shared.adapters.database.MasterPartWarehouseStockEntity;
import com.blazebit.persistence.view.EntityView;
import com.blazebit.persistence.view.IdMapping;
import com.blazebit.persistence.view.Mapping;

@EntityView(MasterPartWarehouseStockEntity.class)
public interface MasterPartWarehouseStockEntityView {
    @IdMapping
    Integer getId();

    @Mapping
    int getWarehouseId();

    @Mapping
    Double getStock();
}
