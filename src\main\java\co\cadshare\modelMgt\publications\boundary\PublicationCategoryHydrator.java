package co.cadshare.modelMgt.publications.boundary;

import co.cadshare.modelMgt.publicationCategories.boundary.PublicationCategoryQueryPort;
import co.cadshare.modelMgt.publicationCategories.core.PublicationCategory;
import co.cadshare.modelMgt.shared.core.Publication;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class PublicationCategoryHydrator implements PublicationAttributeHydrator {

	private final PublicationCategoryQueryPort publicationCategoryQueryPort;

	@Autowired
	public PublicationCategoryHydrator(PublicationCategoryQueryPort publicationCategoryQueryPort) {
		this.publicationCategoryQueryPort = publicationCategoryQueryPort;
	}

	public void hydrate(PublicationCommand publicationCommand, Publication publication) {
		if(publicationCommand.hasPublicationCategory()) {
			PublicationCategory category = publicationCategoryQueryPort.get(publicationCommand.getPublicationCategoryId());
			publication.setPublicationCategory(category);
		} else {
			publication.setPublicationCategory(null);
		}
	}
}
