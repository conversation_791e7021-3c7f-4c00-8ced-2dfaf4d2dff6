package co.cadshare.models.boundary;

import co.cadshare.domainmodel.autodesk.AutodeskResource;
import co.cadshare.models.core.Model;
import co.cadshare.models.core.ModelManifestWrapper;
import co.cadshare.models.core.ParsedMetadata;
import co.cadshare.models.core.model.TranslateType;
import co.cadshare.models.core.MetadataWrapper;
import co.cadshare.shared.core.manufacturer.ManufacturerConfig;
import com.autodesk.client.auth.Credentials;

import java.util.HashMap;
import java.util.List;

public interface AutodeskPort {
    Credentials getAutodeskToken() throws Exception;
    Credentials getAutodeskTokenForWebClient() throws Exception;
    List<AutodeskResource> getAllAutodeskModelResources(Model model);
    void translateModel(Model model, TranslateType translateType) throws Exception;
    ModelManifestWrapper getManifest(Model model) throws Exception;
    ParsedMetadata extractMetadata(Model model, ModelManifestWrapper manifest) throws Exception;
}
