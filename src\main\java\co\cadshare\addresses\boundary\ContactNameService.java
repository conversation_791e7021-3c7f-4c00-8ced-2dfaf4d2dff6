package co.cadshare.addresses.boundary;

import co.cadshare.addresses.core.ContactName;
import co.cadshare.addresses.adapters.database.ContactNameDao;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class ContactNameService {

  @Autowired
  private ContactNameDao contactNameDao;

  public List<ContactName> getAllNamesForUser(int userId) {
    List<ContactName> nameList = contactNameDao.getNamesForUser(userId);
    return nameList;
  }

  public int createNameForUser(int userId, ContactName contactName) {
    return contactNameDao.createContactName(userId, contactName);
  }

  public int updateName(int userId, ContactName contactName) {
    return contactNameDao.updateContactName(userId, contactName);
  }

  public boolean deleteName(int userId, int nameId) {
    return contactNameDao.deleteContactName(userId, nameId);
  }

  public ContactName getContactNameById(int nameId) {
    return contactNameDao.getContactNameById(nameId);
  }
}