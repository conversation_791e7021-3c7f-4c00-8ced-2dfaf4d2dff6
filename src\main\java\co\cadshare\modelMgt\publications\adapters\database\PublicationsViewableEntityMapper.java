package co.cadshare.modelMgt.publications.adapters.database;

import co.cadshare.modelMgt.shared.core.Viewable;
import co.cadshare.shared.adapters.database.ImageEntityMapper;
import org.mapstruct.InheritInverseConfiguration;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.SubclassExhaustiveStrategy;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper(uses = {
		ImageEntityMapper.class,
		PublicationsProductEntityMapper.class,
		PublicationEntityMapper.class},
		subclassExhaustiveStrategy = SubclassExhaustiveStrategy.RUNTIME_EXCEPTION)
public interface PublicationsViewableEntityMapper {

    PublicationsViewableEntityMapper Instance = Mappers.getMapper(PublicationsViewableEntityMapper.class);

	@Mapping(source="modelId", target="id")
	@Mapping(source="modelName", target="name")
	Viewable entityToCore(PublicationsModelEntity entity);

    List<Viewable> entitiesToCores(List<PublicationsModelEntity> publication);

	@Mapping(source="id", target="modelId")
	@Mapping(source="name", target="modelName")
	PublicationsModelEntity coreToEntity(Viewable core);

	List<PublicationsModelEntity> coresToEntities(List<Viewable> cores);
}
