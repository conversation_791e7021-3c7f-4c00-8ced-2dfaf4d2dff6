<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                   http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.0.xsd
">

    <changeSet author="AndyB" id="2.2-integration-test-data-create-models-1">
        <sql stripComments="true">
            INSERT INTO public.model (modelid, modelname, modeldescription, machineid, autodeskstatus, autodeskurn, createddate, createdbyuserid, filename, archived, filetype, autodeskprogress, toplevelassembly, modifieddate, modifiedbyuserid, originalfilename, is2d, leafnodes, issetupcomplete, translatetype, retries, reasonforfailure)
            VALUES (
            1, 	                                        --modelid,
            'Caterpillar Model 1', 	                    --modelname,
            '', 	                                    --modeldescription,
            1, 	                                        --machineid,
            'PROPERTIES_PROCESSED', 	                --autodeskstatus,
            'dXJuOmFkc2sub2JqZWN0czpvcy5vYmplY3Q6d2ViYXBwLmNhZHNoYXJlLmNvLzE2MTMzODkwMDc2OTQtMTVtJTIwUmFkaWFsJTIwQ29udmV5b3IuanQ=', 	--autodeskurn,
            '2021-02-15 11:36:45.878', 	                --createddate,
            1, 	                                        --createdbyuserid,
            '1613389007694-15m Radial Conveyor.jt', 	--filename,
            false, 	                                    --archived,
            'SINGLE_FILE', 	                            --filetype,
            'complete', 	                            --autodeskprogress,
            NULL, 	                                    --toplevelassembly,
            NULL, 	                                    --modifieddate,
            NULL, 	                                    --modifiedbyuserid,
            '15m Radial Conveyor.jt', 	                --originalfilename,
            false,	                                    --is2d,
            '[5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 30, 33, 35, 37, 38, 40, 42, 44, 46, 47, 49, 50, 52, 53, 55, 57, 59, 60, 62, 64, 66, 67, 69, 70, 72, 74, 76, 77, 79, 80, 82, 85, 87, 89, 91, 93, 95, 97, 99, 101, 103, 105, 107, 109, 111, 113, 115, 117, 119, 121, 124, 126, 128, 131, 133, 135, 138, 140, 142, 144, 146, 148, 150, 152, 154, 156, 158, 160, 162, 164, 166, 168, 170, 172, 174, 176, 178, 180, 182, 184, 186, 188, 190, 193, 195, 197, 200, 202, 204, 206, 208, 210, 211, 214, 216, 218, 220, 222, 225, 227, 229, 232, 234, 236, 238, 240, 242, 244, 246, 248, 250, 252, 254, 256, 258, 260, 262, 264, 266, 268, 270, 272, 274, 276, 278, 280, 282, 284, 286, 288, 290, 292, 294, 296, 298, 300, 302, 304, 306, 308, 310, 312, 314, 316, 318, 320, 322, 324, 326, 328, 330, 332, 334, 336, 338, 340, 342, 344, 346, 348, 350, 352, 354, 356, 358, 360, 362, 364, 366, 368, 370, 372, 374, 376, 378, 380, 383, 385, 387, 389, 391, 393, 395, 397, 399, 401, 403, 405, 407, 409, 411, 413, 415, 418, 420, 422, 424, 427, 429, 431, 433, 435, 437, 440, 442, 444, 446, 448, 449, 450, 451, 452, 453, 454, 455, 456, 458, 460, 463, 465, 467, 469, 471, 473, 475, 477, 479, 481, 483, 485, 487, 489, 491, 493, 495, 497, 499, 501, 503, 505, 507, 509, 511, 513, 515, 517, 519, 521, 523, 525, 527, 529, 531, 533, 535, 537, 539, 541, 543, 545, 547, 549, 551, 553, 555, 557, 559, 561, 563, 565, 567, 569, 571, 573, 575, 577, 579, 581, 583, 585, 587, 589, 591, 593, 595, 597, 599, 601, 603, 605, 607, 609, 612, 614, 616, 618, 621, 623, 625, 628, 630, 632, 635, 637, 639, 642, 644, 646, 648, 651, 653, 655, 658, 660, 662, 665, 667, 669, 671, 674, 676, 678, 680, 683, 685, 687, 690, 692, 694, 696, 699, 701, 703, 705, 708, 710, 712, 714, 716, 718, 720, 722, 724, 726, 728, 730, 732, 734, 736, 738, 740, 742, 744, 746, 748, 750, 752, 754, 756, 758, 760, 762, 764, 766, 768, 770, 772, 774, 776, 778, 780, 782, 784, 786, 788, 790, 792, 794, 797, 799, 801, 804, 806, 808, 811, 813, 815, 817, 819, 821, 824, 826, 828, 830, 832, 834, 837, 839, 841, 843, 845, 847, 849, 851, 852, 854, 856, 858, 860, 863, 865, 867, 869, 871, 873, 875, 877, 879, 881, 883, 885, 887, 889, 891, 893, 895, 897, 901, 903, 905, 907, 909, 912, 914, 916, 918, 920, 922, 924, 926, 927, 930, 932, 934, 937, 939, 941, 943, 946, 948, 950, 952, 954, 956, 958, 960, 962, 964, 966, 970, 972, 974, 976, 978, 980, 983, 985, 987, 989, 992, 994, 996, 998, 1000, 1002, 1004, 1006, 1008, 1010, 1012, 1014, 1017, 1018, 1020, 1022, 1024, 1027, 1028, 1030, 1032, 1034, 1037, 1038, 1040, 1042, 1044, 1047, 1048, 1050, 1052, 1054, 1056]', 	--leafnodes,
            true, 	                                    --issetupcomplete,
            'SVF', 	                                    --translatetype,
            0, 	                                        --retries,
            NULL);	                                    --reasonforfailure
        </sql>
    </changeSet>

    <changeSet author="AndyB" id="2.2-integration-test-data-create-models-2">
        <sql stripComments="true">
            INSERT INTO public.model (modelid, modelname, modeldescription, machineid, autodeskstatus, autodeskurn, createddate, createdbyuserid, filename, archived, filetype, autodeskprogress, toplevelassembly, modifieddate, modifiedbyuserid, originalfilename, is2d, leafnodes, issetupcomplete, translatetype, retries, reasonforfailure)
            VALUES (
            2, 	                                        --modelid,
            'JCB Model 1', 	                            --modelname,
            '', 	                                    --modeldescription,
            2, 	                                        --machineid,
            'PROPERTIES_PROCESSED', 	                --autodeskstatus,
            'dXJuOmFkc2sub2JqZWN0czpvcy5vYmplY3Q6d2ViYXBwLmNhZHNoYXJlLmNvLzE2MTMzODkwMDc2OTQtMTVtJTIwUmFkaWFsJTIwQ29udmV5b3IuanQ=', 	--autodeskurn,
            '2021-02-15 11:36:45.878', 	                --createddate,
            1, 	                                        --createdbyuserid,
            '1613389007694-15m Radial Conveyor.jt', 	--filename,
            false, 	                                    --archived,
            'SINGLE_FILE', 	                            --filetype,
            'complete', 	                            --autodeskprogress,
            NULL, 	                                    --toplevelassembly,
            NULL, 	                                    --modifieddate,
            NULL, 	                                    --modifiedbyuserid,
            '15m Radial Conveyor.jt', 	                --originalfilename,
            false,	                                    --is2d,
            '[5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 30, 33, 35, 37, 38, 40, 42, 44, 46, 47, 49, 50, 52, 53, 55, 57, 59, 60, 62, 64, 66, 67, 69, 70, 72, 74, 76, 77, 79, 80, 82, 85, 87, 89, 91, 93, 95, 97, 99, 101, 103, 105, 107, 109, 111, 113, 115, 117, 119, 121, 124, 126, 128, 131, 133, 135, 138, 140, 142, 144, 146, 148, 150, 152, 154, 156, 158, 160, 162, 164, 166, 168, 170, 172, 174, 176, 178, 180, 182, 184, 186, 188, 190, 193, 195, 197, 200, 202, 204, 206, 208, 210, 211, 214, 216, 218, 220, 222, 225, 227, 229, 232, 234, 236, 238, 240, 242, 244, 246, 248, 250, 252, 254, 256, 258, 260, 262, 264, 266, 268, 270, 272, 274, 276, 278, 280, 282, 284, 286, 288, 290, 292, 294, 296, 298, 300, 302, 304, 306, 308, 310, 312, 314, 316, 318, 320, 322, 324, 326, 328, 330, 332, 334, 336, 338, 340, 342, 344, 346, 348, 350, 352, 354, 356, 358, 360, 362, 364, 366, 368, 370, 372, 374, 376, 378, 380, 383, 385, 387, 389, 391, 393, 395, 397, 399, 401, 403, 405, 407, 409, 411, 413, 415, 418, 420, 422, 424, 427, 429, 431, 433, 435, 437, 440, 442, 444, 446, 448, 449, 450, 451, 452, 453, 454, 455, 456, 458, 460, 463, 465, 467, 469, 471, 473, 475, 477, 479, 481, 483, 485, 487, 489, 491, 493, 495, 497, 499, 501, 503, 505, 507, 509, 511, 513, 515, 517, 519, 521, 523, 525, 527, 529, 531, 533, 535, 537, 539, 541, 543, 545, 547, 549, 551, 553, 555, 557, 559, 561, 563, 565, 567, 569, 571, 573, 575, 577, 579, 581, 583, 585, 587, 589, 591, 593, 595, 597, 599, 601, 603, 605, 607, 609, 612, 614, 616, 618, 621, 623, 625, 628, 630, 632, 635, 637, 639, 642, 644, 646, 648, 651, 653, 655, 658, 660, 662, 665, 667, 669, 671, 674, 676, 678, 680, 683, 685, 687, 690, 692, 694, 696, 699, 701, 703, 705, 708, 710, 712, 714, 716, 718, 720, 722, 724, 726, 728, 730, 732, 734, 736, 738, 740, 742, 744, 746, 748, 750, 752, 754, 756, 758, 760, 762, 764, 766, 768, 770, 772, 774, 776, 778, 780, 782, 784, 786, 788, 790, 792, 794, 797, 799, 801, 804, 806, 808, 811, 813, 815, 817, 819, 821, 824, 826, 828, 830, 832, 834, 837, 839, 841, 843, 845, 847, 849, 851, 852, 854, 856, 858, 860, 863, 865, 867, 869, 871, 873, 875, 877, 879, 881, 883, 885, 887, 889, 891, 893, 895, 897, 901, 903, 905, 907, 909, 912, 914, 916, 918, 920, 922, 924, 926, 927, 930, 932, 934, 937, 939, 941, 943, 946, 948, 950, 952, 954, 956, 958, 960, 962, 964, 966, 970, 972, 974, 976, 978, 980, 983, 985, 987, 989, 992, 994, 996, 998, 1000, 1002, 1004, 1006, 1008, 1010, 1012, 1014, 1017, 1018, 1020, 1022, 1024, 1027, 1028, 1030, 1032, 1034, 1037, 1038, 1040, 1042, 1044, 1047, 1048, 1050, 1052, 1054, 1056]', 	--leafnodes,
            true, 	                                    --issetupcomplete,
            'SVF', 	                                    --translatetype,
            0, 	                                        --retries,
            NULL);	                                    --reasonforfailure
        </sql>
    </changeSet>
</databaseChangeLog>
