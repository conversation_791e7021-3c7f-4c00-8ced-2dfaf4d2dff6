package co.cadshare.masterKits.boundary;

import co.cadshare.shared.core.Language;
import co.cadshare.masterParts.boundary.MasterSearchRequest;
import co.cadshare.shared.core.user.User;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class PurchaserMasterKitSearchRequest extends MasterSearchRequest {

    private int purchaserId;

    public static PurchaserMasterKitSearchRequest build(int purchaserId, String partNumber, String partDescription, Language language) {
        PurchaserMasterKitSearchRequest request = new PurchaserMasterKitSearchRequest();
        request.purchaserId = purchaserId;
        build(request, partNumber, partDescription, language);
        return request;
    }
}
