package co.cadshare.addresses.boundary;

import java.util.List;

import co.cadshare.addresses.core.ContactName;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import co.cadshare.addresses.core.Address;

@Service
public class MaintainAddressService {

	private final AddressQueryPort addressQuery;
	private final AddressCommandPort addressCommand;

	@Autowired
	public MaintainAddressService(AddressQueryPort addressQuery, AddressCommandPort addressCommand) {
		this.addressQuery = addressQuery;
		this.addressCommand = addressCommand;
	}

	public List<Address> getAllAddressesForUser(int userId) {
		return addressQuery.getAddressesForUser(userId);
	}

	public void updateUserAddress(int userId, Address address) {
	addressCommand.updateUserAddress(userId, address);
	}

	public boolean deleteUserAddress(int userId, int addressId) {
		return addressCommand.deleteUserAddress(userId, addressId);
	}

    public Address getAddressById(int addressId) {
    return addressQuery.getAddressById(addressId);
  }

	public List<ContactName> getAddressContactsForUser(int addressId, int userId) {
		List<ContactName> contactList = addressQuery.getAllNamesForAddressForUser(addressId, userId);
		if(contactList.isEmpty())
			contactList = addressQuery.getAllNamesForUser(userId);
		return contactList;
	}
}
