package co.cadshare.aspects;

import co.cadshare.exceptions.ResourceValidationException;
import co.cadshare.oauth.domainmodel.OAuthUser;
import co.cadshare.shared.core.Language;
import co.cadshare.shared.core.user.User;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.reflect.CodeSignature;
import org.aspectj.lang.reflect.MethodSignature;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.lang.reflect.Type;
import java.util.Arrays;

public class BaseAspect {

    protected String trim = "\n\n============================\n";

    protected Logger logger;

    protected MethodSignature signature;

    protected String methodName;

    protected Object[] args;

    protected User user;

    protected void configureInterception(JoinPoint joinPoint) {
        signature = (MethodSignature) joinPoint.getSignature();
        logger = LoggerFactory.getLogger(signature.getDeclaringType());
        methodName = signature.getMethod().getName();
	    args = joinPoint.getArgs();
        user = (User)getArgumentByType(User.class);
    }

    protected Object getArgumentByName(JoinPoint joinPoint, String argName) {
        CodeSignature codeSignature = (CodeSignature) joinPoint.getSignature();
        String[] paramNames = codeSignature.getParameterNames();
        int paramIndex = Arrays.asList(paramNames).indexOf(argName);
        if(paramIndex > -1)
            return args[paramIndex];
        else
            throw new ResourceValidationException(String.format("%s must be provided", argName));
    }

    protected Object getArgumentByType(Class<?> classType) {

        return Arrays.stream(args)
                .filter(arg -> arg != null && classType.isAssignableFrom(arg.getClass()))
                .findFirst()
                .orElse(null);


		 /*
        return Arrays.stream(args)
                .filter(arg -> arg != null && arg.getClass() == classType)
                .findFirst()
                .orElse(null);
    */
    }

}
