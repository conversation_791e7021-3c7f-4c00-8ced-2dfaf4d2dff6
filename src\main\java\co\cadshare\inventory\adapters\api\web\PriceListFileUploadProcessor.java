package co.cadshare.inventory.adapters.api.web;

import co.cadshare.inventory.core.PriceListUploadFile;
import co.cadshare.shared.core.Currency;
import co.cadshare.domainmodel.priceListIdentifier.PriceListIdentifier;
import co.cadshare.persistence.CurrencyDao;
import co.cadshare.persistence.PriceListDao;
import co.cadshare.inventory.adapters.database.PriceListIdentifierDao;
import com.opencsv.CSVReader;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStreamReader;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;

@Component
@Slf4j
public class PriceListFileUploadProcessor {

    private PriceListDao priceListDao;
    private CurrencyDao currencyDao;
    private PriceListIdentifierDao priceListIdentifierDao;

    @Autowired
    public PriceListFileUploadProcessor(PriceListDao priceListDao, CurrencyDao currencyDao, PriceListIdentifierDao priceListIdentifierDao) {
        this.priceListDao = priceListDao;
        this.currencyDao = currencyDao;
        this.priceListIdentifierDao = priceListIdentifierDao;
    }
    private List<String[]> readAll(MultipartFile file) throws IOException {

        try (CSVReader reader = new CSVReader(new InputStreamReader(file.getInputStream(), "UTF-8"))){
            List<String[]> list;
            list = reader.readAll();
            return list;
        }
    }

    public PriceListUploadFile convert(MultipartFile file, String partNumberColumn, int manufacturerId) throws PriceListUploadFile.UnparseablePriceListFileException, IOException {
        HashMap<String, Integer> priceListIdentifiers = priceListDao.getPriceIdentifiers(manufacturerId);

        List<PriceListIdentifier> identifiers = new ArrayList<>();
        String[] headers = readAll(file).get(0);

        if (hasInvalidPriceIdentifiers(headers)) {
            throw new PriceListUploadFile.UnparseablePriceListFileException("Header contains invalid prices(s). Header [" + Arrays.toString(headers) + "]." +
                    " Known Price List Identifiers: [" + priceListIdentifiers.keySet()+ "]");

        }
        for (int i = 1; i < headers.length; i++) {
            try {
                PriceListIdentifier existingIdentifier = priceListIdentifierDao.getIdentifier(priceListIdentifiers.get(headers[i].trim()));
                identifiers.add(existingIdentifier);
            } catch (Exception ex) {
                String identifierCurrencyCode = headers[i].substring(headers[i].length()-3);
                Currency currency = currencyDao.getCurrencyByCode(identifierCurrencyCode);
                PriceListIdentifier newIdentifier = new PriceListIdentifier();
                newIdentifier.setCurrencyId(currency.getId());
                newIdentifier.setManufacturerId(manufacturerId);
                newIdentifier.setIdentifier(headers[i].trim());
                int identifierId = priceListIdentifierDao.createIdentifier(newIdentifier);
                newIdentifier.setId(identifierId);

                identifiers.add(newIdentifier);
            }
        }

        PriceListUploadFile priceList = new PriceListUploadFile(readAll(file), identifiers, partNumberColumn);
        return priceList;
    }

    private boolean hasInvalidPriceIdentifiers(String[] headers) {
        for (int i = 1; i < headers.length; i++) {
            String identifierCurrencyCode = headers[i].substring(headers[i].length()-3);
            if (!currencyDao.isValidCurrency(identifierCurrencyCode)) {
                return true;
            }
        }
        return false;
    }
}
