package co.cadshare.modelMgt.models.core.model.pdfPage;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import java.sql.Timestamp;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@JsonInclude(Include.NON_NULL)
@Data
@NoArgsConstructor
public class PdfPage {
  private Integer id;
  private int modelId;
  private String stateId;
  private String stateName;
  private Integer sequence;
  private Integer createdByUserId;
  private Timestamp createdDate;
  private Integer modifiedByUserId;
  private Timestamp modifiedDate;

  public static PdfPage createFrom(PdfPage pdfPage) {
    return PdfPage.builder()
      .modelId(pdfPage.getModelId())
      .stateId(pdfPage.getStateId())
      .stateName(pdfPage.getStateName())
      .sequence(pdfPage.getSequence())
      .build();
  }

  @Builder
  public PdfPage(int modelId, String stateId, String stateName, Integer sequence, Inte<PERSON> createdByUserId, Timestamp createdDate, Integer modifiedByUserId,
                     Timestamp modifiedDate) {
    this.id = id;
    this.modelId = modelId;
    this.stateId = stateId;
    this.stateName = stateName;
    this.sequence = sequence;
    this.createdByUserId = createdByUserId;
    this.createdDate = createdDate;
    this.modifiedByUserId = modifiedByUserId;
    this.modifiedDate = modifiedDate;
  }
}


