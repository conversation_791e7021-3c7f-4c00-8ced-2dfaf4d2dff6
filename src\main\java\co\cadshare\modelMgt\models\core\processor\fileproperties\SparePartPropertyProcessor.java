package co.cadshare.modelMgt.models.core.processor.fileproperties;

import co.cadshare.modelMgt.models.core.MetadataObjectExtended;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Component;

import java.util.LinkedHashMap;
import java.util.List;

@Component
public class SparePartPropertyProcessor extends AbstractPropertiesProcessor implements FilePropertiesProcessor, InitializingBean {

    List<String> sparePartSynonyms;

    @Override
    public void setProperties(LinkedHashMap properties, MetadataObjectExtended object) {

        object.setSparePartIdentifier(null);
        object.setSparePart(true);
        object.setSellablePart(false);
    }

    @Override
    public List<String> getSynonyms() {
        return sparePartSynonyms;
    }

    @Override
    public void setSynonyms(List<String> synonyms) {
        sparePartSynonyms = synonyms;
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        //not checking properties as synonyms won't be set at this point.
    }
}



