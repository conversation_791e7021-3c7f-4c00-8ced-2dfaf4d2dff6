package co.cadshare.exceptions;

import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ResponseStatus;

@ResponseStatus(value = HttpStatus.BAD_REQUEST)
public class ResourceValidationException extends RuntimeException {

    private static final long serialVersionUID = 1L;

    public ResourceValidationException(String message) {
        super(message);
    }

    public ResourceValidationException(String message, Throwable throwable) {
        super(message, throwable);
    }
}
