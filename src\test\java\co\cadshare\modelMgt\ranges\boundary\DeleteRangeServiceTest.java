package co.cadshare.modelMgt.ranges.boundary;

import co.cadshare.aspects.logging.ServiceLoggingAspect;
import co.cadshare.modelMgt.ranges.boundary.DeleteRangeService;
import co.cadshare.modelMgt.ranges.boundary.RangeCommandPort;
import co.cadshare.modelMgt.ranges.boundary.RangeQueryPort;
import co.cadshare.shared.core.user.User;
import co.cadshare.modelMgt.ranges.core.Range;
import org.junit.Before;
import org.junit.Test;
import org.mockito.MockitoAnnotations;
import org.junit.runner.RunWith;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import static org.mockito.Mockito.*;

@EnableAspectJAutoProxy
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(classes = {DeleteRangeService.class, ServiceLoggingAspect.class})
public class DeleteRangeServiceTest {

    @MockBean
    private RangeCommandPort commandPort;
    @MockBean
    private RangeQueryPort queryPort;
    @Autowired
    private DeleteRangeService out;
    private User user;
    private Range range;
    private Range errorRange = new Range();

    @Before
    public void Before() {
        MockitoAnnotations.initMocks(this);
        range = buildRange();
        user = new User() {{
            setUserId(1);
            setFirstName("firstName");
            setLastName("lastName");
        }};
    }

    @Test
    public void DeleteRangeSuccess() throws Exception {
        Integer returnVal = Integer.valueOf(1);
        when(queryPort.get(returnVal)).thenReturn(range);
        doNothing().when(commandPort).delete(user, range);
        out.delete(user, returnVal);
    }

    @Test(expected = RuntimeException.class)
    public void DeleteRangeFailureException() throws Exception {
        Integer returnVal = Integer.valueOf(2);
        when(queryPort.get(returnVal)).thenReturn(errorRange);
        doThrow(new RuntimeException("terrible")).when(commandPort).delete(user, errorRange);
        out.delete(user, returnVal);
    }

    private Range buildRange() {
        Range range = new Range();
        return range;
    }
}
