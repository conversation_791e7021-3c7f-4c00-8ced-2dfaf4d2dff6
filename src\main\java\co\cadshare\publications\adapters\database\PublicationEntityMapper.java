package co.cadshare.publications.adapters.database;

import co.cadshare.publications.core.Publication;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper(uses = {CoverImageEntityMapper.class, PublicationsDealerEntityMapper.class})
public interface PublicationEntityMapper {
    PublicationEntityMapper Instance = Mappers.getMapper(PublicationEntityMapper.class);

    Publication entityToCore(PublicationsPublicationEntity publication);

    PublicationsPublicationEntity coreToEntity(Publication publication);
    List<Publication> entitiesToCores(List<PublicationsPublicationEntity> publication);
    List<PublicationsPublicationEntity> coresToEntities(List<Publication> publications);

}
