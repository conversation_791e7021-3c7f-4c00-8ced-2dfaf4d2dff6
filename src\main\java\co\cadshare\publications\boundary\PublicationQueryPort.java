package co.cadshare.publications.boundary;

import co.cadshare.publications.core.Publication;
import co.cadshare.publications.core.Viewable;
import co.cadshare.shared.boundary.QueryPort;
import co.cadshare.shippingRequirements.core.ShippingRequirement;

import java.util.List;

public interface PublicationQueryPort extends QueryPort<Publication, Integer> {

    List<Publication> getPublicationsForManufacturer(Integer manufacturerId);
}
