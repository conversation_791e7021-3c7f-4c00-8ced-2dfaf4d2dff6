package co.cadshare.masterParts.boundary;

import co.cadshare.shared.core.Language;
import lombok.Data;

@Data
public abstract class MasterSearchRequest {
    private int page;
    private int size;
    private String partNumber;
    private String description;
    private String note;
    private Language language;

    protected static void build(MasterSearchRequest request, String partNumber, String partDescription, Language language) {
        request.setPartNumber(partNumber);
        request.setDescription(partDescription);
        request.setLanguage(language);
        request.setPage(1);
        request.setSize(100);
    }

	public boolean isPartNumberSearch() {
		return partNumber != null;
	}
}
