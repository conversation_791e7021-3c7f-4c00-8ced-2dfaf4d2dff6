package co.cadshare.addresses.core;

import co.cadshare.utils.ObjectUtilsExtension;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import lombok.ToString;
import lombok.experimental.ExtensionMethod;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.List;

@Data
@ExtensionMethod(ObjectUtilsExtension.class)
public class ExternalContact {
	private Integer id;
	private String externalRefId;
	private String name;
	private boolean deleted;

	@ToString.Exclude
	private List<ExternalAddressContact> addressContacts;

	@JsonIgnore
	protected final Logger logger = LoggerFactory.getLogger(getClass());

	public ExternalContact() {
		this.addressContacts = new ArrayList<>();
	}

	public boolean matchesExternally(ExternalContact contact) {
		if(externalRefId.isNull() || contact.getExternalRefId().isNull())
			return false;
		return (externalRefId.equals(contact.getExternalRefId()));
	}

	public boolean hasChanged(ExternalContact contact) {
		return !name.equals(contact.name);
	}

	public void updateContactDetails(ExternalContact contact) {
		name = contact.name;
	}

	public boolean isNew() {
		return id == null || id == 0;
	}

	public boolean isValid() {
		if(name == null) {
			this.logger.error(String.format("External contact failed CADshare contact validation, externalRefId [ %s ] | Cause: %s",
					externalRefId,
					"Contact Name cannot be null or empty"));
			return false;
		} else
			return true;
	}

	public void refresh(ExternalContact contact) {
		updateContactDetails(contact);
		deleted = false;
	}
}
