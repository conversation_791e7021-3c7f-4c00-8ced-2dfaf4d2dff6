package co.cadshare.modelMgt.publicationCategories.boundary;

import co.cadshare.aspects.logging.ServiceLoggingAspect;
import co.cadshare.exceptions.UnprocessableEntityException;
import co.cadshare.modelMgt.shared.boundary.PublicationQueryPort;
import co.cadshare.modelMgt.shared.core.Publication;
import co.cadshare.shared.core.user.User;
import co.cadshare.modelMgt.publicationCategories.core.PublicationCategory;
import org.junit.Before;
import org.junit.Test;
import org.mockito.MockitoAnnotations;
import org.junit.runner.RunWith;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.util.ArrayList;
import java.util.List;

import static org.mockito.Mockito.*;

@EnableAspectJAutoProxy
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(classes = {DeletePublicationCategoryService.class, ServiceLoggingAspect.class})
public class DeletePublicationCategoryServiceTest {

    @MockBean
    private PublicationCategoryCommandPort commandPort;
    @MockBean
    private PublicationCategoryQueryPort queryPort;
    @MockBean
    private PublicationQueryPort publicationQueryPort;
    @Autowired
    private DeletePublicationCategoryService out;
    private User user;
    private PublicationCategory publicationCategory;
    private PublicationCategory errorPublicationCategory = new PublicationCategory();

    @Before
    public void Before() {
        MockitoAnnotations.initMocks(this);
        publicationCategory = buildPublicationCategory();
        user = new User() {{
            setUserId(1);
            setFirstName("firstName");
            setLastName("lastName");
        }};
    }

    @Test
    public void DeletePublicationCategorySuccess() throws Exception {
        Integer returnVal = Integer.valueOf(1);
        when(queryPort.get(returnVal)).thenReturn(publicationCategory);
        when(publicationQueryPort.getPublicationsForPublicationCategory(returnVal)).thenReturn(new java.util.ArrayList<>());
        doNothing().when(commandPort).delete(user, publicationCategory);
        out.delete(user, returnVal);
    }

    @Test(expected = RuntimeException.class)
    public void DeletePublicationCategoryFailureException() throws Exception {
        Integer returnVal = Integer.valueOf(2);
        when(queryPort.get(returnVal)).thenReturn(errorPublicationCategory);
        when(publicationQueryPort.getPublicationsForPublicationCategory(returnVal)).thenReturn(new java.util.ArrayList<>());
        doThrow(new RuntimeException("terrible")).when(commandPort).delete(user, errorPublicationCategory);
        out.delete(user, returnVal);
    }

    @Test(expected = UnprocessableEntityException.class)
    public void DeletePublicationCategoryFailure() throws Exception {
        Integer returnVal = Integer.valueOf(2);
        when(queryPort.get(returnVal)).thenReturn(publicationCategory);
        List<Publication> publications = new ArrayList<>();
        publications.add(new Publication());
        when(publicationQueryPort.getPublicationsForPublicationCategory(returnVal)).thenReturn(publications);
        doThrow(new UnprocessableEntityException("terrible")).when(commandPort).delete(user, publicationCategory);
        out.delete(user, returnVal);
    }

    private PublicationCategory buildPublicationCategory() {
        PublicationCategory publicationCategory = new PublicationCategory();
        return publicationCategory;
    }
}
