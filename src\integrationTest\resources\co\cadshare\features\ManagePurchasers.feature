Feature: Manage Purchasers
  Manufacturers need to manage Dealers and Customers (Purchasers)

  Background:
    Given I am a Manufacturer with <NAME_EMAIL>

  Scenario: Create Dealer
    Given no Purchaser with name "Dealer-1" exists
    When I create a Dealer named "Dealer-1"
    Then a Purchaser with name "Dealer-1" should now exist

  Scenario: Update Dealer
    Given a Purchaser with name "Dealer-2" exists
    When I change the name of the Purchaser to "Dealer-3"
    Then a Purchaser with name "Dealer-3" should now exist
    And the Purchaser with name "Dealer-2" should no longer exist

  Scenario: Create Customer
    Given no Purchaser with name "Customer-1" exists
    When I create a Customer named "Customer-1"
    Then a Purchaser with name "Customer-1" should now exist

  Scenario: Delete Customer
    Given a Purchaser with name "Customer-2" exists
    When I delete a Purchaser named "Customer-2"
    Then the Purchaser with name "Customer-2" should no longer exist