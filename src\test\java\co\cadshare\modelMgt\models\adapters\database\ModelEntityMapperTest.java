package co.cadshare.modelMgt.models.adapters.database;

import co.cadshare.modelMgt.models.adapters.database.ModelsModelEntity;
import co.cadshare.modelMgt.models.adapters.database.ModelsModelEntityMapper;
import co.cadshare.modelMgt.models.core.model.AutodeskStatus;
import co.cadshare.modelMgt.models.core.Model;
import org.junit.Test;

import static org.junit.Assert.assertTrue;


public class ModelEntityMapperTest {

    @Test
    public void Map2dTest()  {
        ModelsModelEntity entity = new ModelsModelEntity(){{
            setIs2d(true);
            setIsSetupComplete(true);
            setAutodeskStatus(AutodeskStatus.FAILED);
        }};
        Model model = ModelsModelEntityMapper.Instance.entityToCore(entity);
        assertTrue(model.getIs2d());
    }

    @Test
    public void MapSetupCompleteTest()  {
        ModelsModelEntity entity = new ModelsModelEntity(){{
            setIs2d(true);
            setIsSetupComplete(true);
            setAutodeskStatus(AutodeskStatus.FAILED);
        }};
        Model model = ModelsModelEntityMapper.Instance.entityToCore(entity);
        assertTrue(model.getIsSetupComplete());
    }
}
