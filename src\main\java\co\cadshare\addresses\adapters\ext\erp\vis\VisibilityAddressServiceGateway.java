package co.cadshare.addresses.adapters.ext.erp.vis;

import co.cadshare.addresses.adapters.database.AddressDao;
import co.cadshare.addresses.boundary.ExternalAddressCommandPort;
import co.cadshare.addresses.boundary.ExternalAddressQueryPort;
import co.cadshare.addresses.core.ExternalPurchaser;
import co.cadshare.addresses.core.Address;
import co.cadshare.addresses.core.ExternalAddress;
import co.cadshare.shared.core.user.User;
import co.cadshare.exceptions.ExternalServiceException;
import co.cadshare.shared.adapters.ext.erp.vis.VisibilityXmlExtensions;
import co.cadshare.shared.adapters.ext.soap.SerialisedXmlExtensions;
import co.cadshare.shared.adapters.database.manufacturer.ManufacturerIntegrationDao;
import co.cadshare.shared.adapters.ext.erp.vis.BaseVisibilityServiceGateway;
import co.cadshare.persistence.ManufacturerSubEntityDao;
import co.cadshare.users.adapters.database.UserDetailsDao;
import co.cadshare.utils.CountryStateUtils;
import co.cadshare.utils.HttpRequestHelper;
import co.cadshare.utils.ObjectUtilsExtension;

import lombok.experimental.ExtensionMethod;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.HttpClientErrorException;
import java.util.ArrayList;
import java.util.List;

@Service
@ExtensionMethod({SerialisedXmlExtensions.class,
        VisibilityXmlExtensions.class,
        VisibilityAddressXmlExtensions.class,
        ObjectUtilsExtension.class})
public class VisibilityAddressServiceGateway extends BaseVisibilityServiceGateway implements ExternalAddressQueryPort, ExternalAddressCommandPort {

    private final String MISSING_VIS_ADDR_DATA = "Missing Address details for %s - address not synced.";
    private final ManufacturerSubEntityDao manufacturerSubEntityDao;
    private final AddressDao addressDao;

    @Autowired
    public VisibilityAddressServiceGateway(HttpRequestHelper httpRequestHelper,
                                           ManufacturerSubEntityDao manufacturerSubEntityDao,
                                           AddressDao addressDao,
                                           ManufacturerIntegrationDao manufacturerIntegrationDao,
                                           UserDetailsDao userDetailsDao) {

        super(httpRequestHelper, manufacturerIntegrationDao, userDetailsDao);
        this.manufacturerSubEntityDao = manufacturerSubEntityDao;
        this.addressDao = addressDao;
    }


    @Override
    public void createNewAddress(User user, Address address, Integer addressId) {
        if (user.getAssociatedManufacturerId() == 0) {
            log.error("No manufacturer id to get visibility's config");
            StringBuilder error = new StringBuilder();
            String visibilityConfigUrl = getVisibilityApiUrl(user.getAssociatedManufacturerId(), new StringBuilder());
            if (visibilityConfigUrl == null) return;

            String customerCode = manufacturerSubEntityDao.getCustomerCodeForManufacturerSubEntity(user.getManufacturerSubEntityId());
            if (customerCode == null) {
                log.error("Cannot create address, error: Visibility customer code is null");
                return;
            }
            try {
                String session = getSession(user.getAssociatedManufacturerId(), error);
                if (session.isEmpty()) return;
                String taxCode = "EX Tax";
                if (CountryStateUtils.checkIsCanadaCountry(address.getCountry())) {
                    taxCode = "AV-C";
                }
                if (CountryStateUtils.checkIsUnitedStatesCountry(address.getCountry())) {
                    taxCode = "AV-U";
                }
                address.setTaxCode(taxCode);
                log.info("Create address Visibility tax code = " + address.getTaxCode());
                createAddress(session, customerCode, address, visibilityConfigUrl, addressId, error);
                killSession(session, user.getAssociatedManufacturerId());
            } catch (Exception e) {
                log.error("Can not create visibility address [{}]", e.getMessage());
            }
        }
    }



    @Override
    public String createAddress(String session, String customerCode, Address address, String visibilityConfigUrl, Integer addressId, StringBuilder error) {
        String visAddressId;

        String body = "<?xml version=\"1.0\" encoding=\"utf-8\"?>\n" +
                "<soap:Envelope xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\" xmlns:xsd=\"http://www.w3.org/2001/XMLSchema\" xmlns:soap=\"http://schemas.xmlsoap.org/soap/envelope/\">\n" +
                "  <soap:Body>\n" +
                "    <CreateAddress xmlns=\"http://visibility.com/\">\n" +
                "      <strSecureID>" + session + "</strSecureID>\n" +
                "      <strTP_NAME_X>" + customerCode + "</strTP_NAME_X>" +
                "      <strSTREET_ADDR1_X>" + address.getAddressLine1() + "</strSTREET_ADDR1_X>\n" +
                "      <strSTREET_ADDR2_X>" + address.getAddressLine2() + "</strSTREET_ADDR2_X>\n" +
                "      <strCITY_X>" + address.getCity() + "</strCITY_X> " +
                "      <strZIP_POSTAL_CODE_X>" + address.getPostcode() + "</strZIP_POSTAL_CODE_X> " +
                "      <strSTATE_PROVINCE_X>" + (CountryStateUtils.checkIsUnitedStatesOrCanadaCountry(address.getCountry()) ? CountryStateUtils.getStateCodeByState (address.getState()) : "") + "</strSTATE_PROVINCE_X> " +
                "      <strCOUNTRY_X>" + CountryStateUtils.getCountryCodeByCountry(address.getCountry()) + "</strCOUNTRY_X> " +
                "      <StrTAX_X>" + address.getTaxCode() + "</StrTAX_X> " +
                "    </CreateAddress> " +
                "  </soap:Body> " +
                "</soap:Envelope>";
        try {
            log.info("Create CreateAddress request:" + body);
            ResponseEntity<String> response = httpRequestHelper.postSoapService(visibilityConfigUrl, body, String.class);
            String responseBody = response.getBody();
            visAddressId = responseBody.getVisAddressId();
            if (responseBody.returnCodeIsError()) {
                String errorMessage = responseBody.getReturnMessage();
                log.error("Cannot create address, error [{}], input data [{}]", errorMessage, body);
                error.append(errorMessage);
                return null;
            }
        } catch (HttpClientErrorException e) {
            log.error("Cannot create address, error [{}]", e.getResponseBodyAsString());
            error.append(e.getResponseBodyAsString());
            return null;
        } catch (Exception e) {
            log.error("Cannot create address, error [{}]", e.getMessage());
            error.append(e.getMessage());
            return null;
        }
        log.info("Create visibility address id [{}]", visAddressId);
        addressDao.updateVisibilityInfoForAddress(addressId, visAddressId);
        return visAddressId;
    }

    @Override
    public List<ExternalAddress> getExternalAddresses(ExternalPurchaser purchaser) throws Exception {

        List<ExternalAddress> cores = new ArrayList<>();
        if (purchaser.getExternalRefIdentifier().isNotNull()) {
            Integer manufacturerId = purchaser.getManufacturer().getManufacturerId();
            StringBuilder error = new StringBuilder();
            String visibilityConfigUrl = getVisibilityApiUrl(manufacturerId, new StringBuilder());
            if (visibilityConfigUrl == null)
                throw new ExternalServiceException("External service location is not available (URL is missing)");
            String session = getSession(manufacturerId, error);

            GetShipToAddressListRequest request = new GetShipToAddressListRequest(session, purchaser.getExternalRefIdentifier());
            String body = request.serialiseToSoapBody(VISIBILITY_NAMESPACE);
            log.debug("GetShipToAddressList request:" + body);

            ResponseEntity<String> response = httpRequestHelper.postSoapService(visibilityConfigUrl, body, String.class);
            String responseBody = response.getBody();
            if (responseBody.returnCodeIsError()) {
                String errorMessage = responseBody.getReturnMessage();
                log.error("Cannot get addresses for purchase, error [{}], input data [{}]", errorMessage, body);
                error.append(errorMessage);
            } else {
                if (responseBody == null) throw new AssertionError();
                String resultAsString = responseBody.getNestedXmlElementsByTagName("GetSHIP_TO_ADDRESSSResult");
                GetShipToAddressListResponse responseObject = resultAsString.deserialise();
                if (responseObject.getAddressDetails().isNull())
                    log.error(String.format(MISSING_VIS_ADDR_DATA, purchaser.getExternalRefIdentifier()));
                else
                    cores.addAll(ExternalAddressMapper.Instance.externalToCores(responseObject.getAddressDetails(),
                            manufacturerId, purchaser));
            }
        }
        return cores;
    }

}
