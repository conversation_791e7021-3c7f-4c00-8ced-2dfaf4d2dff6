package co.cadshare.masterKits.adapters.api.web;

import co.cadshare.masterKits.core.MasterKitSearchResult;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper(uses = MasterKitPriceSearchResultMapper.class)
public interface MasterKitSearchResultMapper {

    MasterKitSearchResultMapper Instance = Mappers.getMapper(MasterKitSearchResultMapper.class);

    MasterKitsSearchResponseItemDto coreToDto(MasterKitSearchResult core);

    List<MasterKitsSearchResponseItemDto> coresToDtos(List<MasterKitSearchResult> cores);
}
