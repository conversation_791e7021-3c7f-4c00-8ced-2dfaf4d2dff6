package co.cadshare.modelMgt.ranges.adapters.api.web;

import co.cadshare.aspects.access.roles.IsManufacturer;
import co.cadshare.domainmodel.range.Range;
import co.cadshare.services.ManufacturerService;
import co.cadshare.shared.core.user.User;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;

@RestController
@RequestMapping("/manufacturers/{manufacturer-id}/product-ranges")
public class ManufacturerProductRangesController {

	private final ManufacturerService manufacturerService;

	public ManufacturerProductRangesController(ManufacturerService manufacturerService) {
		this.manufacturerService = manufacturerService;
	}

	@GetMapping(produces = "application/json")
    @IsManufacturer
    @ResponseStatus(HttpStatus.OK)
    @Operation(summary = "Get the summary details of the list of all Product Ranges belonging to the Manufacturer")
    public ResponseEntity<GetProductRangeListResponseDto> getProductRanges(@PathVariable("manufacturer-id") int manufacturerId,
                                                                           @AuthenticationPrincipal User currentUser) {

		List<Range> rangeList = manufacturerService.getRangesForManufacturer(manufacturerId);
		List<ProductRangeListItemDto> productRangeDtos = ProductRangeMapper.Instance.RangesToDtos(rangeList);
        GetProductRangeListResponseDto response = new GetProductRangeListResponseDto();
		response.setProductRanges(productRangeDtos);
		return new ResponseEntity<>(response, HttpStatus.OK);
    }
}
