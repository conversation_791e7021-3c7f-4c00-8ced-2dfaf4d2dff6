{"info": {"_postman_id": "f700cd71-d9bc-434e-887b-7e4e4ed9aea3", "name": "API USER", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "300252"}, "item": [{"name": "1- API MANUFACTURER LOGIN", "event": [{"listen": "test", "script": {"exec": ["", "var responseData = JSON.parse(responseBody);", "postman.setEnvironmentVariable(\"access-token\", responseData.access_token);", "postman.setEnvironmentVariable(\"refresh-token\", responseData.refresh_token);", "tests[\"Status code is 200\"] = responseCode.code === 200;"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Basic Y2xpZW50YXBwOnNlY3JldA=="}, {"key": "Content-Type", "name": "Content-Type", "value": "application/x-www-form-urlencoded", "type": "text"}], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "grant_type", "value": "password", "type": "text"}, {"key": "username", "value": "api_manufacturer", "type": "text"}, {"key": "password", "value": "{{password}}", "type": "text"}]}, "url": {"raw": "http://localhost:8080/oauth/token", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["o<PERSON>h", "token"]}}, "response": []}, {"name": "2. Get manufacturerSubEntities", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{access-token}} ", "type": "text"}], "url": {"raw": "http://localhost:8080/api/companies", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["api", "companies"]}}, "response": []}, {"name": "2. Get users for Company", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{access-token}} ", "type": "text"}], "url": {"raw": "http://localhost:8080/api/companies/19/users", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["api", "companies", "19", "users"]}}, "response": []}, {"name": "2. Get user by id", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{access-token}} ", "type": "text"}], "url": {"raw": "http://localhost:8080/api/users/42", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["api", "users", "42"]}}, "response": []}, {"name": "3. Create User", "event": [{"listen": "test", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{access-token}} "}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n\t\"firstName\": \"<PERSON><PERSON><PERSON>\",\r\n\t\"lastName\": \"MILL<PERSON>\",\r\n\t\"emailAddress\": \"<EMAIL>\",\r\n\t\"userType\": \"MANUFACTURER_SUB_ENTITY_DEALER\",\r\n\t\"companyId\": 19\r\n}\r\n"}, "url": {"raw": "http://localhost:8080/api/users", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["api", "users"]}}, "response": []}, {"name": "4. Update User", "event": [{"listen": "test", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{access-token}} "}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n    \"userId\": 29,\r\n    \"firstName\": \"<PERSON>er\",\r\n    \"lastName\": \"UPDATED MITCHELL\",\r\n    \"emailAddress\": \"<EMAIL>\",\r\n    \"userType\": \"MANUFACTURER_SUB_ENTITY_CUSTOMER\",\r\n    \"companyId\": 19\r\n}"}, "url": {"raw": "http://localhost:8080/api/users/42", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["api", "users", "42"], "query": [{"key": "", "value": "", "disabled": true}]}}, "response": []}, {"name": "6. Delete User", "event": [{"listen": "test", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{access-token}} "}], "body": {"mode": "raw", "raw": "{\r\n\t\"userId\": 1234,\r\n\t\"firstName\": \"DAV<PERSON>\",\r\n\t\"lastName\": \"MILL<PERSON>\",\r\n\t\"emailAddress\": \"<EMAIL>\"\r\n\t\"manufacturerSubEntityId\": 1\r\n}\r\n"}, "url": {"raw": "http://localhost:8080/api/users/42", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["api", "users", "42"]}}, "response": []}]}