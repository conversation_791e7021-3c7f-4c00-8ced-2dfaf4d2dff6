package co.cadshare.modelMgt.viewables.adapters.api.web;

import co.cadshare.aspects.access.roles.CanAccessManufacturer;
import co.cadshare.aspects.access.roles.IsManufacturer;
import co.cadshare.modelMgt.shared.core.Viewable;
import co.cadshare.modelMgt.viewables.boundary.GetViewableService;
import co.cadshare.shared.core.user.User;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/manufacturers/{manufacturer-id}/viewables")
public class ViewablesController {

	private final GetViewableService getModelService;

	@Autowired
	public ViewablesController(GetViewableService getModelService) {
		this.getModelService = getModelService;
	}

	@GetMapping(produces = "application/json")
    @CanAccessManufacturer
	@IsManufacturer
    @ResponseStatus(HttpStatus.OK)
    @Operation(summary = "Get the summary details of the list of all Viewables belonging to the specified Manufacturer")
    public ResponseEntity<GetViewableListResponseDto> getViewables(@PathVariable("manufacturer-id") int manufacturerId,
                                                                   @AuthenticationPrincipal User currentUser) {


		List<Viewable> viewableList = getModelService.getViewablesForManufacturer(manufacturerId);
        List<ViewableListItemDto> viewables = ViewableMapper.Instance.viewableToViewableListItemDto(viewableList);
        GetViewableListResponseDto response = new GetViewableListResponseDto() {{ setViewables(viewables);}};
        return new ResponseEntity<>(response, HttpStatus.OK);
    }

	@PostMapping(produces = "application/json", value = "/search")
	@CanAccessManufacturer
	@IsManufacturer
	@ResponseStatus(HttpStatus.OK)
	@Operation(summary = "Search the list of all Viewables belonging to the specified Manufacturer")
	public ResponseEntity<PostSearchViewablesResponseDto> searchViewables(@PathVariable("manufacturer-id") int manufacturerId,
	                                                                    @AuthenticationPrincipal User currentUser,
	                                                                    @RequestBody PostSearchViewablesRequestDto request) {


		List<Viewable> viewableList = getModelService.searchViewables(manufacturerId, request.getSearchParameter());
		List<ViewableListItemDto> viewables = ViewableMapper.Instance.viewableToViewableListItemDto(viewableList);
		PostSearchViewablesResponseDto response = new PostSearchViewablesResponseDto() {{ setSearchResults(viewables);}};
		return new ResponseEntity<>(response, HttpStatus.OK);
	}

	@PostMapping(produces = "application/json", value = "/filter")
	@CanAccessManufacturer
	@IsManufacturer
	@ResponseStatus(HttpStatus.OK)
	@Operation(summary = "Search the list of all Viewables belonging to the specified Manufacturer")
	public ResponseEntity<PostFilterViewablesResponseDto> filterViewables(@PathVariable("manufacturer-id") int manufacturerId,
	                                                                      @AuthenticationPrincipal User currentUser,
	                                                                      @RequestBody PostFilterViewablesRequestDto request) {


		List<Viewable> viewableList = getModelService.filterViewablesByProduct(manufacturerId, request.getFilterProductId());
		List<ViewableListItemDto> viewables = ViewableMapper.Instance.viewableToViewableListItemDto(viewableList);
		PostFilterViewablesResponseDto response = new PostFilterViewablesResponseDto() {{ setFilterResults(viewables);}};
		return new ResponseEntity<>(response, HttpStatus.OK);
	}
}
