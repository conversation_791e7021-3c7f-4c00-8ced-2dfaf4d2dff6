package co.cadshare.masterParts.adapters.database;

import co.cadshare.masterParts.core.MasterPart;
import co.cadshare.masterParts.core.SupersessionHistoryItem;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper(uses = MasterPartTranslationEntityViewMapper.class)
public interface MasterPartEntityViewMapper {

    MasterPartEntityViewMapper Instance = Mappers.getMapper(MasterPartEntityViewMapper.class);

    @Mapping(source = "id", target = "masterPartId")
    @Mapping(source = "partNumber", target = "partNumber")
    @Mapping(source = "translations", target = "languageDescriptions")
    @Mapping(source = "supersessionPartNumber", target = "supersedingPartNumber")
    SupersessionHistoryItem entityToSupersessionHistoryItem(MasterPartSupersessionEntityView entity);

    List<SupersessionHistoryItem> entityToSupersessionHistoryItems(List<MasterPartSupersessionEntityView> entities);

    @Mapping(source = "id", target = "masterPartId")
    MasterPart entityToMasterPart(MasterPartEntityView entity);

    List<MasterPart> entitiesToMasterParts(List<MasterPartEntityView> entities);
}
