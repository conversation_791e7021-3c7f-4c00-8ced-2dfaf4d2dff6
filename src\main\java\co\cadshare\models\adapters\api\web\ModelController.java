/*
 * Copyright 2016 Bell.
 */
package co.cadshare.models.adapters.api.web;

import co.cadshare.masterParts.boundary.MasterPartLegacySupersessionService;
import co.cadshare.aspects.access.languages.CanUseLanguage;
import co.cadshare.aspects.access.users.HasAdminPermissionsForUserId;
import co.cadshare.models.boundary.PartViewerDetailsService;
import co.cadshare.masterParts.boundary.MasterPartService;
import co.cadshare.masterParts.core.extensions.partModelLink.PartModelLink;
import co.cadshare.masterParts.core.extensions.partModelLink.LegacySupersededModelLink;
import co.cadshare.shared.core.Language;

import java.util.ArrayList;
import java.util.List;

import co.cadshare.models.boundary.CreateModelService;
import co.cadshare.models.boundary.ModelService;
import co.cadshare.models.core.model.viewable.Viewable;
import co.cadshare.publications.boundary.ManualService;
import co.cadshare.services.*;
import co.cadshare.models.boundary.AutodeskPort;
import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.security.access.prepost.PostAuthorize;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import co.cadshare.domainmodel.autodesk.AutodeskResource;
import co.cadshare.publications.core.Manual;
import co.cadshare.models.core.Model;
import co.cadshare.domainmodel.part.Part;
import co.cadshare.domainmodel.part.PartViewerDetails;
import co.cadshare.domainmodel.partTree.PartTree;
import co.cadshare.shared.core.user.User;
import co.cadshare.response.ModelTranslationWarnings;


@Slf4j
@Controller
@RequestMapping("/model")
public class ModelController {

	private final ModelService modelService;
	private final CreateModelService createModelService;
	private final ManualService manualService;
	private final ViewableService viewableService;
	private final PartService partService;
	private final PartModelLinkService partLinkService;
	private final PermissionsService permissionsService;
	private final AutodeskPort autodesk;
	private final PartViewerDetailsService partViewerDetailsService;
	private final MasterPartLegacySupersessionService supersessionService;

  @Autowired
  public ModelController(ModelService modelService,
                         CreateModelService createModelService,
                         ManualService manualService,
                         ViewableService viewableService,
                         PartService partService,
                         PartModelLinkService partLinkService,
                         PermissionsService permissionsService,
                         AutodeskPort autodesk,
                         PartViewerDetailsService partViewerDetailsService,
                         MasterPartLegacySupersessionService supersessionService) {
		this.modelService = modelService;
		this.createModelService = createModelService;
		this.manualService = manualService;
		this.viewableService = viewableService;
		this.partService = partService;
		this.partLinkService = partLinkService;
		this.permissionsService = permissionsService;
		this.autodesk = autodesk;
		this.partViewerDetailsService = partViewerDetailsService;
		this.supersessionService = supersessionService;
  }

  @PostMapping(consumes = "application/json")
  public HttpEntity<Integer> createModel(@AuthenticationPrincipal User currentUser,
                                         @RequestBody Model model) throws Exception {

    log.info("ACCESS: User [{}], createModel, model [{}]", currentUser.accessDetails(), model.toString());
    model.setCreatedByUserId(currentUser.getUserId());
    int modelId = createModelService.createModel(currentUser, model);
    log.info("Model with id [{}] created", modelId);
    return new ResponseEntity<>(modelId, HttpStatus.OK);
  }

  @GetMapping(value = "/{modelId}")
  public HttpEntity<Model> getModel(@AuthenticationPrincipal User currentUser,
                                    @PathVariable int modelId) {

    log.info("ACCESS: User [{}], getModel, modelId [{}]", currentUser.accessDetails(), modelId);
    try {
      userHasPermissionsForModel(currentUser.getUserId(), modelId);
    } catch (Exception ex) {
      return new ResponseEntity<>(HttpStatus.FORBIDDEN);
    }
    Model model = modelService.getModel(modelId);
    return new ResponseEntity<>(model, HttpStatus.OK);
  }

  @GetMapping(value = "/{modelId}/manuals")
  public HttpEntity<List<Manual>> getManualsForModel(@AuthenticationPrincipal User currentUser,
                                                     @PathVariable int modelId) {

    log.info("ACCESS: User [{}], getManualsForModel, modelId [{}]", currentUser.accessDetails(), modelId);
    try {
      userHasPermissionsForModel(currentUser.getUserId(), modelId);
    } catch (Exception ex) {
      return new ResponseEntity<>(HttpStatus.FORBIDDEN);
    }
    List<Manual> manualList = manualService.getManualsForModel(modelId);
    return new ResponseEntity<>(manualList, HttpStatus.OK);
  }

  @GetMapping(value = "/{modelId}/snapshotStates")
  public HttpEntity<Viewable> getStatesForModel(@AuthenticationPrincipal User currentUser,
                                                @PathVariable int modelId) {

    log.info("ACCESS: User [{}], getStateForModel, modelId [{}]", currentUser.accessDetails(), modelId);

    try {
      userHasPermissionsForModel(currentUser.getUserId(), modelId);
    } catch (Exception ex) {
      return new ResponseEntity<>(HttpStatus.FORBIDDEN);
    }
    Viewable viewableList = viewableService.getStatesForModel(modelId);

    return new ResponseEntity<>(viewableList, HttpStatus.OK);
  }

  @GetMapping(value = "/{modelId}/pdfStates")
  public HttpEntity<Viewable> getPdfStatesForModel(@AuthenticationPrincipal User currentUser,
                                                   @PathVariable int modelId) {

    log.info("ACCESS: User [{}], getPdfStatesForModel, modelId [{}]", currentUser.accessDetails(), modelId);
    try {
      userHasPermissionsForModel(currentUser.getUserId(), modelId);
    } catch (Exception ex) {
      return new ResponseEntity<>(HttpStatus.FORBIDDEN);
    }
    Viewable viewableList = viewableService.getStatesForModel(modelId);
    return new ResponseEntity<>(viewableList, HttpStatus.OK);
  }

  @DeleteMapping(value = "/{modelId}")
  @PreAuthorize("hasRole('ROLE_MANUFACTURER') and @permissionsDao.hasAccessToModel(#currentUser.getUserId(), #modelId)")
  public HttpEntity<Boolean> deleteModel(@AuthenticationPrincipal User currentUser,
                                         @PathVariable int modelId) throws Exception {

    log.info("ACCESS: User [{}], deleteModel, model id [{}]", currentUser.accessDetails(), modelId);
    modelService.deleteModel(currentUser, modelId);
    return new ResponseEntity<>(true, HttpStatus.OK);
  }

  @PutMapping(value = "/{modelId}", consumes = "application/json")
  @PreAuthorize("hasRole('ROLE_MANUFACTURER') and @permissionsDao.hasAccessToModel(#currentUser.getUserId(), #modelId)")
  public HttpEntity<Boolean> updateModel(@AuthenticationPrincipal User currentUser,
                                         @RequestBody Model model,
                                         @PathVariable int modelId) {

    log.info("ACCESS: User [{}], updateModel, model id [{}], model [{}]", currentUser.accessDetails(), modelId, model);
    model.setModelId(modelId);
    model.setModifiedByUserId(currentUser.getUserId());
    modelService.updateModel(model);
    return new ResponseEntity<>(HttpStatus.OK);
  }

  @PutMapping(value = "/setupComplete", consumes = "application/json")
  @PreAuthorize("hasRole('ROLE_MANUFACTURER') and @permissionsDao.hasAccessToModel(#currentUser.getUserId(), #modelSetup.getModelId())")
  public HttpEntity<Boolean> modelSetupComplete(@AuthenticationPrincipal User currentUser,
                                                @RequestBody Model modelSetup) {

    modelService.getModel(modelSetup.getModelId());
    modelService.modelSetupComplete(modelSetup);
    return new ResponseEntity<>(HttpStatus.OK);
  }


  @GetMapping(value = "/{modelId}/parts", consumes = "application/json")
  @PreAuthorize("@permissionsDao.hasAccessToModel(#currentUser.getUserId(), #modelId)")
  @CanUseLanguage
  public HttpEntity<List<Part>> getPartsForModelId(@AuthenticationPrincipal User currentUser,
                                                   @PathVariable int modelId,
                                                   @RequestParam(value = "language", required = false) Language language) throws Exception {

    log.info("ACCESS: User [{}], getPartsForModelId, model id [{}]", currentUser.accessDetails(), modelId);

    try {
      userHasPermissionsForModel(currentUser.getUserId(), modelId);
    } catch (Exception ex) {
      return new ResponseEntity<>(HttpStatus.FORBIDDEN);
    }
    List<Part> partList = partService.getPartsForModel(currentUser, modelId, currentUser.findLanguage(language));
    return new ResponseEntity<>(partList, HttpStatus.OK);
  }

  @GetMapping(value = "/{modelId}/partTree", produces = "application/json")
  @PreAuthorize("@permissionsDao.hasAccessToModel(#currentUser.getUserId(), #modelId)")
  @CanUseLanguage
  public HttpEntity<PartTree> getPartTreeForModelId(@AuthenticationPrincipal User currentUser,
                                                    @PathVariable int modelId,
                                                    @RequestParam(value = "language", required = false) Language language) throws Exception {

    log.info("ACCESS: User [{}], getPartTreeForModelId, model id [{}]", currentUser.accessDetails(), modelId);

    try {
      userHasPermissionsForModel(currentUser.getUserId(), modelId);
    } catch (Exception ex) {
      return new ResponseEntity<>(HttpStatus.FORBIDDEN);
    }
    Integer languageId = language != null ? currentUser.findLanguage(language).getLanguageId() : null;
    PartTree partTree = partService.getPartTreeForModel(modelId, languageId, currentUser.obtainDefaultLanguage());
    return new ResponseEntity<>(partTree, HttpStatus.OK);
  }

  @GetMapping(value = "/{modelId}/part/{objectId}", produces = "application/json")
  @CanUseLanguage
  public ResponseEntity<Part> getPartForModelId(@AuthenticationPrincipal User currentUser,
                                                @PathVariable int modelId,
                                                @PathVariable int objectId,
                                                @RequestParam(value = "language", required = false) Language language) throws Exception {

    log.info("ACCESS: User [{}], getPartForModelId, object id [{}], objectId [{}]", currentUser.accessDetails(), modelId, objectId);
    Integer languageId = language != null ? currentUser.findLanguage(language).getLanguageId() : null;
    Part part = partService.getPartForModel(modelId, objectId, languageId, currentUser.obtainDefaultLanguage());
    return new ResponseEntity<Part>(part, HttpStatus.OK);
  }

  @GetMapping(value = "/{modelId}/linkableViewables", produces = "application/json")
  public HttpEntity<List<Model>> getPartsLinkableViewables(@AuthenticationPrincipal User currentUser,
                                                           @PathVariable int modelId) {

    log.info("ACCESS: User [{}], getPartsLinkableViewables", currentUser.accessDetails());
    List<Model> linkableModelList = partLinkService.getLinkableModels(modelId);
    return new ResponseEntity<>(linkableModelList, HttpStatus.OK);
  }

  @GetMapping(value = "/{modelId}/part/{objectId}/linkedViewable", produces = "application/json")
  public HttpEntity<PartModelLink> getPartsLinkedViewable(@AuthenticationPrincipal User currentUser,
                                                          @PathVariable int modelId,
                                                          @PathVariable int objectId) {

    log.info("ACCESS: User [{}], getPartsLinkableViewables, partId [{}]", currentUser.accessDetails(), objectId);
    Part part = partService.getPartForModel(modelId, objectId, null, null);
    PartModelLink partLink = partLinkService.getActivePartLink(part.getPartId());
    return new ResponseEntity<>(partLink, HttpStatus.OK);
  }

  @PutMapping(value = "/{modelId}/part/{objectId}/link", consumes = "application/json", produces = "application/json")
  @PreAuthorize("hasRole('ROLE_MANUFACTURER')")
  public HttpEntity<PartModelLink> createPartsLinkableViewables(@AuthenticationPrincipal User currentUser,
                                                                @PathVariable int modelId,
                                                                @PathVariable int objectId,
                                                                @RequestBody int viewableId) {

    log.info("ACCESS: User [{}], savePartsLinkableViewables, modelId = [{}], partObjectId [{}], link to viewable [{}]",
		    currentUser.accessDetails(),
		    modelId,
		    objectId,
		    viewableId);
    PartModelLink linkedPart = partLinkService.savePartLink(modelId, objectId, viewableId, currentUser.getUserId());
    return new ResponseEntity<>(linkedPart, HttpStatus.OK);
  }

  @PutMapping(value = "/partLink/{partLinkId}", consumes = "application/json", produces = "application/json")
  @PreAuthorize("hasRole('ROLE_MANUFACTURER') and @permissionsDao.hasAccessToModel(#currentUser.getUserId(), @partModelLinkService.getPartLinkById(#partLinkId).getModelId())")
  public HttpEntity<PartModelLink> updatePartLink(@AuthenticationPrincipal User currentUser,
                                                  @PathVariable int partLinkId,
                                                  @RequestBody PartModelLink partModelLink) {

    log.info("ACCESS: User [{}], updatePartLink, partLinkId = [{}]", currentUser.accessDetails(), partLinkId);
    PartModelLink linkedPart = partLinkService.updatePartLink(partLinkId, partModelLink, currentUser.getUserId());
    return new ResponseEntity<>(linkedPart, HttpStatus.OK);
  }

  @DeleteMapping(value = "/partLink/{partLinkId}", produces = "application/json")
  @PreAuthorize("hasRole('ROLE_MANUFACTURER') and @permissionsDao.hasAccessToModel(#currentUser.getUserId(), @partModelLinkService.getPartLinkById(#partLinkId).getModelId())")
  public HttpEntity<Boolean> deletePartsLinkableViewables(@AuthenticationPrincipal User currentUser, @PathVariable int partLinkId) {

    log.info("ACCESS: User [{}], deletePartsLinkableViewables, partLinkId = [{}]", currentUser.accessDetails(), partLinkId);
    boolean response = partLinkService.deletePartLink(partLinkId, currentUser);
    return new ResponseEntity<>(response, HttpStatus.OK);
  }

  @RequestMapping(value = "/{modelId}/partLink", method = RequestMethod.GET, produces = "application/json")
  @PreAuthorize("@permissionsDao.hasAccessToModel(#currentUser.getUserId(), #modelId)")
  @CanUseLanguage
  public ResponseEntity<List<PartModelLink>> getPartsLinksByModelId(@AuthenticationPrincipal User currentUser,
                                                                    @PathVariable int modelId,
                                                                    @RequestParam(value = "language", required = false) Language language) throws Exception {

    log.info("ACCESS: User [{}], getPartsLinksByModelId, modelId [{}]", currentUser.accessDetails(), modelId);
    Integer languageId = language != null ? currentUser.findLanguage(language).getLanguageId() : null;
    List<PartModelLink> partLink = partLinkService.getPartLinksForModelId(modelId,
            languageId,
            currentUser.obtainDefaultLanguage(),
            currentUser.getManufacturerId());
    return new ResponseEntity<>(partLink, HttpStatus.OK);
  }

  @GetMapping(value = "/partLink/{linkId}", produces = "application/json")
  @PostAuthorize("@permissionsDao.hasAccessToModel(#currentUser.getUserId(), returnObject.getBody().getModelId())")
  @CanUseLanguage
  public ResponseEntity<PartModelLink> getPartLinkById(@AuthenticationPrincipal User currentUser,
                                                       @PathVariable int linkId,
                                                       @RequestParam(value = "language", required = false) Language language) {

    log.info("ACCESS: User [{}], getPartLinkById, linkId [{}]", currentUser.accessDetails(), linkId);
    PartModelLink partLink = partLinkService.getPartLinkById(linkId);
    return new ResponseEntity<>(partLink, HttpStatus.OK);
  }

  @GetMapping(value = "/{modelId}/part/{objectId}/viewerDetails", produces = "application/json")
  @PreAuthorize("@permissionsDao.hasAccessToModel(#currentUser.getUserId(), #modelId)")
  @HasAdminPermissionsForUserId
  @CanUseLanguage
  public ResponseEntity<PartViewerDetails> getPartViewerDetails(@AuthenticationPrincipal User currentUser,
                                                                @PathVariable int modelId,
                                                                @PathVariable int objectId,
                                                                @RequestParam(value = "language", required = false) Language language,
                                                                @RequestParam(value = "filterKitsManualId", required = false) Integer manualId,
                                                                @RequestParam(value = "userId", required = false) Integer userId,
                                                                @RequestParam(value="userType", required = false, defaultValue = "manufacturer") String userType) throws Exception {

    log.info("ACCESS: User [{}], getModelPartViewerDetails, associatedDbId [{}]", currentUser.accessDetails(), objectId);
	PartViewerDetails partViewerDetails = partViewerDetailsService.getPartViewerDetails(currentUser,
		    manualId,
		    modelId,
		    objectId,
			currentUser.findLanguage(language),
		    userId);
    return new ResponseEntity<>(partViewerDetails, HttpStatus.OK);
  }

  @GetMapping(value = "/{modelId}/partCount", produces = "application/json")
  public ResponseEntity<Integer> getModelPartCount(@AuthenticationPrincipal User currentUser,
                                                   @PathVariable int modelId) {

    log.info("ACCESS: User [{}], getModelPartCount, modelId [{}]", currentUser.accessDetails(), modelId);
    int count = modelService.getPartCountForModel(modelId);
    return new ResponseEntity<>(count, HttpStatus.OK);
  }

  //TODO this may be from old offline mode download. Possibly delete?
  @GetMapping(value = "/{modelId}/viewerDetails", produces = "application/json")
  @CanUseLanguage
  public ResponseEntity<List<PartViewerDetails>> getModelViewerDetails(@AuthenticationPrincipal User currentUser,
                                                                       @PathVariable int modelId,
                                                                       @RequestParam int start,
                                                                       @RequestParam int size,
                                                                       @RequestParam(value = "language", required = false) Language language) throws Exception {

    log.info("ACCESS: User [{}], getModelViewerDetails, modelId [{}]", currentUser.accessDetails(), modelId);
    List<PartViewerDetails> modelViewerDetails = new ArrayList<>();
    List<Part> parts = (start == 0 && size == 0) ?
      partService.getPartsForModel(currentUser, modelId, currentUser.findLanguage(language)) :
      partService.getPaginatedPartsForModel(currentUser, modelId, start, size, currentUser.findLanguage(language));
    Integer languageId = currentUser.findLanguage(language).getLanguageId();
    for (Part part : parts)
      modelViewerDetails.add(modelService.getModelPartViewerDetails(modelId,
              part,
              languageId,
              currentUser.obtainDefaultLanguage(),
              currentUser.getManufacturerId()));
    return new ResponseEntity<>(modelViewerDetails, HttpStatus.OK);
  }

  @GetMapping(value = "/{modelId}/translationErrors", produces = "application/json")
  @PreAuthorize("@permissionsDao.hasAccessToModel(#currentUser.getUserId(), #modelId)")
  public ResponseEntity<ModelTranslationWarnings> getTranslationErrorReportForModelId(@AuthenticationPrincipal User currentUser,
                                                                                      @PathVariable int modelId) {

    log.info("ACCESS: User [{}], getTranslationErrorReportForModelId, model id [{}]", currentUser.accessDetails(), modelId);
    ModelTranslationWarnings warnings = modelService.getTranslationErrors(modelId);
    return new ResponseEntity<>(warnings, HttpStatus.OK);
  }

  @GetMapping("/{modelId}/autodeskresources")
  public HttpEntity<List<AutodeskResource>> getAllAutodeskModelResources(@AuthenticationPrincipal User currentUser,
                                                                         @PathVariable Integer modelId) {

    log.info("ACCESS: User [{}], getAllAutodeskModelResources, model id [{}]", currentUser.accessDetails(), modelId);
    Model model = modelService.getModel(modelId);
    return new HttpEntity<>(autodesk.getAllAutodeskModelResources(model));
  }

  @PutMapping(value = "/{modelId}/settings", consumes = "application/json")
  public ResponseEntity<Boolean> updateViewerSettings(@AuthenticationPrincipal User currentUser,
                                                      @PathVariable int modelId,
                                                      @RequestBody Viewable viewableSettings) {

    viewableSettings.setModifiedByUserId(currentUser.getUserId());
    Viewable coreViewable = viewableService.getViewableByModelId(modelId);
    viewableService.updateViewableSettings(coreViewable, viewableSettings);
    return new ResponseEntity<>(true, HttpStatus.OK);
  }

  @GetMapping("/{modelId}/masterPart/superseded")
  public HttpEntity<List<LegacySupersededModelLink>> getSupersededMasterPartsForModel(@AuthenticationPrincipal User currentUser,
                                                                                      @PathVariable Integer modelId) {

    log.info("ACCESS: User [{}], getSupersededMasterPartsForModel, model id [{}]", currentUser.accessDetails(), modelId);

    List<LegacySupersededModelLink> supersededParts = supersessionService.getSupersededMasterPartsForModel(modelId);

    return new ResponseEntity<List<LegacySupersededModelLink>>(supersededParts, HttpStatus.OK);
  }

	private void userHasPermissionsForModel(int userId, int modelId) throws Exception {
		permissionsService.userHasPermissionsToViewModel(userId, modelId);
	}

}
