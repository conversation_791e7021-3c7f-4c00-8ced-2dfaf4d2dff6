package co.cadshare.modelMgt.publicationCategories.boundary;

import co.cadshare.modelMgt.publicationCategories.core.PublicationCategory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.dao.EmptyResultDataAccessException;

import co.cadshare.exceptions.NotFoundException;
import co.cadshare.shared.core.user.User;
import co.cadshare.aspects.logging.Log;

@Service
public class UpdatePublicationCategoryService {

    private final PublicationCategoryCommandPort commandPort;
    private final PublicationCategoryQueryPort queryPort;

    @Autowired
    public UpdatePublicationCategoryService(PublicationCategoryCommandPort commandPort,
                                    PublicationCategoryQueryPort queryPort) {
        this.commandPort = commandPort;
        this.queryPort = queryPort;
    }

    @Log
    public void update(User user, PublicationCategory publicationCategory) throws Exception {
        try {
	        PublicationCategory existingPublicationCategory = this.queryPort.get(publicationCategory.getId());
			existingPublicationCategory.update(publicationCategory);
            this.commandPort.update(user, existingPublicationCategory);
        } catch (EmptyResultDataAccessException ex) {
            throw new NotFoundException("PublicationCategory does not exist");
        }
    }
}
