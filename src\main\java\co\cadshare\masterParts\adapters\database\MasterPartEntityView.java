package co.cadshare.masterParts.adapters.database;

import co.cadshare.shared.adapters.database.MasterPartEntity;
import com.blazebit.persistence.view.EntityView;
import com.blazebit.persistence.view.IdMapping;
import com.blazebit.persistence.view.Mapping;

import javax.persistence.Column;
import java.util.List;

@EntityView(MasterPartEntity.class)
public interface MasterPartEntityView {

    @IdMapping
    Integer getId();

    @Mapping
    String getPartNumber();

    @Mapping
    String getSupersessionPartNumber();

    @Mapping
    boolean getSuperseded();

    @Mapping
    Integer getMaxSupersessionPartId();

    @Mapping
    String getMaxSupersessionPartNumber();

    @Mapping
    boolean getInSupersession();

    @Mapping
    Integer getSupersessionReverseIndex();

}
