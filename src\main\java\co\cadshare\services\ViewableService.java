/*
 * Copyright 2016 Bell.
 */
package co.cadshare.services;

import co.cadshare.shared.core.manufacturer.Manufacturer;
import co.cadshare.models.core.model.viewable.Viewable;
import co.cadshare.models.core.model.viewable.StateDetail;
import co.cadshare.shared.core.manufacturer.ManufacturerSettings;
import co.cadshare.shared.adapters.database.manufacturer.ManufacturerDao;
import co.cadshare.persistence.ModelDataDao;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
public class ViewableService {

    private ModelDataDao modelDataDao;
    private ManufacturerDao manufacturerDao;
    private StateDetailService stateDetailService;
    private ModelUpdateService modelUpdateService;

    public ViewableService(ModelDataDao modelDataDao, ManufacturerDao manufacturerDao, StateDetailService stateDetailService, ModelUpdateService modelUpdateService) {
        this.modelDataDao = modelDataDao;
        this.manufacturerDao = manufacturerDao;
        this.stateDetailService = stateDetailService;
        this.modelUpdateService = modelUpdateService;
    }

    public Viewable getViewableByModelId(int modelId) {
        return modelDataDao.getCoreViewableByModelId(modelId);
    }

    public Viewable getStatesForModel(int modelId) {
        Viewable viewable = getViewableByModelId(modelId);

        List<StateDetail> stateDetails = stateDetailService.getStateDetailsForViewable(viewable.getId());
        viewable.setStateDetails(stateDetails);

        return viewable;
    }

    public int createViewable(Viewable viewable) {
        if (viewable.getId() == 0) {
            boolean edgingDefault = getManufacturerDefaultEdgingForModel(viewable.getModelId());
            viewable.setEdgingEnabled(edgingDefault);
            int viewableId = modelDataDao.createViewable(viewable);
            viewable.setId(viewableId);
        }
        modelUpdateService.update(viewable.getModelId());

        return viewable.getId();
    }

    private boolean getManufacturerDefaultEdgingForModel(int modelId) {
        Manufacturer manufacturer = manufacturerDao.getManufacturerForModel(modelId);
        ManufacturerSettings settings = manufacturerDao.getManufacturerSettingsById(manufacturer.getManufacturerId());
        return settings.isEdgingEnabledDefault();
    }

    public void updateViewable(Viewable viewable) {
        modelDataDao.updateViewable(viewable);
        stateDetailService.updateStateDetailsForModelState(viewable);

        modelUpdateService.update(viewable.getModelId());
    }

    public Viewable getViewable(int viewableId) {
        return modelDataDao.getViewable(viewableId);
    }

    public void updateViewableSettings(int viewableId, Viewable viewableSettings) {
        Viewable viewable = modelDataDao.getViewable(viewableId);
        updateViewableSettings(viewable, viewableSettings);
    }

    public void updateViewableSettings(Viewable viewable, Viewable viewableSettings) {
        viewable.setLineDrawingEnabled(viewableSettings.getLineDrawingEnabled());
        viewable.setViewLocked(viewableSettings.getViewLocked());
        viewable.setEdgingEnabled(viewableSettings.getEdgingEnabled());
        modelDataDao.updateViewable(viewable);
    }
}
