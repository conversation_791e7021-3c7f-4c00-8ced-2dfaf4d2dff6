package co.cadshare.addresses.adapters.database;

import co.cadshare.addresses.boundary.AddressQueryPort;
import co.cadshare.addresses.core.Address;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.namedparam.BeanPropertySqlParameterSource;
import org.springframework.jdbc.core.namedparam.MapSqlParameterSource;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.jdbc.support.GeneratedKeyHolder;
import org.springframework.jdbc.support.KeyHolder;
import org.springframework.stereotype.Repository;

import java.sql.Timestamp;
import java.util.Calendar;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Repository
public class AddressDao {

  private final Logger log = LoggerFactory.getLogger(getClass());
  private final NamedParameterJdbcTemplate namedParamJdbcTemplate;
  private final static String SELECT_ADDRESS_BY_ID = "SELECT a.*, uam.taxexempt FROM address a " +
          "INNER JOIN useraddressmap uam ON a.id = uam.addressid " +
          "WHERE a.id = :addressId ";

  @Autowired
  public AddressDao(NamedParameterJdbcTemplate namedParamJdbcTemplate) {
      this.namedParamJdbcTemplate = namedParamJdbcTemplate;
  }

    public Address getAddressById(int addressId) {

    Map<String, Object> parameters = new HashMap<String, Object>();
    parameters.put("addressId", addressId);

    Address address = namedParamJdbcTemplate.queryForObject(SELECT_ADDRESS_BY_ID, parameters, new BeanPropertyRowMapper<>(Address.class));
    return address;
  }

  private final static String GET_ADDRESSES_FOR_USER_ID = "SELECT a.*, uam.taxexempt FROM address a " +
          "INNER JOIN useraddressmap uam ON uam.addressid = a.id " +
          "WHERE uam.userid = :userId AND uam.archived = FALSE AND a.archived = FALSE ";

  public List<Address> getAddressesForUser(int userId) {
    Map<String, Object> parameters = new HashMap<String, Object>();
    parameters.put("userId", userId);

    List<Address> addressList = namedParamJdbcTemplate.query(GET_ADDRESSES_FOR_USER_ID, parameters, new BeanPropertyRowMapper<Address>(Address.class));

    return addressList;
  }

  private final static String SELECT_ADDRESS_FOR_WAREHOUSE = "SELECT a.* FROM address a " +
          "LEFT JOIN warehouse wh ON wh.addressid = a.id " +
          "WHERE wh.id = :warehouseId AND a.archived = FALSE";

  public Address getAddressForWareHouse(int warehouseId) {
    Map<String, Object> parameters = new HashMap<>();
    parameters.put("warehouseId", warehouseId);

    Address address = namedParamJdbcTemplate.queryForObject(SELECT_ADDRESS_FOR_WAREHOUSE, parameters, new BeanPropertyRowMapper<>(Address.class));
    return address;
  }

  private static final String CREATE_ADDRESS = "INSERT INTO address (companyName, addressLine1, addressLine2, city, state, postcode, country, archived, createdDate, createdByUserId, modifiedDate, modifiedByUserId) "
      + "VALUES(:companyName, :addressLine1, :addressLine2, :city, :state, :postcode, :country, FALSE, :createdDate, :createdByUserId, :modifiedDate, :modifiedByUserId)";

  private static final String CREATE_USER_ADDRESS_MAP = "INSERT INTO useraddressmap (addressid, userid, archived, createdDate, createdByUserId, modifiedDate, modifiedByUserId) "
      + "VALUES(:addressId, :userId, FALSE, :createdDate, :createdByUserId, :modifiedDate, :modifiedByUserId)";

  public Integer createUserAddress(int userId, Address address) {

    Timestamp now = new java.sql.Timestamp(Calendar.getInstance().getTime().getTime());
    address.setCreatedDate(now);
    address.setModifiedDate(now);
    address.setCreatedByUserId(userId);
    address.setModifiedByUserId(userId);

    if (address.getState() == null) {
      address.setState("");
    }

    BeanPropertySqlParameterSource namedParameters = new BeanPropertySqlParameterSource(address);
    KeyHolder keyHolder = new GeneratedKeyHolder();

    namedParamJdbcTemplate.update(CREATE_ADDRESS, namedParameters, keyHolder, new String[] { "id" });
    int addressId = keyHolder.getKey().intValue();

    MapSqlParameterSource mapperParameters = new MapSqlParameterSource();
    mapperParameters.addValue("addressId", addressId);
    mapperParameters.addValue("userId", userId);
    mapperParameters.addValue("createdDate", now);
    mapperParameters.addValue("createdByUserId", userId);
    mapperParameters.addValue("modifiedDate", now);
    mapperParameters.addValue("modifiedByUserId", userId);

    namedParamJdbcTemplate.update(CREATE_USER_ADDRESS_MAP, mapperParameters);

    return addressId;
  }


  public int updateUserAddress(int userId, Address address) {
    deleteUserAddress(userId, address.getId());
    int addressId = createUserAddress(userId, address);
    return addressId;
  }

  private static final String DELETE_ADDRESS = "UPDATE address SET archived = TRUE where addressid = :addressId";
  private static final String DELETE_USER_ADDRESS_MAP = "UPDATE useraddressmap SET archived = TRUE where addressid = :addressId AND userid = :userId";


  public Boolean deleteUserAddress(int userId, int addressId) {
    Map namedParameters = new HashMap();
    namedParameters.put("addressId", addressId);
    namedParameters.put("userId", userId);

    namedParamJdbcTemplate.update(DELETE_ADDRESS, namedParameters);
    namedParamJdbcTemplate.update(DELETE_USER_ADDRESS_MAP, namedParameters);

    return true;
  }

  private static final String CREATE_MANUFACTURER_ADDRESS_MAP = "INSERT INTO manufactureraddressmap (addressid, manufacturerid, archived) VALUES(:addressId, :manufacturerId, FALSE)";

  public Integer createManufacturerAddress(int manufacturerId, Address address) {

    Timestamp now = new java.sql.Timestamp(Calendar.getInstance().getTime().getTime());
    address.setCreatedDate(now);

    BeanPropertySqlParameterSource namedParameters = new BeanPropertySqlParameterSource(address);
    KeyHolder keyHolder = new GeneratedKeyHolder();

    namedParamJdbcTemplate.update(CREATE_ADDRESS, namedParameters, keyHolder, new String[] { "id" });
    int addressId = keyHolder.getKey().intValue();

    MapSqlParameterSource mapperParameters = new MapSqlParameterSource();
    mapperParameters.addValue("addressId", addressId);
    mapperParameters.addValue("manufactirerId", manufacturerId);
    mapperParameters.addValue("archived", false);

    namedParamJdbcTemplate.update(CREATE_MANUFACTURER_ADDRESS_MAP, mapperParameters);

    return addressId;
  }

  private final static String UPDATE_VISIBILITY_INFO_FOR_ADDRESS= "UPDATE address SET externalrefid = :externalrefid " +
          "WHERE id = :id";
  public int updateVisibilityInfoForAddress(Integer addressId, String externalRefId){
    Map<String,Object> params = new HashMap<>();
    params.put("id",addressId);
    params.put("externalrefid", externalRefId);
    return namedParamJdbcTemplate.update(UPDATE_VISIBILITY_INFO_FOR_ADDRESS, params);
  }
}
