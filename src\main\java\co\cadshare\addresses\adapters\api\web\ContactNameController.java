package co.cadshare.addresses.adapters.api.web;

import co.cadshare.addresses.core.ContactName;
import co.cadshare.shared.core.user.User;
import co.cadshare.addresses.boundary.ContactNameService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

@Controller
@RequestMapping("/contactName")
@Slf4j
public class ContactNameController {

  private ContactNameService contactNameService;

  public ContactNameController(ContactNameService contactNameService) {
    this.contactNameService = contactNameService;
  }

  @CanMaintainAddress
  @PostMapping(consumes = "application/json")
  public HttpEntity<Integer> createContactName(@AuthenticationPrincipal User currentUser, @RequestBody ContactName contactName, int userId) throws Exception {

    log.info("ACCESS: User [{}], createContactName", currentUser.accessDetails());

    int userIdForNewAddress = userId == 0 ? currentUser.getUserId() : userId;

    int nameId = contactNameService.createNameForUser(userIdForNewAddress, contactName);

    return new ResponseEntity<>(nameId, HttpStatus.OK);
  }

  @PutMapping(value = "/{nameId}", consumes = "application/json")
  public HttpEntity<Integer> updateContactName(@AuthenticationPrincipal User currentUser, @RequestBody ContactName contactName, @PathVariable int nameId) throws Exception {

    log.info("ACCESS: User [{}], updateContactName, nameId [{}]", currentUser.accessDetails(), contactName.getId());

    contactNameService.updateName(currentUser.getUserId(), contactName);
    return new ResponseEntity<>(HttpStatus.OK);
  }

  @DeleteMapping("/{nameId}")
  public HttpEntity<Boolean> deleteContactName(@AuthenticationPrincipal User currentUser, @PathVariable int nameId) {

    log.info("ACCESS: User [{}], deleteContactName,nameId [{}]", currentUser.accessDetails(), nameId);
    Boolean isDeleted = contactNameService.deleteName(currentUser.getUserId(), nameId);

    return new ResponseEntity<Boolean>(isDeleted, HttpStatus.OK);
  }
}
