package co.cadshare.models.core.processor;

import co.cadshare.models.core.MetadataObjectExtended;
import co.cadshare.models.core.processor.fileproperties.NexatPartNumberPropertyProcessor;
import org.junit.Before;
import org.junit.Test;

import java.util.Collections;
import java.util.LinkedHashMap;
import static org.junit.Assert.assertEquals;

public class NexatPartNumberPropertyProcessorTest {
    
    private NexatPartNumberPropertyProcessor out;
    private LinkedHashMap<String, String> properties;
    private MetadataObjectExtended objectExtended;
    
    @Before
    public void Before() {
        out = new NexatPartNumberPropertyProcessor();
        out.setSynonyms(Collections.singletonList("PartNumber"));
        setProperties();
        objectExtended = new MetadataObjectExtended();
    }

    @Test
    public void LeaveFormattingAsIs() {
        properties.put("PartNumber", "01432-87673-01");
        out.setProperties(properties, objectExtended);

        assertEquals("01432-87673-01", objectExtended.getPartNumber());
    }

    @Test
    public void LeaveFormattingAsIsForSingleHyphen() {
        properties.put("PartNumber", "0432-87673");
        out.setProperties(properties, objectExtended);

        assertEquals("0432-87673", objectExtended.getPartNumber());
    }

    @Test
    public void StripFrom2ndHyphen() {
        properties.put("PartNumber", "0432-87673-01");
        out.setProperties(properties, objectExtended);

        assertEquals("0432-87673", objectExtended.getPartNumber());
    }

    @Test
    public void StripFromLastHyphen() {
        properties.put("PartNumber", "0432-87673-00998-01");
        out.setProperties(properties, objectExtended);
        assertEquals("0432-87673-00998", objectExtended.getPartNumber());
    }

    @Test
    public void StripFromLastHyphenDoubleZeroStarter() {
        properties.put("PartNumber", "00200-0000691-00");
        out.setProperties(properties, objectExtended);
        assertEquals("00200-0000691", objectExtended.getPartNumber());
    }

    private void setProperties() {
        properties = new LinkedHashMap<>();
        properties.put("Original System", "NX");
        properties.put("CAD_PARTNAME", "16005-0011353-01");
    }
}
