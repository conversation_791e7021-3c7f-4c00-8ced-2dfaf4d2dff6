package co.cadshare.masterKits.adapters.api.web;

import co.cadshare.aspects.access.languages.CanUseLanguage;
import co.cadshare.aspects.access.roles.CanAccessManufacturer;
import co.cadshare.shared.core.Language;
import co.cadshare.shared.core.user.User;
import co.cadshare.masterKits.boundary.*;
import co.cadshare.masterKits.core.MasterKitSearchResult;
import co.cadshare.masterKits.core.MasterKit;
import io.swagger.v3.oas.annotations.Operation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.*;

import java.util.List;


@RestController
@RequestMapping("/manufacturers/{manufacturer-id}/master-part-kits")
public class ManufacturerMasterKitsController {

	private static final Logger logger = LoggerFactory.getLogger(ManufacturerMasterKitsController.class);

	private final CreateMasterKitService createMasterKitService;
    private final UpdateMasterKitService updateMasterKitService;
    private final DeleteMasterKitService deleteMasterKitService;
    private final GetMasterKitService getMasterKitService;
	private final SearchMasterKitService searchMasterKitService;

    @Autowired
    public ManufacturerMasterKitsController(CreateMasterKitService createMasterKitService,
                                            UpdateMasterKitService updateMasterKitService,
                                            DeleteMasterKitService deleteMasterKitService,
                                            GetMasterKitService getMasterKitService,
                                            SearchMasterKitService searchMasterKitService){
        this.createMasterKitService = createMasterKitService;
        this.updateMasterKitService = updateMasterKitService;
        this.deleteMasterKitService = deleteMasterKitService;
        this.getMasterKitService = getMasterKitService;
	    this.searchMasterKitService = searchMasterKitService;
    }

    @PostMapping(consumes = "application/json", produces = "application/json")
    @PreAuthorize("hasRole('ROLE_MANUFACTURER')")
    @CanAccessManufacturer
    @ResponseStatus(HttpStatus.OK)
    @Operation(summary = "Create a MasterKit")
    public ResponseEntity<PostMasterKitResponseDto> postMasterKit(@PathVariable("manufacturer-id") int manufacturerId,
                                                                @AuthenticationPrincipal User currentUser,
                                                                @RequestBody PostMasterKitRequestDto postMasterKit) {

        CreateMasterKitCommand masterKit = MasterKitMapper.Instance.postRequestDtoToMasterKitCommand(postMasterKit, manufacturerId);
        Integer createdId = this.createMasterKitService.create(currentUser, masterKit);
        PostMasterKitResponseDto response = new PostMasterKitResponseDto() {{setMasterKitId(createdId);}};
        return new ResponseEntity<>(response, HttpStatus.OK);
    }

    @PutMapping(path = "/{master-part-kit-id}", consumes = "application/json")
    @PreAuthorize("hasRole('ROLE_MANUFACTURER')")
    @CanAccessManufacturer
    @ResponseStatus(HttpStatus.OK)
    @Operation(summary = "Maintain the details of a MasterKit")
    public ResponseEntity putMasterKit(@PathVariable("manufacturer-id") int manufacturerId,
                                         @PathVariable("master-part-kit-id") Integer masterKitId,
                                         @AuthenticationPrincipal User currentUser,
                                         @RequestBody PutMasterKitRequestDto putMasterKit) throws Exception {

        UpdateMasterKitCommand updateMasterKitCommand = MasterKitMapper.Instance.putRequestDtoToMasterKitCommand(putMasterKit, manufacturerId, masterKitId);
        this.updateMasterKitService.update(currentUser, updateMasterKitCommand);
        return new ResponseEntity<>(HttpStatus.OK);
    }

    @DeleteMapping(path = "/{master-part-kit-id}")
    @PreAuthorize("hasRole('ROLE_MANUFACTURER')")
    @CanAccessManufacturer
    @ResponseStatus(HttpStatus.OK)
    @Operation(summary = "Delete a MasterKit")
    public ResponseEntity deleteMasterKit(@PathVariable("manufacturer-id") int manufacturerId,
                                            @PathVariable("master-part-kit-id") Integer masterKitId,
                                            @AuthenticationPrincipal User currentUser) throws Exception {

        this.deleteMasterKitService.delete(currentUser, masterKitId);
        return new ResponseEntity<>(HttpStatus.OK);
    }

    @GetMapping(path = "/{master-part-kit-id}", produces = "application/json")
    @PreAuthorize("hasRole('ROLE_SUPER_USER') or hasRole('ROLE_MANUFACTURER')")
    @CanAccessManufacturer
    @ResponseStatus(HttpStatus.OK)
    @Operation(summary = "Get the details of a specific MasterKit")
    public ResponseEntity<GetMasterKitResponseDto> getMasterKit(@PathVariable("manufacturer-id") int manufacturerId,
                                            @PathVariable("master-part-kit-id") Integer masterKitId,
                                            @AuthenticationPrincipal User currentUser) {

        MasterKit masterKit = this.getMasterKitService.get(masterKitId);
        GetMasterKitResponseDto getResponseDto = MasterKitMapper.Instance.masterKitToGetResponseDto(masterKit);
        return new ResponseEntity<>(getResponseDto, HttpStatus.OK);
    }

    @GetMapping(produces = "application/json")
    @PreAuthorize("hasRole('ROLE_SUPER_USER') or hasRole('ROLE_MANUFACTURER')")
    @CanAccessManufacturer
    @ResponseStatus(HttpStatus.OK)
    @Operation(summary = "Get the summary details of the list of all MasterKits belonging to the Manufacturer")
    public ResponseEntity<GetMasterKitListResponseDto> getMasterKits(@PathVariable("manufacturer-id") int manufacturerId,
                                                                          @AuthenticationPrincipal User currentUser) {

        List<MasterKit> masterKits = this.getMasterKitService.getMasterKitsForManufacturer(manufacturerId);
        List<GetMasterKitListItemResponseDto> masterKitResponses = MasterKitMapper.Instance.masterKitToGetListResponseDto(masterKits);
        GetMasterKitListResponseDto getListResponseDto = new GetMasterKitListResponseDto(){{
            setMasterKits(masterKitResponses);
        }};
        return new ResponseEntity<>(getListResponseDto, HttpStatus.OK);
    }

	@PreAuthorize("hasRole('ROLE_MANUFACTURER') and hasRole('Parts')")
	@PostMapping ("/search")
	@CanUseLanguage
	@CanAccessManufacturer
	public ResponseEntity<PostMasterKitsSearchResponseDto> searchMasterKitsForManufacturer(@PathVariable("manufacturer-id") int manufacturerId,
	                                                                                    @AuthenticationPrincipal User currentUser,
	                                                                                    @RequestBody PostMasterKitsSearchRequestDto dto,
	                                                                                    @RequestParam(value = "language") Language language) {

		if (dto.isSearchStringEmptyOrNull()) {
			logger.error("Kit search failed no PartNumber or Description to search");
			return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
		}
		ManufacturerMasterKitSearchRequest searchRequest = ManufacturerMasterKitSearchRequest.build(currentUser,
				dto.getPartNumber(),
				dto.getPartDescription(),
				language);

		List<MasterKitSearchResult> results = this.searchMasterKitService.searchForManufacturer(currentUser, searchRequest);
		PostMasterKitsSearchResponseDto response = new PostMasterKitsSearchResponseDto();
		response.setKits(MasterKitSearchResultMapper.Instance.coresToDtos(results));
		response.setTotalResults(results.size());
		return new ResponseEntity<>(response, HttpStatus.OK);
	}
}
