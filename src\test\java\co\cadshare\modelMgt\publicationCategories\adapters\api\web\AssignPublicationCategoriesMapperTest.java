package co.cadshare.modelMgt.publicationCategories.adapters.api.web;

import co.cadshare.modelMgt.publicationCategories.boundary.AssignPublicationCategoriesCommand;
import co.cadshare.modelMgt.publicationCategories.core.PublicationCategory;
import co.cadshare.modelMgt.publications.adapters.api.web.PostAssignCustomersRequestDto;
import org.junit.Test;

import java.util.Arrays;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertTrue;

public class AssignPublicationCategoriesMapperTest {

	@Test
	public void assignPublicationCategoriesMapperTest() {

		PostPublicationCategoriesDto dto = new PostPublicationCategoriesDto();
		dto.setPublicationCategoryIds(Arrays.asList(1, 2, 3));
		dto.setPurchaserId(5);

		AssignPublicationCategoriesCommand command = AssignPublicationCategoriesMapper.Instance.dtoToCommand(dto);

		assertEquals(5, command.getPurchaserId().intValue());
		assertEquals(3, command.getPublicationCategoryIds().size());
		assertTrue(command.getPublicationCategoryIds().contains(1));
		assertTrue(command.getPublicationCategoryIds().contains(2));
		assertTrue(command.getPublicationCategoryIds().contains(3));
	}
}
