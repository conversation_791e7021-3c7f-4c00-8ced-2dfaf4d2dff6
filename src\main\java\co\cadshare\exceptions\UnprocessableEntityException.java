package co.cadshare.exceptions;

import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ResponseStatus;

@ResponseStatus(value = HttpStatus.UNPROCESSABLE_ENTITY)
public class UnprocessableEntityException extends RuntimeException {
    public UnprocessableEntityException(String viewableNotReadyForPublication) {
        super(viewableNotReadyForPublication);
    }
    public UnprocessableEntityException(String message, Throwable throwable) {
        super(message, throwable);
    }
}
