package co.cadshare.inventory.core;

import co.cadshare.modelMgt.models.core.Model;
import co.cadshare.shared.core.manufacturer.ManufacturerProgress;

public class ManufacturerBomDownloadProgress extends ManufacturerProgress {

    public ManufacturerBomDownloadProgress() { super(); }

    public ManufacturerBomDownloadProgress(int manufacturerId, Model model) {
        super();
        setManufacturerId(manufacturerId);
        setMachineId(model.getMachineId());
        setModelId(model.getModelId());
        setProcess(ManufacturerProgress.Process.VIEWABLE_BOM_EXPORT);
    }
}
