package co.cadshare.inventory.adapters.database;

import co.cadshare.domainmodel.priceListIdentifier.PriceListIdentifier;
import lombok.extern.slf4j.Slf4j;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.namedparam.BeanPropertySqlParameterSource;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.jdbc.support.GeneratedKeyHolder;
import org.springframework.jdbc.support.KeyHolder;
import org.springframework.stereotype.Repository;

import java.util.HashMap;
import java.util.Map;

@Slf4j
@Repository
public class PriceListIdentifierDao {

    private NamedParameterJdbcTemplate namedParamJdbcTemplate;

    public PriceListIdentifierDao(NamedParameterJdbcTemplate namedParamJdbcTemplate) {
        this.namedParamJdbcTemplate = namedParamJdbcTemplate;
    }

    private static final String IS_VALID_CURRENCY = "SELECT COUNT(*) FROM currency "
        + "WHERE code = :code ";

    public boolean isValidCurrency(String code) {
        Map<String, Object> connectionsParameters = new HashMap<String, Object>();
        connectionsParameters.put("code", code);

        int count = namedParamJdbcTemplate.queryForObject(IS_VALID_CURRENCY, connectionsParameters, Integer.class);
        return count > 0;
    }

    private static final String GET_IDENTIFIER_BY_ID = "SELECT * FROM pricelistidentifier WHERE id = :id ";

    public PriceListIdentifier getIdentifier(Integer identifierId) {
            Map<String, Object> parameters = new HashMap<String, Object>();
            parameters.put("id", identifierId);

            PriceListIdentifier identifier = namedParamJdbcTemplate.queryForObject(GET_IDENTIFIER_BY_ID, parameters, new BeanPropertyRowMapper<>(PriceListIdentifier.class));
            return identifier;
    }

    private static final String CREATE_IDENTIFIER = "INSERT INTO pricelistidentifier (currencyid, manufacturerid, identifier) "
            + "VALUES(:currencyId, :manufacturerId, :identifier)";


    public int createIdentifier(PriceListIdentifier identifier) {
        BeanPropertySqlParameterSource namedParameters = new BeanPropertySqlParameterSource(identifier);
        KeyHolder keyHolder = new GeneratedKeyHolder();

        namedParamJdbcTemplate.update(CREATE_IDENTIFIER, namedParameters, keyHolder, new String[] { "id" });

        return keyHolder.getKey().intValue();
    }
}
