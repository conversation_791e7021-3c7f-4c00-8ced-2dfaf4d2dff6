/*
 * Copyright 2016 Bell.
 */
package co.cadshare.masterParts.adapters.api.web;

import co.cadshare.aspects.access.languages.CanUseLanguage;
import co.cadshare.masterParts.boundary.MasterPartOptionsSetService;
import co.cadshare.masterParts.core.extensions.optionsSet.OptionsSet;

import co.cadshare.shared.core.Language;
import co.cadshare.shared.core.user.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Slf4j
@Controller
@RequestMapping("/masterPart")
public class MasterPartOptionsSetController {

  private final MasterPartOptionsSetService optionsSetService;


  public MasterPartOptionsSetController(MasterPartOptionsSetService optionsSetService) {
    this.optionsSetService = optionsSetService;
  }

  //Option Set Services
  @RequestMapping(value = "/{masterpartId}/optionSet", method = RequestMethod.GET)
  @CanUseLanguage
  public HttpEntity<List<OptionsSet>> getOptionsSetsForPart(@AuthenticationPrincipal User currentUser,
                                                            @PathVariable int masterpartId,
                                                            @RequestParam(value = "language", required = false) Language language) throws Exception {

    log.info("ACCESS: User [{}], getOptionsSetsForPart, partId [{}]", currentUser.accessDetails(), masterpartId);
	Integer languageId = language != null ? currentUser.findLanguage(language).getLanguageId() : null;
    List<OptionsSet> optionsSets = optionsSetService.getOptionsSetsForPart(masterpartId, languageId, currentUser.obtainDefaultLanguage());
    return new ResponseEntity<>(optionsSets, HttpStatus.OK);
  }

  @RequestMapping(value = "/optionSet/{optionSetId}", method = RequestMethod.GET)
  @CanUseLanguage
  public HttpEntity<OptionsSet> getOptionsSetForPartById(@AuthenticationPrincipal User currentUser,
                                                         @PathVariable int optionSetId,
                                                         @RequestParam(value = "language", required = false) Language language) throws Exception {

    log.info("ACCESS: User [{}], getOptionsSetForPartById, partId [{}]", currentUser.accessDetails(), optionSetId);
	  Integer languageId = language != null ? currentUser.findLanguage(language).getLanguageId() : null;
    OptionsSet optionsSets = optionsSetService.getOptionsSetForPartById(optionSetId, languageId, currentUser.obtainDefaultLanguage());
    return new ResponseEntity<>(optionsSets, HttpStatus.OK);
  }


  @PostMapping(value = "/{masterpartId}/optionSet", consumes = "application/json")
  public HttpEntity<Integer> createOptionsSet(@AuthenticationPrincipal User currentUser,
                                              @PathVariable int masterpartId,
                                              @RequestBody OptionsSet optionsSet) {

    log.info("ACCESS: User [{}], createOptionsSet, for masterpart Id [{}]", currentUser.accessDetails(), masterpartId);
    optionsSet.setMasterPartId(masterpartId);
    int optionSetId = optionsSetService.createOptionsSet(optionsSet);
    log.info("OptionSet with id [{}] created", optionSetId);
    return new ResponseEntity<>(optionSetId, HttpStatus.OK);
  }

  @PutMapping(value = "/optionSet/{optionSetId}", consumes = "application/json")
  public HttpEntity<Boolean> editOptionsSet(@AuthenticationPrincipal User currentUser,
                                            @PathVariable int optionSetId,
                                            @RequestBody OptionsSet optionsSet) {

    log.info("ACCESS: User [{}], editOptionSet, OptionSetId [{}]", currentUser.accessDetails(), optionSetId);
    optionsSet.setId(optionSetId);
    boolean response =  optionsSetService.updateOptionsSet(optionsSet);
    return new ResponseEntity<>(response, HttpStatus.OK);
  }

  @DeleteMapping(value = "/optionSet/{optionSetId}")
  public HttpEntity<Boolean> deleteOptionsSet(@AuthenticationPrincipal User currentUser,
                                              @PathVariable int optionSetId) {

    log.info("ACCESS: User [{}], deleteOptionSet, id [{}]", currentUser.accessDetails(), optionSetId);
    Boolean isDeleted = optionsSetService.deleteOptionsSet(optionSetId);
    return new ResponseEntity<Boolean>(isDeleted, HttpStatus.OK);
  }
}
