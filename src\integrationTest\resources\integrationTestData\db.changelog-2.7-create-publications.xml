<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                   http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.0.xsd
">

    <changeSet author="AndyB" id="2.7-integration-test-data-create-publications-1">
        <sql stripComments="true">
            INSERT INTO public.manual(
            manualid, manualname, manufacturerid, createddate, createdbyuserid, status, archived, modifieddate, modifiedbyuserid, useviewableimage)
            VALUES (
            1,                                  -- manualid
            'Publication for Caterpillar',      -- manualname
            1,                                  -- manufacturerid
            '2017-06-09 10:42:56.176',          -- createddate
            1,                                  -- createdbyuserid
            'PUBLISHED',                        -- status
            FALSE,                              -- archived
            '2017-06-09 10:42:56.176',          -- modifieddate
            1,                                  -- modifiedbyuserid
            FALSE);                             -- useviewableimage

            INSERT INTO public.manufacturersubentitymanualmap(
            manualid, manufacturersubentityid)
            VALUES (1, (select manufacturersubentityid
            from manufacturersubentity
            where name like '%Caterpillar Dealer %'));

            INSERT INTO public.manufacturersubentitymanualmap(
            manualid, manufacturersubentityid)
            VALUES (1, (select manufacturersubentityid
            from manufacturersubentity
            where name like '%Caterpillar DealerPlus %'));

            INSERT INTO public.manufacturersubentitymanualmap(
            manualid, manufacturersubentityid)
            VALUES (1, (select manufacturersubentityid
            from manufacturersubentity
            where name like '%Caterpillar Customer for DealerPlus %'));

            INSERT INTO public.manualmodelmap(
            manualid, modelid, archived)
            VALUES (1, 1, FALSE);
        </sql>
    </changeSet>

    <changeSet author="AndyB" id="2.7-integration-test-data-create-publications-2">
        <sql stripComments="true">
            INSERT INTO public.manual(
            manualid, manualname, manufacturerid, createddate, createdbyuserid, status, archived, modifieddate, modifiedbyuserid, useviewableimage)
            VALUES (
            2,                                  -- manualid
            'Publication for JCB',              -- manualname
            2,                                  -- manufacturerid
            '2017-06-09 10:42:56.176',          -- createddate
            1,                                  -- createdbyuserid
            'PUBLISHED',                        -- status
            FALSE,                              -- archived
            '2017-06-09 10:42:56.176',          -- modifieddate
            1,                                  -- modifiedbyuserid
            FALSE);                             -- useviewableimage

            INSERT INTO public.manufacturersubentitymanualmap(
            manualid, manufacturersubentityid)
            VALUES (2, (select manufacturersubentityid
            from manufacturersubentity
            where name like '%JCB Dealer %'));

            INSERT INTO public.manufacturersubentitymanualmap(
            manualid, manufacturersubentityid)
            VALUES (2, (select manufacturersubentityid
            from manufacturersubentity
            where name like '%JCB DealerPlus %'));

            INSERT INTO public.manufacturersubentitymanualmap(
            manualid, manufacturersubentityid)
            VALUES (2, (select manufacturersubentityid
            from manufacturersubentity
            where name like '%JCB Customer for DealerPlus %'));

            INSERT INTO public.manualmodelmap(
            manualid, modelid, archived)
            VALUES (2, 2, FALSE);
        </sql>
    </changeSet>

    <changeSet author="AndyB" id="2.7-integration-test-data-create-publications-3">
        <sql stripComments="true">
            INSERT INTO public.manual(
            manualid, manualname, manufacturerid, createddate, createdbyuserid, status, archived, modifieddate, modifiedbyuserid, useviewableimage)
            VALUES (
            3,                                  -- manualid
            'Publication for Liebherr',         -- manualname
            3,                                  -- manufacturerid
            '2017-06-09 10:42:56.176',          -- createddate
            1,                                  -- createdbyuserid
            'PUBLISHED',                        -- status
            FALSE,                              -- archived
            '2017-06-09 10:42:56.176',          -- modifieddate
            1,                                  -- modifiedbyuserid
            FALSE);                             -- useviewableimage

            INSERT INTO public.manufacturersubentitymanualmap(
            manualid, manufacturersubentityid)
            VALUES (3, (select manufacturersubentityid
            from manufacturersubentity
            where name like '%Liebherr Dealer %'));

            INSERT INTO public.manufacturersubentitymanualmap(
            manualid, manufacturersubentityid)
            VALUES (3, (select manufacturersubentityid
            from manufacturersubentity
            where name like '%Liebherr DealerPlus %'));

            INSERT INTO public.manufacturersubentitymanualmap(
            manualid, manufacturersubentityid)
            VALUES (3, (select manufacturersubentityid
            from manufacturersubentity
            where name like '%Liebherr Customer for DealerPlus %'));

            INSERT INTO public.manualmodelmap(
            manualid, modelid, archived)
            VALUES (3, 3, FALSE);
        </sql>
    </changeSet>

    <changeSet author="AndyB" id="2.7-integration-test-data-create-publications-4">
        <sql stripComments="true">
            INSERT INTO public.manual(
            manualid, manualname, manufacturerid, createddate, createdbyuserid, status, archived, modifieddate, modifiedbyuserid, useviewableimage)
            VALUES (
            4,                                  -- manualid
            'Publication for Terex',            -- manualname
            4,                                  -- manufacturerid
            '2017-06-09 10:42:56.176',          -- createddate
            1,                                  -- createdbyuserid
            'PUBLISHED',                        -- status
            FALSE,                              -- archived
            '2017-06-09 10:42:56.176',          -- modifieddate
            1,                                  -- modifiedbyuserid
            FALSE);                             -- useviewableimage

            INSERT INTO public.manufacturersubentitymanualmap(
            manualid, manufacturersubentityid)
            VALUES (4, (select manufacturersubentityid
            from manufacturersubentity
            where name like '%Terex Dealer %'));

            INSERT INTO public.manufacturersubentitymanualmap(
            manualid, manufacturersubentityid)
            VALUES (4, (select manufacturersubentityid
            from manufacturersubentity
            where name like '%Terex DealerPlus %'));

            INSERT INTO public.manufacturersubentitymanualmap(
            manualid, manufacturersubentityid)
            VALUES (4, (select manufacturersubentityid
            from manufacturersubentity
            where name like '%Terex Customer for DealerPlus %'));

            INSERT INTO public.manualmodelmap(
            manualid, modelid, archived)
            VALUES (4, 4, FALSE);
        </sql>
    </changeSet>

</databaseChangeLog>
