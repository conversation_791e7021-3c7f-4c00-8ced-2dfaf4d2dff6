package co.cadshare.modelMgt.publications.boundary;

import co.cadshare.modelMgt.shared.core.Publication;
import co.cadshare.modelMgt.shared.boundary.PurchasersQueryPort;
import co.cadshare.modelMgt.shared.core.Purchaser;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service
public class PurchaserHydrator implements PublicationAttributeHydrator {

	private final PurchasersQueryPort purchaserQueryPort;

	@Autowired
	public PurchaserHydrator(PurchasersQueryPort purchaserQueryPort) {
		this.purchaserQueryPort = purchaserQueryPort;
	}

	@Override
	public void hydrate(PublicationCommand command, Publication publication) {

		List<Purchaser> purchasers = new ArrayList<>();
		if(command.hasCustomers())
			command.getCustomers().forEach(p -> purchasers.add(purchaserQueryPort.getPurchaser(p)));
		publication.setPurchasers(purchasers);
	}
}
