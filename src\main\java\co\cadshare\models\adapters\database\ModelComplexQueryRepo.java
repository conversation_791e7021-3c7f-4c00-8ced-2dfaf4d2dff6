package co.cadshare.models.adapters.database;

import co.cadshare.models.boundary.PublishedModelsForMasterPartSearchCriteria;
import co.cadshare.models.core.model.AutodeskStatus;
import co.cadshare.publications.core.ManualStatus;
import com.querydsl.jpa.impl.JPAQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import javax.persistence.EntityManager;
import java.util.List;

@Repository
public class ModelComplexQueryRepo {

    private JPAQueryFactory queryFactory;

    @Autowired
    public ModelComplexQueryRepo(EntityManager entityManager) {
        this.queryFactory = new JPAQueryFactory(entityManager);
    }

    public List<ModelsModelEntity> getModelsForProduct(Integer productId) {
        QModelsModelEntity model = QModelsModelEntity.modelsModelEntity;
        return queryFactory.selectFrom(model)
                .where(model.product.id.eq(productId)
                        .and(model.archived.eq(false)))
                .fetch();
    }

    public List<ModelsModelEntity> getAllModels() {
        QModelsModelEntity model = QModelsModelEntity.modelsModelEntity;
        return queryFactory.selectFrom(model)
                        .where(model.archived.eq(false))
                .fetch();
    }

    public ModelsModelEntity getModelById(Integer modelId) {
        QModelsModelEntity model = QModelsModelEntity.modelsModelEntity;
        return queryFactory.selectFrom(model)
                .where(model.modelId.eq(modelId)).fetchOne();
    }

    public List<ModelsModelEntity> getModelsForProduct(Integer productId, AutodeskStatus status, boolean setupComplete) {
        QModelsModelEntity model = QModelsModelEntity.modelsModelEntity;
        JPAQuery<ModelsModelEntity> query = queryFactory.selectFrom(model)
                .where(model.product.id.eq(productId)
                        .and(model.archived.eq(false)));

        if(status != null)
            query = query.where(model.autodeskStatus.eq(status));


        if (setupComplete)
            query = query.where(model.isSetupComplete.eq(true));

        return query.fetch();
    }

    public List<ModelsModelEntity> getPublishedModelsForMasterPart(PublishedModelsForMasterPartSearchCriteria searchCriteria) {
        QModelsModelEntity model = QModelsModelEntity.modelsModelEntity;
        return queryFactory.selectFrom(model)
                .where(model.parts.any().partNumber.eq(searchCriteria.getMasterPartNumber())
                        .and(model.publications.any().dealers.any().id.eq(searchCriteria.getDealerId()))
                        .and(model.publications.any().status.eq(ManualStatus.Status.PUBLISHED))
                        .and(model.archived.eq(false)))
                .fetch();
    }
}

