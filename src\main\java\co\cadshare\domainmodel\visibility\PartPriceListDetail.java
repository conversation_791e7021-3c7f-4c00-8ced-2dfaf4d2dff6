package co.cadshare.domainmodel.visibility;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;

@XmlRootElement(name = "PartPriceListDetail", namespace = "http://visibility.com/")
@XmlAccessorType(XmlAccessType.FIELD)
public class PartPriceListDetail {

    @XmlElement(name = "ENTITY_CODE")
    private String ENTITY_CODE;

    @XmlElement(name = "PRICE_BOOK_X")
    private String PRICE_BOOK_X;

    @XmlElement(name = "DESCR_X")
    private String DESCR_X;

    @XmlElement(name = "DISCOUNT_N")
    private String DISCOUNT_N;

    @XmlElement(name = "CURRENCY")
    private String CURRENCY;

    @XmlElement(name = "PART_X")
    private String PART_X;

    @XmlElement(name = "PART_SELLING_PRICE_N")
    private String PART_SELLING_PRICE_N;

    @XmlElement(name = "BREAK_IS_PRICE_B")
    private String BREAK_IS_PRICE_B;

    @XmlElement(name = "BREAK_QTY_N")
    private String BREAK_QTY_N;

    @XmlElement(name = "PRICE_OR_PERCENT_N")
    private String PRICE_OR_PERCENT_N;

    public String getENTITY_CODE() {
        return ENTITY_CODE;
    }

    public void setENTITY_CODE(String ENTITY_CODE) {
        this.ENTITY_CODE = ENTITY_CODE;
    }

    public String getPRICE_BOOK_X() {
        return PRICE_BOOK_X;
    }

    public void setPRICE_BOOK_X(String PRICE_BOOK_X) {
        this.PRICE_BOOK_X = PRICE_BOOK_X;
    }

    public String getDESCR_X() {
        return DESCR_X;
    }

    public void setDESCR_X(String DESCR_X) {
        this.DESCR_X = DESCR_X;
    }

    public String getDISCOUNT_N() {
        return DISCOUNT_N;
    }

    public void setDISCOUNT_N(String DISCOUNT_N) {
        this.DISCOUNT_N = DISCOUNT_N;
    }

    public String getCURRENCY() {
        return CURRENCY;
    }

    public void setCURRENCY(String CURRENCY) {
        this.CURRENCY = CURRENCY;
    }

    public String getPART_X() {
        return PART_X;
    }

    public void setPART_X(String PART_X) {
        this.PART_X = PART_X;
    }

    public String getPART_SELLING_PRICE_N() {
        return PART_SELLING_PRICE_N;
    }

    public void setPART_SELLING_PRICE_N(String PART_SELLING_PRICE_N) {
        this.PART_SELLING_PRICE_N = PART_SELLING_PRICE_N;
    }

    public String getBREAK_IS_PRICE_B() {
        return BREAK_IS_PRICE_B;
    }

    public void setBREAK_IS_PRICE_B(String BREAK_IS_PRICE_B) {
        this.BREAK_IS_PRICE_B = BREAK_IS_PRICE_B;
    }

    public String getBREAK_QTY_N() {
        return BREAK_QTY_N;
    }

    public void setBREAK_QTY_N(String BREAK_QTY_N) {
        this.BREAK_QTY_N = BREAK_QTY_N;
    }

    public String getPRICE_OR_PERCENT_N() {
        return PRICE_OR_PERCENT_N;
    }

    public void setPRICE_OR_PERCENT_N(String PRICE_OR_PERCENT_N) {
        this.PRICE_OR_PERCENT_N = PRICE_OR_PERCENT_N;
    }
}
