{"info": {"_postman_id": "ae4d5b2c-9818-6a1b-8fa5-8e6c2ba3d633", "name": "Cadshare Autodesk", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "300252"}, "item": [{"name": "Part Scrapper ", "item": [{"name": "1: GET metadata", "event": [{"listen": "test", "script": {"exec": ["var requestURN = pm.request.url.path[3]", "postman.setEnvironmentVariable(\"urn\", requestURN);", "", "var responseData = JSON.parse(responseBody);", "postman.setEnvironmentVariable(\"guid\", responseData.data.metadata[0].guid);"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{autodesk-access-token}} "}], "url": {"raw": "https://developer.api.autodesk.com/modelderivative/v2/designdata/:urn/metadata", "protocol": "https", "host": ["developer", "api", "autodesk", "com"], "path": ["modelderivative", "v2", "designdata", ":urn", "metadata"], "variable": [{"key": "urn", "value": "dXJuOmFkc2sub2JqZWN0czpvcy5vYmplY3Q6d2ViYXBwLmNhZHNoYXJlLmNvLzE2NTI0NDA0NTk1ODQtTWluYSUyME5haXJvdXolMjAtJTIwU0hYNDcwLnppcA==", "type": "string"}]}}, "response": []}, {"name": "2: GET Metadata for GUID", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{autodesk-access-token}} "}], "url": {"raw": "https://developer.api.autodesk.com/modelderivative/v2/designdata/:urn/metadata/:guid", "protocol": "https", "host": ["developer", "api", "autodesk", "com"], "path": ["modelderivative", "v2", "designdata", ":urn", "metadata", ":guid"], "variable": [{"key": "urn", "value": "{{urn}}", "type": "string"}, {"key": "guid", "value": "{{guid}}"}]}}, "response": []}, {"name": "3. GET Properties for GUID", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{autodesk-access-token}} "}], "url": {"raw": "https://developer.api.autodesk.com/modelderivative/v2/designdata/:urn/metadata/:guid/properties?forceget=true", "protocol": "https", "host": ["developer", "api", "autodesk", "com"], "path": ["modelderivative", "v2", "designdata", ":urn", "metadata", ":guid", "properties"], "query": [{"key": "forceget", "value": "true"}], "variable": [{"key": "urn", "value": "{{urn}}", "type": "string"}, {"key": "guid", "value": "{{guid}}", "type": "string"}]}}, "response": []}]}, {"name": "Get token from autodesk", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/x-www-form-urlencoded"}], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "client_id", "value": "LySVr3xlAjS0byN3NP5iMWJu8MG30bKW", "type": "text"}, {"key": "client_secret", "value": "XrudtWPlqZI7yhKR", "type": "text"}, {"key": "grant_type", "value": "client_credentials", "type": "text"}, {"key": "scope", "value": "data:read", "type": "text"}]}, "url": {"raw": "https://developer.api.autodesk.com/authentication/v1/authenticate", "protocol": "https", "host": ["developer", "api", "autodesk", "com"], "path": ["authentication", "v1", "authenticate"]}}, "response": []}, {"name": "Check bucket exists", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer G5vpI2cq2XZ6bGy7uRpvRycrsjmR"}], "url": {"raw": "https://developer.api.autodesk.com/oss/v2/buckets/cadsharedevbucket/details", "protocol": "https", "host": ["developer", "api", "autodesk", "com"], "path": ["oss", "v2", "buckets", "cadsharedevbucket", "details"]}, "description": "Remember to get an up to date token and put its value in the header!"}, "response": []}, {"name": "Translate file to svf", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{autodesk-access-token}} "}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n   \"input\": {\r\n     \"urn\": \"dXJuOmFkc2sub2JqZWN0czpvcy5vYmplY3Q6d2ViYXBwLmNhZHNoYXJlLmNvLzE2MTQyNTkzODU0NzctRjEwMTAyNy5qdA==\"\r\n   },\r\n   \"output\": {\r\n          \"formats\": [\r\n            {\r\n              \"type\": \"svf\",\r\n              \"views\": [\r\n                \"2d\",\r\n                \"3d\"\r\n              ]\r\n            }\r\n          ]\r\n        }\r\n      }"}, "url": {"raw": "https://developer.api.autodesk.com/modelderivative/v2/designdata/job", "protocol": "https", "host": ["developer", "api", "autodesk", "com"], "path": ["modelderivative", "v2", "designdata", "job"]}}, "response": []}, {"name": "Translate file to svf2", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{autodesk-access-token}} "}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n   \"input\": {\r\n     \"urn\": \"dXJuOmFkc2sub2JqZWN0czpvcy5vYmplY3Q6ZGV2MWJ1Y2tldC5jYWRzaGFyZS5jby8xNjA3MDc1Mzg4NTE0LTAxLTAxMC0xMDcwLTUwMF9EdW1wZXIlMjAzMDEyJTIwVDRGLVJfMTkwNzE5LnN0cA\",\r\n     \"rootFilename\": \"#SD1450-15A.SLDASM\",\r\n     \"compressedUrn\": true\r\n   },\r\n   \"output\": {\r\n          \"formats\": [\r\n            {\r\n              \"type\": \"svf2\",\r\n              \"views\": [\r\n                \"2d\",\r\n                \"3d\"\r\n              ]\r\n            }\r\n          ]\r\n        }\r\n      }"}, "url": {"raw": "https://developer.api.autodesk.com/modelderivative/v2/designdata/job", "protocol": "https", "host": ["developer", "api", "autodesk", "com"], "path": ["modelderivative", "v2", "designdata", "job"]}}, "response": []}, {"name": "Get translation status", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{autodesk-access-token}} "}], "url": {"raw": "https://developer.api.autodesk.com/modelderivative/v2/designdata/:urn/manifest", "protocol": "https", "host": ["developer", "api", "autodesk", "com"], "path": ["modelderivative", "v2", "designdata", ":urn", "manifest"], "variable": [{"key": "urn", "value": "dXJuOmFkc2sub2JqZWN0czpvcy5vYmplY3Q6d2ViYXBwLmNhZHNoYXJlLmNvLzE2NjAwNDk1OTc1MjEtMTYwMDVfMDAxODcyMV8wMCUyMExhcnMlMjBMZWZmZXJpbmcuanQ=", "type": "string"}]}}, "response": []}, {"name": "Get bucket contents", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer Vr3l54iRNYS07Eay5Gwjgm9YOLBb"}], "url": {"raw": "https://developer.api.autodesk.com/oss/v2/buckets/cadshare-dev-bucket/objects", "protocol": "https", "host": ["developer", "api", "autodesk", "com"], "path": ["oss", "v2", "buckets", "cadshare-dev-bucket", "objects"]}}, "response": []}, {"name": "Get buckets for account", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer eyJhbGciOiJIUzI1NiIsImtpZCI6Imp3dF9zeW1tZXRyaWNfa2V5In0.***********************************************************************************************************************************************************************************************************************************************************************************************************************.IV7W6Z9PAiNzm5LdI9vl-vFUNRaD9A9Oin2jQNJDXS4"}], "url": {"raw": "https://developer.api.autodesk.com/oss/v2/buckets", "protocol": "https", "host": ["developer", "api", "autodesk", "com"], "path": ["oss", "v2", "buckets"]}}, "response": []}, {"name": "Create Bucket ", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer eyJhbGciOiJIUzI1NiIsImtpZCI6Imp3dF9zeW1tZXRyaWNfa2V5In0.***********************************************************************************************************************************************************************************************************************************************************************************************************************.ZIeA0XBJztSowSAvWMpHHchMKB-6aQRlVBv0zUQpnes"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n    \"bucketKey\":\"webapp.cadshare.co\",\r\n    \"policyKey\":\"persistent\"\r\n  }"}, "url": {"raw": "https://developer.api.autodesk.com/oss/v2/buckets", "protocol": "https", "host": ["developer", "api", "autodesk", "com"], "path": ["oss", "v2", "buckets"]}}, "response": []}, {"name": "Get Token from Autodesk (demo1 app)", "event": [{"listen": "test", "script": {"exec": ["var responseData = JSON.parse(responseBody);", "postman.setEnvironmentVariable(\"autodesk-access-token\", responseData.access_token);"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/x-www-form-urlencoded"}], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "client_id", "value": "ptEqDV9Yko7ZPwz0yJoLx2ECIGKaAujv", "type": "text"}, {"key": "client_secret", "value": "ZLtBn5QB7HEtGeOT", "type": "text"}, {"key": "grant_type", "value": "client_credentials", "type": "text"}, {"key": "scope", "value": "data:write bucket:read bucket:create data:read", "type": "text"}]}, "url": {"raw": "https://developer.api.autodesk.com/authentication/v1/authenticate", "protocol": "https", "host": ["developer", "api", "autodesk", "com"], "path": ["authentication", "v1", "authenticate"]}}, "response": []}, {"name": "Get Token from Autodesk (test1 app)", "event": [{"listen": "test", "script": {"exec": ["var responseData = JSON.parse(responseBody);", "postman.setEnvironmentVariable(\"autodesk-access-token\", responseData.access_token);"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/x-www-form-urlencoded"}], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "client_id", "value": "sZbZfPAqgSgJFwsHTq9BGwXlMv41npbQ", "type": "text"}, {"key": "client_secret", "value": "dn3vk3THkk5AOWXI", "type": "text"}, {"key": "grant_type", "value": "client_credentials", "type": "text"}, {"key": "scope", "value": "data:write bucket:read bucket:create data:read", "type": "text"}]}, "url": {"raw": "https://developer.api.autodesk.com/authentication/v1/authenticate", "protocol": "https", "host": ["developer", "api", "autodesk", "com"], "path": ["authentication", "v1", "authenticate"]}}, "response": []}, {"name": "Get Token from Autodesk (prod1 app)", "event": [{"listen": "test", "script": {"exec": ["var responseData = JSON.parse(responseBody);", "postman.setEnvironmentVariable(\"autodesk-access-token\", responseData.access_token);"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/x-www-form-urlencoded"}], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "client_id", "value": "jtAYmQawcXWBdHj2Q6YHAln6cdmYM3mM", "type": "text"}, {"key": "client_secret", "value": "jcvV3bXBrRHWHCun", "type": "text"}, {"key": "grant_type", "value": "client_credentials", "type": "text"}, {"key": "scope", "value": "data:write bucket:read bucket:create data:read", "type": "text"}]}, "url": {"raw": "https://developer.api.autodesk.com/authentication/v1/authenticate", "protocol": "https", "host": ["developer", "api", "autodesk", "com"], "path": ["authentication", "v1", "authenticate"]}}, "response": []}, {"name": "Get Token from Autodesk (Dev)", "event": [{"listen": "test", "script": {"exec": ["var responseData = JSON.parse(responseBody);", "postman.setEnvironmentVariable(\"autodesk-access-token\", responseData.access_token);"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/x-www-form-urlencoded"}], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "client_id", "value": "LySVr3xlAjS0byN3NP5iMWJu8MG30bKW", "type": "text"}, {"key": "client_secret", "value": "XrudtWPlqZI7yhKR", "type": "text"}, {"key": "grant_type", "value": "client_credentials", "type": "text"}, {"key": "scope", "value": "data:write bucket:read bucket:create data:read", "type": "text"}]}, "url": {"raw": "https://developer.api.autodesk.com/authentication/v1/authenticate", "protocol": "https", "host": ["developer", "api", "autodesk", "com"], "path": ["authentication", "v1", "authenticate"]}}, "response": []}]}