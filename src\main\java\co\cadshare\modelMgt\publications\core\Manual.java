/*
 * Copyright 2016 Bell.
 */
package co.cadshare.modelMgt.publications.core;

import co.cadshare.modelMgt.shared.core.Viewable;
import co.cadshare.shared.core.manufacturer.subentity.ManufacturerSubEntity;
import co.cadshare.shared.core.user.User;
import co.cadshare.utils.ObjectExtension;
import co.cadshare.utils.ObjectListExtension;
import lombok.AccessLevel;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.ExtensionMethod;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import static java.util.stream.Collectors.toList;

@Data
@ExtensionMethod({ObjectListExtension.class, ObjectExtension.class})
public class Manual {

    private int manualId;
    private String manualName;
    private String manualDescription;
    private List<Integer> modelId;
    private Integer featuredModelId;
    private String featuredModelUrl;
    private List<Integer> techDocId;
    private List<Integer> videoId;
    private List<Integer> kitId;
    private Timestamp createdDate;
    private Integer createdByUserId;
    private Timestamp modifiedDate;
    private Integer modifiedByUserId;
    private ManualStatus.Status status;
    private int manufacturerId;
    private String serialNumber;
    private boolean useViewableImage;

    private List<ManufacturerSubEntity> manufacturerSubEntity;

    @Getter(AccessLevel.NONE)
    @Setter(AccessLevel.NONE)
    private List<Viewable> viewables = new ArrayList<>();

    public static void determinePurchasersForManual(List<Integer> subEntitiesToBeAssigned,
                                                    List<ManufacturerSubEntity> subEntitiesAlreadyAssignedToManual,
                                                    List<Integer> unassignList,
                                                    List<Integer> assignList) {
        checkForSubEntitiesToBeUnassigned(subEntitiesToBeAssigned, subEntitiesAlreadyAssignedToManual, unassignList);
        checkForSubEntitiesToBeAssigned(subEntitiesToBeAssigned, subEntitiesAlreadyAssignedToManual, assignList);
    }

    private static void checkForSubEntitiesToBeUnassigned(List<Integer> subEntitiesToBeAssigned,
                                                          List<ManufacturerSubEntity> subEntitiesAlreadyAssignedToManual,
                                                          List<Integer> unassignList) {

        subEntitiesAlreadyAssignedToManual.forEach(subEntity -> {
            if (subEntitiesToBeAssigned.doesNotContain(subEntity.getManufacturerSubEntityId()) &&
                    subEntitiesToBeAssigned.doesNotContain(subEntity.getParentSubEntityId()))
                unassignList.add(subEntity.getManufacturerSubEntityId());
        });
    }

    private static void checkForSubEntitiesToBeAssigned(List<Integer> subEntitiesToBeAssigned,
                                                        List<ManufacturerSubEntity> subEntitiesAlreadyAssignedToManual,
                                                        List<Integer> assignList) {
        //check if any manufacturerSubEntities not in existing manufacturerSubEntities list
        //if so, add to assign list
        subEntitiesToBeAssigned.forEach(setba -> {
            if (subEntitiesAlreadyAssignedToManual
                    .stream().noneMatch(seaatm ->
                            (seaatm.getManufacturerSubEntityId() == setba) ||
                                    (Objects.equals(seaatm.getParentSubEntityId(), setba))))
                assignList.add(setba);
        });
    }

    public static Manual buildForImmediatePublication(Viewable viewable, User user) {
        Manual manual = new Manual();
        manual.manualName = viewable.getName();
        manual.serialNumber = viewable.getName().indexOf(" ") > 0 ?
                viewable.getName().substring(0,viewable.getName().indexOf(" ")) : "";
        manual.status = ManualStatus.Status.PUBLISHED;
        manual.viewables.add(viewable);
        manual.createdByUserId = user.getUserId();
        manual.modifiedByUserId = user.getUserId();
        List<Integer> models = new ArrayList<>();
        models.add(viewable.getId());
        manual.setModelId(models);
        manual.setManufacturerId(user.getManufacturerId());
        if(viewable.getProduct().getThumbnailUrl().isNotNull()) {
            manual.setFeaturedModelUrl(viewable.getProduct().getThumbnailUrl());
            manual.setUseViewableImage(true);
        }
        return manual;
    }


    public boolean shouldBeAssignedToPurchasers() {
        if(viewables.size() == 0) return false;
        List<Integer> assignedPurchasers = getAssignedPurchaserIds();
        return (assignedPurchasers.size() > 0);
    }

    public List<Integer> getPurchasersAssignedToRanges() {
        return getAssignedPurchaserIds();
    }

    private List<Integer> getAssignedPurchaserIds() {
        List<Integer> assignedPurchasers = viewables.stream()
                .flatMap(viewable -> viewable.getPurchasersAssignedToRange().stream()
                        .map(purchaser -> purchaser.getId())).collect(toList()).stream()
                .distinct().collect(toList());
        return assignedPurchasers;
    }
}