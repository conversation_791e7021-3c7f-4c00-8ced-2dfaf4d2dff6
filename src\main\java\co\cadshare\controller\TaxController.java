package co.cadshare.controller;

import co.cadshare.shared.core.user.User;
import co.cadshare.request.OrderWithWareHouseId;
import co.cadshare.services.AvalaraService;
import net.avalara.avatax.rest.client.enums.DocumentType;
import net.avalara.avatax.rest.client.models.TransactionModel;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

@Controller
@RequestMapping("/tax")
public class TaxController {

    private final Logger log = LoggerFactory.getLogger(getClass());

    @Autowired
    AvalaraService avalaraService;

    @PostMapping("/calculate")
    public HttpEntity<Object> calculateTax(@AuthenticationPrincipal User currentUser, @RequestParam Integer userId, @RequestBody OrderWithWareHouseId orderWithWareHouseId) {
        log.info("ACCESS: User [{}], calculateTax", currentUser.accessDetails());
        TransactionModel result;
        try {
            if (orderWithWareHouseId.getOrder() == null || orderWithWareHouseId.getWareHouseId() == 0)
                return new ResponseEntity<>("Input data invalid.", HttpStatus.BAD_REQUEST);
            int userIdForTax = userId == 0 ? currentUser.getUserId() : userId;
            result = avalaraService.createTransaction(userIdForTax, orderWithWareHouseId, DocumentType.SalesInvoice);
        } catch (IllegalArgumentException e) {
            return new ResponseEntity<>(e.getMessage(), HttpStatus.BAD_REQUEST);
        } catch (Exception e) {
            return new ResponseEntity<>(e.getMessage(), HttpStatus.INTERNAL_SERVER_ERROR);
        }
        return new ResponseEntity<>(result, HttpStatus.OK);
    }
}
