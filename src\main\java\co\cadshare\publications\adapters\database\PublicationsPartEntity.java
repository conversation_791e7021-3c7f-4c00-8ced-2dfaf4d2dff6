package co.cadshare.publications.adapters.database;

import lombok.Data;

import javax.persistence.*;

@Data
@Entity
@Table(name = "part")
public class PublicationsPartEntity {
    @Id
    @GeneratedValue
    @Column(name = "partid")
    private Integer id;

    @Column(name = "partnumber")
    private String partNumber;

    @ManyToOne
    @JoinColumn(name = "modelid")
    private PublicationsModelEntity model;
}
