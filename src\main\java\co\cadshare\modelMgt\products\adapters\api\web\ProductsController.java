package co.cadshare.modelMgt.products.adapters.api.web;

import co.cadshare.aspects.access.roles.CanAccessManufacturer;
import co.cadshare.aspects.access.roles.IsManufacturer;
import co.cadshare.modelMgt.products.adapters.api.GetProductListResponseDto;
import co.cadshare.modelMgt.products.adapters.api.GetProductResponseDto;
import co.cadshare.modelMgt.products.adapters.api.ProductMapper;
import co.cadshare.modelMgt.products.boundary.GetProductService;
import co.cadshare.modelMgt.products.core.Product;
import co.cadshare.shared.core.user.User;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController("ProductsInternalController")
@RequestMapping("/manufacturers/{manufacturer-id}")
public class ProductsController {

	private final GetProductService getProductService;

	public ProductsController(GetProductService getProductService) {
		this.getProductService = getProductService;
	}


	@GetMapping(produces = "application/json", path="/product-ranges/{product-range-id}/products")
    @CanAccessManufacturer
	@IsManufacturer
	@ResponseStatus(HttpStatus.OK)
    @Operation(summary = "Get the summary details of the list of all Products belonging to the specified Product Range")
    public ResponseEntity<GetProductListResponseDto> getProductsForRange(@PathVariable("manufacturer-id") int manufacturerId,
	                                                                     @PathVariable("product-range-id") int productRangeId,
	                                                                     @AuthenticationPrincipal User currentUser) {

	    List<Product> products = this.getProductService.getProductsForRange(productRangeId);
	    List<GetProductResponseDto> productResponses = ProductMapper.Instance.productToGetListResponseDto(products);
	    GetProductListResponseDto getListResponseDto = new GetProductListResponseDto(){{
		    setProducts(productResponses);
	    }};
	    return new ResponseEntity<>(getListResponseDto, HttpStatus.OK);
    }

	@GetMapping(produces = "application/json", path="/products")
	@CanAccessManufacturer
	@IsManufacturer
	@ResponseStatus(HttpStatus.OK)
	@Operation(summary = "Get the summary details of the list of all Products belonging to the Manufacturer")
	public ResponseEntity<GetManufacturerProductListResponseDto> getAllProducts(@PathVariable("manufacturer-id") int manufacturerId,
	                                                                     @AuthenticationPrincipal User currentUser) {

		List<Product> products = this.getProductService.getProductsForManufacturer(currentUser);
		List<GetManufacturerProductResponseDto> productResponses = ProductMapper.Instance.productToGetManufacturerListResponseDto(products);
		GetManufacturerProductListResponseDto getListResponseDto = new GetManufacturerProductListResponseDto(){{
			setProducts(productResponses);
		}};
		return new ResponseEntity<>(getListResponseDto, HttpStatus.OK);
	}
}
