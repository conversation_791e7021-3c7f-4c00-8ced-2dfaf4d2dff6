/*
 * Copyright 2016 Bell.
 */
package co.cadshare.domainmodel.part;

import java.util.List;

import co.cadshare.modelMgt.publications.core.TechDoc;
import co.cadshare.masterParts.core.extensions.kit.Kit;
import co.cadshare.masterParts.core.extensions.optionsSet.OptionsSet;
import co.cadshare.masterParts.core.extensions.partModelLink.PartModelLink;
import lombok.Data;

@Data
public class PartViewerDetails {

  private boolean linked = false;
  private boolean hasLinkedTechDocs = false;
  private boolean nonModelled = false;
  private Integer nonModelledId;
  private boolean inKit = false;
  private boolean containsOptionsSet = false;
  private PartModelLink linkedModel;
  private List<TechDoc> linkedTechDocs;
  private List<Part> nonModelledPart;
  private Part part;
  private List<Kit> kits;
  private List<OptionsSet> optionsSet;

}
