package co.cadshare.controller.dealer;

import co.cadshare.domainmodel.GenericMessageResponse;
import co.cadshare.shared.core.Currency;
import co.cadshare.shared.core.manufacturer.subentity.ManufacturerSubEntity;
import co.cadshare.shared.core.user.User;
import co.cadshare.shared.core.user.settings.ManufacturerSubEntitySettings;
import co.cadshare.publications.boundary.ManualService;
import co.cadshare.services.ManufacturerSubEntityService;
import co.cadshare.services.PermissionsService;
import co.cadshare.services.dealer.DealerManufacturerSubEntityService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.List;

@Slf4j
@Controller
@PreAuthorize("hasRole('ROLE_MANUFACTURER_SUB_ENTITY_DEALER_PLUS')")
@RequestMapping("/dealerplus/manufacturersubentity")
public class DealerManufacturerSubEntityController {

  private DealerManufacturerSubEntityService dealerManufacturerSubEntityService;
  private ManufacturerSubEntityService manufacturerSubEntityService;
  private ManualService manualService;
  private PermissionsService permissionsService;

  public DealerManufacturerSubEntityController(DealerManufacturerSubEntityService dealerManufacturerSubEntityService, ManufacturerSubEntityService manufacturerSubEntityService,
                                               ManualService manualService, PermissionsService permissionsService) {
    this.dealerManufacturerSubEntityService = dealerManufacturerSubEntityService;
    this.manufacturerSubEntityService = manufacturerSubEntityService;
    this.manualService = manualService;
    this.permissionsService = permissionsService;
  }

  @RequestMapping(method = RequestMethod.POST, consumes = "application/json")
  public ResponseEntity<?> createManufacturerSubEntity(@AuthenticationPrincipal User currentUser,
      @RequestBody ManufacturerSubEntity manufacturerSubEntity) throws Exception {

    log.info("ACCESS: User [{}], dealerplus - createManufacturerSubEntity, manufacturerSubEntity: [{}]", currentUser.accessDetails(),
        manufacturerSubEntity.toString());
    try {
        int manufacturerSubEntityId = dealerManufacturerSubEntityService.createManufacturerSubEntity(currentUser,
            manufacturerSubEntity);
        manufacturerSubEntity.setManufacturerSubEntityId(manufacturerSubEntityId);
        log.info("manufacturerSubEntity [{}] successfully created", manufacturerSubEntity);

        return new ResponseEntity<>(manufacturerSubEntityId, HttpStatus.OK);
      } catch (
              IllegalArgumentException ex) {
      return new ResponseEntity<>(new GenericMessageResponse(ex.getMessage()), HttpStatus.CONFLICT);
      }
  }

  @RequestMapping(method = RequestMethod.PUT, consumes = "application/json")
  public HttpEntity<GenericMessageResponse> updateManufacturerSubEntity(@AuthenticationPrincipal User currentUser, @RequestBody ManufacturerSubEntity manufacturerSubEntity) throws Exception {

    try {
      hasAdminPermissionsForSubEntity(currentUser, manufacturerSubEntity.getManufacturerSubEntityId());
    } catch (Exception ex) {
      return new ResponseEntity<>(null, HttpStatus.FORBIDDEN);
    }
    try {
      int result = manufacturerSubEntityService.updateManufacturerSubEntity(currentUser, manufacturerSubEntity);
      return new ResponseEntity<>(new GenericMessageResponse("Successfully updated subentity"), HttpStatus.OK);
    } catch (IllegalArgumentException ex) {
      return new ResponseEntity<>(new GenericMessageResponse(ex.getMessage()), HttpStatus.CONFLICT);
    }
  }

  @DeleteMapping("/{manufacturerSubEntityId}")
  @PreAuthorize("@manufacturerSubEntityService.getManufacturerSubEntity(#manufacturerSubEntityId).getParentSubEntityId() == #currentUser.getManufacturerSubEntityId()")
  public HttpEntity<GenericMessageResponse> deleteManufacturerSubEntity(@AuthenticationPrincipal User currentUser, @PathVariable int manufacturerSubEntityId) throws Exception {
    try {
      hasAdminPermissionsForSubEntity(currentUser, manufacturerSubEntityId);
    } catch (Exception ex) {
      return new ResponseEntity<>(null, HttpStatus.FORBIDDEN);
    }

    if(manufacturerSubEntityService.manufacturerSubEntityCanBeDeleted(manufacturerSubEntityId)) {
      manufacturerSubEntityService.deleteManufacturerSubEntity(manufacturerSubEntityId);
    } else {
      return new ResponseEntity<>(new GenericMessageResponse("Company cannot be deleted due to associated (or previously associated) users, orders or manuals."), HttpStatus.BAD_REQUEST);
    }

    return new ResponseEntity<>(new GenericMessageResponse("Successfully deleted subentity"), HttpStatus.OK);
  }

  @PreAuthorize("@manufacturerSubEntityService.getManufacturerSubEntity(#manufacturerSubEntityId).parentSubEntityId == #currentUser.getManufacturerSubEntityId()")
  @GetMapping(value = "/{manufacturerSubEntityId}/currency")
  public HttpEntity<Currency> getManufacturerSubEntityCurrency(@AuthenticationPrincipal User currentUser, @PathVariable int manufacturerSubEntityId) throws IOException {
    log.info("ACCESS: User [{}], dealerplus - getManufacturerSubEntityCurrency", currentUser.accessDetails());

    try {
      hasAdminPermissionsForSubEntity(currentUser, manufacturerSubEntityId);
    } catch (Exception ex) {
      return new ResponseEntity<>(null, HttpStatus.FORBIDDEN);
    }
    Currency currency = manufacturerSubEntityService.getCurrencyBySubEntityId(currentUser, manufacturerSubEntityId);

    return new ResponseEntity<>(currency, HttpStatus.OK);
  }

  @PreAuthorize("@manufacturerSubEntityService.getManufacturerSubEntity(#manufacturerSubEntityId).parentSubEntityId == #currentUser.getManufacturerSubEntityId()")
  @RequestMapping(value = "/{manufacturerSubEntityId}/settings", method = RequestMethod.GET)
  public HttpEntity<ManufacturerSubEntitySettings> getManufacturerSubEntitySettings(@AuthenticationPrincipal User currentUser, @PathVariable int manufacturerSubEntityId) throws Exception {
    log.info("ACCESS: User [{}], dealerplus - getManufacturerSubEntitySettings, manufacturer [{}]", currentUser, currentUser.getManufacturerSubEntityId());

    try {
      hasAdminPermissionsForSubEntity(currentUser, manufacturerSubEntityId);
    } catch (Exception ex) {
      return new ResponseEntity<>(null, HttpStatus.FORBIDDEN);
    }
    ManufacturerSubEntitySettings settings = manufacturerSubEntityService.getManufacturerSubEntitySettings(manufacturerSubEntityId);

    return new ResponseEntity<ManufacturerSubEntitySettings>(settings, HttpStatus.OK);
  }

  @PreAuthorize("@manufacturerSubEntityService.getManufacturerSubEntity(#manufacturerSubEntityId).parentSubEntityId == #currentUser.getManufacturerSubEntityId()")
  @RequestMapping(value = "/{manufacturerSubEntityId}/assignedManualIds", method = RequestMethod.GET)
  public HttpEntity<List<Integer>> getAssignedManualIds (
          @AuthenticationPrincipal User currentUser,
          @PathVariable int manufacturerSubEntityId) throws Exception {

    log.info("ACCESS: User [{}], dealerplus - getAssignedManuals, manufacturerSubEntityId [{}]",
            currentUser.accessDetails(), manufacturerSubEntityId);
    try {
      hasAdminPermissionsForSubEntity(currentUser, manufacturerSubEntityId);
    } catch (Exception ex) {
      return new ResponseEntity<>(null, HttpStatus.FORBIDDEN);
    }

    List<Integer> manualIDList = manualService.getAssignedManualIdsForManufacturerSubEntity(manufacturerSubEntityId);
    return new ResponseEntity<>(manualIDList, HttpStatus.OK);
  }

  @PreAuthorize("@manufacturerSubEntityService.getManufacturerSubEntity(#manufacturerSubEntityId).parentSubEntityId == #currentUser.getManufacturerSubEntityId()")
  @RequestMapping(value = "/{manufacturerSubEntityId}/manuals/assign", method = RequestMethod.PUT, consumes = "application/json")
  public HttpEntity<Boolean> updateAssignedManuals (
          @AuthenticationPrincipal User currentUser,
          @PathVariable int manufacturerSubEntityId,
          @RequestBody List<Integer> assignedManualIds) throws Exception {

    log.info("ACCESS: User [{}], dealerplus - updateAssignedManuals, manufacturerSubEntityId [{}]",
            currentUser.accessDetails(), manufacturerSubEntityId);

    try {
      hasAdminPermissionsForSubEntity(currentUser, manufacturerSubEntityId);
      if (assignedManualIds.size() > 0) {
        hasPermissionsToManuals(currentUser, assignedManualIds);
      }
    } catch (Exception ex) {
      throw new Exception("User can't be assigned to submitted manual IDs");
    }

    Boolean updated = manualService.updateAssignedManuals(manufacturerSubEntityId, assignedManualIds);
    return new ResponseEntity<Boolean>(updated, HttpStatus.OK);
  }

    private boolean hasAdminPermissionsForSubEntity(User currentUser, int manufacturerSubEntityId) throws Exception {
    return permissionsService.hasDealerAdminPermissionsForSubEntity(currentUser, manufacturerSubEntityId);
  }

  private boolean hasPermissionsToManuals(User currentUser, List<Integer> assignedManualIds) throws Exception {
    return permissionsService.hasDealerPermissionsToAssignManuals(currentUser, assignedManualIds);
  }
}
