package co.cadshare.addresses.core;

import co.cadshare.shared.core.user.UserType;
import co.cadshare.utils.ObjectUtilsExtension;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.ExtensionMethod;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Data
@ExtensionMethod(ObjectUtilsExtension.class)
public class User {
    private Integer id;
    private String firstName;

    private UserType userType;

    private String lastName;
    private List<ExternalPurchaser> purchasers;

    @ToString.Exclude
    @EqualsAndHashCode.Exclude
    private List<UserAddressMap> addressMaps;

    public User() {
        this.purchasers = new ArrayList<>();
        this.addressMaps = new ArrayList<>();
    }

    public List<ExternalAddress> getExternalAddresses() {
        return addressMaps.stream()
                .filter(userAddressMap -> !userAddressMap.isDeleted())
                .map(UserAddressMap::getAddress)
                .filter(address -> address.getExternalRefId().isNotNull())
                .collect(Collectors.toList());
    }

    public boolean doesNotHaveAddressMapped(ExternalAddress address) {
        return getAddressMaps().isEmpty() ||
                getAddressMaps()
                .stream()
                .noneMatch(addressMap -> addressMap.getAddress().matchesExternally(address));
    }

}
