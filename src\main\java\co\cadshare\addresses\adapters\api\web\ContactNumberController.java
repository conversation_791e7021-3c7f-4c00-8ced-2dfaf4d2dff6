package co.cadshare.addresses.adapters.api.web;

import co.cadshare.addresses.core.ContactNumber;
import co.cadshare.shared.core.user.User;
import co.cadshare.addresses.boundary.ContactNumberService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Controller
@RequestMapping("/contactNumber")
@Slf4j
public class ContactNumberController {

  private ContactNumberService contactNumberService;

  public ContactNumberController(ContactNumberService contactNumberService) {
    this.contactNumberService = contactNumberService;
  }

  @CanMaintainAddress
  @GetMapping
  public HttpEntity<List<ContactNumber>> getNumbersForUser(@AuthenticationPrincipal User currentUser, int userId) {
    log.info("ACCESS: User [{}]", currentUser.accessDetails());

    List<ContactNumber> nameList = contactNumberService.getAllNumbersForUser(userId);

    return new ResponseEntity<>(nameList, HttpStatus.OK);
  }

  @CanMaintainAddress
  @PostMapping(consumes = "application/json")
  public HttpEntity<Integer> createContactNumber(@AuthenticationPrincipal User currentUser, @RequestBody ContactNumber contactNumber, int userId) throws Exception {

    log.info("ACCESS: User [{}], createContactNumber", currentUser.accessDetails());

    int userIdForNewAddress = userId == 0 ? currentUser.getUserId() : userId;

    int numberId = contactNumberService.createNumberForUser(userIdForNewAddress, contactNumber);

    return new ResponseEntity<>(numberId, HttpStatus.OK);
  }

  @PutMapping(value = "/{numberId}", consumes = "application/json")
  public HttpEntity<Integer> updateContactNumber(@AuthenticationPrincipal User currentUser, @RequestBody ContactNumber contactNumber, @PathVariable int numberId) throws Exception {

    log.info("ACCESS: User [{}], updateContactNumber, numberId [{}]", currentUser.accessDetails(), contactNumber.getId());

    contactNumberService.updateNumber(currentUser.getUserId(), contactNumber);
    return new ResponseEntity<>(HttpStatus.OK);
  }

  @DeleteMapping("/{numberId}")
  public HttpEntity<Boolean> deleteContactNumber(@AuthenticationPrincipal User currentUser, @PathVariable int numberId) {

    log.info("ACCESS: User [{}], deleteContactNumber, numberId [{}]", currentUser.accessDetails(), numberId);
    Boolean isDeleted = contactNumberService.deleteNumber(currentUser.getUserId(), numberId);

    return new ResponseEntity<Boolean>(isDeleted, HttpStatus.OK);
  }
}
