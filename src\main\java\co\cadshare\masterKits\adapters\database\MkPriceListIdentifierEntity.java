package co.cadshare.masterKits.adapters.database;

import co.cadshare.shared.adapters.database.CurrencyEntity;
import lombok.Data;

import javax.persistence.*;

@Data
@Entity
@Table(name="pricelistidentifier")
public class MkPriceListIdentifierEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name="id")
    private int id;
    private String identifier;

    @OneToOne
    @JoinColumn(name="currencyid", referencedColumnName="id")
    private CurrencyEntity currency;
}
