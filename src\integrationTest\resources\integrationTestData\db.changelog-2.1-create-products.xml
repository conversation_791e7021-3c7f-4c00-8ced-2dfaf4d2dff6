<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                   http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.0.xsd
">

    <changeSet author="AndyB" id="2.1-integration-test-data-create-products-1">
        <sql stripComments="true">
            INSERT INTO public.machine (machineid, name, description, rangeid, createddate, createdbyuserid, archived, modifieddate, modifiedbyuserid, thumbnailurl)
            VALUES (
            1, 	                        --machineid,
            'Caterpillar Product 1',    --name,
            '', 	                    --description,
            1, 	                        --rangeid,
            '2017-06-09 10:42:56.176', 	--createddate,
            1, 	                        --createdbyuserid,
            false, 	                    --archived,
            '2017-06-09 10:42:56.176', 	--modifieddate,
            1, 	                        --modifiedbyuserid,
            NULL);	                    --thumbnailurl)
        </sql>
    </changeSet>

    <changeSet author="AndyB" id="2.1-integration-test-data-create-products-2">
        <sql stripComments="true">
            INSERT INTO public.machine (machineid, name, description, rangeid, createddate, createdbyuserid, archived, modifieddate, modifiedbyuserid, thumbnailurl)
            VALUES (
            2, 	                        --machineid,
            'JCB Product 1',            --name,
            '', 	                    --description,
            2, 	                        --rangeid,
            '2017-06-09 10:42:56.176', 	--createddate,
            1, 	                        --createdbyuserid,
            false, 	                    --archived,
            '2017-06-09 10:42:56.176', 	--modifieddate,
            1, 	                        --modifiedbyuserid,
            NULL);	                    --thumbnailurl)
        </sql>
    </changeSet>

    <changeSet author="AndyB" id="2.1-integration-test-data-create-products-3">
        <sql stripComments="true">
            INSERT INTO public.machine (machineid, name, description, rangeid, createddate, createdbyuserid, archived, modifieddate, modifiedbyuserid, thumbnailurl)
            VALUES (
            3, 	                        --machineid,
            'Liebherr Product 3',       --name,
            '', 	                    --description,
            3, 	                        --rangeid,
            '2017-06-09 10:42:56.176', 	--createddate,
            1, 	                        --createdbyuserid,
            false, 	                    --archived,
            '2017-06-09 10:42:56.176', 	--modifieddate,
            1, 	                        --modifiedbyuserid,
            NULL);	                    --thumbnailurl)
        </sql>
    </changeSet>

    <changeSet author="AndyB" id="2.1-integration-test-data-create-products-4">
        <sql stripComments="true">
            INSERT INTO public.machine (machineid, name, description, rangeid, createddate, createdbyuserid, archived, modifieddate, modifiedbyuserid, thumbnailurl)
            VALUES (
            4, 	                        --machineid,
            'Terex Product 1',          --name,
            '', 	                    --description,
            4, 	                        --rangeid,
            '2017-06-09 10:42:56.176', 	--createddate,
            1, 	                        --createdbyuserid,
            false, 	                    --archived,
            '2017-06-09 10:42:56.176', 	--modifieddate,
            1, 	                        --modifiedbyuserid,
            NULL);	                    --thumbnailurl)
        </sql>
    </changeSet>
</databaseChangeLog>
