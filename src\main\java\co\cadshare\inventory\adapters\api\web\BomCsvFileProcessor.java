package co.cadshare.inventory.adapters.api.web;

import co.cadshare.inventory.core.BomUpload;
import co.cadshare.inventory.core.BomUpload.UnparseableModelPartsFileException;
import com.opencsv.CSVReader;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStreamReader;
import java.util.List;

@Component
@Slf4j
public class BomCsvFileProcessor {

    private List<String[]> readAll(MultipartFile file) throws IOException {

        try (CSVReader reader = new CSVReader(new InputStreamReader(file.getInputStream(), "UTF-8"))){
            List<String[]> list;
            list = reader.readAll();
            return list;
        }
    }

    public BomUpload convert(MultipartFile file) throws IOException, UnparseableModelPartsFileException {
        BomUpload modelPartsFile = new BomUpload(readAll(file));
        return modelPartsFile;
    }
}
