/*
 * Copyright 2016 Bell.
 */
package co.cadshare.models.core.processor.fileproperties;

import co.cadshare.models.core.MetadataObjectExtended;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.LinkedHashMap;
import java.util.List;

@Component
public class MassUnitPropertyProcessor extends AbstractPropertiesProcessor implements FilePropertiesProcessor, InitializingBean {

    @Value("#{'${properties.processor.massUnit.synonyms}'.split(',')}")
    List<String> massUnitSynonyms;

    @Override
    public void setProperties(LinkedHashMap properties, MetadataObjectExtended object) {
        object.setMassUnit(getPropertyValue(properties));
    }

    @Override
    public List<String> getSynonyms() {
        return massUnitSynonyms;
    }

    @Override
    public void setSynonyms(List<String> synonyms) {
        massUnitSynonyms = synonyms;
    }
}
