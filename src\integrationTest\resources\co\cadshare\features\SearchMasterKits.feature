Feature: Search MasterKits

  Scenario Outline: Manufacturer Search MasterKits by <PERSON><PERSON>umber
    Given I am a Manufacturer with email address <emailAddress>
    And I use <priceLists> and <warehouses>
    When I search MasterKits for part number by <searchTerm>
    Then I am presented with a list of <count> MasterKits that match part number on <searchTerm>
    Examples:
      | emailAddress                 | priceLists | warehouses | searchTerm       | count |
      | <EMAIL> | false      | false      | Cat-kit-part-211 | 1     |
      | <EMAIL>         | true       | false      | Jcb-kit-part-221 | 1     |
      | <EMAIL>    | false      | true       | Lbr-kit-part-231 | 1     |
      | <EMAIL>       | true       | true       | Trx-kit-part-241 | 1     |


  Scenario Outline: Manufacturer Search MasterKits by PartDescription
    Given I am a Manufacturer with <NAME_EMAIL>
    When I search MasterKits for part description by <searchTerm> using <languageCode>
    Then I am presented with a list of <count> MasterKits that match part description on <searchTerm>
    Examples:
      | searchTerm                    | languageCode | count |
      | Cat-part-211 desc in English  | EN           | 1     |
      | Cat-part-211 desc en Français | FR           | 1     |
      | Cat-part-211 desc in English  | FR           | 0     |

  @assign
  Scenario Outline: Dealer Search MasterKits by PartNumber
    Given I am a Dealer with email address <emailAddress>
    And my Manufacturer uses <priceLists> and <warehouses>
    When I search MasterKits for part number by <searchTerm>
    Then I am presented with a list of <count> MasterKits that match part number on <searchTerm>
    Examples:
      | emailAddress           | priceLists | warehouses | searchTerm       | count |
      | <EMAIL> | false      | false      | Cat-kit-part-211 | 1     |
      | <EMAIL>         | true       | false      | Jcb-kit-part-221 | 1     |
      | <EMAIL>    | false      | true       | Lbr-kit-part-231 | 1     |
      | <EMAIL>       | true       | true       | Trx-kit-part-241 | 1     |

  Scenario Outline: Dealer Search MasterKits by PartDescription
    Given I am a Dealer with email address <emailAddress>
    And my Manufacturer uses <priceLists> and <warehouses>
    When I search MasterKits for part description by <searchTerm> using <languageCode>
    Then I am presented with a list of <count> MasterKits that match part description on <searchTerm>
    Examples:
      | emailAddress           | priceLists | warehouses | searchTerm                    | languageCode | count |
      | <EMAIL> | false      | false      | Cat-part-211 desc in English  | EN           | 1     |
      | <EMAIL> | false      | false      | Cat-part-211 desc en Français | FR           | 1     |
      | <EMAIL> | false      | false      | Cat-part-211 desc in English  | FR           | 0     |

  Scenario Outline: DealerPlus Search MasterKits by PartNumber
    Given I am a DealerPlus with <NAME_EMAIL>
    When I search MasterKits for part number by <searchTerm>
    Then I am presented with a list of <count> MasterKits that match part number on <searchTerm>
    Examples:
      | searchTerm       | count |
      | Cat-kit-part-211 | 1     |
      | Cat-kit-part     | 1     |

  Scenario Outline: DealerPlus Search MasterKits by PartDescription
    Given I am a Dealer with email address <emailAddress>
    And my Manufacturer uses <priceLists> and <warehouses>
    When I search MasterKits for part description by <searchTerm> using <languageCode>
    Then I am presented with a list of <count> MasterKits that match part description on <searchTerm>
    Examples:
      | emailAddress                       | priceLists | warehouses | searchTerm                    | languageCode | count |
      | <EMAIL> | false      | false      | Cat-part-211 desc in English  | EN           | 1     |
      | <EMAIL> | false      | false      | Cat-part-211 desc en Français | FR           | 1     |
      | <EMAIL> | false      | false      | Cat-part-211 desc in English  | FR           | 0     |
      | <EMAIL>         | true       | false      | Jcb-part-221 desc in English  | EN           | 1     |
      | <EMAIL>    | false      | true       | Lbr-part-231 desc in English  | EN           | 1     |
      | <EMAIL>       | true       | true       | Trx-part-241 desc in English  | EN           | 1     |