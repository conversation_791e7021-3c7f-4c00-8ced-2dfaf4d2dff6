package co.cadshare.glue;

import co.cadshare.domainmodel.part.PartViewerDetails;
import co.cadshare.masterParts.adapters.api.web.SupersessionHistoryDto;
import co.cadshare.masterParts.adapters.api.web.SupersessionHistoryItemDto;
import co.cadshare.masterParts.boundary.MasterPartSearchResult;
import co.cadshare.masterParts.core.MasterPart;
import co.cadshare.masterParts.core.MasterPartDetails;
import io.cucumber.java.en.And;
import io.cucumber.java.en.Then;
import io.cucumber.java.en.When;
import io.restassured.response.Response;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

import static org.junit.Assert.*;

public class MasterPartsStepsIT extends BasicStepsIT {

    private SupersessionHistoryDto supersessionHistoryDto;
    private MasterPartDetails identifiedMasterPart;
	private final UserIT loggedInUser;
	private MasterPartDetails supersededMasterPart;
	private MasterPartSearchResult masterPartSearchResult;

	@Autowired
	public MasterPartsStepsIT(AuthenticationIT auth) {
		loggedInUser = auth.getLoggedInUser();
	}

    @When("I identify a {string} MasterPart")
    public void iIdentifyAMasterPart(String identifiedPart) {
        getMasterPart(identifiedPart);
    }

    @Then("I am offered a {string} as a replacement")
    public void iAmOfferedTheCorresponding(String supersessionMasterPart) {
        PartViewerDetails partViewerDetails = loggedInUser.getPartViewerDetails(identifiedMasterPart);
        assertEquals(supersessionMasterPart, partViewerDetails.getPart().getPartNumber());
    }

    @Then("there is a Supersession History from {string} that goes right back to {string}")
    public void iCanSeeTheSupersessionHistoryForThatGoesRightBackTo(String supersessionMasterPart ,
                                                                    String supersededMasterPart) {

        supersessionHistoryDto = loggedInUser.getSupersessionHistoryForPart(supersessionMasterPart);
        canSeeSupersessionHistoryForManufacturerOrDealer(supersessionMasterPart, supersededMasterPart, supersessionHistoryDto);
    }

    @When("I select a {string}")
    public void iSelectAMasterPart(String identifiedPart) {
        MasterPartSearchResult masterPartSearchResult = loggedInUser.searchForMasterPartByNumber(identifiedPart);
        identifiedMasterPart = loggedInUser.getMasterPart(masterPartSearchResult.getMasterParts().get(0).getMasterPartId());
        assertNotNull(identifiedMasterPart);
    }

    @Then("there is a Supersession History from {string} to {string}")
    public void iCanSeeTheSupersessionHistoryFromTo(String fromMasterPart,
                                                    String toMasterPart) {
        assertNotNull(identifiedMasterPart);
        supersessionHistoryDto = loggedInUser.getSupersessionHistoryForPart(identifiedMasterPart.getPartNumber());
        canSeeSupersessionHistoryForManufacturerOrDealer(toMasterPart, fromMasterPart, supersessionHistoryDto);
    }

    @When("I supersede a {string} by {string}")
    public void iSupersedeABy(String supersededMasterPart, String supersessionMasterPart) {
        Response response = loggedInUser.supersedeMasterPart(supersededMasterPart, supersessionMasterPart);
        assertEquals(200, response.getStatusCode());
    }

    @When("the {string} has supersession history starting at {string}")
    public void theHasSupersessionHistoryStartingAt(String supersessionOriginMasterPart, String supersessionMasterPart) {

    }

    @And("the {string} has no supersession history")
    public void theHasNoSupersessionHistory(String arg0) {

    }

    @And("there are {int} of MasterParts in that Supersession History")
    public void thereAreOfMasterPartsInTheSupersessionHistory(int numberOfParts) {//assert
        assertEquals(numberOfParts, supersessionHistoryDto.getSupersessionHistory().size());
    }


    @When("I revise the supersession at {string}")
    public void iReviseTheSupersessionAt(String masterPartNumber) {
        Response response = loggedInUser.reviseSupersessionHistory(masterPartNumber);
        assertEquals(200, response.getStatusCode());
    }


    @When("I split the supersession at {string}")
    public void iSplitTheSupersessionAt(String masterPartNumber) {
        Response response = loggedInUser.splitSupersessionHistory(masterPartNumber);
        assertEquals(200, response.getStatusCode());
    }

    private void canSeeSupersessionHistoryForManufacturerOrDealer(String supersessionMasterPart,
                                                                  String supersededMasterPart,
                                                                  SupersessionHistoryDto dto) {
        List<SupersessionHistoryItemDto> supersessionHistory = dto.getSupersessionHistory();
        assertEquals(supersededMasterPart, supersessionHistory.get(0).getPartNumber());
        int supersessionHistorySize = supersessionHistory.size();
        assertEquals(supersessionMasterPart, supersessionHistory.get(supersessionHistorySize-1).getPartNumber());
    }

    @Then("there is no Supersession History for {string}")
    public void thereIsNoSupersessionHistoryFor(String masterPartNumber) {
	    loggedInUser.doNotGetSupersessionHistoryForPart(masterPartNumber);
    }

    @Then("there is no Supersession History")
    public void thereIsNoSupersessionHistoryFor() {
	    loggedInUser.doNotGetSupersessionHistoryForPart(identifiedMasterPart.getPartNumber());
    }

    @When("I fail to revise the supersession at {string}")
    public void iFailToReviseTheSupersessionAt(String masterPartNumber) {
        Response response = loggedInUser.attemptRevisionSupersessionHistory(masterPartNumber, 400);
        assertEquals(400, response.getStatusCode());
    }

    @When("I fail to supersede {string} by {string}")
    public void iFailToSupersedeBy(String supersededMasterPart, String supersessionMasterPart) {
	    loggedInUser.failToSupersedeMasterPart(supersededMasterPart, supersessionMasterPart);
    }

    @When("I identify a {} in a model")
    public void asAIIdentifyAInAModel(String selectedPart) {
        getMasterPart(selectedPart);
    }

    @Then("I can see the details of {}")
    public void asAICanSeeTheDetailsOf(String arg1) {
        PartViewerDetails partViewerDetails = loggedInUser.getPartViewerDetails(identifiedMasterPart);
        assertNotNull(partViewerDetails);
    }

    @Then("I can see details of {} on behalf of {}")
    public void asAICanSeeDetailsOfOnBehalfOf(String arg1, String userId) {
        PartViewerDetails partViewerDetails = loggedInUser.getPartViewerDetailsOnBehalfOf(identifiedMasterPart, Integer.parseInt(userId));
        assertNotNull(partViewerDetails);
    }

    @Then("I cannot see details of {} on behalf of {}")
    public void asAICannotSeeDetailsOfOnBehalfOf(String arg1, String userId) {
	    loggedInUser.failToGetPartViewerDetailsOnBehalfOf(identifiedMasterPart, Integer.parseInt(userId));
    }

    private void getMasterPart(String identifiedPart) {
        MasterPartSearchResult masterPartSearchResult = loggedInUser.searchForMasterPartByNumber(identifiedPart);
        identifiedMasterPart = loggedInUser.getMasterPart(masterPartSearchResult.getMasterParts().get(0).getMasterPartId());
        assertNotNull(identifiedMasterPart);
    }

	@And("the MasterPart details of {string} show its in a supersession and is not superseded")
	public void theMasterPartDetailsOfShowItsInASupersessionAndIsNotSuperseded(String masterPartNumber) {
		MasterPartDetails masterPartDetails = getMasterPartDetailsFromPartNumber(masterPartNumber);
		assertTrue(masterPartDetails.isInSupersession());
		assertFalse(masterPartDetails.isSuperseded());
	}

	@And("the MasterPart details of {string} show its in a supersession and is superseded")
	public void theMasterPartDetailsOfShowItsInASupersessionAndIsSuperseded(String masterPartNumber) {
		MasterPartDetails masterPartDetails = getMasterPartDetailsFromPartNumber(masterPartNumber);
		assertTrue(masterPartDetails.isInSupersession());
		assertTrue(masterPartDetails.isSuperseded());
	}

	@And("{string} is no longer in supersession history")
	public void isNoLongerInSupersessionHistory(String masterPartNumber) {
		loggedInUser.doNotGetSupersessionHistoryForPart(masterPartNumber);
		MasterPartDetails masterPart = getMasterPartDetailsFromPartNumber(masterPartNumber);
		assertFalse(masterPart.isInSupersession());
		assertFalse(masterPart.isSuperseded());
	}

	private MasterPartDetails getMasterPartDetailsFromPartNumber(String masterPartNumber) {
		MasterPartSearchResult masterPartSearchResult = loggedInUser.searchForMasterPartByNumber(masterPartNumber);
		return loggedInUser.getMasterPart(masterPartSearchResult.getMasterParts().get(0).getMasterPartId());
	}

	@When("I search MasterParts for part number by {}")
	public void iSearchMasterPartsForPartNumberBy(String masterPartNumberSearch) {
		masterPartSearchResult = loggedInUser.searchForMasterPartByNumber(masterPartNumberSearch);
		assertNotNull(masterPartSearchResult);
	}

	@When("I search MasterParts for part description by {} using {}")
	public void iSearchMasterPartsForPartDescriptionByUsing(String masterPartDescSearch, String languageCode) {
		masterPartSearchResult = loggedInUser.searchForMasterPartByDescriptionUsingLanguage(masterPartDescSearch, languageCode);
		assertNotNull(masterPartSearchResult);
	}

	@Then("I am presented with a list of {} MasterParts that match part number on {}")
	public void iAmPresentedWithAListOfMasterPartsThatMatchPartNumberOn(String count, String masterPartNumberSearch) {
		assertFalse(masterPartSearchResult.getMasterParts().isEmpty());
		assertEquals(Integer.parseInt(count), masterPartSearchResult.getMasterParts().size());
		for (MasterPart masterPartDto : masterPartSearchResult.getMasterParts()) {
			assertNotNull(masterPartDto);
			assertTrue(masterPartDto.getPartNumber().contains(masterPartNumberSearch));
		}
	}

	@Then("I am presented with a list of {} MasterParts that match part description on {}")
	public void iAmPresentedWithAListOfMasterPartsThatMatchPartDescriptionOn(String count, String masterPartDescSearch) {
		assertEquals(Integer.parseInt(count), masterPartSearchResult.getMasterParts().size());
		for (MasterPart masterPartDto : masterPartSearchResult.getMasterParts()) {
			assertNotNull(masterPartDto);
			assertTrue(masterPartDto.getDescription().contains(masterPartDescSearch));
		}
	}

	@And("my Manufacturer uses {} and {}")
	@And("I use {} and {}")
	public void myManufacturerUsesAnd(String priceListsEnabled, String wareshousesEnabled) {
		priceListsAndWarehouses(priceListsEnabled, wareshousesEnabled);
	}

	private void priceListsAndWarehouses(String priceListsEnabled, String wareshousesEnabled) {
		assertEquals(Boolean.parseBoolean(priceListsEnabled), loggedInUser.user.getManufacturerSettings().isPriceListsEnabled());
		assertEquals(Boolean.parseBoolean(wareshousesEnabled), loggedInUser.user.getManufacturerSettings().isStockWarehousesEnabled());
	}

	@When("I {} search MasterParts for part number by {}")
	public void iSearchMasterPartsForPartNumberBy(boolean exactMatch, String masterPartNumberSearch) {
		masterPartSearchResult = exactMatch?
				loggedInUser.exactSearchForMasterPartByNumber(masterPartNumberSearch):
				loggedInUser.searchForMasterPartByNumber(masterPartNumberSearch);
		assertNotNull(masterPartSearchResult);
	}
}
