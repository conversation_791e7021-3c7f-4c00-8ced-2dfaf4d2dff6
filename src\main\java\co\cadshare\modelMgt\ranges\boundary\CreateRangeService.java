package co.cadshare.modelMgt.ranges.boundary;

import co.cadshare.modelMgt.models.boundary.UserQueryPort;
import co.cadshare.modelMgt.ranges.core.Range;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import co.cadshare.shared.core.user.User;
import co.cadshare.aspects.logging.Log;

@Service
public class CreateRangeService {

    private final RangeCommandPort rangeCommandPort;
    private final UserQueryPort userQueryPort;

    @Autowired
    public CreateRangeService(RangeCommandPort rangeCommandPort, UserQueryPort userQueryPort) {
        this.rangeCommandPort = rangeCommandPort;
        this.userQueryPort = userQueryPort;
    }

    @Log
    public Integer create(User user, Range range) {
        return this.rangeCommandPort.create(user, range);
    }

    public int create(Range range, int manufacturerId) {
        User apiUser = this.userQueryPort.getApiUserForManufacturer(manufacturerId);
        return  create(apiUser, range);
    }
}
