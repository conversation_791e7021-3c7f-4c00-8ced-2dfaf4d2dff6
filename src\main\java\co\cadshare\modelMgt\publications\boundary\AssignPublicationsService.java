package co.cadshare.modelMgt.publications.boundary;

import co.cadshare.modelMgt.publicationCategories.boundary.PublicationCategoryQueryPort;
import co.cadshare.modelMgt.publicationCategories.core.PublicationCategory;
import co.cadshare.modelMgt.shared.core.Publication;
import co.cadshare.modelMgt.shared.boundary.PublicationQueryPort;
import co.cadshare.modelMgt.shared.boundary.PurchasersQueryPort;
import co.cadshare.modelMgt.shared.core.Purchaser;
import co.cadshare.shared.core.user.User;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class AssignPublicationsService {

    private final PurchasersQueryPort purchasersQueryPort;
    private final PublicationQueryPort publicationQueryPort;
    private final PublicationCategoryQueryPort publicationCategoryQueryPort;
    private final PublicationCommandPort publicationCommandPort;

    public AssignPublicationsService(PurchasersQueryPort purchasersQueryPort,
                                     PublicationQueryPort publicationQueryPort,
                                     PublicationCategoryQueryPort publicationCategoryQueryPort,
                                     PublicationCommandPort publicationCommandPort) {
        this.purchasersQueryPort = purchasersQueryPort;
        this.publicationQueryPort = publicationQueryPort;
        this.publicationCategoryQueryPort = publicationCategoryQueryPort;
        this.publicationCommandPort = publicationCommandPort;
    }

    @Transactional
    public void assignPublicationsToPurchaser(User currentUser, AssignPublicationsCommand command) {
        Purchaser purchaser = purchasersQueryPort.getPurchaser(command.getPurchaserId());
        if(purchaser.isDealerPlus()) {
            List<Publication> currentlyAssignedPublications = publicationQueryPort.getPublicationsAssignedToPurchaser(command.getPurchaserId());
            List<Integer> publicationsToBeRemoved = new ArrayList<>();
            currentlyAssignedPublications.forEach(pub -> {
                if (!command.getPublications().contains(pub.getId())) {
                    publicationsToBeRemoved.add(pub.getId());
                }
            });
            if (!publicationsToBeRemoved.isEmpty()) {
                List<PublicationCategory> currentCategories = publicationCategoryQueryPort.getAssignedPublicationCategoriesForPurchaser(command.getPurchaserId());
                List<Integer> publicationsToBeUnassigned;
                if (currentCategories != null && !currentCategories.isEmpty()) {
                    List<Integer> currentPublicationCategoriesIds = currentCategories
                            .stream()
                            .map(PublicationCategory::getId)
                            .collect(Collectors.toList());
                    List<Integer> publicationsForPublicationCategories = publicationQueryPort.getPublicationsForPublicationCategories(currentPublicationCategoriesIds);
                    //Check manuals to be removed aren't still assigned via range to the Dealer Plus company
                    List<Integer> toBeUnassigned = new ArrayList<>();
                    publicationsToBeRemoved.forEach(manualId -> {
                        if (!publicationsForPublicationCategories.contains(manualId)) {
                            //If manual is not also assigned via publication add to list to be removed
                            toBeUnassigned.add(manualId);
                        }
                    });
                    publicationsToBeUnassigned = toBeUnassigned;
                } else {
                    publicationsToBeUnassigned = publicationsToBeRemoved;
                }
                //Remove manual list from customers of current dealer subentity
                if (!publicationsToBeUnassigned.isEmpty()) {
                    publicationCommandPort.unassignPublicationsFromDealerPlusCustomers(publicationsToBeUnassigned,
                            command.getPurchaserId());
                }
            }
        }

        publicationCommandPort.unassignPublicationsFromPurchaser(command.getPurchaserId());
        if (!command.getPublications().isEmpty()) {
            publicationCommandPort.assignPublicationsToPurchaser(command.getPurchaserId(),
                    command.getPublications());
        }

    }
}
