package co.cadshare.glue;

import co.cadshare.modelMgt.ranges.adapters.api.web.GetProductRangeListResponseDto;
import io.cucumber.java.en.And;
import io.cucumber.java.en.Then;
import io.cucumber.java.en.When;
import org.springframework.beans.factory.annotation.Autowired;

import static org.junit.Assert.*;

public class ProductRangeStepsIT {

	private final CadshareIT cadshare;

	@Autowired
	public ProductRangeStepsIT(CadshareIT cadshare) {
		this.cadshare = cadshare;
	}


	@Then("I can view all ProductRanges belonging to my Manufacturer")
	public void iCanViewAllProductRangesBelongingToMyManufacturer() {
		GetProductRangeListResponseDto productRanges = cadshare.loggedInUser().getProductRanges();
		assertNotNull(productRanges);
		assertFalse(productRanges.getProductRanges().isEmpty());
	}

	@And("a ProductRange with name {} doesn't exist")
	public void aProductRangeWithNameDoesnTExist(String name) {
		/*
		GetProductRangeListResponseDto publicationCategories = auth.getLoggedInUser().getPublicationCategoriesList();
		boolean found = publicationCategories.getPublicationCategories().stream()
				.anyMatch(pc -> pc.getName().equals(name));
		assertFalse(found);
		 */
	}

	@When("I create a new ProductRange named {}")
	public void iCreateANewProductRangeNamed(String name) {
		//auth.getLoggedInUser().createProductRange(name);
	}

	@Then("a ProductRange with name {} exists")
	public void aNewProductRangeWithNameNowExists(String name) {
		/*
		GetProductRangeListResponseDto publicationCategories = auth.getLoggedInUser().getPublicationCategoriesList();
		boolean found = publicationCategories.getPublicationCategories().stream()
				.anyMatch(pc -> pc.getName().equals(name));
		assertTrue(found);
		 */
	}

	@When("I delete the ProductRange named {}")
	public void iDeleteTheProductRangeNamed(String name) {
		/*
		GetProductRangeListResponseDto publicationCategories = auth.getLoggedInUser().getPublicationCategoriesList();
		Optional<GetProductRangeListItemResponseDto> found = publicationCategories.getPublicationCategories().stream()
				.filter(pc -> pc.getName().equals(name)).findFirst();
		assertTrue(found.isPresent());
		auth.getLoggedInUser().deleteProductRange(found.get().getId().toString());

		 */
	}

	@When("I update the ProductRange named {} to {}")
	public void iUpdateTheProductRangeNamed(String oldName, String newName) {
		/*
		GetProductRangeListResponseDto publicationCategories = auth.getLoggedInUser().getPublicationCategoriesList();
		Optional<GetProductRangeListItemResponseDto> found = publicationCategories.getPublicationCategories().stream()
				.filter(pc -> pc.getName().equals(oldName)).findFirst();
		assertTrue(found.isPresent());
		auth.getLoggedInUser().updateProductRange(newName, found.get().getId().toString());

		 */
	}

	@When("I get the ProductRange named {}")
	public void iGetTheProductRangeNamed(String name) {
		/*
		GetProductRangeListResponseDto publicationCategories = auth.getLoggedInUser().getPublicationCategoriesList();
		Optional<GetProductRangeListItemResponseDto> found = publicationCategories.getPublicationCategories().stream()
				.filter(pc -> pc.getName().equals(name)).findFirst();
		assertTrue(found.isPresent());
		GetProductRangeResponseDto ProductRange = auth.getLoggedInUser().getProductRange(found.get().getId().toString());
		assertNotNull(ProductRange);
		assertEquals(name, ProductRange.getName());

		 */
	}
}
