package co.cadshare.preauth;

import co.cadshare.shared.core.user.User;
import co.cadshare.oauth.services.CadshareUserDetailsService;
import java.io.Serializable;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

import co.cadshare.users.adapters.database.UserDetailsDao;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.oauth2.common.OAuth2AccessToken;
import org.springframework.security.oauth2.provider.OAuth2Authentication;
import org.springframework.security.oauth2.provider.OAuth2Request;
import org.springframework.security.oauth2.provider.token.DefaultTokenServices;
import org.springframework.security.oauth2.provider.token.TokenEnhancer;
import org.springframework.security.web.authentication.preauth.PreAuthenticatedAuthenticationToken;
import org.springframework.stereotype.Service;

@Service
public class CadshareTokenCreator {

    private CadshareUserDetailsService userDetailsService;
    private DefaultTokenServices defaultTokenServices;
    private TokenEnhancer tokenEnhancer;
    private UserDetailsDao userDetailsDao;

    public CadshareTokenCreator(UserDetailsDao userDetailsDao, CadshareUserDetailsService userDetailsService,
        DefaultTokenServices defaultTokenServices, TokenEnhancer tokenEnhancer) {
        this.userDetailsDao = userDetailsDao;
        this.userDetailsService = userDetailsService;
        this.defaultTokenServices = defaultTokenServices;
        this.tokenEnhancer = tokenEnhancer;
    }

    public OAuth2AccessToken createTokenForUsername(String username, int manufacturerId) {

        User user = userDetailsDao.findByEmailAddressAndManufacturer(username, manufacturerId);

        UserDetails userDetails = userDetailsService.loadUserByUsername(String.valueOf(user.getUserId()));

        Map<String, String> requestParameters = new HashMap<>();
        String clientId = "vaultapp";
        Set<String> scope = new HashSet<>();
        scope.add("read");
        scope.add("write");
        Set<String> resourceIds = new HashSet<>();
        Set<String> responseTypes = new HashSet<>();
        responseTypes.add("code");
        Map<String, Serializable> extensionProperties = new HashMap<>();

        OAuth2Request oAuth2Request = new OAuth2Request(requestParameters, clientId,
            userDetails.getAuthorities(), true, scope,
            resourceIds, null, responseTypes, extensionProperties);

        PreAuthenticatedAuthenticationToken authenticationToken = new PreAuthenticatedAuthenticationToken(userDetails, null, userDetails.getAuthorities());
        OAuth2Authentication auth = new OAuth2Authentication(oAuth2Request, authenticationToken);
        OAuth2AccessToken oAuth2AccessToken = defaultTokenServices.createAccessToken(auth);

        return tokenEnhancer.enhance(oAuth2AccessToken, auth);
    }
}
