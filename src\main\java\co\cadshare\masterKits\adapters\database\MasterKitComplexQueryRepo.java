package co.cadshare.masterKits.adapters.database;

import com.querydsl.jpa.impl.JPAQueryFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import javax.persistence.EntityManager;
import java.util.List;

@Repository
public class MasterKitComplexQueryRepo {

    private JPAQueryFactory queryFactory;

    @Autowired
    public MasterKitComplexQueryRepo(EntityManager entityManager) {
        this.queryFactory = new JPAQueryFactory(entityManager);
    }

    public List<MkMasterKitEntity> getMasterKitsForManufacturer(Integer manufacturerId) {
        QMkMasterKitEntity masterKit = QMkMasterKitEntity.mkMasterKitEntity;
        return queryFactory.selectFrom(masterKit)
                .where(masterKit.masterParts.any().masterPartDetail.manufacturer.id.eq(manufacturerId)
                .and(masterKit.deleted.eq(false)))
                .fetch();
    }

}

