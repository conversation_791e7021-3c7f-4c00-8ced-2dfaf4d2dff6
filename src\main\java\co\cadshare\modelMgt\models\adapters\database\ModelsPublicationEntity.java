package co.cadshare.modelMgt.models.adapters.database;

import co.cadshare.shared.adapters.database.ImageEntity;
import co.cadshare.modelMgt.publications.adapters.database.PublicationsDealerEntity;
import co.cadshare.modelMgt.publications.adapters.database.PublicationsModelEntity;
import co.cadshare.modelMgt.publications.core.ManualStatus;
import lombok.Data;

import javax.persistence.*;
import java.util.List;

@Entity
@Table(name="Manual")
@Data
public class ModelsPublicationEntity {
    @Id
    @GeneratedValue
    @Column(name="manualid")
    private Integer id;

    @Column(name="manualname")
    private String name;

    @Column(name="manufacturerid")
    private Integer manufacturerId;

    private boolean archived;

    @ManyToOne
    @JoinColumn(name = "mediaid")
    private ImageEntity coverImage;

    @ManyToMany(cascade = { CascadeType.MERGE, CascadeType.REFRESH })
    @JoinTable(
            name = "manufacturersubentitymanualmap",
            joinColumns = { @JoinColumn(name = "manualid") },
            inverseJoinColumns = { @JoinColumn(name = "manufacturersubentityid") }
    )
    private List<PublicationsDealerEntity> dealers;

    @Enumerated(EnumType.STRING)
    private ManualStatus.Status status;

	@ManyToMany(cascade = { CascadeType.MERGE, CascadeType.REFRESH })
	@JoinTable(
			name = "manual_techdoc_map",
			joinColumns = { @JoinColumn(name = "manualid") },
			inverseJoinColumns = { @JoinColumn(name = "techdocid") }
	)
    private List<PublicationsModelEntity> models;

}


