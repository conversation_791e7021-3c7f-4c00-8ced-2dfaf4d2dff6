package co.cadshare.addresses.adapters.database;

import co.cadshare.addresses.core.ExternalPurchaser;
import co.cadshare.shared.adapters.database.addresses.ExternalAddressEntity;
import co.cadshare.shared.adapters.database.addresses.QExternalAddressEntity;
import com.querydsl.jpa.impl.JPAQueryFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import javax.persistence.EntityManager;

@Repository
public class ExternalAddressComplexQueryRepo {
    private JPAQueryFactory queryFactory;

    @Autowired
    public ExternalAddressComplexQueryRepo(EntityManager entityManager) {
        this.queryFactory = new JPAQueryFactory(entityManager);
    }

    public ExternalAddressEntity getExternalAddress(ExternalPurchaser purchaser, String externalRefId) {
        QExternalAddressEntity address = QExternalAddressEntity.externalAddressEntity;
        return queryFactory.selectFrom(address)
                .where(address.manufacturerId.eq(purchaser.getManufacturer().getManufacturerId())
                .and(address.externalRefId.eq(externalRefId)))
                .fetchOne();
    }

}