<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                   http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.0.xsd
">

    <changeSet author="AndyB" id="1.1-integration-test-data-create-manufacturer-1">
        <sql>
            INSERT INTO public.manufacturer(name, subdomain, createddate, emailsignature,
            logourl, supportemail, phone,
            createdbyuserid, modifieddate, modifiedbyuserid, paid)
            VALUES ('Caterpillar', 'caterpillar.cadshare.com', NOW(), 'Regards, CAT',
            'https://1000logos.net/wp-content/uploads/2016/11/Caterpillar-Logo.png', '<EMAIL>', '01-2345-6789',
            0, NOW(), 0, false);
        </sql>
    </changeSet>

    <changeSet author="AndyB" id="1.1-integration-test-data-create-manufacturer-2">
        <sql>
            INSERT INTO public.manufacturer(name, subdomain, createddate, emailsignature,
            logourl, supportemail, phone,
            createdbyuserid, modifieddate, modifiedbyuserid, paid)
            VALUES ('JCB', 'jcb.cadshare.com', NOW(), 'Regards, JCB',
            'https://1000logos.net/wp-content/uploads/2020/07/JCB-Logo.png', '<EMAIL>', '02-3456-7891',
            0, NOW(), 0, false);
        </sql>
    </changeSet>

    <changeSet author="AndyB" id="1.1-integration-test-data-create-manufacturer-3">
        <sql>
            INSERT INTO public.manufacturer(name, subdomain, createddate, emailsignature,
            logourl, supportemail, phone,
            createdbyuserid, modifieddate, modifiedbyuserid, paid)
            VALUES ('Liebherr', 'liebherr.cadshare.com', NOW(), 'Regards, Liebherr',
            'https://1000logos.net/wp-content/uploads/2021/05/Liebherr-logo.png', '<EMAIL>', '03-4567-8912',
            0, NOW(), 0, false);
        </sql>
    </changeSet>

    <changeSet author="AndyB" id="1.1-integration-test-data-create-manufacturer-4">
        <sql>
            INSERT INTO public.manufacturer(name, subdomain, createddate, emailsignature,
            logourl, supportemail, phone,
            createdbyuserid, modifieddate, modifiedbyuserid, paid)
            VALUES ('Terex', 'terex.cadshare.com', NOW(), 'Regards, Terex',
            'https://1000logos.net/wp-content/uploads/2021/05/Terex-logo-500x281.png', '<EMAIL>', '03-4567-8912',
            0, NOW(), 0, false);
        </sql>
    </changeSet>

</databaseChangeLog>
