package co.cadshare.modelMgt.models.adapters.database;

import co.cadshare.aspects.logging.ServiceLoggingAspect;
import co.cadshare.shared.core.user.User;
import co.cadshare.modelMgt.models.boundary.ModelCommandPort;
import co.cadshare.modelMgt.models.boundary.ModelQueryPort;
import co.cadshare.modelMgt.models.core.Model;
import co.cadshare.persistence.ModelUpdateDao;
import com.flextrade.jfixture.annotations.Fixture;
import com.flextrade.jfixture.rules.FixtureRule;
import org.junit.Before;
import org.junit.Rule;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.MockitoAnnotations;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.*;

@EnableAspectJAutoProxy
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(classes = {ModelDataGateway.class, ServiceLoggingAspect.class})
public class ModelDataGatewayTest {

    @MockBean
    private ModelDao modelDao;
    @MockBean
    private ModelUpdateDao modelUpdateDao;
    @MockBean
    private ModelCommandRepo modelCommandRepo;
    @MockBean
    private ModelComplexQueryRepo modelQueryRepo;
    @Autowired
    ModelCommandPort cmdOut;
    @Autowired
    ModelQueryPort queryOut;
    private User user;
    private ModelCommandEntity modelEntity;
    private ModelCommandEntity modelEntityWithId;
    private ModelCommandEntity errorModelEntity;

    @Fixture
    private Model model;
    @Fixture
    private Model errorModel;

    @Rule
    public FixtureRule fr = FixtureRule.initFixtures();


    @Before
    public void Before() {
        MockitoAnnotations.initMocks(this);
        user = new User() {{
            setUserId(1);
            setFirstName("firstName");
            setLastName("lastName");
        }};
        modelEntity = ModelsModelEntityMapper.Instance.coreToEntity(model);
        errorModelEntity = ModelsModelEntityMapper.Instance.coreToEntity(errorModel);
        modelEntityWithId = ModelsModelEntityMapper.Instance.coreToEntity(model);
        modelEntityWithId.setModelId(Integer.valueOf(1));
    }

    @Test
    public void CreateModelSuccess() {
        when(modelDao.createModel(any(Model.class))).thenReturn(999);
        doNothing().when(modelUpdateDao).create(999);
        Integer result = cmdOut.create(user, model);
        verify(modelUpdateDao, times(1)).create(999);
        assertEquals(999, result);
    }

    @Test(expected = RuntimeException.class)
    public void CreateModelFailureException() throws Exception {
        when(modelDao.createModel(any(Model.class))).thenThrow(new RuntimeException("terrible"));
        cmdOut.create(user, errorModel);
    }

    @Test
    public void UpdateModelSuccess()  throws Exception {
        when(modelCommandRepo.save(modelEntity)).thenReturn(modelEntityWithId);
        doNothing().when(modelUpdateDao).update(model.getModelId());
        cmdOut.update(user, model);
        verify(modelCommandRepo, times(1)).save(argThat(new ModelEntityMatcher(modelEntity)));
        verify(modelUpdateDao, times(1)).update(model.getModelId());
    }

    @Test(expected = RuntimeException.class)
    public void UpdateModelFailureException() throws Exception {
        doThrow(new RuntimeException("terrible")).when(modelCommandRepo).save(any(ModelCommandEntity.class));
        cmdOut.update(user, errorModel);
        verify(modelCommandRepo, times(1)).save(argThat(new ModelEntityMatcher(errorModelEntity)));
    }

    @Test
    public void DeleteModelSuccess()  throws Exception {
        when(modelCommandRepo.save(modelEntity)).thenReturn(modelEntity);;
        cmdOut.delete(user, model);
        verify(modelCommandRepo, times(1)).save(argThat(new ModelEntityMatcher(modelEntity)));
    }

    @Test(expected = RuntimeException.class)
    public void DeleteModelFailureException() throws Exception {
        doThrow(new RuntimeException("terrible")).when(modelCommandRepo).save(any(ModelCommandEntity.class));
        cmdOut.delete(user, errorModel);
        verify(modelCommandRepo, times(1)).save(argThat(new ModelEntityMatcher(errorModelEntity)));
    }


}

