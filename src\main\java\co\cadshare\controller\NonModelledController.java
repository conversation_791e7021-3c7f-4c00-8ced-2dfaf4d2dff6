/*
 * Copyright 2016 Bell.
 */
package co.cadshare.controller;

import co.cadshare.aspects.access.languages.CanUseLanguage;
import co.cadshare.shared.core.Language;

import java.util.List;

import co.cadshare.utils.ManufacturerFinder;
import co.cadshare.utils.ObjectExtension;
import lombok.experimental.ExtensionMethod;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import co.cadshare.domainmodel.nonmodeledparts.NonModelled;
import co.cadshare.shared.core.user.User;
import co.cadshare.services.NonModelledService;
import org.springframework.web.bind.annotation.RequestParam;

@ExtensionMethod(ObjectExtension.class)
@Controller
@RequestMapping("/nonmodelled")
public class NonModelledController {

  private static final Logger logger = LoggerFactory.getLogger(NonModelledController.class);

  private NonModelledService nonModelledService;
  private ManufacturerFinder manufacturerFinder;

  public NonModelledController (NonModelledService nonModelledService,
                                ManufacturerFinder manufacturerFinder) {
    this.nonModelledService = nonModelledService;
    this.manufacturerFinder = manufacturerFinder;
  }
  
  @GetMapping(value = "/{id}")
  @CanUseLanguage
  public HttpEntity<NonModelled> getNonModelled(@AuthenticationPrincipal User currentUser,
                                                @PathVariable int id,
                                                @RequestParam(value = "language", required = false) Language language) throws Exception {

    logger.info("ACCESS: User [{}], getNonModelled, nonModelled id[{}]", currentUser.accessDetails(), id);
    int manufacturerId = manufacturerFinder.getUserManufacturerId(currentUser);
	Integer languageId = language != null ? currentUser.findLanguage(language).getLanguageId() : null;
    NonModelled nonModelled = nonModelledService.getNonModelledById(id, languageId, currentUser.obtainDefaultLanguage(), manufacturerId );
    return new ResponseEntity<>(nonModelled, HttpStatus.OK);
  }

  @GetMapping(value = "/model/{modelId}")
  @CanUseLanguage
  public HttpEntity<List<NonModelled>> getNonModelledForModel(@AuthenticationPrincipal User currentUser,
                                                              @PathVariable int modelId,
                                                              @RequestParam(value = "language", required = false) Language language) throws Exception {

    logger.info("ACCESS: User [{}], getNonModelledForModel, modelId [{}]", currentUser.accessDetails(), modelId);
    int manufacturerId = manufacturerFinder.getUserManufacturerId(currentUser);
	Integer languageId = language != null ? currentUser.findLanguage(language).getLanguageId() : null;
    List<NonModelled> nonModelledParts = nonModelledService.getNonModelledByModelId(modelId, languageId, currentUser.obtainDefaultLanguage(), manufacturerId);
    return new ResponseEntity<>(nonModelledParts, HttpStatus.OK);
  }

  @GetMapping(value = "/part/{partId}")
  @CanUseLanguage
  public HttpEntity<NonModelled> getNonModelledForPart(@AuthenticationPrincipal User currentUser,
                                                       @PathVariable int partId,
                                                       @RequestParam(value = "language", required = false) Language language) throws Exception {

    logger.info("ACCESS: User [{}], getNonModelledForPart, partId [{}]", currentUser.accessDetails(), partId);
    int manufacturerId = manufacturerFinder.getUserManufacturerId(currentUser);
	Integer languageId = language != null ? currentUser.findLanguage(language).getLanguageId() : null;
    NonModelled nonModelledParts = nonModelledService.getNonModelledByPartId(partId, languageId, currentUser.obtainDefaultLanguage(), manufacturerId);
    return new ResponseEntity<>(nonModelledParts, HttpStatus.OK);
  }

  @PostMapping(value = "/model/{modelId}", consumes = "application/json")
  public HttpEntity<Integer> createNonModelled(@AuthenticationPrincipal User currentUser, @PathVariable int modelId, @RequestBody NonModelled nonModelled) {

    logger.info("ACCESS: User [{}], createNonModelled, for model Id [{}] and part Id", currentUser.accessDetails(), modelId, nonModelled.getPartId());
    nonModelled.setCreatedByUserId(currentUser.getUserId());
    int nonModelledId = nonModelledService.createNonModelled(modelId, nonModelled, currentUser.getUserId());
    logger.info("NonModelled with id [{}] created", nonModelledId);
    return new ResponseEntity<>(nonModelledId, HttpStatus.OK);
  }

  @PutMapping(value = "/{id}", consumes = "application/json")
  public HttpEntity<Boolean> editNonModelled(@AuthenticationPrincipal User currentUser, @PathVariable int id, @RequestBody NonModelled nonModelled) {

    logger.info("ACCESS: User [{}], editNonModelled, nonModelled Id [{}]", currentUser.accessDetails(), id);
    nonModelled.setModifiedByUserId(currentUser.getUserId());
    nonModelledService.updateNonModelled(id, nonModelled, currentUser);
    return new ResponseEntity<>(HttpStatus.OK);
  }

  @DeleteMapping(value = "/{id}")
  public HttpEntity<Boolean> deleteNonModelled(@AuthenticationPrincipal User currentUser, @PathVariable int id) {

    logger.info("ACCESS: User [{}], deleteOptionSet, id [{}]", currentUser.accessDetails(), id);
    Boolean isDeleted = nonModelledService.deleteNonModelled(id);
    return new ResponseEntity<>(isDeleted, HttpStatus.OK);
  }

}
