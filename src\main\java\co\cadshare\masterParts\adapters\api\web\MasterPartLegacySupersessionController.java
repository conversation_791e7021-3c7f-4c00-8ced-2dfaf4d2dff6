/*
 * Copyright 2016 Bell.
 */
package co.cadshare.masterParts.adapters.api.web;

import co.cadshare.masterParts.boundary.MasterPartLegacySupersessionService;
import co.cadshare.masterParts.core.extensions.partModelLink.LegacySupersededModelLink;
import co.cadshare.shared.core.user.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

@Slf4j
@Controller
@RequestMapping("/masterPart")
public class MasterPartLegacySupersessionController {

  private final MasterPartLegacySupersessionService legacySupersessionService;

  @Autowired
  public MasterPartLegacySupersessionController(MasterPartLegacySupersessionService legacySupersessionService) {
      this.legacySupersessionService = legacySupersessionService;
  }

  //Legacy Superseded Parts Services
  @RequestMapping(value = "/{masterpartId}/superseded", method = RequestMethod.GET)
  public HttpEntity<LegacySupersededModelLink> getLegacySupersededPartsForPart(@AuthenticationPrincipal User currentUser,
                                                                               @PathVariable int masterpartId) {

    log.info("ACCESS: User [{}], getLegacySupersededPartsForPart, partId [{}]", currentUser.accessDetails(), masterpartId);
    LegacySupersededModelLink superseded = legacySupersessionService.getLegacySupersededPartsForPart(masterpartId);
    return new ResponseEntity<>(superseded, HttpStatus.OK);
  }


  @PostMapping(value = "/{masterpartId}/superseded", consumes = "application/json")
  public HttpEntity<Boolean> createSupersededPart(@AuthenticationPrincipal User currentUser,
                                                  @PathVariable int masterpartId,
                                                  @RequestBody LegacySupersededModelLink modelLink) {

    log.info("ACCESS: User [{}], createSupersededPart, for masterpart Id [{}]", currentUser.accessDetails(), masterpartId);
    modelLink.setMasterPartId(masterpartId);
    Boolean success = legacySupersessionService.createLegacySupersededPart(modelLink);
    return new ResponseEntity<>(success, HttpStatus.OK);
  }

  @PutMapping(value = "/{masterpartId}/superseded", consumes = "application/json")
  public HttpEntity<Boolean> updateSupersededPart(@AuthenticationPrincipal User currentUser,
                                                  @PathVariable int masterpartId,
                                                  @RequestBody LegacySupersededModelLink modelLink) {

    log.info("ACCESS: User [{}], updateSupersededPart", currentUser.accessDetails());
    modelLink.setMasterPartId(masterpartId);
    boolean response = legacySupersessionService.updateLegacySupersededPart(modelLink);
    return new ResponseEntity<>(response, HttpStatus.OK);
  }

  @DeleteMapping(value = "/{masterpartId}/superseded")
  public HttpEntity<Boolean> deleteSupersededPart(@AuthenticationPrincipal User currentUser,
                                                  @PathVariable int masterpartId) {

    log.info("ACCESS: User [{}], deleteSupersededPart, masterpartid [{}]", currentUser.accessDetails(), masterpartId);
    boolean isDeleted = legacySupersessionService.deleteLegacySupersededPart(masterpartId);
    return new ResponseEntity<>(isDeleted, HttpStatus.OK);
  }
}
