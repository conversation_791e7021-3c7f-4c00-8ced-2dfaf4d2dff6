package co.cadshare.addresses.adapters.ext.erp.vis;

import co.cadshare.shared.adapters.ext.erp.vis.VisibilityXmlExtensions;
import com.fasterxml.jackson.core.JsonProcessingException;

public class VisibilityAddressXmlExtensions extends VisibilityXmlExtensions {

    public static GetShipToAddressListResponse deserialise(String xmlResponseBody) throws JsonProcessingException {
        return deserialiseToObject(xmlResponseBody, GetShipToAddressListResponse.class);
    }

    public static String getVisAddressId(String xmlResponseBody) {
        return getElementsByTagName(xmlResponseBody, "n_ID");
    }
}
