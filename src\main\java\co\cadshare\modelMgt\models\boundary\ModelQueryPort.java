package co.cadshare.modelMgt.models.boundary;

import co.cadshare.modelMgt.models.core.Model;
import co.cadshare.modelMgt.models.core.model.AutodeskStatus;
import co.cadshare.shared.boundary.QueryPort;

import java.util.List;

public interface ModelQueryPort extends QueryPort<Model, Integer> {

    List<Model> getModelsListForProduct(Integer id);
    List<Model> getModelsListForProduct(Integer id, AutodeskStatus status, boolean setupComplete);

    List<Model> getPublishedModelsForMasterPart(PublishedModelsForMasterPartSearchCriteria searchCriteria);

}
