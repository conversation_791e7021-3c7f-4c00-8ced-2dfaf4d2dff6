package co.cadshare.modelMgt.publications.adapters.database;

import co.cadshare.persistence.ManufacturerSubEntityDao;
import co.cadshare.persistence.dealer.DealerPlusPublicationDao;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.namedparam.MapSqlParameterSource;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;

import java.util.ArrayList;

import static org.mockito.Mockito.when;
import static org.junit.Assert.assertFalse;

public class ManualDaoTest {

    @Mock
    JdbcTemplate jdbcTemplate;
    @Mock
    NamedParameterJdbcTemplate namedParamJdbcTemplate;
    @Mock
    ManufacturerSubEntityDao manufacturerSubEntityDao;
    @Mock
    DealerPlusPublicationDao dealerManualDao;

    private ArrayList<Integer> subEntityIds = new ArrayList<Integer>(){{
        add(1);
        add(2);
        add(3);
    }};

    private static final String DELETE_STR = "DELETE FROM manufacturersubentitymanualmap WHERE manualid = :manualid AND manufacturersubentityid in (:subEntities)";

    ManualDao out;

    @Before
    public void Before() {
        MockitoAnnotations.initMocks(this);
        out = new ManualDao(jdbcTemplate, namedParamJdbcTemplate, manufacturerSubEntityDao, dealerManualDao);
    }

    @Test
    public void ClearMappingsForManualOk() {
        MapSqlParameterSource params = new MapSqlParameterSource()
                .addValue("manualid", 1)
                .addValue("subEntities", "1,2,3");
        when(namedParamJdbcTemplate.update(DELETE_STR, params)).thenReturn(1);

        out.clearMappingsForManual(1, subEntityIds);
    }

    @Test
    public void ClearMappingsForManualNoSubEntities() {
        boolean result = out.clearMappingsForManual(1, new ArrayList<Integer>());
        assertFalse(result);
    }
}
