package co.cadshare.modelMgt.publications.adapters.database;

import co.cadshare.modelMgt.shared.boundary.PurchasersQueryPort;
import co.cadshare.modelMgt.shared.core.Purchaser;
import org.springframework.stereotype.Service;

@Service
public class PurchaserDataFacade implements PurchasersQueryPort {

	private final PublicationsPurchaserRepo repo;

	public PurchaserDataFacade(PublicationsPurchaserRepo repo) {
		this.repo = repo;
	}

	@Override
	public Purchaser getPurchaser(int purchaserId) {
		PublicationsPurchaserEntity entity = repo.getOne(purchaserId);
		return PublicationsPurchaserEntityMapper.Instance.entityToPurchaserCore(entity);
	}
}
