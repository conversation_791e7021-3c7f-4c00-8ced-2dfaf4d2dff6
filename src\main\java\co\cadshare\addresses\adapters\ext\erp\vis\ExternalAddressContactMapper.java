package co.cadshare.addresses.adapters.ext.erp.vis;

import co.cadshare.addresses.core.ExternalAddressContact;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper(uses={ExternalContactMapper.class})
public interface ExternalAddressContactMapper {

	ExternalAddressContactMapper Instance = Mappers.getMapper(ExternalAddressContactMapper.class);

	@Mapping(source="tradingPartnerAddressContactCompositeKey", target="compositeExternalRefId")
	@Mapping(source=".", target="contact")
	@Mapping(target="id", ignore=true)
	ExternalAddressContact externalToCore(ContactDetail source);

	List<ExternalAddressContact> externalToCores(List<ContactDetail> source);
}
