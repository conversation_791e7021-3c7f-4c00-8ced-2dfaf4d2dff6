package co.cadshare.addresses.core;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.sql.Timestamp;

@Data
public class UserAddressMap {

    private Integer id;
    @ToString.Exclude
    @EqualsAndHashCode.Exclude
    private ExternalAddress address;
    @ToString.Exclude
    private User user;
    private boolean deleted;
    private Timestamp createdDate;
    private Integer createdByUserId;
    private Timestamp modifiedDate;
    private Integer modifiedByUserId;
}

