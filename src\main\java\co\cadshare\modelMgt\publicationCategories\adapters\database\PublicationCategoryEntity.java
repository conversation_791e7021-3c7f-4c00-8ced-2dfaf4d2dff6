package co.cadshare.modelMgt.publicationCategories.adapters.database;

import co.cadshare.modelMgt.publications.adapters.database.PublicationsPurchaserEntity;
import lombok.Data;
import javax.persistence.*;
import java.sql.Timestamp;
import java.util.List;

@Data
@Entity
@Table(name="publicationcategory")
public class PublicationCategoryEntity {

    @Id
    @GeneratedValue (strategy = GenerationType.IDENTITY)
    @Column(name="id")
    private Integer id;

	@Column(name="name")
	private String name;

    @Column(name="manufacturerid")
    private int manufacturerId;

	@ManyToMany
	@JoinTable(name="purchaserpublicationcategorymap",
			joinColumns = { @JoinColumn(name = "publicationcategoryid") },
			inverseJoinColumns = { @JoinColumn(name = "purchaserid") })
	private List<PublicationsPurchaserEntity> purchasers;

    private boolean deleted;

    @Column(name="createdbyuserid")
    private Integer createdByUserId;

    @Column(name="modifiedbyuserid")
    private Integer modifiedByUserId;

    @Column(name="createddate")
    private Timestamp createdDate;

    @Column(name="modifieddate")
    private Timestamp modifiedDate;
}

