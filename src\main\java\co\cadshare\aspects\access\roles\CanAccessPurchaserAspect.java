package co.cadshare.aspects.access.roles;

import co.cadshare.aspects.BaseAspect;
import co.cadshare.exceptions.ForbiddenException;
import co.cadshare.shared.core.manufacturer.subentity.ManufacturerSubEntity;
import co.cadshare.utils.ObjectUtilsExtension;
import lombok.experimental.ExtensionMethod;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.security.oauth2.common.exceptions.UnauthorizedUserException;
import org.springframework.stereotype.Component;

@Aspect
@Component
@ExtensionMethod(ObjectUtilsExtension.class)
public class CanAccessPurchaserAspect extends BaseAspect {


	@Pointcut("@annotation(CanAccessPurchaser)")
	public void serviceLogPointcut(){}

	@Before("serviceLogPointcut()")
	public void checkUser(JoinPoint joinPoint) throws ForbiddenException {
		configureInterception(joinPoint);
		Object arg = getArgumentByName(joinPoint, "purchaserId");
		if (arg != null) {
			int purchaserId = (int)arg;
			if (!user.getManufacturerSubEntityId().equals(purchaserId))
				throw new ForbiddenException("User does not have permission to access this purchaser's data.");
		}
	}
}
