package co.cadshare.modelMgt.publications.adapters.database;

import com.blazebit.persistence.view.EntityView;
import com.blazebit.persistence.view.IdMapping;
import com.blazebit.persistence.view.Mapping;

@EntityView(PublicationsSnapshotEntity.class)
public interface PublicationsSnapshotEntityView {

    @IdMapping
    int getId();

    @Mapping
    String getStateId();

    @Mapping
    String getImgUrl();
}
