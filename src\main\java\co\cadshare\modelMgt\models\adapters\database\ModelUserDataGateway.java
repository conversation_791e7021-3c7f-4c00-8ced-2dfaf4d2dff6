package co.cadshare.modelMgt.models.adapters.database;

import co.cadshare.shared.core.user.User;
import co.cadshare.exceptions.ForbiddenException;
import co.cadshare.modelMgt.models.boundary.UserQueryPort;
import co.cadshare.users.adapters.database.UserDetailsDao;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

@Service
public class ModelUserDataGateway implements UserQueryPort {

    private final UserDetailsDao userDetailsDao;

    public ModelUserDataGateway(UserDetailsDao userDetailsDao) {
        this.userDetailsDao = userDetailsDao;
    }

    @Override
    public User getApiUserForManufacturer(int manufacturerId) {
        List<User> manufacturerUsers = this.userDetailsDao.getUsersForManufacturer(manufacturerId);
        Optional<User> userFound = manufacturerUsers.stream().filter(user -> user.isApiUser()).findFirst();
        if(userFound.isPresent())
            return userFound.get();
        else
            throw new ForbiddenException("No API User exists for this Manufacturer. Please contact CADshare admin.");
    }
}
