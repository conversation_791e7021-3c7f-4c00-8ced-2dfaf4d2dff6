package co.cadshare.modelMgt.publications.adapters.api.web;

import co.cadshare.modelMgt.publications.boundary.AssignPublicationsCommand;
import org.junit.Test;

import java.util.Arrays;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertTrue;

public class AssignPublicationsMapperTest {


	@Test
	public void assignPublicationsMapperTest() {

		PostAssignPublicationsDto dto = new PostAssignPublicationsDto();
		dto.setPublications(Arrays.asList(1, 2, 3));
		dto.setPurchaserId(5);

		AssignPublicationsCommand command = AssignPublicationsMapper.Instance.dtoToCommand(dto);

		assertEquals(dto.getPurchaserId(), command.getPurchaserId().intValue());
		assertEquals(3, command.getPublications().size());
		assertTrue(command.getPublications().contains(1));
		assertTrue(command.getPublications().contains(2));
		assertTrue(command.getPublications().contains(3));
	}
}
