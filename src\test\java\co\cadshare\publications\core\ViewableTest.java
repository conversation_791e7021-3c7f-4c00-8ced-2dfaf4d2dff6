package co.cadshare.publications.core;

import co.cadshare.shared.core.manufacturer.ManufacturerSettings;
import co.cadshare.shared.core.user.User;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.security.core.parameters.P;

import java.util.ArrayList;
import java.util.List;

import static org.junit.Assert.*;
import static org.mockito.Mockito.when;

public class ViewableTest {

    @Mock
    private ManufacturerSettings mockManufacturerSettings;
    @Mock
    private User mockUser;
    private Viewable out;
    List<Snapshot> snapshots;
    Product product;

    @Before
    public void Before() {
        product = new Product();
        buildSnapshots();
        MockitoAnnotations.initMocks(this);
        out = new Viewable();
        out.setProduct(product);
        out.setSnapshots(snapshots);
        when(mockUser.getManufacturerSettings()).thenReturn(mockManufacturerSettings);
    }

    @Test
    public void isReadyForPublication() {
        out.setSetupComplete(true);
        when(mockManufacturerSettings.isAutoPublishable()).thenReturn(true);
        assertTrue(out.isReadyForPublication(mockUser));
    }

    @Test
    public void isNotReadyForPublicationAsManufacturerDoesNotAutoPublish() {
        out.setSetupComplete(true);
        when(mockManufacturerSettings.isAutoPublishable()).thenReturn(false);
        assertFalse(out.isReadyForPublication(mockUser));
    }

    @Test
    public void isNotReadyForPublicationAsNotInitialised() {
        out.setSetupComplete(false);
        when(mockManufacturerSettings.isAutoPublishable()).thenReturn(true);
        assertFalse(out.isReadyForPublication(mockUser));
    }

    @Test
    public void setsThumbnailWhenThereIsNotOne() {
        assertNull(out.getProduct().getThumbnailUrl());
        out.ensureProductHasThumbnail();
        assertNotNull(out.getProduct().getThumbnailUrl());
        assertEquals("imgurl", out.getProduct().getThumbnailUrl());
    }

    @Test
    public void doesNotSetThumbnailWhenThereIsOne() {
        product = new Product();
        product.setThumbnailUrl("thumbnailUrl");
        out.setProduct(product);
        out.ensureProductHasThumbnail();
        assertNotNull(out.getProduct().getThumbnailUrl());
        assertEquals("thumbnailUrl", out.getProduct().getThumbnailUrl());
    }

    private void buildSnapshots() {
        snapshots = new ArrayList<>();
        Snapshot snapshot = new Snapshot();
        snapshot.setStateId("ROOT");
        snapshot.setImgUrl("imgurl");
        snapshots.add(snapshot);
    }
}
