package co.cadshare.modelMgt.publicationCategories.adapters.database;

import co.cadshare.modelMgt.publicationCategories.adapters.database.PublicationCategoryEntity;
import org.mockito.ArgumentMatcher;

public class PublicationCategoryEntityMatcher implements ArgumentMatcher<PublicationCategoryEntity> {

    private PublicationCategoryEntity left;

    public PublicationCategoryEntityMatcher(PublicationCategoryEntity left) {
        this.left = left;
    }

    @Override
    public boolean matches(PublicationCategoryEntity right) {
        boolean returnVal = (left.getId() == null && right.getId() == null) ||
                left.getId() == right.getId();
        return returnVal;
    }
}
