package co.cadshare.modelMgt.publications.adapters.database;

import co.cadshare.modelMgt.publicationCategories.adapters.database.PublicationCategoryEntity;
import co.cadshare.modelMgt.publications.core.ManualStatus;
import co.cadshare.shared.adapters.database.ImageEntity;
import lombok.Data;
import lombok.ToString;

import javax.persistence.*;
import java.util.Date;
import java.util.List;
import java.util.Set;

@Entity
@Table(name="Manual")
@Data
public class PublicationsPublicationEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name="manualid")
    private Integer id;

    @Column(name="manualname")
    private String name;

	@Column
	private String serialNumber;

    @Column(name="manufacturerid")
    private Integer manufacturerId;

	@Column(name="featuredmodelid")
	private Integer featuredViewableId;

	@ManyToOne
	@JoinColumn(name="publicationcategoryid")
	private PublicationCategoryEntity publicationCategory;

	@Column(name="archived")
    private boolean deleted;

    @ManyToOne
    @JoinColumn(name = "coverimageid")
    private ImageEntity coverImage;

	@ManyToOne
	@JoinColumn(name = "featuredviewableimageid")
	private ImageEntity featuredViewableImage;

	@ManyToMany(cascade = {CascadeType.MERGE, CascadeType.REFRESH})
    @JoinTable(
            name = "manufacturersubentitymanualmap",
            joinColumns = { @JoinColumn(name = "manualid") },
            inverseJoinColumns = { @JoinColumn(name = "manufacturersubentityid") }
    )
    private Set<PublicationsPurchaserEntity> purchasers;

    @Enumerated(EnumType.STRING)
    private ManualStatus.Status status;

    @ToString.Exclude
    @ManyToMany(cascade = { CascadeType.REFRESH })
    @JoinTable(
		    name = "manualmodelmap",
		    joinColumns = { @JoinColumn(name = "manualid") },
		    inverseJoinColumns = { @JoinColumn(name = "modelid") }
    )
    private Set<PublicationsModelEntity> models;

	@ManyToMany(cascade = { CascadeType.REFRESH })
	@JoinTable(
			name = "manual_techdoc_map",
			joinColumns = { @JoinColumn(name = "manualid") },
			inverseJoinColumns = { @JoinColumn(name = "techdocid") }
	)
	private Set<PublicationsTechDocEntity> techDocs;

	@ManyToMany(cascade = { CascadeType.REFRESH })
	@JoinTable(
			name = "manual_video_map",
			joinColumns = { @JoinColumn(name = "manualid") },
			inverseJoinColumns = { @JoinColumn(name = "videoid") }
	)
	private Set<PublicationsVideoEntity> videos;

	@ManyToMany(cascade = { CascadeType.REFRESH })
	@JoinTable(
			name = "manual_kit_map",
			joinColumns = { @JoinColumn(name = "manualid") },
			inverseJoinColumns = { @JoinColumn(name = "kitid") }
	)
	private Set<PublicationsKitEntity> kits;

	@Column(name="createddate")
	private Date createdDate;

	@Column(name="createdbyuserid")
	private int createdByUserId;

	@Column(name="modifieddate")
	private Date modifiedDate;

	@Column(name="modifiedbyuserid")
	private int modifiedByUserId;
}


