package co.cadshare.modelMgt.publicationCategories.boundary;

import co.cadshare.modelMgt.publicationCategories.core.PublicationCategory;
import co.cadshare.shared.boundary.QueryPort;

import java.util.List;

public interface PublicationCategoryQueryPort extends QueryPort<PublicationCategory, Integer> {

    List<PublicationCategory> getListForManufacturer(Integer manufacturerId);

	List<PublicationCategory> getAssignedPublicationCategoriesForPurchaser(int purchaserId);

}
