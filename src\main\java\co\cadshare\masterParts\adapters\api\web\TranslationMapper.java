package co.cadshare.masterParts.adapters.api.web;

import co.cadshare.masterParts.core.Translation;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

@Mapper
public interface TranslationMapper {
    TranslationMapper Instance = Mappers.getMapper(TranslationMapper.class);

    @Mapping(source="languageCode", target="code")
    LanguageDescriptionDto coreToDto(Translation core);
}
