package co.cadshare.publications.adapters.database;

import co.cadshare.shared.adapters.database.BasePurchaserEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DiscriminatorOptions;

import javax.persistence.*;

@EqualsAndHashCode(callSuper = true)
@Data
@Entity
@Table(name="manufacturersubentity")
@Inheritance(strategy = InheritanceType.SINGLE_TABLE)
@DiscriminatorColumn(name="manufacturersubentitytypeid", discriminatorType = DiscriminatorType.INTEGER)
@DiscriminatorOptions(force = true)  //this is due to a bug in Hibernate 5.2.x versions - once upgraded, this can be removed
public abstract class PublicationsPurchaserEntity extends BasePurchaserEntity { }
