package co.cadshare.modelMgt.publications.adapters.database;

import co.cadshare.modelMgt.viewables.adapters.database.ViewableEntityViewMapper;
import co.cadshare.modelMgt.shared.core.Purchaser;
import org.mapstruct.Mapper;

@Mapper
public interface PublicationsPurchaserEntityViewMapper {

    ViewableEntityViewMapper mapper = ViewableEntityViewMapper.Instance;

    Purchaser entityToCore(PublicationsPurchaserEntityView entityView);
}
