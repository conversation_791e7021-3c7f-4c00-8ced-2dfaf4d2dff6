package co.cadshare.publications.adapters.api.web.manufacturers.publications;

import co.cadshare.publications.adapters.api.web.manufacturers.publicationsCoverImage.CoverImageMapper;
import co.cadshare.publications.core.Publication;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper(uses = {CoverImageMapper.class, CustomerMapper.class, PublicationStatusMapper.class, ViewableMapper.class})
public interface PublicationMapper {

    public final PublicationMapper Instance = Mappers.getMapper(PublicationMapper.class);

    @Mapping(source = "publicationName", target = "name")
    Publication postRequestDtoToPublication(PostPublicationRequestDto postRequestDto);

    @Mapping(source = "id", target = "publicationId")
    @Mapping(source = "name", target = "publicationName")
    @Mapping(source = "coverImage.locationUrl", target = "coverImage")
    @Mapping(source = "dealers", target = "customers")

    GetPublicationResponseDto publicationToGetResponseDto(Publication publication);

    @Mapping(source = "coverImage.locationUrl", target = "coverImage")
    @Mapping(source = "dealers", target = "customers")
    @Mapping(source = "models", target = "viewables")
    PublicationListItemDto publicationToPublicationListItemDto(Publication publication);

    List<PublicationListItemDto> publicationsToPublicationListItemDtos(List<Publication> publications);

}
