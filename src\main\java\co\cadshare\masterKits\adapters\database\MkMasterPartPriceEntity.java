package co.cadshare.masterKits.adapters.database;

import lombok.Data;

import javax.persistence.*;

@Data
@Entity
@Table(name="masterpartprice")
public class MkMasterPartPriceEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name="id")
    private int id;

    private Float price;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "priceid")
    private MkPriceListIdentifierEntity priceListIdentifier;

}
