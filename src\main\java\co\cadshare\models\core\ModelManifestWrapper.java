package co.cadshare.models.core;

import co.cadshare.models.boundary.UploadModelService;
import co.cadshare.models.core.model.AutodeskStatus;
import com.autodesk.client.model.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;

import static co.cadshare.models.core.model.AutodeskStatus.*;

public class ModelManifestWrapper {

    private static final Logger logger = LoggerFactory.getLogger(UploadModelService.class);
    private final Manifest manifest;

    public ModelManifestWrapper(Manifest manifest) {
        this.manifest = manifest;
    }

    public boolean isNotNull() {
        boolean manifestIsNull = manifest == null;
        if (manifestIsNull) logger.error("Manifest returned from autodesk was null");
        return !manifestIsNull;
    }

    public boolean statusOrProgressHasChanged(Model model) {
        logger.info("Manifest retrieved from autodesk for model [{}]. autodesk status [{}], autodesk progress [{}]", model.getModelId(), manifest.getStatus(), manifest.getProgress());
        AutodeskStatus latestModelStatus = getAutodeskUploadStatus();
        if (!latestModelStatus.equals(model.getAutodeskStatus()) ||
                !manifest.getProgress().equals(model.getAutodeskProgress()) ||
                latestModelStatus.equals(UPLOADED)) {
            logger.debug("Manifest status or progress for model [{}], has changed on autodesk. Updating model.", model.getModelId());
            model.setAutodeskStatus(latestModelStatus);
            model.setAutodeskProgress(manifest.getProgress());
            if (manifest.getDerivatives() != null && !manifest.getDerivatives().isEmpty() && manifest.getDerivatives().get(0).getChildren() != null && !manifest.getDerivatives().get(0).getChildren().isEmpty()) {
                model.setIs2d(manifest.getDerivatives().get(0).getChildren().get(0).getRole().equals(ManifestChildren.RoleEnum._2D));
                if (model.partsUpdateNotRequired())
                    model.setAutodeskStatus(PROPERTIES_PROCESSED);
            }
            return true;
        } else {
            logger.info("Manifest for model with id [{}] and urn [{}] has not changed",
                    model.getModelId(),
                    model.getAutodeskUrn());
            return false;
        }
    }

    public boolean hasWarnings(){
        return !manifest.getDerivatives().get(0).getChildren().get(0).getMessages().isEmpty();
    }

    public Messages getWarnings(){
        return manifest.getDerivatives().get(0).getChildren().get(0).getMessages();
    }

    public String getEncodedPropertiesDbUrn() {
        String urn = manifest.getDerivatives().get(0).getChildren()
                .stream()
                .filter(child -> child.getType().equals(ManifestChildren.TypeEnum.RESOURCE) &&
                        child.getRole().equals(ManifestChildren.RoleEnum.PROPERTYDB))
                .findFirst()
                .map(ManifestChildren::getUrn)
                .orElse(null);
        if (urn == null) return null;
            return urn;
    }

    private AutodeskStatus getAutodeskUploadStatus() {
        String autodeskStatus = this.manifest.getStatus().toUpperCase();
        logger.info("Model status is {}", autodeskStatus);
        // Even if the overall model has failed to translate (this can be because
        // some files are missing),
        // it is still possible that the model is actually viewable. To see if this
        // is the case we check the top
        // level assembly and see if it was successfully translated and if its type
        // is SVF
        if (autodeskStatus.equals(FAILED.name()) && hasTopLevelAssemblySucceeded()) {
            logger.info("Overall status is failed however the top level assembly succeeded and is therefore viewable. Setting autodesk status to UPLOADED");
            return UPLOADED;
        } else {
            // TODO really should have a map from autodesk statuses to our own.
            return autodeskStatus.equals("SUCCESS") ? UPLOADED : valueOf(autodeskStatus);
        }
    }


    private boolean hasTopLevelAssemblySucceeded() {
        return (manifest.getDerivatives() != null &&
                !manifest.getDerivatives().isEmpty() &&
                manifest.getDerivatives().get(0) != null &&
                manifest.getDerivatives().get(0).getStatus() != null &&
                manifest.getDerivatives().get(0).getStatus().equals(ManifestDerivative.StatusEnum.SUCCESS) &&
                (manifest.getDerivatives().get(0).getOutputType().equals(ManifestDerivative.OutputTypeEnum.SVF) ||
                 manifest.getDerivatives().get(0).getOutputType().equals(ManifestDerivative.OutputTypeEnum.SVF2)));
    }
}
