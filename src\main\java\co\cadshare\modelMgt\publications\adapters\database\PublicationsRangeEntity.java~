package co.cadshare.publications.adapters.database;

import lombok.Data;
import javax.persistence.*;
import java.sql.Timestamp;
import java.util.List;

@Data
@Entity
@Table(name="Range")
public class PublicationsRangeEntity {

    @Id
    @GeneratedValue (strategy = GenerationType.IDENTITY)
    @Column(name="rangeid")
    private Integer id;

    private String name;

    @Column(name="manufacturerid")
    private int manufacturerId;

    @ManyToMany(cascade = { CascadeType.PERSIST })
    @JoinTable(
            name = "manufacturersubentityrangemap",
            joinColumns = { @JoinColumn(name = "rangeid") },
            inverseJoinColumns = { @JoinColumn(name = "manufacturersubentityid") }
    )
    private List<PublicationsPurchaserEntity> assignedPurchasers;
    
    private boolean deleted;

    @Column(name="createdbyuserid")
    private Integer createdByUserId;

    @Column(name="modifiedbyuserid")
    private Integer modifiedByUserId;

    @Column(name="createddate")
    private Timestamp createdDate;

    @Column(name="modifieddate")
    private Timestamp modifiedDate;
}

