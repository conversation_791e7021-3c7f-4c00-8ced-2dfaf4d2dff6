package co.cadshare.modelMgt.models.core.processor;

import co.cadshare.modelMgt.models.core.MetadataDataExtended;
import co.cadshare.modelMgt.models.core.MetadataObjectExtended;
import co.cadshare.modelMgt.models.core.MetadataWrapper;
import co.cadshare.modelMgt.models.core.processor.fileproperties.*;
import co.cadshare.shared.core.manufacturer.ManufacturerConfig;
import co.cadshare.utils.ObjectListExtension;
import co.cadshare.utils.ObjectUtilsExtension;
import com.autodesk.client.model.MetadataCollection;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.experimental.ExtensionMethod;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

@Component
@ExtensionMethod({ObjectUtilsExtension.class, ObjectListExtension.class})
public class PropertiesProcessor implements InitializingBean {

  private PartNumberPropertyProcessor partNumberPropertyProcessor;
  private final DescriptionPropertyProcessor descriptionPropertyProcessor;
  private final WeightPropertyProcessor weightPropertyProcessor;
  private final MassUnitPropertyProcessor massUnitPropertyProcessor;
  private final AlternatePartNumberPropertyProcessor alternatePartNumberPropertyProcessor;
  private SparePartPropertyProcessor sparePartPropertyProcessor;
  private final ObjectMapper mapper = new ObjectMapper();
  private final List<FilePropertiesProcessor> filePropertiesProcessors = new ArrayList<>();
  private final String defaultNode;
  private final String rootNode;

  @Autowired
  public PropertiesProcessor(PartNumberPropertyProcessor partNumberPropertyProcessor,
                             DescriptionPropertyProcessor descriptionPropertyProcessor,
                             WeightPropertyProcessor weightPropertyProcessor,
                             MassUnitPropertyProcessor massUnitPropertyProcessor,
                             AlternatePartNumberPropertyProcessor alternatePartNumberPropertyProcessor,
                             @Value("${properties.processor.default.node}") String defaultNode,
                             @Value("${properties.processor.root.node}") String rootNode) {
    this.partNumberPropertyProcessor = partNumberPropertyProcessor;
    this.descriptionPropertyProcessor = descriptionPropertyProcessor;
    this.weightPropertyProcessor = weightPropertyProcessor;
    this.massUnitPropertyProcessor = massUnitPropertyProcessor;
    this.alternatePartNumberPropertyProcessor = alternatePartNumberPropertyProcessor;
    this.defaultNode = defaultNode;
    this.rootNode = rootNode;
  }

  public MetadataDataExtended getCombinedProperties(MetadataWrapper propertiesForGuid,
                                                    MetadataWrapper metadataForGuid,
                                                    ManufacturerConfig config) throws IOException {

    MetadataDataExtended extended = mapper.readValue(mapper.writeValueAsString(metadataForGuid.getMetadata().getData()),
            MetadataDataExtended.class);

    if (config.exists()) {
      this.partNumberPropertyProcessor = config.getPartNumberProcessorStrategy().createProcessor();
      if (config.getPartnumberSynonyms().exists()) {
        partNumberPropertyProcessor.setSynonyms(Arrays.stream(config.getPartnumberSynonyms().split(","))
                .map(String::trim).collect(Collectors.toList()));
      }
      if (config.getAlternatePartNumberSynonyms().exists()) {
        alternatePartNumberPropertyProcessor.setSynonyms(Arrays.stream(config.getAlternatePartNumberSynonyms().split(","))
                .map(String::trim).collect(Collectors.toList()));
      }
      if (config.getDescriptionSynonyms().exists()) {
        descriptionPropertyProcessor.setSynonyms(Arrays.asList(config.getDescriptionSynonyms().split(",")));
      }
      if (config.getWeightSynonyms().exists()) {
        weightPropertyProcessor.setSynonyms(Arrays.stream(config.getWeightSynonyms().split(","))
                .map(String::trim).collect(Collectors.toList()));
      }
      if (config.getMassUnitSynonyms().exists()) {
        massUnitPropertyProcessor.setSynonyms(Arrays.stream(config.getMassUnitSynonyms().split(","))
                .map(String::trim).collect(Collectors.toList()));
      }
      sparePartPropertyProcessor = config.getSparePartProcessorStrategy().createProcessor();
      if (config.getSparePartIdentifierSynonyms().exists()) {
        sparePartPropertyProcessor.setSynonyms(Arrays.stream(config.getSparePartIdentifierSynonyms().split(","))
                .map(String::trim).collect(Collectors.toList()));
      }

      populateMetadataTree(extended.getObjects(),
              propertiesForGuid.getMetadata().getData().getCollection(),
              -1,
              config);
    }
    return extended;
  }

  private void populateMetadataTree(List<MetadataObjectExtended> metadata,
                                    List<MetadataCollection> propertiesList,
                                    Integer parentId,
                                    ManufacturerConfig config) {

    for (MetadataObjectExtended metadataObject : metadata) {
      metadataObject.setParentObjectId(parentId);
      Optional<MetadataCollection> propertyMatch = propertiesList
              .stream()
              .filter(property -> property.getObjectid().equals(metadataObject.getObjectid()))
              .findFirst();

      if (propertyMatch.isPresent()) {
        MetadataCollection property = propertyMatch.get();
        LinkedHashMap defaultPropertiesMap = getPropertiesMap(property, defaultNode);
        if (defaultPropertiesMap != null)
          filePropertiesProcessors.forEach(processor -> processor.setProperties(defaultPropertiesMap, metadataObject));

        LinkedHashMap partNumberPropertiesMap = getPropertiesMap(property, config.getPartNumberNode());
        if(partNumberPropertiesMap != null)
          partNumberPropertyProcessor.setProperties(partNumberPropertiesMap, metadataObject);

        if(config.getAlternatePartNumberNode() != null) {
          LinkedHashMap alternatePartNumberPropertiesMap = getPropertiesMap(property, config.getAlternatePartNumberNode());
          if(alternatePartNumberPropertiesMap != null)
            alternatePartNumberPropertyProcessor.setProperties(alternatePartNumberPropertiesMap, metadataObject);
        }

        if(config.getSparePartIdentifierNode() != null) {
          LinkedHashMap sparePartIdentifierPropertiesMap = getPropertiesMap(property, config.getSparePartIdentifierNode());
          if(sparePartIdentifierPropertiesMap != null)
            sparePartPropertyProcessor.setProperties(sparePartIdentifierPropertiesMap, metadataObject);
        }
      }

      if (metadataObject.getObjects().existsAndHasItems())
        populateMetadataTree(metadataObject.getObjects(), propertiesList, metadataObject.getObjectid(), config);
    }
  }

  private LinkedHashMap getPropertiesMap(MetadataCollection metadataCollection, String propertiesNode) {
    if (metadataCollection.getProperties().isNotNull()
            && metadataCollection.getProperties() instanceof LinkedHashMap && propertiesNode.isNotNull()) {
      if(propertiesNode.equals(rootNode)) {
        return (LinkedHashMap) metadataCollection.getProperties();
      }
      else if (((LinkedHashMap) metadataCollection.getProperties()).get(propertiesNode).isNotNull()
        && ((LinkedHashMap) metadataCollection.getProperties()).get(propertiesNode) instanceof LinkedHashMap){
          return (LinkedHashMap) ((LinkedHashMap) metadataCollection.getProperties()).get(propertiesNode);
        }
      else
        return null;
    }
    return null;
  }

  public List<MetadataObjectExtended> toFlatList(List<MetadataObjectExtended> metadata) {
    List<MetadataObjectExtended> flatList = new ArrayList<>();

    flattenList(metadata, flatList);

    return flatList;
  }

  private void flattenList(List<MetadataObjectExtended> metadata, List<MetadataObjectExtended> flatList) {

    for (MetadataObjectExtended object : metadata) {
      if (object.getObjects().existsAndHasItems()) {
        flatList.add(object);
        flattenList(object.getObjects(), flatList);
      }
    }
  }

  @Override
  public void afterPropertiesSet() throws Exception {
    filePropertiesProcessors.add(descriptionPropertyProcessor);
    filePropertiesProcessors.add(weightPropertyProcessor);
    filePropertiesProcessors.add(massUnitPropertyProcessor);
  }
}
