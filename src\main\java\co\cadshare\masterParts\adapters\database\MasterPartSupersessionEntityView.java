package co.cadshare.masterParts.adapters.database;

import co.cadshare.shared.adapters.database.MasterPartEntity;
import com.blazebit.persistence.view.EntityView;
import com.blazebit.persistence.view.IdMapping;
import com.blazebit.persistence.view.Mapping;

import java.util.List;

@EntityView(MasterPartEntity.class)
public interface MasterPartSupersessionEntityView {
    @IdMapping
    Integer getId();

    @Mapping
    String getPartNumber();

    @Mapping
    List<MasterPartTranslationEntityView> getTranslations();

    @Mapping
    String getSupersessionPartNumber();

}
