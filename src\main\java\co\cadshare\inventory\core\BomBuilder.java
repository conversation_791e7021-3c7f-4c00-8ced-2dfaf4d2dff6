package co.cadshare.inventory.core;

import co.cadshare.domainmodel.part.Part;
import co.cadshare.utils.ObjectUtilsExtension;
import lombok.experimental.ExtensionMethod;

import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

@ExtensionMethod(ObjectUtilsExtension.class)
public class BomBuilder {

    public static String buildFromParts(List<Part> parts) {
        StringBuilder csvBuilder = new StringBuilder();

        if(parts == null || parts.isEmpty())
            return null;

        List<Part> sortedParts = parts.stream()
                .filter(part -> part.getObjectId().isNotNull()) //ignore parts without an objectId (additional parts or option sets)
                .sorted(Comparator.comparingInt(Part::getObjectId))
                .collect(Collectors.toList());

        csvBuilder.append("ObjectId, FileName, PartNumber, PartDescription\n");

        for (Part part : sortedParts) {

            String description = part.getPartDescription() != null ? part.getPartDescription() : "";
            StringBuilder rowString = new StringBuilder();
            rowString.append(part.getObjectId());
            rowString.append(",\"");
            rowString.append(part.getFileName());
            rowString.append("\",\"");
            rowString.append(part.getPartNumber());
            rowString.append("\",\"");
            rowString.append(description);
            rowString.append("\"");
            csvBuilder.append(rowString).append("\n");
        }
        return csvBuilder.toString();
    }
}
