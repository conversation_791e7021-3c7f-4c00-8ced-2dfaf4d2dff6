/*
 * Copyright 2016 Bell.
 */
package co.cadshare.shared.adapters.aws.s3;

import co.cadshare.shared.core.user.User;
import co.cadshare.users.core.Manufacturer;
import co.cadshare.utils.ObjectUtilsExtension;
import com.amazonaws.AmazonServiceException;
import com.amazonaws.HttpMethod;
import com.amazonaws.SdkClientException;
import com.amazonaws.internal.StaticCredentialsProvider;
import com.amazonaws.services.apigateway.model.NotFoundException;
import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.AmazonS3Client;
import com.amazonaws.services.s3.model.DeleteObjectRequest;
import com.amazonaws.services.s3.model.GeneratePresignedUrlRequest;
import com.amazonaws.services.s3.model.ObjectMetadata;
import com.amazonaws.services.s3.model.PutObjectResult;
import lombok.experimental.ExtensionMethod;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.InputStream;
import java.net.URL;
import java.sql.Timestamp;
import java.util.Calendar;
import java.util.Date;

/**
 * TODO(dallanmc) Description of class.
 */
@Component
@ExtensionMethod(ObjectUtilsExtension.class)
public class S3StorageClient {

    @Autowired
    private StaticCredentialsProvider staticCredentialsProvider;

    @Value("${aws.bucketName}")
    private String bucketName;

    private final Logger log = LoggerFactory.getLogger(getClass());

    public String getBucketName() {
        return bucketName;
    }

    public URL getFileURLForManufacturerUpload(String objectKey, Integer manufacturerId) {
        log.debug("Attempting to generate PUT URL for (getFileURLForManufacturerUpload) filename [{}] bucketName [{}]", objectKey, bucketName);
        AmazonS3 s3Client = new AmazonS3Client(staticCredentialsProvider);

        try {
            if (manufacturerId == null || manufacturerId == 0) {
                throw new Exception("No manufacturer id found in token");
            }

            String bucketFolderUrl = "/manufacturer/" + manufacturerId;

            GeneratePresignedUrlRequest generatePresignedUrlRequest =
                    new GeneratePresignedUrlRequest(bucketName + bucketFolderUrl, objectKey);
            buildPresignedURLRequest(generatePresignedUrlRequest, "image/png");

            return s3Client.generatePresignedUrl(generatePresignedUrlRequest);
        }
        catch (Exception e)
        {
            log.error("Unable to generate PUT URL for (getFileURLForManufacturerUpload) filename [{}] in bucket [{}]. Error [{}]", objectKey, bucketName, e.getMessage());
            throw new NotFoundException("Unable to generate amazon s3 link for given input");
        }
    }

    public URL getFileURLForMachineUpload(String objectKey, Integer manufacturerId) {
        log.debug("Attempting to generate PUT URL for (getFileURLForMachineUpload) filename [{}] bucketName [{}]", objectKey, bucketName);
        AmazonS3 s3Client = new AmazonS3Client(staticCredentialsProvider);

        try {
            if (manufacturerId == null || manufacturerId == 0) {
                throw new Exception("No manufacturer id found in token");
            }

            String bucketFolderUrl = "/manufacturer/" + manufacturerId + "/machine";

            GeneratePresignedUrlRequest generatePresignedUrlRequest =
                    new GeneratePresignedUrlRequest(bucketName + bucketFolderUrl, objectKey);
            buildPresignedURLRequest(generatePresignedUrlRequest, "image/png");

            return s3Client.generatePresignedUrl(generatePresignedUrlRequest);
        }
        catch (Exception e)
        {
            log.error("Unable to generate PUT URL for (getFileURLForMachineUpload) filename [{}] in bucket [{}]. Error [{}]", objectKey, bucketName, e.getMessage());
            throw new NotFoundException("Unable to generate amazon s3 link for given input");
        }
    }

    public URL getFileURLForModelUpload(String objectKey, Integer manufacturerId, int machineId, int modelId) {
        log.debug("Attempting to generate PUT URL for (getFileURLForModelUpload) filename [{}] bucketName [{}]", objectKey, bucketName);
        AmazonS3 s3Client = new AmazonS3Client(staticCredentialsProvider);

        try {
            if (manufacturerId == null || manufacturerId == 0) {
                throw new Exception("No manufacturer id found in token");
            }

            String bucketFolderUrl = "/manufacturer/" + manufacturerId + "/machine/" + machineId + "/model" + modelId;

            GeneratePresignedUrlRequest generatePresignedUrlRequest =
                    new GeneratePresignedUrlRequest(bucketName + bucketFolderUrl, objectKey);
            buildPresignedURLRequest(generatePresignedUrlRequest, "image/png");

            return s3Client.generatePresignedUrl(generatePresignedUrlRequest);
        }
        catch (Exception e)
        {
            log.error("Unable to generate PUT URL for (getFileURLForModelUpload) filename [{}] in bucket [{}]. Error [{}]", objectKey, bucketName, e.getMessage());
            throw new NotFoundException("Unable to generate amazon s3 link for given input");
        }
    }

    public URL getFileURLForOrderInvoiceUpload(String objectKey, Integer manufacturerId) {
        log.debug("Attempting to generate PUT URL for (getFileURLForOrderInvoiceUpload) filename [{}] bucketName [{}]", objectKey, bucketName);
        AmazonS3 s3Client = new AmazonS3Client(staticCredentialsProvider);

        try {
            if (manufacturerId == null || manufacturerId == 0) {
                throw new Exception("No manufacturer id found in token");
            }

            String bucketFolderUrl = "/manufacturer/" + manufacturerId + "/order";

            GeneratePresignedUrlRequest generatePresignedUrlRequest =
                    new GeneratePresignedUrlRequest(bucketName + bucketFolderUrl, objectKey);
            buildPresignedURLRequest(generatePresignedUrlRequest, "application/pdf");

            return s3Client.generatePresignedUrl(generatePresignedUrlRequest);
        }
        catch (Exception e)
        {
            log.error("Unable to generate PUT URL for (getFileURLForOrderInvoiceUpload) filename [{}] in bucket [{}]. Error [{}]", objectKey, bucketName, e.getMessage());
            throw new NotFoundException("Unable to generate amazon s3 link for given input");
        }
    }

    public URL getDealerFileURLForOrderInvoiceUpload(String objectKey, User dealerUser) {
        log.debug("Attempting to generate PUT URL for (getDealerFileURLForOrderInvoiceUpload) filename [{}] bucketName [{}]", objectKey, bucketName);
        AmazonS3 s3Client = new AmazonS3Client(staticCredentialsProvider);

        try {
            if (dealerUser.getAssociatedManufacturerId() == null || dealerUser.getAssociatedManufacturerId() == 0) {
                throw new Exception("No manufacturer id found in token");
            } else if (dealerUser.getManufacturerSubEntityId() == null || dealerUser.getManufacturerSubEntityId() == 0) {
                throw new Exception("No dealer entity id found in token");
            }

            String bucketFolderUrl = "/manufacturer/" + dealerUser.getAssociatedManufacturerId() + "/dealerPlus/" + dealerUser.getManufacturerSubEntityId() + "/order";

            GeneratePresignedUrlRequest generatePresignedUrlRequest =
                    new GeneratePresignedUrlRequest(bucketName + bucketFolderUrl, objectKey);
            buildPresignedURLRequest(generatePresignedUrlRequest, "application/pdf");

            return s3Client.generatePresignedUrl(generatePresignedUrlRequest);
        }
        catch (Exception e)
        {
            log.error("Unable to generate PUT URL for (getDealerFileURLForOrderInvoiceUpload) filename [{}] in bucket [{}]. Error [{}]", objectKey, bucketName, e.getMessage());
            throw new NotFoundException("Unable to generate amazon s3 link for given input");
        }
    }

    public URL getFileURLForTechDocUpload(String objectKey, String fileType, Integer manufacturerId) {
        log.debug("Attempting to generate PUT URL for (getFileURLForTechDocUpload) filename [{}] bucketName [{}]", objectKey, bucketName);
        AmazonS3 s3Client = new AmazonS3Client(staticCredentialsProvider);

        try {
            if (manufacturerId == null || manufacturerId == 0) {
                throw new Exception("No manufacturer id found in token");
            }

            String bucketFolderUrl = "/manufacturer/" + manufacturerId + "/techDoc";

            GeneratePresignedUrlRequest generatePresignedUrlRequest =
                    new GeneratePresignedUrlRequest(bucketName + bucketFolderUrl, objectKey);

            // Set the content type based on the file type
            String contentType;
            if ("excel".equalsIgnoreCase(fileType) || "xlsx".equalsIgnoreCase(fileType)) {
                contentType = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
            } else if ("pdf".equalsIgnoreCase(fileType)) {
                contentType = "application/pdf";
            } else {
                throw new IllegalArgumentException("Unsupported file type: " + fileType);
            }

            // Log the ContentType being used
            log.debug("Generating presigned URL with ContentType: {}", contentType);

            buildPresignedURLRequest(generatePresignedUrlRequest, contentType);

            return s3Client.generatePresignedUrl(generatePresignedUrlRequest);
        }
        catch (Exception e) {
            log.error("Unable to generate PUT URL for (getFileURLForTechDocUpload) filename [{}] in bucket [{}]. Error [{}]", objectKey, bucketName, e.getMessage());
            throw new NotFoundException("Unable to generate amazon s3 link for given input");
        }
    }

    public String uploadCSVForMasterPartLanguage(String languageCSV, Integer manufacturerId) {
        log.debug("Attempting to upload for (uploadCSVForMasterPartLanguage) bucketName [{}]", bucketName);
        AmazonS3 s3Client = new AmazonS3Client(staticCredentialsProvider);

        try {
            if (manufacturerId == null || manufacturerId == 0) {
                throw new Exception("No manufacturer id found in token");
            }

            String bucketFolderUrl = "/manufacturer/" + manufacturerId + "/progress/masterpart/language";
            String bucket = bucketName + bucketFolderUrl;

            Timestamp now = new java.sql.Timestamp(Calendar.getInstance().getTime().getTime());
            long longTime = now.getTime();
            String uploadKey = longTime + "mpLanguage.csv";
            PutObjectResult data = s3Client.putObject(bucket, uploadKey, languageCSV);
            String url = ((AmazonS3Client) s3Client).getResourceUrl(bucket, uploadKey);

            return url;
        }
        catch (Exception e)
        {
            log.error("Unable to generate PUT URL for (uploadCSVForMasterPartLanguage) in bucket [{}]. Error [{}]", bucketName, e.getMessage());
            throw new NotFoundException("Unable to generate amazon s3 link for given input");
        }
    }

    public String uploadCSVForMasterPartInventory(String inventoryCSV, Integer manufacturerId) {
        log.debug("Attempting to upload for (uploadCSVForMasterPartInventory) bucketName [{}]", bucketName);
        AmazonS3 s3Client = new AmazonS3Client(staticCredentialsProvider);

        try {
            if (manufacturerId == null || manufacturerId == 0) {
                throw new Exception("No manufacturer id found in token");
            }

            String bucketFolderUrl = "/manufacturer/" + manufacturerId + "/progress/masterpart/inventory";
            String bucket = bucketName + bucketFolderUrl;

            Timestamp now = new java.sql.Timestamp(Calendar.getInstance().getTime().getTime());
            long longTime = now.getTime();
            String uploadKey = longTime + "mpInventory.csv";
            PutObjectResult data = s3Client.putObject(bucket, uploadKey, inventoryCSV);
            String url = ((AmazonS3Client) s3Client).getResourceUrl(bucket, uploadKey);

            return url;
        }
        catch (Exception e)
        {
            log.error("Unable to generate PUT URL for (uploadCSVForMasterPartInventory) in bucket [{}]. Error [{}]", bucketName, e.getMessage());
            throw new NotFoundException("Unable to generate amazon s3 link for given input");
        }
    }

    public String uploadCSVForViewableBOM(String bomCSV, Integer manufacturerId) {
        log.debug("Attempting to upload for (uploadCSVForViewableBOM) bucketName [{}]", bucketName);
        AmazonS3 s3Client = new AmazonS3Client(staticCredentialsProvider);

        try {
            if (manufacturerId == null || manufacturerId == 0) {
                throw new Exception("No manufacturer id found in token");
            }

            String bucketFolderUrl = "/manufacturer/" + manufacturerId + "/progress/viewable/bom";
            String bucket = bucketName + bucketFolderUrl;

            Timestamp now = new java.sql.Timestamp(Calendar.getInstance().getTime().getTime());
            long longTime = now.getTime();
            String uploadKey = longTime + "viewableBOM.csv";
            PutObjectResult data = s3Client.putObject(bucket, uploadKey, bomCSV);
            String url = ((AmazonS3Client) s3Client).getResourceUrl(bucket, uploadKey);

            return url;
        }
        catch (Exception e)
        {
            log.error("Unable to generate PUT URL for (uploadCSVForViewableBOM) in bucket [{}]. Error [{}]", bucketName, e.getMessage());
            throw new NotFoundException("Unable to generate amazon s3 link for given input");
        }
    }

    public String uploadPropertiesDb(InputStream propertiesDbStream, Integer manufacturerId) {
        log.debug("Attempting to upload propertiesDb for Manufacturer [{}]", manufacturerId);
        AmazonS3Client s3Client = new AmazonS3Client(staticCredentialsProvider);

        try {
            if (manufacturerId.isNull() || manufacturerId == 0)
                throw new Exception("No manufacturer provided for uploadPropertiesDb");

            String bucket = String.format("%s/manufacturer/%s/propertiesDb-temp",
                    bucketName,
                    manufacturerId);

            Timestamp now = new java.sql.Timestamp(Calendar.getInstance().getTime().getTime());
            String uploadKey = String.valueOf(now.getTime()).concat("_propertiesDb.sqlite");
            PutObjectResult data = s3Client.putObject(bucket, uploadKey, propertiesDbStream, new ObjectMetadata(){{setContentType("application/octet-stream");}} );
            return s3Client.getResourceUrl(bucket, uploadKey);
        }
        catch (Exception e)
        {
            log.error("Unable to generate PUT URL for (uploadCSVForViewableBOM) in bucket [{}]. Error [{}]", bucketName, e.getMessage());
            throw new NotFoundException("Unable to generate amazon s3 link for given input");
        }
    }

    private void buildPresignedURLRequest(GeneratePresignedUrlRequest generatePresignedUrlRequest, String contentType) {
        Date expiration = new Date();
        long milliSeconds = new Date().getTime();
        milliSeconds += 1000 * 60 * 60; // Add 1 hour.
        expiration.setTime(milliSeconds);

        generatePresignedUrlRequest.setMethod(HttpMethod.PUT);
        generatePresignedUrlRequest.setExpiration(expiration);
        generatePresignedUrlRequest.setContentType(contentType);
    }

    public boolean deleteS3File(String url) {
        log.debug("Attempting to deleteS3File for url [{}] bucketName [{}]", url, bucketName);
        AmazonS3 s3Client = new AmazonS3Client(staticCredentialsProvider);
        boolean deleted = false;
        try {
            String keyName = "";
            String[] urlSplit = url.split(bucketName + "/");

            if (urlSplit.length == 2) {
                s3Client.deleteObject(new DeleteObjectRequest(bucketName, urlSplit[1]));
                deleted = true;
            }
        } catch (AmazonServiceException e) {
            // The call was transmitted successfully, but Amazon S3 couldn't process
            // it, so it returned an error response.
            log.error(e.getMessage());
        } catch (SdkClientException e) {
            // Amazon S3 couldn't be contacted for a response, or the client
            // couldn't parse the response from Amazon S3.
            log.error(e.getMessage());
        }
        return deleted;
    }

    public boolean deleteS3FileHelper(String existingS3Url, String currentS3Url) {
        boolean deleted = false;
        //Check existing file is not default placeholder image as it does not exist in S3 to be deleted.
        //Also check existing Url is different to latest to prevent deleting in use S3 files.
        if (!existingS3Url.isEmpty() &&
                !existingS3Url.equalsIgnoreCase("images/placeholder.jpg") &&
                !existingS3Url.equalsIgnoreCase(currentS3Url)) {

             deleted = deleteS3File(existingS3Url);
        }
        return deleted;
    }
}
