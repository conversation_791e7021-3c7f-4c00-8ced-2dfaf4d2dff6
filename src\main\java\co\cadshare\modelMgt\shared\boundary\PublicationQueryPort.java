package co.cadshare.modelMgt.shared.boundary;

import co.cadshare.modelMgt.shared.core.Publication;
import co.cadshare.shared.boundary.QueryPort;

import java.util.List;

public interface PublicationQueryPort extends QueryPort<Publication, Integer> {

    List<Publication> getPublicationsForManufacturer(Integer manufacturerId);

	List<Integer> getPublicationsForPublicationCategories(List<Integer> publicationCategoriesToBeRemoved);

	List<Publication> getPublicationsAssignedToPurchaser(int purchaserId);

	List<Publication> getPublicationsForPublicationCategory(Integer publicationCategoryId);
}
