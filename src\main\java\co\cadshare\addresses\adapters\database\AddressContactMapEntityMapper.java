package co.cadshare.addresses.adapters.database;

import co.cadshare.addresses.core.*;
import co.cadshare.shared.adapters.database.addresses.AddressEntity;
import co.cadshare.utils.CycleAvoidingMappingContext;
import org.mapstruct.Context;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper(uses = { ExternalAddressEntityMapper.class, AddressesContactEntityMapper.class })
public interface AddressContactMapEntityMapper {

	AddressContactMapEntityMapper Instance = Mappers.getMapper(AddressContactMapEntityMapper.class);

    //CoreToEntity
    AddressContactMapEntity coreToEntity(ExternalAddressContact core, @Context CycleAvoidingMappingContext context);
    
    List<AddressContactMapEntity> coresToEntities(List<ExternalAddressContact> cores, @Context CycleAvoidingMappingContext context);

    
    //Entity To Core
    
    ExternalAddressContact  entityToCore(AddressContactMapEntity entity, @Context CycleAvoidingMappingContext context);

    List<ExternalAddressContact > entitiesToCores(List<AddressContactMapEntity> entities, @Context CycleAvoidingMappingContext context);
    
    
    //Named   

    @Named("userEntityToCore")
    public static User userEntityToCore(AddressesUserEntity entity) {
        return AddressesUserEntityMapper.Instance.entityToCore(entity, new CycleAvoidingMappingContext());
    }

    @Named("userCoreToEntity")
    public static AddressesUserEntity userCoreToEntity(User core) {
        return AddressesUserEntityMapper.Instance.coreToEntity(core, new CycleAvoidingMappingContext());
    }

}
