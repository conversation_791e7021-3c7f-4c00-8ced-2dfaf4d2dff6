package co.cadshare.media.adapters.database;

import co.cadshare.shared.boundary.MediaQueryPort;
import co.cadshare.media.core.Image;
import co.cadshare.shared.adapters.database.ImageEntity;

import co.cadshare.shared.core.user.User;
import org.springframework.stereotype.Service;

@Service
public class MediaDataFacade implements MediaQueryPort {

	private final ImageRepo repo;

	public MediaDataFacade(ImageRepo repo) {
		this.repo = repo;
	}

	@Override
	public Image get(Integer id) {
		ImageEntity entity = this.repo.getOne(id);
		return ImageEntityMapper.Instance.entityToCore(entity);
	}

	@Override
	public Integer create(User user, Image image) {
		ImageEntity entity = ImageEntityMapper.Instance.coreToEntity(image);
		ImageEntity savedEntity = this.repo.save(entity);
		return savedEntity.getId();
	}
}
