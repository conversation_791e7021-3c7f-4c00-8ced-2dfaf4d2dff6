package co.cadshare.controller;

import co.cadshare.shared.core.user.User;
import co.cadshare.request.OrderWithWareHouseId;
import co.cadshare.orders.boundary.OrderService;
import co.cadshare.services.ShipEngineService;
import groovy.util.logging.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

@Controller
@RequestMapping("/shipment")
@Slf4j
public class ShipEngineController {

    @Autowired
    ShipEngineService shipEngineService;

    @Autowired
    OrderService orderService;

    private final Logger log = LoggerFactory.getLogger(getClass());

    @PostMapping(value = "/rates")
    public HttpEntity<Object> getRates(@AuthenticationPrincipal User currentUser,
                                       @RequestBody OrderWithWareHouseId orderWithWareHouseId,
                                       @RequestParam Integer userId) {


        if (!shipEngineService.checkAllOrderPartsHavePricesAndWeight(orderWithWareHouseId.getOrder())) {
            return new ResponseEntity<>("Cannot get shipping rates. All parts must contain price and weight.", HttpStatus.BAD_REQUEST);
        }

        log.info("API: getRates, ACCESS: User [{}], getRates, OrderWithWareHouseId [{}] ", currentUser.accessDetails(), orderWithWareHouseId);
        int userIdForOrder = userId == 0 ? currentUser.getUserId() : userId;
        try {
            Map<String, Object> result = shipEngineService.getRates(orderWithWareHouseId, userIdForOrder);
            return new ResponseEntity<>(result, HttpStatus.OK);
        }  catch (IllegalArgumentException e) {
            return new ResponseEntity<>(e.getMessage(), HttpStatus.BAD_REQUEST);
        }
        catch (Exception e){
            return new ResponseEntity<>(e.getMessage(), HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

}
