package co.cadshare.modelMgt.ranges.boundary;

import co.cadshare.modelMgt.models.boundary.UserQueryPort;
import co.cadshare.modelMgt.ranges.core.Range;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.dao.EmptyResultDataAccessException;

import co.cadshare.exceptions.NotFoundException;
import co.cadshare.shared.core.user.User;
import co.cadshare.aspects.logging.Log;

@Service
public class UpdateRangeService {

    private final RangeCommandPort commandPort;
    private final RangeQueryPort queryPort;
    private final UserQueryPort userQueryPort;

    @Autowired
    public UpdateRangeService(RangeCommandPort commandPort,
                              RangeQueryPort queryPort, UserQueryPort userQueryPort) {
        this.commandPort = commandPort;
        this.queryPort = queryPort;
        this.userQueryPort = userQueryPort;
    }

    @Log
    public void update(User user, Range range) throws Exception {
        try {
            this.queryPort.get(range.getId());
            this.commandPort.update(user, range);
        } catch (EmptyResultDataAccessException ex) {
            throw new NotFoundException("Range does not exist");
        }
    }

    public void update(Range range, int manufacturerId) throws Exception {
        User apiUser = this.userQueryPort.getApiUserForManufacturer(manufacturerId);
        update(apiUser, range);
    }
}
