package co.cadshare.inventory.core;

import co.cadshare.modelMgt.models.core.Model;
import co.cadshare.shared.core.manufacturer.ManufacturerProgress;

public class ManufacturerBomUploadProgress extends ManufacturerProgress {

    public ManufacturerBomUploadProgress() { super(); }

    public ManufacturerBomUploadProgress(int manufacturerId, Model model) {
        super();
        setManufacturerId(manufacturerId);
        setMachineId(model.getMachineId());
        setModelId(model.getModelId());
        setProcess(Process.VIEWABLE_BOM_UPLOAD);
    }
}
