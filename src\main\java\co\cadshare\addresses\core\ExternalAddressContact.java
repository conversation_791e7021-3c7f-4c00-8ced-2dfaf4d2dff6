package co.cadshare.addresses.core;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.sql.Timestamp;
import java.util.Optional;

@Data
public class ExternalAddressContact {
	private Integer id;
	@EqualsAndHashCode.Exclude
	@ToString.Exclude
	private ExternalAddress address;
	@EqualsAndHashCode.Exclude
	@ToString.Exclude
	private ExternalContact contact;
	private String compositeExternalRefId;
	private boolean deleted;
	private Timestamp createdDate;
	private Integer createdByUserId;
	private Timestamp modifiedDate;
	private Integer modifiedByUserId;

	public boolean isValid() {
		return compositeExternalRefId != null;
	}

	public void delete() {
		deleted = true;
		contact.setDeleted(true);
	}

	public void refresh(ExternalAddress addressChanges) {
		Optional<ExternalAddressContact> addressContactFromChanges = addressChanges.getContactMaps().stream()
				.filter(c -> c.getCompositeExternalRefId().equals(compositeExternalRefId))
				.findFirst();
		if(addressContactFromChanges.isPresent()) {
			deleted = false;
			contact.refresh(addressContactFromChanges.get().getContact());
		}
	}
}
