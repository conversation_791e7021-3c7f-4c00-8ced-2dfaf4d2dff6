package co.cadshare.modelMgt.publications.core;

import co.cadshare.modelMgt.shared.core.Publication;
import org.junit.Test;

import static org.junit.Assert.*;

public class PublicationTest {

	@Test
	public void testDelete() {
		Publication publication = new Publication();
		assertFalse(publication.isDeleted());
		publication.delete();
		assertTrue(publication.isDeleted());
	}

	@Test
	public void testPublish() {
		Publication publication = new Publication();
		publication.setStatus(ManualStatus.Status.UNPUBLISHED);
		publication.publish();
		assertEquals(ManualStatus.Status.PUBLISHED, publication.getStatus());
		publication.unpublish();
		assertEquals(ManualStatus.Status.UNPUBLISHED, publication.getStatus());
	}
}
