package co.cadshare.publications.adapters.database;

import co.cadshare.publications.core.CoverImage;
import co.cadshare.publications.core.Publication;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface CoverImageEntityMapper {
    CoverImageEntityMapper Instance = Mappers.getMapper(CoverImageEntityMapper.class);

    CoverImage entityToCore(CoverImageEntity coverImage);

    CoverImageEntity coreToEntity(CoverImage coverImage);
    List<CoverImage> entitiesToCores(List<CoverImageEntity> coverImages);
    List<CoverImageEntity> coresToEntities(List<Publication> coverImages);
}
