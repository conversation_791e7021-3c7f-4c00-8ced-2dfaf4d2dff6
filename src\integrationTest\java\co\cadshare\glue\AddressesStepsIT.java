package co.cadshare.glue;

import co.cadshare.addresses.core.Address;
import io.cucumber.java.en.Then;
import io.cucumber.java.en.When;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

import static org.junit.Assert.assertEquals;

public class AddressesStepsIT  {

    private List<Address> addresses;
	protected UserIT loggedInUser;
	private final CadshareIT auth;

	@Autowired
	public AddressesStepsIT(CadshareIT auth) {
		this.auth = auth;
	}

	@When("I attempt to get all addresses mapped to me")
    public void asAIAttemptToGetAllAddressesMappedToMe() {
        addresses = auth.loggedInUser().getAllAddresses();
    }

    @Then("all addresses mapped to me should be returned")
    public void asAAllAddressesMappedToMeShouldBeReturned() {
        assert(!addresses.isEmpty());
        assertEquals(1, addresses.size());
    }

    @When("I attempt to get all addresses mapped to another manufacturer user")
    public void asAIAttemptToGetAllAddressesMappedToAnotherManufacturerUser() {
        auth.loggedInUser().tryGetAllAddressesFor(4);
    }

    @Then("no addresses mapped to me should be returned")
    public void noAddressesMappedToMeShouldBeReturned() {
    }
}
