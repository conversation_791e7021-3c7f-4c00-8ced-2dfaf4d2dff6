package co.cadshare.glue;

import co.cadshare.addresses.core.Address;
import io.cucumber.java.en.Then;
import io.cucumber.java.en.When;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

import static org.junit.Assert.assertEquals;

public class AddressesStepsIT extends BasicStepsIT {

    private List<Address> addresses;
	private final UserIT loggedInUser;

	@Autowired
	public AddressesStepsIT(AuthenticationIT auth) {
		loggedInUser = auth.getLoggedInUser();
	}

	@When("I attempt to get all addresses mapped to me")
    public void asAIAttemptToGetAllAddressesMappedToMe() {
        addresses = loggedInUser.getAllAddresses();
    }

    @Then("all addresses mapped to me should be returned")
    public void asAAllAddressesMappedToMeShouldBeReturned() {
        assert(!addresses.isEmpty());
        assertEquals(1, addresses.size());
    }

    @When("I attempt to get all addresses mapped to another manufacturer user")
    public void asAIAttemptToGetAllAddressesMappedToAnotherManufacturerUser() {
        loggedInUser.tryGetAllAddressesFor(4);
    }

    @Then("no addresses mapped to me should be returned")
    public void noAddressesMappedToMeShouldBeReturned() {
    }
}
