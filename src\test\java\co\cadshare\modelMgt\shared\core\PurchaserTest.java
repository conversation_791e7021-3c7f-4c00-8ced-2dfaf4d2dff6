package co.cadshare.modelMgt.shared.core;

import co.cadshare.modelMgt.publications.core.Customer;
import co.cadshare.modelMgt.publications.core.Dealer;
import co.cadshare.modelMgt.publications.core.DealerPlus;
import org.junit.Test;

import static org.junit.Assert.assertTrue;

public class PurchaserTest {


	@Test
	public void PurchaserIsDealer() {
		Purchaser purchaser = new Dealer();
		assertTrue(purchaser.isDealer());
	}

	@Test
	public void PurchaserIsDealerPlus() {
		Purchaser purchaser = new DealerPlus();
		assertTrue(purchaser.isDealerPlus());
	}

	@Test
	public void PurchaserIsCustomer() {
		Purchaser purchaser = new Customer();
		assertTrue(purchaser.isCustomer());
	}

}
