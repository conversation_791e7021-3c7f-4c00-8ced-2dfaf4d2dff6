package co.cadshare.modelMgt.publicationCategories.boundary;

import co.cadshare.modelMgt.publicationCategories.core.PublicationCategory;
import co.cadshare.shared.boundary.GetService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.util.List;

@Service
public class GetPublicationCategoryService extends GetService<PublicationCategory, Integer> {

    private PublicationCategoryQueryPort complexQueryPort;

    @Autowired
    public GetPublicationCategoryService(PublicationCategoryQueryPort queryPort) {
        super(queryPort);
        this.complexQueryPort = queryPort;
    }

    public List<PublicationCategory> getPublicationCategoriesForManufacturer(int manufacturerId){
        return this.complexQueryPort.getListForManufacturer(manufacturerId);
    }

	public List<PublicationCategory> getPublicationCategoriesForPurchaser(int purchaserId) {
		return this.complexQueryPort.getAssignedPublicationCategoriesForPurchaser(purchaserId);
	}
}
