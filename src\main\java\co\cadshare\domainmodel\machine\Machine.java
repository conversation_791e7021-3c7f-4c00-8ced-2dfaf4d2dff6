package co.cadshare.domainmodel.machine;

import lombok.Data;

import java.sql.Timestamp;

@Data
public class Machine {

    private int machineId;
    private int rangeId;
    private String rangeName;
    private String name;
    private String description;
    private String thumbnailUrl;
    private Timestamp createdDate;
    private Integer createdByUserId;
    private Timestamp modifiedDate;
    private Integer modifiedByUserId;

    private String serialNumber;
    private int modelId;
    private String autodeskURN;

    @Override
    public boolean equals(Object o){
        if (o instanceof Machine){
            Machine temp = (Machine)o;
            if (this.getMachineId() == (temp.getMachineId()))
                return true;
        }
        return false;
    }
}

