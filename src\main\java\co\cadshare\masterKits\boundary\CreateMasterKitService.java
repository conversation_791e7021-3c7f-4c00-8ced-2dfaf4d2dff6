package co.cadshare.masterKits.boundary;

import co.cadshare.masterKits.core.KitMasterPart;
import co.cadshare.masterKits.core.PartMasterPart;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import co.cadshare.shared.core.user.User;
import co.cadshare.aspects.logging.Log;
import co.cadshare.masterKits.core.MasterKit;

import java.util.ArrayList;
import java.util.List;

@Service
public class CreateMasterKitService {

    private final MasterKitQueryPort queryPort;
    private final MasterKitCommandPort commandPort;

    @Autowired
    public CreateMasterKitService(MasterKitQueryPort queryPort,
                                  MasterKitCommandPort masterKitCommandPort) {
        this.queryPort = queryPort;
        this.commandPort = masterKitCommandPort;
    }

    @Log
    public Integer create(User user, CreateMasterKitCommand create) {

        KitMasterPart kitMasterPart = this.queryPort.getKitMasterPartForKit(create.getKitMasterPartId());
        List<Integer> partIds = new ArrayList<>(create.getParts().keySet());
        List<PartMasterPart> hydratedMasterParts = this.queryPort.getMasterPartsForKit(new ArrayList<>(partIds));

        MasterKit masterKit = MasterKit.builder()
                .kitMasterPart(kitMasterPart)
                .partMasterParts(hydratedMasterParts)
                .description(create.getKitMasterPartDescription())
                .manufacturerId(create.getManufacturerId())
                .build();
        masterKit.create(create);
        masterKit.validate();

        return this.commandPort.create(user, masterKit);
    }

}
