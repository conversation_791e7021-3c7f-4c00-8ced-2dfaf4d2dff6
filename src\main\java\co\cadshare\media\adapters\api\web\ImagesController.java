package co.cadshare.media.adapters.api.web;

import co.cadshare.aspects.access.roles.CanAccessManufacturer;
import co.cadshare.exceptions.BadGatewayException;
import co.cadshare.exceptions.InternalServerErrorCustomException;
import co.cadshare.media.boundary.GetImageService;
import co.cadshare.media.boundary.UploadImageService;
import co.cadshare.media.core.Image;
import co.cadshare.shared.core.user.User;
import com.amazonaws.services.pinpoint.model.BadRequestException;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;
import java.util.*;

@Controller
@RequestMapping("/manufacturers/{manufacturer-id}/images")
public class ImagesController {

	private final UploadImageService uploadService;
	private final GetImageService getImageService;

	public ImagesController(UploadImageService uploadService,
	                        GetImageService getImageService) {
		this.uploadService = uploadService;
		this.getImageService = getImageService;
	}

	@PostMapping()
	@CanAccessManufacturer
	public ResponseEntity<PostImageResponseDto> postImage(@AuthenticationPrincipal User user,
	                                                      @PathVariable("manufacturer-id") int manufacturerId,
	                                                      @RequestBody PostImageRequestDto request) {
		try {
			byte[] decodedBytes = Base64.getDecoder().decode(request.getImageBase64());
			InputStream targetStream = new ByteArrayInputStream(decodedBytes);
			Image image = new Image(request.getContentType(), targetStream);
			Integer imageId = uploadService.execute(user, image);
			PostImageResponseDto responseDto = new PostImageResponseDto();
			responseDto.setId(imageId);
			return new ResponseEntity<>(responseDto, HttpStatus.OK);
		} catch (IOException exception) {
			throw new BadGatewayException("The file could not be uploaded.");
		} catch (Exception exception) {
			throw new InternalServerErrorCustomException("The image file could not be saved.");
		}
	}

	@GetMapping(value = "/{image-id}", produces = "application/json")
	@CanAccessManufacturer
	@ResponseStatus(HttpStatus.OK)
	@Operation(summary = "Get the summary details of the list of all Images belonging to the specified Manufacturer")
	public ResponseEntity<GetImageResponseDto> getImage(@AuthenticationPrincipal User currentUser,
	                                                    @PathVariable("manufacturer-id") int manufacturerId,
	                                                    @PathVariable("image-id") int imageId) throws Exception {

		Image image = getImageService.getImage(imageId);
		GetImageResponseDto response = ImageMapper.Instance.toDto(image);
		return new ResponseEntity<>(response, HttpStatus.OK);
	}

	@GetMapping(produces = "application/json")
	@CanAccessManufacturer
	@ResponseStatus(HttpStatus.OK)
	@Operation(summary = "Get the summary details of the list of all Images belonging to the specified Manufacturer")
	public ResponseEntity<GetImageListResponseDto> getImages(@PathVariable("manufacturer-id") int manufacturerId,
	                                                         @AuthenticationPrincipal User currentUser) {

		List<ImageListItemDto> images = new ArrayList<>();
		for (Integer i = 1; i < manufacturerId  + 1; i++) {
			Integer finalI = i;
			ImageListItemDto image = new ImageListItemDto() {{
				setId(finalI);
				setDescription("Sample Media Description".concat(finalI.toString()));
				setLocationUrl("https://www.wallchimp.co.uk/images/farm-machinery-image-featuring-a-cat-challenger-tractor-p3259-8873_image.jpg");
				setTags(new ArrayList<String>(){{
					add("tag1-".concat(finalI.toString()));
					add("tag2-".concat(finalI.toString()));
					add("tag3-".concat(finalI.toString()));
				}});
			}};
			images.add(image);
		}
		GetImageListResponseDto response = new GetImageListResponseDto()
		{{ setImages(images);}};

		return new ResponseEntity<>(response, HttpStatus.OK);
	}
}
