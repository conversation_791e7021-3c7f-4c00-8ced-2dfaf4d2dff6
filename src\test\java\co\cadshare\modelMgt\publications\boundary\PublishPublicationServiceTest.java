package co.cadshare.modelMgt.publications.boundary;

import co.cadshare.exceptions.UnprocessableEntityException;
import co.cadshare.modelMgt.publications.adapters.database.ManualDao;
import co.cadshare.modelMgt.publications.core.*;
import co.cadshare.modelMgt.shared.boundary.PublicationQueryPort;
import co.cadshare.modelMgt.shared.boundary.ViewableQueryPort;
import co.cadshare.modelMgt.shared.core.Product;
import co.cadshare.modelMgt.shared.core.Purchaser;
import co.cadshare.modelMgt.shared.core.Range;
import co.cadshare.modelMgt.shared.core.Viewable;
import co.cadshare.shared.core.user.User;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.ArrayList;
import java.util.List;

import static org.mockito.Mockito.*;

public class PublishPublicationServiceTest {

    private PublishPublicationService out;

    @Mock
    private ManualDao mockManualCommand;
    @Mock
    private ViewableQueryPort mockModelQuery;
    @Mock
    private ProductCommandPort mockProductCommand;
	@Mock
	private PublicationCommandPort mockPublicationCommandPort;
	@Mock
	private PublicationQueryPort mockPublicationQueryPort;

    @Mock
    private User mockUser;
    @Mock
    private Viewable mockViewable;
    @Mock
    private Product mockProduct;
    @Mock
    private Range mockRange;
    @Mock
    private Purchaser mockPurchaser;
    private List<Purchaser> purchasers;

    private ArrayList<Integer> purchaserIds = new ArrayList<Integer>() {{add(1);}};

    private final int manualId = 1;
    private final int viewableId = 99;

    @Before
    public void Before() {
        MockitoAnnotations.initMocks(this);
        when(mockPurchaser.getId()).thenReturn(1);
        purchasers = new ArrayList<>();
        purchasers.add(mockPurchaser);
        out = new PublishPublicationService(mockManualCommand, mockModelQuery, mockProductCommand, mockPublicationCommandPort, mockPublicationQueryPort);
        when(mockViewable.getName()).thenReturn("0987-7890 Model Name");
        when(mockViewable.getProduct()).thenReturn(mockProduct);
        when(mockProduct.getRange()).thenReturn(mockRange);
        when(mockManualCommand.createManual(any(Manual.class))).thenReturn(manualId);
        when(mockModelQuery.getViewable(viewableId)).thenReturn(mockViewable);
    }

    @Test
    public void shouldPublishAndAssignPurchasers() throws Exception {
        when(mockRange.getAssignedPurchasers()).thenReturn(purchasers);
        when(mockViewable.isReadyForPublication(mockUser)).thenReturn(true);
        when(mockViewable.getPurchasersAssignedToRange()).thenReturn(purchasers);
        out.createAndAutoPublishPublication(mockUser, viewableId);
        verify(mockManualCommand, times(1)).createManual(any(Manual.class));
        verify(mockManualCommand, times(1)).assignManualToManufacturerSubEntities(manualId, purchaserIds);
    }

    @Test
    public void shouldPublishWithoutPurchasers() throws Exception {
        when(mockViewable.isReadyForPublication(mockUser)).thenReturn(true);
        when(mockViewable.getPurchasersAssignedToRange()).thenReturn(new ArrayList<>());
        out.createAndAutoPublishPublication(mockUser, viewableId);
        verify(mockManualCommand, times(1)).createManual(any(Manual.class));
        verify(mockManualCommand, times(0)).assignManualToManufacturerSubEntities(manualId, purchaserIds);
    }

    @Test(expected = UnprocessableEntityException.class)
    public void shouldNotPublishAsViewableNotReadyForPublication() throws Exception {
        when(mockViewable.isReadyForPublication(mockUser)).thenReturn(false);
        when(mockViewable.getPurchasersAssignedToRange()).thenReturn(purchasers);
        out.createAndAutoPublishPublication(mockUser, viewableId);
        verify(mockManualCommand, times(0)).createManual(any(Manual.class));
        verify(mockManualCommand, times(0)).assignManualToManufacturerSubEntities(manualId, purchaserIds);
    }
}
