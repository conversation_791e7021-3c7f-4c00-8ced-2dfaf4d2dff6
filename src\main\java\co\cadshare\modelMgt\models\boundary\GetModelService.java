package co.cadshare.modelMgt.models.boundary;

import co.cadshare.modelMgt.models.adapters.database.ModelDao;
import co.cadshare.modelMgt.models.core.Model;
import co.cadshare.shared.boundary.GetService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.sql.Timestamp;
import java.util.List;

@Service
public class GetModelService extends GetService<Model, Integer> {

    private final ModelQueryPort complexQueryPort;
	private final ModelDao modelDao;

    @Autowired
    public GetModelService(ModelQueryPort queryPort, ModelDao modelDao) {
        super(queryPort);
        this.complexQueryPort = queryPort;
	    this.modelDao = modelDao;
    }

    public List<Model> getModelsForProduct(int id){
        return this.complexQueryPort.getModelsListForProduct(id);
    }


	public List<Model> getModelsForManufacturer(int manufacturerId, Timestamp uploadedSinceDateTimestamp, Integer resultSize) {
		return modelDao.getModelsForManufacturer(manufacturerId, uploadedSinceDateTimestamp, resultSize);
	}

	public List<Model> getModelsForManufacturer(int manufacturerId) {
		return modelDao.getModelsForManufacturer(manufacturerId, null, null);
	}

}
