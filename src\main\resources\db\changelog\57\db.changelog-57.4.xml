<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.0.xsd
        http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd">

  <changeSet author="AndyB" id="57.4-create-purchaser-publicationCategory-map-table-and-foreign-key">
    <preConditions onFail="MARK_RAN">
      <not>
        <tableExists tableName="purchaserpublicationcategorymap" />
      </not>
    </preConditions>

    <createTable schemaName="public" tableName="purchaserpublicationcategorymap">
      <column name="id" type="integer" autoIncrement="true">
        <constraints nullable="false" primaryKey="true" />
      </column>
      <column name="purchaserid" type="integer">
        <constraints nullable="false" foreignKeyName="fk_purchaserpublicationcategorymap_purchaser" references="manufacturersubentity(manufacturersubentityid)"/>
      </column>
      <column name="publicationcategoryid" type="integer">
          <constraints nullable="false" foreignKeyName="fk_purchaserpublicationcategorymap_publicationcategory" references="publicationcategory(id)"/>
      </column>
        <column name="createddate" type="timestamp">
            <constraints nullable="false" />
        </column>
        <column name="createdbyuserid" type="integer">
            <constraints nullable="false" />
        </column>
    </createTable>

      <sql>
          insert into purchaserpublicationcategorymap (purchaserid, publicationcategoryid, createddate, createdbyuserid)
          select msrm.manufacturersubentityid, pc.id, now(), 1
          from ManufacturerSubEntityRangeMap msrm,
          range r,
          publicationcategory pc
          where msrm.rangeid = r.rangeid
          and r.name = pc.name
      </sql>
  </changeSet>

</databaseChangeLog>
