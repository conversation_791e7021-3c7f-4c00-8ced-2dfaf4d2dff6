---
to: src/test/java/co/cadshare/<%= h.inflection.pluralize(name) %>/adapters/database/<%= Name %>DataGatewayTest.java
---

package co.cadshare.<%= h.inflection.pluralize(name) %>.adapters.database;

import co.cadshare.shared.core.user.User;
import co.cadshare.aspects.logging.LoggingAspect;
import co.cadshare.<%= h.inflection.pluralize(name) %>.core.<%= Name %>;
import co.cadshare.<%= h.inflection.pluralize(name) %>.boundary.<%= Name %>CommandPort;
import co.cadshare.<%= h.inflection.pluralize(name) %>.boundary.<%= Name %>QueryPort;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.MockitoAnnotations;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.sql.Timestamp;
import java.util.Calendar;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.*;

@EnableAspectJAutoProxy
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(classes = {<%= Name %>DataGateway.class, LoggingAspect.class})
public class <%= Name %>DataGatewayTest {

    @MockBean
    private <%= Name %>Repo <%= name %>Repo;
    @MockBean
    private <%= Name %>ComplexQueryRepo <%= name %>QueryRepo;
    @Autowired
    <%= Name %>CommandPort cmdOut;
    @Autowired
    <%= Name %>QueryPort queryOut;
    private User user;
    private <%= Name %> <%= name %>;
    private <%= Name %>Entity <%= name %>Entity;
    private <%= Name %>Entity <%= name %>EntityWithId;
    private <%= Name %> error<%= Name %>;
    private <%= Name %>Entity error<%= Name %>Entity;

    @Before
    public void Before() {
        MockitoAnnotations.initMocks(this);
        <%= name %> = build<%= Name %>();
        error<%= Name %> = build<%= Name %>();
        user = new User() {{
            setUserId(1);
            setFirstName("firstName");
            setLastName("lastName");
        }};
        <%= name %>Entity = <%= Name %>EntityMapper.Instance.coreToEntity(<%= name %>);
        error<%= Name %>Entity = <%= Name %>EntityMapper.Instance.coreToEntity(error<%= Name %>);
        <%= name %>EntityWithId = <%= Name %>EntityMapper.Instance.coreToEntity(<%= name %>);
        <%= name %>EntityWithId.setId(<%= idtype %>.valueOf(1));
    }

    @Test
    public void Create<%= Name %>Success() {
        when(<%= name %>Repo.save(any(<%= Name %>Entity.class))).thenReturn(<%= name %>EntityWithId);
        <%= idtype %> result = cmdOut.create(user, <%= name %>);
        verify(<%= name %>Repo, times(1)).save(argThat(new <%= Name %>EntityMatcher(<%= name %>Entity)));
        assertEquals(1, result);
    }

    @Test(expected = RuntimeException.class)
    public void Create<%= Name %>FailureException() throws Exception {
        when(<%= name %>Repo.save(any(<%= Name %>Entity.class))).thenThrow(new RuntimeException("terrible"));
        cmdOut.create(user, error<%= Name %>);
    }

    @Test
    public void Update<%= Name %>Success()  throws Exception {
        when(<%= name %>Repo.save(<%= name %>Entity)).thenReturn(<%= name %>EntityWithId);
        cmdOut.update(user, <%= name %>);
        verify(<%= name %>Repo, times(1)).save(argThat(new <%= Name %>EntityMatcher(<%= name %>Entity)));
    }

    @Test(expected = RuntimeException.class)
    public void Update<%= Name %>FailureException() throws Exception {
        doThrow(new RuntimeException("terrible")).when(<%= name %>Repo).save(any(<%= Name %>Entity.class));
        cmdOut.update(user, error<%= Name %>);
        verify(<%= name %>Repo, times(1)).save(argThat(new <%= Name %>EntityMatcher(error<%= Name %>Entity)));
    }

    @Test
    public void Delete<%= Name %>Success()  throws Exception {
        doNothing().when(<%= name %>Repo).delete(<%= name %>Entity);
        cmdOut.delete(user, <%= name %>);
    }

    @Test(expected = RuntimeException.class)
    public void Delete<%= Name %>FailureException() throws Exception {
        doThrow(new RuntimeException("terrible")).when(<%= name %>Repo).save(any(<%= Name %>Entity.class));
        cmdOut.delete(user, error<%= Name %>);
        verify(<%= name %>Repo, times(1)).save(argThat(new <%= Name %>EntityMatcher(error<%= Name %>Entity)));
    }

    private <%= Name %> build<%= Name %>() {
        <%= Name %> <%= name %> = new <%= Name %>();
        return <%= name %>;
    }
}

