package co.cadshare.modelMgt.viewables.boundary;

import co.cadshare.modelMgt.shared.boundary.ViewableQueryPort;
import co.cadshare.modelMgt.shared.core.Viewable;
import co.cadshare.shared.boundary.GetService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class GetViewableService extends GetService<Viewable, Integer> {

	private final ViewableQueryPort queryPort;

	@Autowired
	public GetViewableService(ViewableQueryPort queryPort) {
		super(queryPort);
		this.queryPort = queryPort;
	}

	public List<Viewable> getViewablesForManufacturer(int manufacturerId) {
		return queryPort.getListForManufacturer(manufacturerId);
	}

	public List<Viewable> searchViewables(int manufacturerId, String searchTerm) {
		return queryPort.searchViewables(manufacturerId, searchTerm);
	}

	public List<Viewable> filterViewablesByProduct(int manufacturerId, int productId) {
		return queryPort.filterViewablesByProduct(manufacturerId, productId);
	}
}
