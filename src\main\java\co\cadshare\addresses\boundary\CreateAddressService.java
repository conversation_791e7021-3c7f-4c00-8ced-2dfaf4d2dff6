package co.cadshare.addresses.boundary;

import co.cadshare.addresses.core.Address;
import co.cadshare.shared.core.user.User;
import co.cadshare.services.ManufacturerService;
import co.cadshare.shared.core.manufacturer.ManufacturerSettings;
import co.cadshare.users.boundary.UsersService;
import co.cadshare.utils.ManufacturerFinder;
import org.springframework.stereotype.Service;

@Service
public class CreateAddressService {
    private final AddressCommandPort addressCommand;
    private final ManufacturerFinder manufacturerFinder;
    private final ManufacturerService manufacturerService;
    private final ExternalAddressCommandPort externalAddressCommand;
    private final UsersService usersService;

    public CreateAddressService(AddressCommandPort addressCommand,
                                ManufacturerFinder manufacturerFinder,
                                ManufacturerService manufacturerService,
                                ExternalAddressCommandPort externalAddressCommand,
                                UsersService usersService) {
        this.addressCommand = addressCommand;
        this.manufacturerFinder = manufacturerFinder;
        this.manufacturerService = manufacturerService;
        this.externalAddressCommand = externalAddressCommand;
        this.usersService = usersService;
    }

    public int createAddressForUser(User currentUser, Address address, int userId) {
        int userIdForNewAddress = userId == 0 ? currentUser.getUserId() : userId;

        User user = usersService.findDetailsByUserid(userId);

        int addressId = addressCommand.createUserAddress(userId, address);
        int manufacturerId = manufacturerFinder.getUserManufacturerId(currentUser);
        ManufacturerSettings settings = manufacturerService.getManufacturerSettings(manufacturerId);
        if (settings != null && settings.isExternallyPublishOrder())
            externalAddressCommand.createNewAddress(user, address, addressId);
        return addressId;
    }
}
