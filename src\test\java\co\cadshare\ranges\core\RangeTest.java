package co.cadshare.ranges.core;

import org.junit.Before;
import org.junit.Ignore;
import org.junit.Test;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;

public class RangeTest {

    private Range out;

    @Before
    public void Before() {
        this.out = new Range();
    }

    @Test
    public void CheckRangeIsNotNullTest() {
        assertTrue(out != null);
    }
}