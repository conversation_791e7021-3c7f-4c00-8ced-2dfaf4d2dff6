package co.cadshare.modelMgt.publications.boundary;

import co.cadshare.exceptions.NotFoundException;
import co.cadshare.modelMgt.shared.core.Publication;
import co.cadshare.modelMgt.shared.boundary.PublicationQueryPort;
import co.cadshare.shared.core.user.User;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.EmptyResultDataAccessException;
import org.springframework.stereotype.Service;

@Service
public class DeletePublicationService {

    private final PublicationCommandPort publicationCommandPort;
	private final PublicationQueryPort publicationQueryPort;

    @Autowired
    public DeletePublicationService(PublicationCommandPort publicationCommandPort,
                                    PublicationQueryPort publicationQueryPort) {
        this.publicationCommandPort = publicationCommandPort;
	    this.publicationQueryPort = publicationQueryPort;
    }

    public void delete(User user, Integer publicationId) throws Exception {
	    try {
		    Publication publication = publicationQueryPort.get(publicationId);
			publication.delete();
		    this.publicationCommandPort.update(user, publication);
	    } catch (EmptyResultDataAccessException e) {
		    throw new NotFoundException(e.getMessage());
	    }
    }
}
