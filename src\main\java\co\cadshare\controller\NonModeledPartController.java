/*
 * Copyright 2016 Bell.
 */
package co.cadshare.controller;

import co.cadshare.aspects.access.languages.CanUseLanguage;
import co.cadshare.shared.core.Language;

import java.util.List;

import co.cadshare.utils.ManufacturerFinder;
import co.cadshare.utils.ObjectUtilsExtension;
import lombok.experimental.ExtensionMethod;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import co.cadshare.domainmodel.nonmodeledparts.NonModeledPart;
import co.cadshare.domainmodel.part.Part;
import co.cadshare.shared.core.user.User;
import co.cadshare.request.UpdateNonModelPartsRequest;
import co.cadshare.services.NonModeledPartService;
import org.springframework.web.bind.annotation.RequestParam;

@ExtensionMethod(ObjectUtilsExtension.class)
@Controller
@RequestMapping("/nonmodeledpart")
public class NonModeledPartController {

  private NonModeledPartService nonModeledPartService;
  private ManufacturerFinder manufacturerFinder;

	public NonModeledPartController(NonModeledPartService nonModeledPartService,
	                                ManufacturerFinder manufacturerFinder) {
		this.nonModeledPartService = nonModeledPartService;
		this.manufacturerFinder = manufacturerFinder;
	}

    private static final Logger logger = LoggerFactory.getLogger(NonModeledPartController.class);

	@RequestMapping(method = RequestMethod.POST, consumes = "application/json")
	public HttpEntity<Integer> createNonModeledPart(@AuthenticationPrincipal User currentUser,
	                                                @RequestBody NonModeledPart nonModeledPart) {

	    logger.info("ACCESS: User [{}], createNonModeledPart, model viewable [{}]", currentUser.accessDetails(),
	        nonModeledPart.toString());
	    nonModeledPart.setCreatedByUserId(currentUser.getUserId());
	    nonModeledPartService.createNonModeledPart(nonModeledPart);
	    return new ResponseEntity<>(HttpStatus.OK);
	}

	@RequestMapping(method = RequestMethod.POST, consumes = "application/json", value = "/remove")
	public HttpEntity<Integer> removeNonModeledPart(@AuthenticationPrincipal User currentUser,
	                                                @RequestBody NonModeledPart nonModeledPart) {

		logger.info("ACCESS: User [{}], removeNonModeledPart, model viewable [{}]", currentUser.accessDetails(),
		    nonModeledPart.toString());
		nonModeledPart.setCreatedByUserId(currentUser.getUserId());
		nonModeledPartService.removeNonModeledPart(nonModeledPart);
		return new ResponseEntity<>(HttpStatus.OK);
	}

	// TODO NM need to review correct place to have this as the URL doesn't match
	// standard
	@RequestMapping(value = "/parts/{associatedDbId}/{modelId}", method = RequestMethod.GET)
	@CanUseLanguage
	public HttpEntity<List<Part>> getPartsForNonModeledPart(@AuthenticationPrincipal User currentUser,
	                                                      @PathVariable int associatedDbId,
	                                                      @PathVariable int modelId,
	                                                      @RequestParam(value = "language", required = false) Language language) throws Exception {

		logger.info("ACCESS: User [{}], getPartsForNonModeledPart, associatedDbId [{}], modelId [{}]",
		    currentUser.accessDetails(), associatedDbId, modelId);
		int manufacturerId = manufacturerFinder.getUserManufacturerId(currentUser);
		Integer languageId = language != null ? currentUser.findLanguage(language).getLanguageId() : null;
		List<Part> partList = nonModeledPartService.getPartsForAssociatedDbIdAndModelId(associatedDbId,
			    modelId,
			    languageId,
			    currentUser.obtainDefaultLanguage(),
			    manufacturerId);
		return new ResponseEntity<>(partList, HttpStatus.OK);
	}

	@RequestMapping(method = RequestMethod.PUT)
	public HttpEntity<Boolean> updatePart(@AuthenticationPrincipal User currentUser,
	  @RequestBody UpdateNonModelPartsRequest updateModelParts) {

		logger.info("ACCESS: User [{}], updateNonModelParts, number of parts to update [{}]", currentUser.accessDetails(),
		    updateModelParts.getPartIds().size());
		boolean responseSucess = nonModeledPartService.updateNonModeledParts(updateModelParts, currentUser);
		return new ResponseEntity<>(responseSucess, HttpStatus.OK);
	}

	@RequestMapping(value = "/model/{modelId}", method = RequestMethod.GET)
	@CanUseLanguage
	public HttpEntity<List<Part>> getNonModeledPartsForModel(@AuthenticationPrincipal User currentUser,
	                                                         @PathVariable int modelId,
	                                                         @RequestParam(value = "language", required = false) Language language) throws Exception {

		logger.info("ACCESS: User [{}], getNonModeledPartsForModel, modelId [{}]", currentUser.accessDetails(), modelId);
		Integer languageId = language != null ? currentUser.findLanguage(language).getLanguageId() : null;
		int manufacturerId = manufacturerFinder.getUserManufacturerId(currentUser);
		List<Part> partList = nonModeledPartService.getPartsByModelId(modelId, languageId, currentUser.obtainDefaultLanguage(), manufacturerId);
		return new ResponseEntity<List<Part>>(partList, HttpStatus.OK);
	}

}
