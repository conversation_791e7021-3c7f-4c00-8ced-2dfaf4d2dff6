package co.cadshare.modelMgt.models.adapters.database;

import co.cadshare.exceptions.NotFoundException;
import co.cadshare.modelMgt.models.boundary.PublishedModelsForMasterPartSearchCriteria;
import co.cadshare.modelMgt.models.core.Model;
import co.cadshare.modelMgt.models.boundary.ModelCommandPort;
import co.cadshare.modelMgt.models.boundary.ModelQueryPort;
import co.cadshare.modelMgt.models.core.model.AutodeskStatus;
import co.cadshare.shared.core.user.User;
import co.cadshare.persistence.ModelUpdateDao;
import co.cadshare.aspects.logging.Log;

import java.sql.Timestamp;
import java.util.Calendar;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class ModelDataGateway implements ModelQueryPort, ModelCommandPort {

    private final ModelCommandRepo modelCommandRepo;
    private final ModelComplexQueryRepo modelQueryRepo;
    private final ModelDao modelDao;
    private final ModelUpdateDao modelUpdateDao;

    @Autowired
    public ModelDataGateway(ModelCommandRepo modelCommandRepo,
                            ModelComplexQueryRepo modelQueryRepo,
                            ModelDao modelDao, ModelUpdateDao modelUpdateDao) {
        this.modelCommandRepo = modelCommandRepo;
        this.modelQueryRepo = modelQueryRepo;
        this.modelDao = modelDao;
        this.modelUpdateDao = modelUpdateDao;
    }

    @Override
    public Model get(Integer modelId) {
        ModelsModelEntity entity = this.modelQueryRepo.getModelById(modelId);
        if(entity.isArchived())
            throw new NotFoundException("Model does not exist");
        return ModelsModelEntityMapper.Instance.entityToCore(entity);
    }

    public List<Model> getModelsListForProduct(Integer id) {
         List<ModelsModelEntity> entities = this.modelQueryRepo.getModelsForProduct(id);
         List<Model> models = ModelsModelEntityMapper.Instance.entitiesToCores(entities);
         return models;
    }

    public List<Model> getModelsListForProduct(Integer id, AutodeskStatus status, boolean setupComplete) {
        List<ModelsModelEntity> entities = this.modelQueryRepo.getModelsForProduct(id, status, setupComplete);
        List<Model> models = ModelsModelEntityMapper.Instance.entitiesToCores(entities);
        return models;
    }

    @Override
    public List<Model> getPublishedModelsForMasterPart(PublishedModelsForMasterPartSearchCriteria searchCriteria) {

        List<ModelsModelEntity> entities = this.modelQueryRepo.getPublishedModelsForMasterPart(searchCriteria);
        return ModelsModelEntityMapper.Instance.entitiesToCores(entities);
    }

    @Override
    public List<Model> getList() {
        List<ModelsModelEntity> entities = this.modelQueryRepo.getAllModels();
        return ModelsModelEntityMapper.Instance.entitiesToCores(entities);
    }

    @Override
    @Log
    public Integer create(User user, Model model) {
        model.setCreatedByUserId(user.getUserId());
        model.setCreatedDate(new Timestamp(Calendar.getInstance().getTime().getTime()));
        int modelId = modelDao.createModel(model);
        modelUpdateDao.create(modelId);
        return modelId;
    }

    @Override
    @Log
    public void update(User user, Model model) throws Exception {
        model.setModifiedByUserId(user.getUserId());
        model.setModifiedDate(new Timestamp(Calendar.getInstance().getTime().getTime()));
        ModelCommandEntity entity = ModelsModelEntityMapper.Instance.coreToEntity(model);
        modelCommandRepo.save(entity);
        modelUpdateDao.update(model.getModelId());
    }

    @Override
    @Log
    public void delete(User user, Model model) throws Exception {
        model.setModifiedByUserId(user.getUserId());
        model.setModifiedDate(new Timestamp(Calendar.getInstance().getTime().getTime()));
        ModelCommandEntity entity = ModelsModelEntityMapper.Instance.coreToEntity(model);
        entity.setArchived(true);
        this.modelCommandRepo.save(entity);
    }
}