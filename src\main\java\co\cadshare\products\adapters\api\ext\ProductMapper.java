package co.cadshare.products.adapters.api.ext;

import co.cadshare.products.core.Product;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import java.util.List;

@Mapper
public interface ProductMapper {

    ProductMapper Instance = Mappers.getMapper(ProductMapper.class);

    Product postRequestDtoToProduct(PostProductRequestDto postRequestDto);

    Product putRequestDtoToProduct(PutProductRequestDto putRequestDto);

    GetProductResponseDto ProductToGetResponseDto(Product product);

    List<GetProductResponseDto> ProductToGetListResponseDto(List<Product> products);
}
