package co.cadshare.modelMgt.publications.boundary;

import co.cadshare.modelMgt.shared.core.Publication;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service
public class PublicationHydrator {

	private final List<PublicationAttributeHydrator> hydrators = new ArrayList<>();

	public PublicationHydrator(PublicationCategoryHydrator publicationCategoryHydrator,
	                           ViewableHydrator viewableHydrator,
	                           VideoHydrator videoHydrator,
	                           KitHydrator kitHydrator,
	                           TechDocHydrator techDocHydrator,
							   CoverImageHydrator coverImageHydrator,
							   FeaturedViewableImageHydrator featuredViewableImageHydrator,
	                           PurchaserHydrator purchaserHydrator) {
		hydrators.add(publicationCategoryHydrator);
		hydrators.add(viewableHydrator);
		hydrators.add(videoHydrator);
		hydrators.add(kitHydrator);
		hydrators.add(techDocHydrator);
		hydrators.add(coverImageHydrator);
		hydrators.add(featuredViewableImageHydrator);
		hydrators.add(purchaserHydrator);
	}

	public void hydrate(PublicationCommand publicationCommand, Publication publication) {
		hydrators.forEach(h -> h.hydrate(publicationCommand, publication));
		hydrateThis(publicationCommand, publication);
	}

	private void hydrateThis(PublicationCommand publicationCommand, Publication publication) {

	}

}
