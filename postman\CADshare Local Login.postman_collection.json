{"info": {"_postman_id": "48068302-ea65-8164-775f-9f3d099b9bd4", "name": "CADshare Local Login", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "300252"}, "item": [{"name": "1. <PERSON><PERSON>", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Basic Y2xpZW50YXBwOg=="}, {"key": "Content-Type", "value": "application/x-www-form-urlencoded"}], "body": {"mode": "raw", "raw": "username={{userId}}&password={{password}}&grant_type=password"}, "url": {"raw": "http://localhost:8080/oauth/token", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["o<PERSON>h", "token"]}}, "response": []}, {"name": "2. <PERSON> User", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************.6_55y1xFz9X6sS4-bT1cFXvgL-rFC_p6kRoCqfBsjUs"}, {"key": "Content-Type", "value": "application/json", "disabled": true}], "url": {"raw": "http://localhost:8080/user", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["user"]}}, "response": []}, {"name": "3. <PERSON><PERSON> CommentThread", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************************************************************************************************************************************************************************************************.Y9E-Xf_y8OC3CjsQM2MdibUbSwKjzqUyTDxLDPq1xSc"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"orderId\": 17,\n    \"createdDate\": \"2018-01-24T10:10:08.052+0000\",\n    \"createdByUserId\": 11,\n    \"modifiedDate\": \"2018-01-24T10:10:43.240+0000\",\n    \"modifiedByUserId\": 11\n}"}, "url": {"raw": "http://localhost:8080/commentthread/", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["commentthread", ""]}}, "response": []}, {"name": "4. Create CommentThread OrderItem", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************************************************************************************************************************************************************************************************.Y9E-Xf_y8OC3CjsQM2MdibUbSwKjzqUyTDxLDPq1xSc"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"orderId\": 17,\n    \"orderItemId\": 53,\n    \"createdDate\": \"2018-01-24T10:10:08.052+0000\",\n    \"createdByUserId\": 11,\n    \"modifiedDate\": \"2018-01-24T10:10:43.240+0000\",\n    \"modifiedByUserId\": 11\n}"}, "url": {"raw": "http://localhost:8080/commentthread/", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["commentthread", ""]}}, "response": []}, {"name": "5. Create Comment Order", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************************************************************************************************************************************************************************************************.Y9E-Xf_y8OC3CjsQM2MdibUbSwKjzqUyTDxLDPq1xSc"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"commentThreadId\": 1,\n    \"message\": \"THIS IS A TEST COMMENT\",\n    \"createdDate\": \"2018-01-24T10:10:08.052+0000\",\n    \"createdByUserId\": 11\n}"}, "url": {"raw": "http://localhost:8080/commentthread/:threadId/message", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["commentthread", ":threadId", "message"], "variable": [{"key": "threadId", "value": "1"}]}}, "response": []}, {"name": "6. Get Comment For Order", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************************************************************************************************************************************************************************************************.Y9E-Xf_y8OC3CjsQM2MdibUbSwKjzqUyTDxLDPq1xSc"}], "url": {"raw": "http://localhost:8080/commentthread/:threadId/", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["commentthread", ":threadId", ""], "variable": [{"key": "threadId", "value": "1"}]}}, "response": []}]}