package co.cadshare.modelMgt.publications.adapters.api.web;

import co.cadshare.modelMgt.publications.boundary.CreatePublicationCommand;
import co.cadshare.modelMgt.publications.boundary.UpdatePublicationCommand;
import co.cadshare.modelMgt.publications.core.ManualStatus;
import co.cadshare.modelMgt.shared.core.Publication;
import co.cadshare.shared.core.user.User;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.util.ArrayList;
import java.util.List;

@Mapper(uses = {CoverImageMapper.class,
		CustomerMapper.class,
		PublicationKitMapper.class,
		PublicationStatusMapper.class})
public interface PublicationMapper {

    PublicationMapper Instance = Mappers.getMapper(PublicationMapper.class);

	@Mapping(source=".", target="manufacturerId", qualifiedByName="userManufacturerId")
	@Mapping(source=".", target="featuredViewableId", qualifiedByName="featuredViewableId")
    CreatePublicationCommand postRequestDtoToCommand(PostPublicationRequestDto postRequestDto, @Context User user);

	@Mapping(source=".", target="manufacturerId", qualifiedByName="userManufacturerId")
	@Mapping(source=".", target="id", qualifiedByName="publicationId")
	@Mapping(source=".", target="featuredViewableId", qualifiedByName="featuredViewableId")
	UpdatePublicationCommand putRequestDtoToCommand(PutPublicationRequestDto postRequestDto, @Context User user, @Context int publicationId);

	@Named("publicationId")
	default Integer publicationId(PutPublicationRequestDto postRequestDto, @Context User user, @Context int publicationId) {
		return publicationId;
	}

	@Named("featuredViewableId")
	default Integer featuredViewableId(PostPutPublicationRequestDto requestDto, @Context User user) {
		if(requestDto.getViewables() == null) return null;
		return requestDto.getViewables().stream()
			.filter(PostPutPublicationViewableDto::isFeaturedViewable)
			.findFirst()
			.map(PostPutPublicationViewableDto::getId).orElse(null);
	}

	@Named("userManufacturerId")
	default Integer userManufacturerId(PostPublicationRequestDto postRequestDto, @Context User user) {
		return user.getManufacturerId();
	}

	@CommonPublicationToDtoMappings
	GetPublicationResponseDto coreToDto(Publication publication);

	@CommonPublicationToDtoMappings
	@Mapping(source="createdDate", target="createdDate")
	GetPublicationListItemResponseDto coreToListItemDto(Publication publication);

	List<GetPublicationListItemResponseDto> coresToListItemDtos(List<Publication> publications);

	@Named("statusToPublished")
	default boolean statusToPublished(ManualStatus.Status status) {
		return status == ManualStatus.Status.PUBLISHED;
	}

	@Named("viewableWithContext")
	default List<PublicationViewableListItemDto> viewableWithContext(Publication publication) {
		List<PublicationViewableListItemDto> list = new ArrayList<>();
		publication.getViewables().forEach(v -> list.add(PublicationViewableMapper.Instance.viewableToViewableListItemDto(v, publication)));
		return list;
	}

	@Mapping(source = "coverImage.locationUrl", target = "coverImage.url")
	@Mapping(source = "featuredViewableImage.locationUrl", target = "featuredViewableImage.url")
	@Mapping(source="status", target="published", qualifiedByName = "statusToPublished")
	@Mapping(source="publicationCategory.id", target="publicationCategoryId")
	@Mapping(source="publicationCategory.name", target="publicationCategoryName")
	@Mapping(source="techDocs", target="techDocs")
	@Mapping(source="videos", target="videos")
	@Mapping(source="kits", target="kits")
	@Mapping(source = "purchasers", target = "customers")
	@Mapping(source = ".", target = "viewables", qualifiedByName = "viewableWithContext")
	@interface CommonPublicationToDtoMappings{}
}


