package co.cadshare.ranges.adapters.database;

import com.querydsl.jpa.impl.JPAQueryFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import javax.persistence.EntityManager;
import java.util.List;

@Repository
public class RangeComplexQueryRepo {

    private JPAQueryFactory queryFactory;

    @Autowired
    public RangeComplexQueryRepo(EntityManager entityManager) {
        this.queryFactory = new JPAQueryFactory(entityManager);
    }

    public List<RangeEntity> getRangesForManufacturer(Integer manufacturerId) {
        QRangeEntity range = QRangeEntity.rangeEntity;
        return queryFactory.selectFrom(range)
                .where(range.manufacturerId.eq(manufacturerId)
                        .and(range.deleted.eq(false)))
                .fetch();
    }

}

