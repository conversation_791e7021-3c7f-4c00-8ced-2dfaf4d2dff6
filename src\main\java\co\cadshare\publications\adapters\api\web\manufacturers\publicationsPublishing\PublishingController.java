package co.cadshare.publications.adapters.api.web.manufacturers.publicationsPublishing;

import co.cadshare.shared.core.user.User;
import co.cadshare.publications.adapters.api.web.manufacturers.publications.PublicationMapper;
import co.cadshare.publications.boundary.CreatePublicationService;
import io.swagger.v3.oas.annotations.Operation;
import org.mapstruct.factory.Mappers;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.*;


@RestController
@RequestMapping("/manufacturers/{manufacturer-id}/publications/{publication-id}")
public class PublishingController {
    private final PublicationMapper publicationMapper = Mappers.getMapper(PublicationMapper.class);
    private final CreatePublicationService createPublicationService;

    @Autowired
    public PublishingController(CreatePublicationService createPublicationService){
        this.createPublicationService = createPublicationService;
    }

    @PostMapping(path = "/publish")
    @PreAuthorize("hasRole('ROLE_MANUFACTURER')")
    @ResponseStatus(HttpStatus.OK)
    @Operation(summary = "Publish a Publication (whether published already or not).")
    public ResponseEntity postPublicationPublish(@PathVariable("manufacturer-id") int manufacturerId,
                                                                                @PathVariable("publication-id") int publicationId,
                                                                        @AuthenticationPrincipal User currentUser) {

        //Integer createdId = this.createPublicationService.create(publicationMapper.postRequestDtoToPublication(postCoverImage));
        return new ResponseEntity<>(HttpStatus.OK);
    }


    @PostMapping(path = "/unpublish")
    @PreAuthorize("hasRole('ROLE_MANUFACTURER')")
    @ResponseStatus(HttpStatus.OK)
    @Operation(summary = "Unpublish a Publication (whether published already or not)")
    public ResponseEntity postPublicationUnpublish(@PathVariable("manufacturer-id") int manufacturerId,
                                                                                @PathVariable("publication-id") int publicationId,
                                                                                @AuthenticationPrincipal User currentUser) {

        //Integer createdId = this.createPublicationService.create(publicationMapper.postRequestDtoToPublication(postCoverImage));
        return new ResponseEntity<>(HttpStatus.OK);
    }
}
