package co.cadshare.inventory.boundary;

import co.cadshare.inventory.core.DealerPlusMasterPartInventoryDownloadProgress;
import co.cadshare.inventory.core.DealerPlusMasterPartInventoryUploadProgress;
import co.cadshare.masterParts.adapters.database.MasterPartDao;
import co.cadshare.shared.adapters.aws.s3.S3StorageClient;
import co.cadshare.shared.adapters.database.manufacturer.ManufacturerProgressDao;
import co.cadshare.shared.core.manufacturer.ManufacturerProgress;
import co.cadshare.shared.core.manufacturer.subentity.ManufacturerSubEntity;
import co.cadshare.masterParts.core.MasterPart;
import co.cadshare.inventory.core.InventoryFile;
import co.cadshare.shared.core.user.User;
import co.cadshare.persistence.*;
import co.cadshare.users.adapters.database.UserDetailsDao;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.time.Instant;
import java.util.ArrayList;
import java.util.List;

@Slf4j
@Service
public class DealerPlusInventoryService {

    @Autowired
    private InventoryDao inventoryDao;

    @Autowired
    private MasterPartDao masterPartDao;

    @Autowired
    private ManufacturerProgressDao manufacturerProgressDao;

    @Autowired
    private ManufacturerSubEntityDao manufacturerSubEntityDao;

    @Autowired
    private UserDetailsDao userDetailsDao;

    @Autowired
    S3StorageClient s3StorageClient;

    @Async
    public void savePrice(User dealerUser, InventoryFile inventoryFile) {
        log.info("Processing Inventory price file with headers [{}] and [{}] updates", inventoryFile.getHeaders(),
                inventoryFile.getDataRows().size());
        ManufacturerProgress progress = new DealerPlusMasterPartInventoryUploadProgress(dealerUser.getAssociatedManufacturerId(), dealerUser.getManufacturerSubEntityId());
        int progressId = manufacturerProgressDao.createManufacturerProgress(progress);
        progress.setId(progressId);
        try {
            List<MasterPart> dealerMasterPartList = processPriceFile(dealerUser.getAssociatedManufacturerId(), inventoryFile);
            Instant startDbInsert = Instant.now();
            inventoryDao.insertDealerInventoryPriceData(dealerMasterPartList, dealerUser.getManufacturerSubEntityId());
            log.info("Translation file processed. Db operation took {} milliseconds", Instant.now().minusMillis(startDbInsert.toEpochMilli()).toEpochMilli());
            progress.complete();
        } catch (Exception ex) {
            progress.error();
        } finally {
            manufacturerProgressDao.updateManufacturerProgress(progress);
        }
    }

    private List<MasterPart> processPriceFile(int manufacturerId, InventoryFile inventoryFile) {
        List<MasterPart> partList = new ArrayList<>();

        Integer pricePosition = inventoryFile.getHeaders().getPricePosition();

        for (String[] currentRow : inventoryFile.getDataRows()) {
            String partNumber = currentRow[0];
            Float priceValue = (currentRow[pricePosition] != null && !currentRow[pricePosition].isEmpty()) ? Float.parseFloat(currentRow[pricePosition]) : null;

            if (priceValue != null) {
                MasterPart part = masterPartDao.getBasicMasterPartByPartNumber(partNumber, manufacturerId);
                if (part != null) {
                    //If part found in manufacturer can be added/updated as a dealer part
                    part.setPrice(priceValue);
                    partList.add(part);
                }
            }
        }

        return partList;
    }

    @Async
    public void downloadMasterPartInventory(Integer dealerEntityId) throws Exception {
        ManufacturerSubEntity dpEntity = manufacturerSubEntityDao.getManufacturerSubEntity(dealerEntityId);
        ManufacturerProgress progress = new DealerPlusMasterPartInventoryDownloadProgress(dpEntity.getManufacturerId(), dealerEntityId);
        int progressId = manufacturerProgressDao.createManufacturerProgress(progress);
        progress.setId(progressId);

        StringBuilder csvBuilder = generateHeaders();
        List<MasterPart> masterParts = masterPartDao.getDealerMasterPartsForManufacturerAndDealerId(dpEntity.getManufacturerId(), dealerEntityId);

        for (int i = 0; i < masterParts.size(); i++) {
            csvBuilder.append("\"" + masterParts.get(i).getPartNumber().trim() + "\",");
            csvBuilder.append(((masterParts.get(i).getPrice() != null) ? masterParts.get(i).getPrice() : ""));

            csvBuilder.append("\n");
        }

        try {
            String url = s3StorageClient.uploadCSVForMasterPartInventory(csvBuilder.toString(), dpEntity.getManufacturerId());
            progress.setS3Url(url);
            progress.complete();
        } catch (Exception ex) {
            progress.error();
        }
        manufacturerProgressDao.updateManufacturerProgress(progress);
    }

    private StringBuilder generateHeaders() {
        StringBuilder csvHeadersBuilder = new StringBuilder();
        String PART_NUMBER_COLUMN_NAME = "Part Number";
        csvHeadersBuilder.append("\"" + PART_NUMBER_COLUMN_NAME);

        csvHeadersBuilder.append("\",\"Price")
                .append("\"")
                .append("\n");
        return csvHeadersBuilder;
    }

    public String downloadMasterPartInventoryTemplate() {
        StringBuilder csvBuilder = generateHeaders();
        return csvBuilder.toString();
    }
}
