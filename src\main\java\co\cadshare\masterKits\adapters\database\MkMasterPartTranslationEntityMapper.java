package co.cadshare.masterKits.adapters.database;

import co.cadshare.masterKits.core.MasterPartPriceListItem;
import co.cadshare.masterParts.core.Translation;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

@Mapper
public interface MkMasterPartTranslationEntityMapper {

    MkMasterPartTranslationEntityMapper Instance = Mappers.getMapper(MkMasterPartTranslationEntityMapper.class);

    @Mapping(source="language.id", target="languageId")
    @Mapping(source="language.code", target="languageCode")
    @Mapping(source="language.display", target="displayText")
    Translation entityToCore(MkMasterPartTranslationEntity entity);

    @Mapping(source="languageId", target="language.id")
    @Mapping(source="languageCode", target="language.code")
    @Mapping(source="displayText", target="language.display")
    MkMasterPartTranslationEntity coreToEntity(Translation core);
}
