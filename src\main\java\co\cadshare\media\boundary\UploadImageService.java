package co.cadshare.media.boundary;

import co.cadshare.media.core.Image;
import co.cadshare.shared.boundary.MediaQueryPort;
import co.cadshare.shared.boundary.S3StoragePort;
import co.cadshare.shared.core.user.User;
import org.springframework.stereotype.Service;

@Service
public class UploadImageService {

	private final S3StoragePort storagePort;
	private final MediaQueryPort mediaPort;

	public UploadImageService(S3StoragePort storagePort, MediaQueryPort mediaPort) {
		this.storagePort = storagePort;
		this.mediaPort = mediaPort;
	}

	public Integer execute(User user, Image image) throws Exception {
		image.validate();
		String imageUrl =  storagePort.uploadImage(user, image);
		image.setLocationUrl(imageUrl);
		return mediaPort.create(user, image);
	}
}
