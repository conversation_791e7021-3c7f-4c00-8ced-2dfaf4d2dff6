package co.cadshare.api.manufacturerSubEntity;

import co.cadshare.shared.core.manufacturer.subentity.ManufacturerSubEntity;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class Company {
  private int companyId;
  private String name;
  private String type;

  public static Company createFrom(ManufacturerSubEntity subEntity) {
    Company company = Company.builder()
        .companyId(subEntity.getManufacturerSubEntityId())
        .name(subEntity.getName())
        .type(subEntity.getManufacturerSubEntityType().toString())
        .build();
    return company;
  }
}
