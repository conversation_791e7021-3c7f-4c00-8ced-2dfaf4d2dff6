package co.cadshare.publications.adapters.database;

import co.cadshare.publications.core.Product;
import co.cadshare.publications.core.Viewable;
import co.cadshare.shared.core.user.User;
import co.cadshare.publications.boundary.*;
import co.cadshare.publications.core.Manual;
import co.cadshare.publications.core.Publication;
import co.cadshare.shared.boundary.QueryFilter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class PublicationsDataFacade implements PublicationCommandPort, PublicationQueryPort, ViewableQueryPort, ProductCommandPort {

    private final ManualCommandPort manualCommandPort;
    private final ManualQueryPort manualQueryPort;
    private final PublicationsRepo publicationsRepo;
    private final PublicationsComplexQueryRepo publicationsQueryRepo;
    private final PublicationsViewableQueryRepo viewableQueryRepo;

    @Autowired
    public PublicationsDataFacade(ManualCommandPort manualCommandPort,
                                  ManualQueryPort manualQueryPort,
                                  PublicationsRepo publicationRepo,
                                  PublicationsComplexQueryRepo publicationsQueryRepo,
                                  PublicationsViewableQueryRepo viewableQueryRepo) {
        this.manualCommandPort = manualCommandPort;
        this.manualQueryPort = manualQueryPort;
        this.publicationsRepo = publicationRepo;
        this.publicationsQueryRepo = publicationsQueryRepo;
        this.viewableQueryRepo = viewableQueryRepo;
    }

    @Override
    public Publication get(Integer id) {
        PublicationsPublicationEntity entity = this.publicationsRepo.getOne(id);
        return PublicationEntityMapper.Instance.entityToCore(entity);
    }

    @Override
    public List<Publication> getList() {
        List<PublicationsPublicationEntity> entities = this.publicationsRepo.findAll();
        return PublicationEntityMapper.Instance.entitiesToCores(entities);
    }

    public List<Publication> getList(QueryFilter filter) {
        List<Manual> manuals = this.manualQueryPort.getFilteredList(filter);
        return ManualMapper.Instance.manualsToPublications(manuals);
    }

    @Override
    public Integer create(User user, Publication publication) {
        PublicationsPublicationEntity savedEntity = this.publicationsRepo.save(PublicationEntityMapper.Instance.coreToEntity(publication));
        return savedEntity.getId();
    }

    @Override
    public void update(User user, Publication publication) throws Exception {
        this.publicationsRepo.save(PublicationEntityMapper.Instance.coreToEntity(publication));
    }

    @Override
    public void delete(User user, Publication publication) throws Exception {
        this.publicationsRepo.delete(PublicationEntityMapper.Instance.coreToEntity(publication));
    }

    @Override
    public List<Publication> getPublicationsForManufacturer(Integer manufacturerId) {
        List<PublicationsPublicationEntity> publications = this.publicationsQueryRepo.getPublicationsForManufacturer(manufacturerId);
        return PublicationEntityMapper.Instance.entitiesToCores(publications);
    }

    @Override
    public Viewable getViewable(int viewableId) {
        PublicationsViewableEntityView viewableEntity = this.viewableQueryRepo.getById(viewableId);
        return PublicationsViewableEntityViewMapper.Instance.entityToCore(viewableEntity);
    }

    @Override
    public void updateProduct(User user, Product product) throws Exception {

    }
}
