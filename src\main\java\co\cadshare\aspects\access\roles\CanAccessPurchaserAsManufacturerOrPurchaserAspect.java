package co.cadshare.aspects.access.roles;

import co.cadshare.aspects.BaseAspect;
import co.cadshare.exceptions.ForbiddenException;
import co.cadshare.services.ManufacturerService;
import co.cadshare.shared.core.manufacturer.subentity.ManufacturerSubEntity;
import co.cadshare.utils.ObjectUtilsExtension;
import lombok.experimental.ExtensionMethod;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

@Aspect
@Component
@ExtensionMethod(ObjectUtilsExtension.class)
public class CanAccessPurchaserAsManufacturerOrPurchaserAspect extends BaseAspect {


	@Autowired
	ManufacturerService manufacturerService;

	@Pointcut("@annotation(CanAccessPurchaserAsManufacturerOrPurchaser)")
	public void serviceLogPointcut(){}

	@Before("serviceLogPointcut()")
	public void checkUser(JoinPoint joinPoint) throws ForbiddenException {
		configureInterception(joinPoint);
		Object arg = getArgumentByName(joinPoint, "purchaserId");
		if (arg != null) {
			int purchaserId = (int)arg;
			List<ManufacturerSubEntity> dealers = manufacturerService.getManufacturerSubEntitiesForManufacturer(user.getManufacturerId(), ManufacturerSubEntity.ManufacturerSubEntityType.DEALER);
			if (!user.getManufacturerSubEntityId().equals(purchaserId)) {
				if(dealers.stream().noneMatch(dealer -> dealer.getManufacturerSubEntityId() == purchaserId)) {
					List<ManufacturerSubEntity> dealerPluses = manufacturerService.getManufacturerSubEntitiesForManufacturer(user.getManufacturerId(), ManufacturerSubEntity.ManufacturerSubEntityType.DEALER_PLUS);
					if (dealerPluses.stream().noneMatch(dealer -> dealer.getManufacturerSubEntityId() == purchaserId)) {
						List<ManufacturerSubEntity> customers = manufacturerService.getManufacturerSubEntitiesForManufacturer(user.getManufacturerId(), ManufacturerSubEntity.ManufacturerSubEntityType.CUSTOMER);
						if (customers.stream().noneMatch(dealer -> dealer.getManufacturerSubEntityId() == purchaserId)) {
							List<ManufacturerSubEntity> offices = manufacturerService.getManufacturerSubEntitiesForManufacturer(user.getManufacturerId(), ManufacturerSubEntity.ManufacturerSubEntityType.REGIONAL_OFFICE);
							if (offices.stream().noneMatch(dealer -> dealer.getManufacturerSubEntityId() == purchaserId))
								throw new ForbiddenException("User does not have permission to access this purchaser's data.");
                        }
                    }
				}
			}
		}
	}
}
