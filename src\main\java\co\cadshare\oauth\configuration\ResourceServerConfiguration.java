package co.cadshare.oauth.configuration;

import org.springframework.context.annotation.Configuration;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.oauth2.config.annotation.web.configuration.EnableResourceServer;
import org.springframework.security.oauth2.config.annotation.web.configuration.ResourceServerConfigurerAdapter;

@Configuration
@EnableResourceServer
public class ResourceServerConfiguration extends ResourceServerConfigurerAdapter {

    @Override
    public void configure(final HttpSecurity http) throws Exception {

        http.authorizeRequests()
            .antMatchers("/oauth/**").permitAll()
            .antMatchers("/preAuth/**").permitAll()
            .antMatchers("/api/preAuth/**").permitAll()
            .antMatchers("/user/password/**").permitAll()
            .antMatchers("/user/manufacturers").permitAll()
            .antMatchers("/manufacturer/subdomain/**").permitAll()
            .antMatchers("/security/user/**").permitAll()
            .antMatchers("/actuator/**").permitAll()
            .antMatchers("/health/**").permitAll()
            .antMatchers("/info/**").permitAll()
            .antMatchers("/register/**").permitAll()
            .antMatchers("/swagger-ui/**").permitAll()
            .antMatchers("/v3/api-docs/**").permitAll()
                .antMatchers("/api/manufacturers/**/ranges").access("#oauth2.hasScope('ext-api')")
                .antMatchers("/api/manufacturers/**/ranges/**").access("#oauth2.hasScope('ext-api')")
            .anyRequest().authenticated();
    }
}

