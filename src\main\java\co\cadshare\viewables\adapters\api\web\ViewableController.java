/*
 * Copyright 2016 Bell.
 */
package co.cadshare.viewables.adapters.api.web;

import co.cadshare.models.core.model.viewable.Viewable;
import co.cadshare.models.core.model.viewable.ViewableType;
import co.cadshare.shared.core.user.User;
import co.cadshare.services.ViewableService;

import lombok.extern.slf4j.Slf4j;

import org.springframework.security.access.prepost.PostAuthorize;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.*;

@Slf4j
@RestController
@RequestMapping("/viewable")
public class ViewableController {

    private ViewableService viewableService;

    public ViewableController(ViewableService viewableService) {
        this.viewableService = viewableService;
    }

    @PostMapping(consumes = "application/json")
    @PreAuthorize("@permissionsDao.hasAccessToModel(#currentUser.getUserId(), #viewable.getModelId())")
    public Integer create3dModelState(@AuthenticationPrincipal User currentUser, @RequestBody Viewable viewable) {
        log.info("ACCESS: User [{}], create3dModelState, model viewable [{}]", currentUser.accessDetails(), viewable.toString());
        viewable.setCreatedByUserId(currentUser.getUserId());
        viewable.setModifiedByUserId(currentUser.getUserId());
        viewable.setType(ViewableType.CORE);
        int viewableId = viewableService.createViewable(viewable);

        log.info("Model State with id [{}] created", viewableId);
        return viewableId;
    }

    @PutMapping(consumes = "application/json")
    @PreAuthorize("@permissionsDao.hasAccessToModel(#currentUser.getUserId(), #viewable.getModelId())")
    public void updateModelState(@AuthenticationPrincipal User currentUser, @RequestBody Viewable viewable) {

        log.info("ACCESS: User [{}], updateViewable, model viewable [{}]", currentUser.accessDetails(), viewable.toString());
        viewable.setModifiedByUserId(currentUser.getUserId());
        viewableService.updateViewable(viewable);

        log.info("Model State with id [{}] updated", viewable.getId());
    }


    @GetMapping(value = "/model/{modelId}")
    @PostAuthorize("@permissionsDao.hasAccessToModel(#currentUser.getUserId(), returnObject.getModelId())")
    public Viewable getViewableByModelId(@AuthenticationPrincipal User currentUser, @PathVariable int modelId) {
        log.info("ACCESS: User [{}], getModelStateByModelId, model ID: [{}]", currentUser.accessDetails(), modelId);
        return viewableService.getStatesForModel(modelId);
    }

    @PutMapping(consumes = "application/json", value = "/settings/{viewableId}")
    public void updateViewerSettings(@AuthenticationPrincipal User currentUser, @PathVariable int viewableId, @RequestBody Viewable viewable) {
        viewable.setModifiedByUserId(currentUser.getUserId());
        viewableService.updateViewableSettings(viewableId, viewable);

        log.info("Settings for viewable with id [{}] updated", viewableId);
    }
}
