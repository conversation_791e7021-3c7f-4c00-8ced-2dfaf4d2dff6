package co.cadshare.modelMgt.ranges.core;

import co.cadshare.modelMgt.ranges.core.Range;
import org.junit.Before;
import org.junit.Test;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;

public class RangeTest {

    private Range out;

    @Before
    public void Before() {
        this.out = new Range();
    }

    @Test
    public void CheckRangeIsNotNullTest() {
        assertTrue(out != null);
    }
}