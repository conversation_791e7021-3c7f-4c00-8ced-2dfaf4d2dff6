<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                   http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.0.xsd
">

    <!-- Caterpillar -->
    <changeSet author="AndyB" id="1.13-integration-test-data-create-manufacturer-settings-1">
        <sql>
            INSERT INTO public.manufacturersettings(
            manufacturerid, pricelistsenabled, stockwarehousesenabled)
            VALUES (
            1,          -- manufacturerid
            FALSE,      -- pricelistsenabled
            FALSE       -- stockwarehousesenabled
            );
        </sql>
    </changeSet>

    <!-- JCB -->
    <changeSet author="AndyB" id="1.13-integration-test-data-create-manufacturer-settings-2">
        <sql>
            INSERT INTO public.manufacturersettings(
            manufacturerid, pricelistsenabled, stockwarehousesenabled)
            VALUES (
            2,          -- manufacturerid
            TRUE,       -- pricelistsenabled
            FALSE       -- stockwarehousesenabled
            );
        </sql>
    </changeSet>

    <!-- Liebherr -->
    <changeSet author="AndyB" id="1.13-integration-test-data-create-manufacturer-settings-3">
        <sql>
            INSERT INTO public.manufacturersettings(
            manufacturerid, pricelistsenabled, stockwarehousesenabled)
            VALUES (
            3,          -- manufacturerid
            FALSE,      -- pricelistsenabled
            TRUE        -- stockwarehousesenabled
            );
        </sql>
    </changeSet>

    <!-- Terex -->
    <changeSet author="AndyB" id="1.13-integration-test-data-create-manufacturer-settings-4">
        <sql>
            INSERT INTO public.manufacturersettings(
            manufacturerid, pricelistsenabled, stockwarehousesenabled)
            VALUES (
            4,          -- manufacturerid
            TRUE,       -- pricelistsenabled
            TRUE        -- stockwarehousesenabled
           );
        </sql>
    </changeSet>

</databaseChangeLog>
