<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.0.xsd
        http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd">

  <changeSet author="AndyB" id="57.3-migrate-range-to-publicationcategory">
      <sql>
          UPDATE public.manual man2
          SET publicationcategoryid = (
          SELECT MIN(pc.id)
          FROM publicationcategory pc,
          manual man,
          manualmodelmap mmm,
          model mo,
          machine mac,
          range r
          WHERE pc.name = r.name
          AND pc.manufacturerid = r.manufacturerid
          AND mac.rangeid = r.rangeid
          AND mo.machineid = mac.machineid
          AND mmm.modelid = mo.modelid
          AND man.manualid = mmm.manualid
          AND pc.manufacturerid = man.manufacturerid
          AND man.manualid = man2.manualid
          GROUP BY man.manualid)
      </sql>
  </changeSet>

</databaseChangeLog>
