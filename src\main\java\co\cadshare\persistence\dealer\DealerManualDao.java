/*
 * Copyright 2016 Bell.
 */
package co.cadshare.persistence.dealer;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.namedparam.MapSqlParameterSource;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.stereotype.Repository;

import java.sql.Types;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Repository
public class DealerManualDao {

  @Autowired
  private NamedParameterJdbcTemplate namedParamJdbcTemplate;

  private static final String DELETE_MANUALS_FOR_DEALERS_CUSTOMERS = "DELETE FROM manufacturersubentitymanualmap " +
          "WHERE manualid IN (:manualIds) " +
          "AND manufacturersubentityid IN (SELECT manufacturersubentityid FROM manufacturersubentity WHERE parentsubentityid = :dealerEntityId) ";

  public void unassignManualIdsFromDealerCustomers(int dealerEntityId, List<Integer> manualIdsToBeUnassigned) {
    MapSqlParameterSource namedParameters = new MapSqlParameterSource();
    namedParameters.addValue("dealerEntityId", dealerEntityId, Types.INTEGER);
    namedParameters.addValue("manualIds", manualIdsToBeUnassigned, Types.INTEGER);

    namedParamJdbcTemplate.update(DELETE_MANUALS_FOR_DEALERS_CUSTOMERS, namedParameters);
  }

  private static final String GET_ASSIGNED_RANGE_IDS_FROM_MANUFACTURER_SUB_ENTITY_RANGE_MAP = "SELECT DISTINCT(rangeid) "
          + "FROM manufacturersubentityrangemap  "
          + "WHERE manufacturersubentityid = :manufacturerSubEntityId ";

  public List<Integer> getAssignedRangeIdsForManufacturerSubEntity(int manufacturerSubEntityId) {
    Map<String, Object> parameters = new HashMap<String, Object>();
    parameters.put("manufacturerSubEntityId", manufacturerSubEntityId);

    return namedParamJdbcTemplate.queryForList(GET_ASSIGNED_RANGE_IDS_FROM_MANUFACTURER_SUB_ENTITY_RANGE_MAP,
            parameters, Integer.class);
  }

  private static final String GET_MANUAL_IDS_FOR_RANGE_IDS = "SELECT DISTINCT man.manualid "
          + "FROM ManufacturerSubEntityRangeMap msesnm "
          + "INNER JOIN range r ON r.rangeid = msesnm.rangeid "
          + "LEFT JOIN machine mac ON mac.rangeid = r.rangeid "
          + "LEFT JOIN model mod ON mod.machineid = mac.machineid "
          + "LEFT JOIN manualmodelmap mmm ON mmm.modelid = mod.modelid "
          + "LEFT JOIN manual man ON man.manualid = mmm.manualid "
          + "WHERE r.rangeid IN (:rangeIds) "
          + "AND man.manualid IS NOT NULL ";

  public List<Integer> getManualIdsforRangeIds(List<Integer> rangeIds) {
    Map<String, Object> parameters = new HashMap<String, Object>();
    parameters.put("rangeIds", rangeIds);

    List<Integer> manualIds = namedParamJdbcTemplate.queryForList(GET_MANUAL_IDS_FOR_RANGE_IDS, parameters, Integer.class);

    return manualIds;
  }
}