package co.cadshare.models.core;

import co.cadshare.models.core.model.AutodeskStatus;
import co.cadshare.models.core.processor.PropertiesProcessor;
import co.cadshare.shared.core.manufacturer.ManufacturerConfig;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.ArrayList;
import java.util.List;

import static org.junit.Assert.*;
import static org.mockito.Mockito.when;

public class MetadataParserTest {

    @Mock
    private MetadataWrapper mockMetadata;
    @Mock
    private MetadataDataExtended mockMetadataData;
    @Mock
    private PropertiesProcessor mockPropertiesProcessor;
    @Mock
    private ManufacturerConfig mockConfig;
    @Mock
    private MetadataObjectExtended mockMetadataObject;
    private Model model;
    private MetadataParser out;

    @Before
    public void Before() {
        MockitoAnnotations.initMocks(this);
        model = new Model();
        out = new MetadataParser(model, mockMetadata, mockPropertiesProcessor);
    }

    @Test
    public void metadataIsNotParseable() throws Exception {
        when(mockMetadata.isParseable()).thenReturn(false);
        assertFalse(out.isGuidProvidedByMetadata());
        assertEquals(1, model.getRetries());
    }

    @Test
    public void metadataIsParsableButGuidNotProvided() throws Exception {
        when(mockMetadata.isParseable()).thenReturn(true);
        when(mockMetadata.isGuidProvidedByMetadata()).thenReturn(false);
        assertFalse(out.isGuidProvidedByMetadata());
        assertEquals(0, model.getRetries());
    }

    @Test
    public void metadataIsParseableAndGuidProvided() throws Exception {
        when(mockMetadata.isParseable()).thenReturn(true);
        when(mockMetadata.isGuidProvidedByMetadata()).thenReturn(true);
        assertTrue(out.isGuidProvidedByMetadata());
        assertEquals(0, model.getRetries());
    }

    @Test
    public void getGuidFromMetadataSuccess() throws Exception {
        when(mockMetadata.isParseable()).thenReturn(true);
        when(mockMetadata.isGuidProvidedByMetadata()).thenReturn(true);
        when(mockMetadata.getGuid()).thenReturn("guid-from-metadata");
        assertEquals("guid-from-metadata", out.retrieveGuidFromMetadata());
    }

    @Test
    public void getGuidFromMetadataFailsWhenNotParseable() throws Exception {
        when(mockMetadata.isParseable()).thenReturn(false);
        assertNull(out.retrieveGuidFromMetadata());
    }

    @Test
    public void getGuidFromMetadataFailsWhenParseableButNoGuidProvided() throws Exception {
        when(mockMetadata.isParseable()).thenReturn(true);
        when(mockMetadata.isGuidProvidedByMetadata()).thenReturn(false);
        assertNull(out.retrieveGuidFromMetadata());
    }

    @Test
    public void guidMetadataIsNotParseable() throws Exception {
        when(mockMetadata.isParseable()).thenReturn(false);
        assertFalse(out.guidMetadataIsParseable(mockMetadata));
        assertEquals(1, model.getRetries());
    }

    @Test
    public void guidMetadataIsParsable() throws Exception {
        when(mockMetadata.isParseable()).thenReturn(true);
        assertTrue(out.guidMetadataIsParseable(mockMetadata));
        assertEquals(0, model.getRetries());
    }

    @Test
    public void parseMetadataForGuidSuccess() throws Exception {
        when(mockMetadata.isParseable()).thenReturn(true);
        List<MetadataObjectExtended> flatList = new ArrayList<>();
        when(mockMetadataObject.isValid()).thenReturn(true);
        flatList.add(mockMetadataObject);
        when(mockMetadataData.getObjects()).thenReturn(flatList);
        when(mockPropertiesProcessor.getCombinedProperties(mockMetadata, mockMetadata, mockConfig))
                .thenReturn(mockMetadataData);
        when(mockPropertiesProcessor.toFlatList(flatList)).thenReturn(flatList);
        ParsedMetadata parsedMetadata = out.parseMetadataForGuid(mockMetadata,
                mockMetadata,
                mockConfig,
                "guid");
        assertNotNull(parsedMetadata);
        assertTrue(parsedMetadata.metadataToBeUploaded());
        assertEquals(1, parsedMetadata.get().size());
        assertEquals(0, model.getRetries());
    }

    @Test
    public void parseMetadataForGuidFailsNoMetadataReadyToBeParsed() throws Exception {
        when(mockMetadata.isParseable()).thenReturn(true);
        when(mockPropertiesProcessor.getCombinedProperties(mockMetadata, mockMetadata, mockConfig))
                .thenReturn(mockMetadataData);
        ParsedMetadata parsedMetadata = out.parseMetadataForGuid(mockMetadata,
                mockMetadata,
                mockConfig,
                "guid");
        assertNotNull(parsedMetadata);
        assertFalse(parsedMetadata.metadataToBeUploaded());
        assertEquals(1, model.getRetries());
    }

    @Test
    public void parseMetadataForGuidFailsInvalidMetadata() throws Exception {
        when(mockMetadata.isParseable()).thenReturn(true);
        when(mockPropertiesProcessor.getCombinedProperties(mockMetadata, mockMetadata, mockConfig))
                .thenReturn(mockMetadataData);
        List<MetadataObjectExtended> flatList = new ArrayList<>();
        when(mockMetadataObject.isValid()).thenReturn(false);
        flatList.add(mockMetadataObject);
        when(mockMetadataData.getObjects()).thenReturn(flatList);
        when(mockPropertiesProcessor.getCombinedProperties(mockMetadata, mockMetadata, mockConfig))
                .thenReturn(mockMetadataData);
        when(mockPropertiesProcessor.toFlatList(flatList)).thenReturn(flatList);
        ParsedMetadata parsedMetadata = out.parseMetadataForGuid(mockMetadata,
                mockMetadata,
                mockConfig,
                "guid");
        assertNotNull(parsedMetadata);
        assertFalse(parsedMetadata.isValid());
        assertEquals(AutodeskStatus.FAILED, model.getAutodeskStatus());
    }
}
