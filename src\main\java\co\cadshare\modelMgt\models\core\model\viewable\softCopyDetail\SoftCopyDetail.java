package co.cadshare.modelMgt.models.core.model.viewable.softCopyDetail;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.sql.Timestamp;

@JsonInclude(Include.NON_NULL)
@Data
@NoArgsConstructor
public class SoftCopyDetail {
    private Integer id;
    private Integer stateDetailId;
    private String highResImgUrl;
    private String markedUpParts;
    private Integer createdByUserId;
    private Timestamp createdDate;
    private Integer modifiedByUserId;
    private Timestamp modifiedDate;

    @Builder
    public SoftCopyDetail(Integer id, int stateDetailId, String highResImgUrl, String markedUpParts, Integer createdByUserId, Timestamp createdDate, Integer modifiedByUserId,
                          Timestamp modifiedDate) {
        this.id = id;
        this.stateDetailId = stateDetailId;
        this.highResImgUrl = highResImgUrl;
        this.markedUpParts = markedUpParts;
        this.createdByUserId = createdByUserId;
        this.createdDate = createdDate;
        this.modifiedByUserId = modifiedByUserId;
        this.modifiedDate = modifiedDate;
    }
}


