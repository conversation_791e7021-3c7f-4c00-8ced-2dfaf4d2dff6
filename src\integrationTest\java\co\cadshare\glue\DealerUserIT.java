package co.cadshare.glue;

import co.cadshare.domainmodel.part.Part;
import co.cadshare.domainmodel.part.PartViewerDetails;
import co.cadshare.masterKits.adapters.api.web.PostMasterKitsSearchRequestDto;
import co.cadshare.masterKits.adapters.api.web.PostMasterKitsSearchResponseDto;
import co.cadshare.masterParts.adapters.api.web.PostPurchaserSearchMasterPartsDto;
import co.cadshare.masterParts.adapters.api.web.SupersessionHistoryDto;
import co.cadshare.masterParts.boundary.MasterPartSearchResult;
import co.cadshare.masterParts.core.MasterPartDetails;
import co.cadshare.modelMgt.models.core.Model;
import co.cadshare.modelMgt.publications.adapters.api.web.GetPublicationListItemResponseDto;
import co.cadshare.modelMgt.publications.adapters.api.web.GetPublicationResponseDto;
import co.cadshare.modelMgt.publications.adapters.api.web.GetPublicationsListResponseDto;
import co.cadshare.modelMgt.publications.core.Manual;
import co.cadshare.response.CustomerManual;
import io.cucumber.spring.ScenarioScope;
import lombok.EqualsAndHashCode;
import org.springframework.stereotype.Component;

import javax.persistence.EntityManager;
import java.util.List;
import java.util.Optional;

import static org.junit.Assert.*;

@Component
@ScenarioScope
@EqualsAndHashCode(callSuper = true)
public class DealerUserIT extends UserIT {
    private Integer purchaserId;

	public DealerUserIT(String port, String clientId, String secret, EntityManager em) {
		super(port, clientId, secret, em);
	}

    @Override
    public void logIn(String emailAddress) {
        user = logInUsingEmailAddress(emailAddress);
        purchaserId = user.getManufacturerSubEntityId();
    }

	@Override
	public MasterPartSearchResult exactSearchForMasterPartByNumber(String identifiedPartNumber) {
		PostPurchaserSearchMasterPartsDto request = new PostPurchaserSearchMasterPartsDto();
		request.setPartNumber(identifiedPartNumber);
		request.setExactMatch(true);
		return searchForMasterPartUsingRequest(request, "EN");
	}

	@Override
    public MasterPartSearchResult searchForMasterPartByNumber(String identifiedPartNumber){
		PostPurchaserSearchMasterPartsDto request = new PostPurchaserSearchMasterPartsDto();
		request.setPartNumber(identifiedPartNumber);
        return searchForMasterPartUsingRequest(request, "EN");
    }

	@Override
	public MasterPartSearchResult searchForMasterPartByNumberUsingLanguage(String identifiedPartNumber, String languageCode) {
		PostPurchaserSearchMasterPartsDto request = new PostPurchaserSearchMasterPartsDto();
		request.setPartNumber(identifiedPartNumber);
		return searchForMasterPartUsingRequest(request, languageCode);
	}

	@Override
	public MasterPartSearchResult searchForMasterPartByDescriptionUsingLanguage(String identifiedPartDesc, String languageCode) {
		PostPurchaserSearchMasterPartsDto request = new PostPurchaserSearchMasterPartsDto();
		request.setPartDescription(identifiedPartDesc);
		return searchForMasterPartUsingRequest(request, languageCode);
	}

    @Override
    public int searchForMasterKit(String identifiedPartNumber){
        String sql = String.format("/purchasers/%s/master-part-kits/search?language=EN", purchaserId);
        PostMasterKitsSearchRequestDto request = new PostMasterKitsSearchRequestDto();
        request.setPartNumber(identifiedPartNumber);
        PostMasterKitsSearchResponseDto responseDto = actionResource(sql, request, PostMasterKitsSearchResponseDto.class);
        return responseDto.getKits().get(0).getKitId();
    }

	@Override
	public PostMasterKitsSearchResponseDto searchForMasterKitByNumber(String identifiedPartNumber){
		return searchForMasterKitByNumber("purchasers", identifiedPartNumber, "EN", purchaserId);
	}

	@Override
	public PostMasterKitsSearchResponseDto searchForMasterKitByDescriptionUsingLanguage(String masterKitDescSearch, String languageCode) {
		return searchForMasterKitByDesc("purchasers", masterKitDescSearch, languageCode, purchaserId);
	}

	@Override
    public SupersessionHistoryDto getSupersessionHistoryForPart(String supersessionMasterPart) {
        String sql = String.format("purchasers/%s/master-parts/%s/supersession-history",
                purchaserId, supersessionMasterPart);
         return getResource(sql, SupersessionHistoryDto.class);
    }

    public void verifyPublicationExists(Model model, String publicationName) {
        Optional<Manual> publication = getPublication(model, publicationName);
        assertTrue(publication.isPresent());
    }

	@Override
	public void verifyPublicationExists(String publicationName) {
		Optional<GetPublicationListItemResponseDto> publication = getGetPublicationListItemResponseDto(publicationName);
		assertTrue(publication.isPresent());
	}

	private Optional<GetPublicationListItemResponseDto> getGetPublicationListItemResponseDto(String publicationName) {
		String url = String.format("/purchasers/%s/publications", purchaserId);
		GetPublicationsListResponseDto response = getResource(url, GetPublicationsListResponseDto.class);
		assertFalse(response.getPublications().isEmpty());
		Optional<GetPublicationListItemResponseDto> publication = response.getPublications()
				.stream()
				.filter(p -> p.getName().equals(publicationName))
				.findFirst();
		return publication;
	}

	@Override
    public void publicationHasDefaultImage(Model model, String publicationName) {
        Optional<Manual> publication = getPublication(model, publicationName);
        assertTrue(publication.isPresent());
        assertNotNull(publication.get().getFeaturedModelUrl());
    }

    private Optional<Manual> getPublication(Model model, String publicationName) {
        String url = String.format("/model/%s/manuals", model.getModelId());
        List<Manual> manuals = getResourceList(url, Manual.class);
        assertFalse(manuals.isEmpty());
        Optional<Manual> publication = manuals
                .stream()
                .filter(p -> p.getManualName().equals(publicationName))
                .findFirst();
        return publication;
    }

	@Override
	public GetPublicationResponseDto getPublicationFromName(String publicationName) {
		int publicationId = getPublicationIdFromName(publicationName);
		String url = String.format("/purchasers/%s/publications/%s?language=EN", purchaserId, publicationId);
		return getResource(url, GetPublicationResponseDto.class);
	}

    public PartViewerDetails getPartViewerDetails(MasterPartDetails masterPart) {
        Part part = getPart(masterPart.getId());
        String sql = String.format("/model/%s/part/%s/viewerDetails?userType=%s",
                part.getModelId(), part.getObjectId(), "purchaser");
        return getResource(sql, PartViewerDetails.class);
    }

    @Override
    public PartViewerDetails getPartViewerDetailsOnBehalfOf(MasterPartDetails masterPart, int userId) {
        return getPartViewerDetailsOnBehalfOf(masterPart, userId, "dealer");
    }

    @Override
    public void failToGetPartViewerDetailsOnBehalfOf(MasterPartDetails masterPart, int userId) {
        failToGetPartViewerDetailsOnBehalfOf(masterPart, userId, "dealer");
    }

	@Override
	protected int getPublicationIdFromName(String publicationName) {
		List<CustomerManual> publications = getPublications();
		assertTrue(publications != null && !publications.isEmpty());
		Optional<CustomerManual> publicationExists = publications
				.stream()
				.filter(p -> p.getManualName().equals(publicationName))
				.findFirst();
		assertTrue(publicationExists.isPresent());
		return publicationExists.get().getManualId();
	}

	private List<CustomerManual> getPublications() {
		String url = String.format("/manufacturersubentity/%s/machines", purchaserId);
		return getResourceList(url, CustomerManual.class);
	}

	private MasterPartSearchResult searchForMasterPartUsingRequest(PostPurchaserSearchMasterPartsDto request, String languageCode){
		String sql = String.format("/manufacturer-sub-entities/%s/master-parts/search?language=%s", purchaserId, languageCode);
		return actionResource(sql, request, MasterPartSearchResult.class);
	}




}
