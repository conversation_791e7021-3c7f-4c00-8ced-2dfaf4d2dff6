package co.cadshare.addresses.adapters.database;

import co.cadshare.addresses.core.ExternalRegionalOffice;
import co.cadshare.addresses.core.ExternalPurchaser;
import co.cadshare.addresses.core.User;
import co.cadshare.utils.CycleAvoidingMappingContext;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper(uses = { ExternalAddressEntityMapper.class, AddressesUserEntityMapper.class})
public interface AddressesRegionalOfficeEntityMapper {

    AddressesRegionalOfficeEntityMapper Instance = Mappers.getMapper(AddressesRegionalOfficeEntityMapper.class);

    //CoreToEntity
    
    @Mapping(source="users", target="users", qualifiedByName="userCoreToEntity")
    AddressesRegionalOfficeEntity externalRegionalOfficeCoreToEntity(ExternalRegionalOffice  dealer);
    
    List<AddressesRegionalOfficeEntity> externalPurchaserCoresToRegionalOfficeEntities(List<ExternalPurchaser> dealers);

    
    //Entity To Core
    
    @Mapping(source="users", target="users", qualifiedByName="userEntityToCore")
    ExternalRegionalOffice  entityToExternalPurchaserCore(AddressesRegionalOfficeEntity dealer);

    List<ExternalRegionalOffice > entitiesToExternalPurchaserCores(List<AddressesRegionalOfficeEntity> dealers);
    
    
    //Named   

    @Named("userEntityToCore")
    public static User userEntityToCore(AddressesUserEntity entity) {
        return AddressesUserEntityMapper.Instance.entityToCore(entity, new CycleAvoidingMappingContext());
    }

    @Named("userCoreToEntity")
    public static AddressesUserEntity userCoreToEntity(User core) {
        return AddressesUserEntityMapper.Instance.coreToEntity(core, new CycleAvoidingMappingContext());
    }

}
