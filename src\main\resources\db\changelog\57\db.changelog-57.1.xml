<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.0.xsd
        http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd">

  <changeSet author="AndyB" id="57.1-create-publicationCategory-table-and-foreign-key">
    <preConditions onFail="MARK_RAN">
      <not>
        <tableExists tableName="publicationcategory" />
      </not>
    </preConditions>

    <createTable schemaName="public" tableName="publicationcategory">
      <column name="id" type="integer" autoIncrement="true">
        <constraints nullable="false" primaryKey="true" />
      </column>
      <column name="name" type="varchar(255)">
        <constraints nullable="false" />
      </column>
      <column name="manufacturerid" type="integer">
        <constraints nullable="false" />
      </column>
      <column name="deleted" type="boolean" defaultValue="false">
          <constraints nullable="false" />
      </column>
      <column name="createddate" type="timestamp">
        <constraints nullable="false" />
      </column>
      <column name="createdbyuserid" type="integer">
        <constraints nullable="false" />
      </column>
      <column name="modifieddate" type="timestamp">
        <constraints nullable="true" />
      </column>
      <column name="modifiedbyuserid" type="integer">
        <constraints nullable="true" />
      </column>
    </createTable>
    <sql>
      INSERT INTO public.publicationcategory(name, manufacturerid, createddate, createdbyuserid, modifieddate, modifiedbyuserid)
      (SELECT name, manufacturerid, NOW(), 0, NOW(), 0 FROM range WHERE deleted = false)
    </sql>
    <addColumn tableName="manual">
      <column name="publicationcategoryid" type="bigint">
          <constraints nullable="true" foreignKeyName="fk_manual_publicationcategory" references="publicationcategory(id)"/>
      </column>
    </addColumn>

  </changeSet>

</databaseChangeLog>
