package co.cadshare.inventory.adapters.api.ext;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import com.google.gson.JsonObject;

import co.cadshare.inventory.adapters.database.CsvProcessor;
import co.cadshare.inventory.boundary.SparePartIdentifierService;
import co.cadshare.inventory.core.BomUpload;
import co.cadshare.shared.core.user.User;
import co.cadshare.services.ManufacturerProgressService;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;

import static org.springframework.http.HttpStatus.ACCEPTED;
import static org.springframework.http.HttpStatus.BAD_REQUEST;

@Slf4j
@RestController
@RequestMapping("/api/georg")
public class SparePartIdentifierController {

    @Autowired
    private SparePartIdentifierService sparePartIdentifierService;

    @Autowired
    private ManufacturerProgressService manufacturerProgressService;
    
    @Autowired
    private CsvProcessor csvProcessor;

    @PostMapping(value = "/{modelId}/uploadSparePartIdentifiersFile")
    @PreAuthorize("hasRole('ROLE_MANUFACTURER')")
    public ResponseEntity<String> uploadSparePartIdentifiers(@AuthenticationPrincipal User currentUser,
                                                             @PathVariable int modelId,
                                                             @RequestParam("file") MultipartFile file,
                                                             @RequestParam(value = "partNumber", required = false) Integer partNumber,
                                                             @RequestParam(value = "sparePartIdentifier", required = false) Integer sparePartIdentifier) throws IOException, BomUpload.UnparseableModelPartsFileException {

        log.info("Uploading spare part identifiers from CSV upload");
        log.info("Received file [{}]", file.getName());
        try{
            BomUpload partsFile = csvProcessor.parseCsv(file);
            sparePartIdentifierService.uploadSparePartIdentifiers(modelId, partsFile, currentUser);
        } catch (Exception e) {
            JsonObject response = new JsonObject();
            response.addProperty("message", "Error processing file: " + e.getMessage());
            return new ResponseEntity<>(response.toString(), BAD_REQUEST);
        }
        return new ResponseEntity<>(ACCEPTED);
    }
}



