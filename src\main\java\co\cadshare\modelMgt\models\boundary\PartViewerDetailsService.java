package co.cadshare.modelMgt.models.boundary;

import co.cadshare.shared.core.Language;
import co.cadshare.domainmodel.part.Part;
import co.cadshare.domainmodel.part.PartViewerDetails;
import co.cadshare.masterParts.boundary.MasterPartQueryPort;
import co.cadshare.persistence.PartDao;
import co.cadshare.shared.core.user.User;
import co.cadshare.masterParts.adapters.database.MasterPartDao;
import co.cadshare.masterParts.adapters.database.MasterPartExtensionsDao;
import co.cadshare.masterParts.core.MasterPart;
import co.cadshare.masterParts.core.extensions.kit.Kit;
import co.cadshare.masterParts.core.extensions.kit.KitPart;
import co.cadshare.masterParts.core.extensions.nonModelled.NonModelled;
import co.cadshare.masterParts.core.extensions.nonModelled.NonModelledPart;
import co.cadshare.masterParts.core.extensions.optionsSet.OptionsSet;
import co.cadshare.masterParts.core.extensions.optionsSet.OptionsSetPart;
import co.cadshare.masterParts.core.extensions.partModelLink.PartModelLink;
import co.cadshare.persistence.ManufacturerSubEntityDao;
import co.cadshare.modelMgt.publications.boundary.ManualQueryPort;
import co.cadshare.services.*;
import co.cadshare.shared.core.manufacturer.subentity.ManufacturerSubEntity;
import co.cadshare.utils.MasterPartHelper;
import co.cadshare.utils.ObjectExtension;
import lombok.experimental.ExtensionMethod;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.support.TransactionTemplate;

import java.util.ArrayList;
import java.util.List;

@Slf4j
@Service
@ExtensionMethod(ObjectExtension.class)
public class PartViewerDetailsService {

    private final MasterPartDao masterPartDao;
    private final ManufacturerSubEntityDao manufacturerSubEntityDao;
    private final MasterPartExtensionsDao masterPartExtensionsDao;
    private final ManualQueryPort manualDao;
    private final NonModeledPartService nonModeledPartService;
    private final PartModelLinkService partLinkService;
    private final OptionsSetService optionsSetService;
    private final KitService kitService;
    private final MasterPartQueryPort masterPartQuery;
    private final PartDao partDao;
    private final MasterPartHelper masterPartHelper;
    private final TransactionTemplate transactionTemplate;

    @Autowired
    public PartViewerDetailsService(MasterPartDao masterPartDao,
                                    ManufacturerSubEntityDao manufacturerSubEntityDao,
                                    MasterPartExtensionsDao masterPartExtensionsDao,
                                    ManualQueryPort manualDao,
                                    NonModeledPartService nonModeledPartService,
                                    PartModelLinkService partLinkService,
                                    OptionsSetService optionsSetService,
                                    KitService kitService,
                                    MasterPartQueryPort masterPartQuery,
                                    PartDao partDao,
                                    MasterPartHelper masterPartHelper,
                                    PlatformTransactionManager transactionManager) {
        this.masterPartDao = masterPartDao;
        this.manufacturerSubEntityDao = manufacturerSubEntityDao;
        this.masterPartExtensionsDao = masterPartExtensionsDao;
        this.manualDao = manualDao;
        this.nonModeledPartService = nonModeledPartService;
        this.partLinkService = partLinkService;
        this.optionsSetService = optionsSetService;
        this.kitService = kitService;
        this.masterPartQuery = masterPartQuery;
        this.partDao = partDao;
        this.masterPartHelper = masterPartHelper;
        this.transactionTemplate = new TransactionTemplate(transactionManager);
    }

    public PartViewerDetails getPartViewerDetails(User currentUser,
                                                  Integer manualId,
                                                  int modelId,
                                                  int objectId,
                                                  Language language,
                                                  Integer onBehalfOfUserId) throws Exception {

        PartViewerDetails partViewerDetails = new PartViewerDetails();
        Integer languageId = language != null ? language.getLanguageId() : null;
        Part part = partDao.getPartForModelId(modelId, objectId, languageId);
	    if (part != null)
		    masterPartHelper.checkDefaultDescription(part, languageId, currentUser.obtainDefaultLanguage());
	    partViewerDetails.setPart(part);
	    MasterPart masterPart = null;
	    if(part.isMasterPartFound()) {
		    masterPart = currentUser.isManufacturer() ?
				    masterPartDao.getMasterPartByPartNumber(part.getPartNumber(), currentUser.getManufacturerId(), languageId) :
				    getMasterPartFromSupersededMasterPart(currentUser, languageId, part);
	    }
	    addDetails(currentUser,
			    manualId,
			    modelId,
			    objectId,
			    languageId,
			    currentUser.obtainDefaultLanguage(),
			    onBehalfOfUserId,
			    partViewerDetails,
			    part,
			    masterPart);

	    return partViewerDetails;
    }


    private void addDetails(User currentUser,
                            Integer manualId,
                            int modelId,
                            int objectId,
                            Integer languageId,
                            Language defaultLanguage,
                            Integer onBehalfOfUserId,
                            PartViewerDetails partViewerDetails,
                            Part part,
                            MasterPart masterPart) {

        Integer priceListIdentifierId = null;
        Integer dealerEntityId = null;
        Integer warehouseId = null;

        Integer entityId = determinePurchaser(currentUser, onBehalfOfUserId);

        if (entityId != null) {
            ManufacturerSubEntity subEntity = manufacturerSubEntityDao.getManufacturerSubEntity(entityId);
            if (currentUser.getManufacturerSettings().isPriceListsEnabled() && subEntity.getPriceListIdentifierId() != null)
                priceListIdentifierId = subEntity.getPriceListIdentifierId();

            if (currentUser.getManufacturerSettings().isStockWarehousesEnabled() && subEntity.getWarehouseId() != null)
                warehouseId = subEntity.getWarehouseId();

            if (subEntity.getParentSubEntityId() != null)
                dealerEntityId = subEntity.getParentSubEntityId();
        }

        augmentPart(partViewerDetails,
                modelId,
                objectId,
                currentUser.getManufacturerId(),
                manualId,
                languageId,
                defaultLanguage,
                part,
                masterPart,
                priceListIdentifierId,
                dealerEntityId,
                warehouseId,
                currentUser);

        if (!currentUser.canViewPrices())
            partViewerDetails.getPart().setPrice(null);
    }

    private MasterPart getMasterPartFromSupersededMasterPart(User currentUser, Integer languageId, Part part) {
        MasterPart masterPart = null;
        masterPart = masterPartQuery.getMasterPartByPartNumber(part.getPartNumber(), currentUser.getManufacturerId(), languageId);
        if(masterPart.isSuperseded())
            masterPart = masterPartQuery.getMasterPartByPartNumber(masterPart.getMaxSupersessionPartNumber(), currentUser.getManufacturerId(), languageId);
        part.applyMasterPart(masterPart);
        return masterPart;
    }

    private Integer determinePurchaser(User currentUser, Integer onBehalfOfUserId) {
        Integer entityId = null;
        if (onBehalfOfUserId != null)
            entityId = manufacturerSubEntityDao.getManufacturerSubEntityIdForCustomerUserId(onBehalfOfUserId);
        else if (currentUser.getManufacturerSubEntityId() != null)
            entityId = currentUser.getManufacturerSubEntityId();
        return entityId;
    }

    private void augmentPart(PartViewerDetails partViewerDetails,
                             int modelId,
                             int objectId,
                             int manufacturerId,
                             Integer manualId,
                             Integer languageId,
                             Language defaultLanguage,
                             Part part,
                             MasterPart masterPart,
                             Integer priceListIdentifierId,
                             Integer dealerEntityId,
                             Integer warehouseId,
                             User currentUser) {
        if (masterPart.isNotNull())
            augmentWithMasterPart(partViewerDetails, part, masterPart, priceListIdentifierId, dealerEntityId, warehouseId);

        if (masterPart.isNotNull() && masterPartExtensionsDao.hasNonModelledPart(masterPart.getMasterPartId()))
                augmentWithMasterPartNonModeledPart(partViewerDetails,
                        defaultLanguage,
                        masterPart,
                        currentUser,
                        languageId,
                        priceListIdentifierId,
                        dealerEntityId,
                        warehouseId);
        else {
            List<Part> nonModelledPartList = nonModeledPartService.getPartsForAssociatedDbIdAndModelId(objectId, modelId, languageId, defaultLanguage, manufacturerId);
            if (nonModelledPartList != null && !nonModelledPartList.isEmpty()) {
                partViewerDetails.setNonModelledPart(nonModelledPartList);
                partViewerDetails.setNonModelled(true);
                partViewerDetails.setNonModelledId(nonModeledPartService.findNonModeledPartIdForAssociatedDbIdAndModelId(objectId, modelId));
            }
        }

        if (masterPart.isNotNull() && masterPartExtensionsDao.hasLinkedParts(masterPart.getMasterPartId()))
                augmentWithMasterPartLinkedParts(partViewerDetails, masterPart);
        else {
            PartModelLink partLink = partLinkService.getActivePartLink(part.getPartId());
            if (partLink != null) {
                partViewerDetails.setLinkedModel(partLink);
                partViewerDetails.setLinked(true);
            }
        }

        if (masterPart.isNotNull() && masterPartExtensionsDao.hasKitParts(masterPart.getMasterPartId()))
            augmentWithMasterPartKitParts(partViewerDetails, manualId, languageId, defaultLanguage, masterPart, currentUser, priceListIdentifierId, dealerEntityId, warehouseId);
        else
            augmentAsPartOfKit(partViewerDetails, part, currentUser, priceListIdentifierId, dealerEntityId, warehouseId);

        if (masterPart.isNotNull() && masterPartExtensionsDao.hasOptionSet(masterPart.getMasterPartId()))
            augmentWithMasterPartOptionsSets(languageId, defaultLanguage, partViewerDetails, masterPart, currentUser, priceListIdentifierId, dealerEntityId, warehouseId);
        else
            augmentWithOptionSets(partViewerDetails, languageId, defaultLanguage, part, currentUser, priceListIdentifierId, dealerEntityId, warehouseId);

        if (masterPart.isNotNull() && masterPartExtensionsDao.hasLinkedTechDocs(masterPart.getMasterPartId())) {
            partViewerDetails.setHasLinkedTechDocs(true);
            partViewerDetails.setLinkedTechDocs(masterPartExtensionsDao.getLinkedTechDocsForPart(part.getMasterPartId()));
        }
    }

    private void augmentWithOptionSets(PartViewerDetails partViewerDetails,
                                       Integer languageId,
                                       Language defaultLanguage,
                                       Part part,
                                       User currentUser,
                                       Integer priceListIdentifierId,
                                       Integer dealerEntityId,
                                       Integer warehouseId) {
        List<OptionsSet> optionsSets = optionsSetService.getOptionsSetByPartId(part.getPartId(), languageId, defaultLanguage);
        partViewerDetails.setContainsOptionsSet(!optionsSets.isEmpty());

        if (partViewerDetails.isContainsOptionsSet()) {
                for (OptionsSet optionSet : optionsSets) {
                    for (OptionsSetPart osPart : optionSet.getOptionsSet()) {
                        if (osPart.getMasterPartId() != null && currentUser.getManufacturerSettings().isPreviewPricingEnabled()) {
                            Float price = masterPartDao.getMasterPartPriceById(osPart.getMasterPartId(), priceListIdentifierId, dealerEntityId);
                            osPart.setPrice(price);
                        }
                        if (warehouseId != null) {
                            Double stock = masterPartDao.getMasterPartStockById(osPart.getMasterPartId(), warehouseId);
                            osPart.setStock(stock);
                        }
                    }
                }
        }
        partViewerDetails.setOptionsSet(optionsSets);
    }

    private void augmentWithMasterPartOptionsSets(Integer languageId,
                                                  Language defaultLanguage,
                                                  PartViewerDetails partViewerDetails,
                                                  MasterPart masterPart,
                                                  User currentUser,
                                                  Integer priceListIdentifierId,
                                                  Integer dealerEntityId,
                                                  Integer warehouseId) {
        partViewerDetails.setContainsOptionsSet(true);
        List<OptionsSet> optionsSets = new ArrayList<>();
        OptionsSet optionSet = new OptionsSet();

        List<OptionsSet> mpOptionSets = masterPartExtensionsDao.getOptionsSetsForMasterPartId(masterPart.getMasterPartId(), languageId, defaultLanguage);

        OptionsSet mpOption = mpOptionSets.get(0);
        optionSet.setMasterPartId(mpOption.getMasterPartId());
        optionSet.setDescription(mpOption.getDescription());

        List<OptionsSetPart> optionParts = new ArrayList<>();

        for (OptionsSetPart mpOptionPart : mpOption.getOptionsSet()) {
            OptionsSetPart option = new OptionsSetPart();

            option.setPartNumber(mpOptionPart.getPartNumber());
            option.setDescription(mpOptionPart.getDescription());
            option.setMasterPartId(mpOptionPart.getMasterPartId());

            if (currentUser.getManufacturerSettings().isPreviewPricingEnabled()) {
                Float price = masterPartDao.getMasterPartPriceById(mpOptionPart.getMasterPartId(), priceListIdentifierId, dealerEntityId);
                option.setPrice(price);
            }
            if (currentUser.getManufacturerSettings().isPreviewStockLevelEnabled()) {
                if (warehouseId != null) {
                    Double stock = masterPartDao.getMasterPartStockById(mpOptionPart.getMasterPartId(), warehouseId);
                    option.setStock(stock);
                } else {
                    option.setStock(mpOptionPart.getStock());
                }
            }

            optionParts.add(option);
        }

        optionSet.setOptionsSet(optionParts);

        optionsSets.add(optionSet);
        partViewerDetails.setOptionsSet(optionsSets);
    }

    private void augmentAsPartOfKit(PartViewerDetails partViewerDetails,
                                    Part part,
                                    User currentUser,
                                    Integer priceListIdentifierId,
                                    Integer dealerEntityId,
                                    Integer warehouseId) {
        boolean inKit = kitService.isPartInKit(part.getPartId());
        partViewerDetails.setInKit(inKit);

        if (inKit) {
            List<Kit> kits = kitService.getKitsByPartId(part.getPartId());
            if (currentUser.getManufacturerSettings().isPreviewPricingEnabled()) {
                for (Kit kit : kits) {
                    for (KitPart kitPart : kit.getParts()) {
                        if (kitPart.getMasterPartId() != null) {
                            Float price = masterPartDao.getMasterPartPriceById(kitPart.getMasterPartId(), priceListIdentifierId, dealerEntityId);
                            kitPart.setPrice(price);
                            if (warehouseId != null) {
                                Double stock = masterPartDao.getMasterPartStockById(kitPart.getMasterPartId(), warehouseId);
                                kitPart.setStock(stock);
                            }
                        }
                    }
                }
            }
            partViewerDetails.setKits(kits);
        }
    }

    private void augmentWithMasterPartKitParts(PartViewerDetails partViewerDetails,
                                               Integer manualId,
                                               Integer languageId,
                                               Language defaultLanguage,
                                               MasterPart masterPart,
                                               User currentUser,
                                               Integer priceListIdentifierId,
                                               Integer dealerEntityId,
                                               Integer warehouseId) {
        boolean restrictKitsToManualEnabled = currentUser.getManufacturerSettings().isCustomerSearchManualsOnly();
        boolean previewStockLevelEnabled = currentUser.getManufacturerSettings().isPreviewStockLevelEnabled();
        boolean previewPricingEnabled = currentUser.getManufacturerSettings().isPreviewPricingEnabled();
        List<Kit> mpKits = masterPartExtensionsDao.getKitsForMasterPartId(masterPart.getMasterPartId(), languageId, defaultLanguage);
        List<Integer> linkedKitIds = new ArrayList<>();
        if (restrictKitsToManualEnabled && manualId != null) {
            linkedKitIds = manualDao.getKitsIdsByManualId(manualId);
        }

        List<Kit> kits = new ArrayList<>();
        for (Kit mpKit : mpKits) {
            int totalParts = 0;
            Kit kit = new Kit();
            kit.setId(mpKit.getId());
            kit.setMasterPartKitId(mpKit.getMasterPartKitId());
            kit.setMasterPartNumber(mpKit.getMasterPartNumber());
			kit.setMasterPartDescription(mpKit.getMasterPartDescription());
            kit.setModelId(mpKit.getModelId());
            kit.setTitle(mpKit.getTitle());
            kit.setDescription(mpKit.getDescription());
            kit.setTotalPartsQuantity(mpKit.getTotalPartsQuantity());
            kit.setKitPrice(mpKit.getKitPrice());
            kit.setKitStockLevel(mpKit.getKitStockLevel());

            if (previewStockLevelEnabled) {
                if (warehouseId != null) {
                    Double stock = masterPartDao.getMasterPartStockById(kit.getMasterPartKitId(), warehouseId);
                    kit.setStock(stock);
                } else {
                    kit.setStock(mpKit.getStock());
                }
            }

            List<KitPart> parts = new ArrayList<>();

            for (KitPart kitPart : mpKit.getParts()) {
                KitPart kPart = new KitPart();

                kPart.setMasterPartId(kitPart.getMasterPartId());
                kPart.setPartNumber(kitPart.getPartNumber());
                kPart.setDescription(kitPart.getDescription());
                kPart.setQuantity(kitPart.getQuantity());

                if (previewPricingEnabled) {
                    Float price = masterPartDao.getMasterPartPriceById(kitPart.getMasterPartId(), priceListIdentifierId, dealerEntityId);
                    kPart.setPrice(price);
                }
                if (previewStockLevelEnabled) {
                    if (warehouseId != null) {
                        Double stock = masterPartDao.getMasterPartStockById(kPart.getMasterPartId(), warehouseId);
                        kPart.setStock(stock);
                    } else {
                        kPart.setStock(kitPart.getStock());
                    }
                }

                totalParts = totalParts + kitPart.getQuantity();

                parts.add(kPart);
            }
            kit.setParts(parts);
            kit.setTotalPartsQuantity(totalParts);

            if (restrictKitsToManualEnabled) {
                if (linkedKitIds.contains(mpKit.getId())) {
                    kits.add(kit);
                }
            } else {
                kits.add(kit);
            }
        }
        partViewerDetails.setInKit(!kits.isEmpty());
        partViewerDetails.setKits(kits);
    }

    private void augmentWithMasterPartLinkedParts(PartViewerDetails partViewerDetails, MasterPart masterPart) {
        PartModelLink mpLinked = masterPartExtensionsDao.getLinkedPartForMasterPart(masterPart.getMasterPartId());
        partViewerDetails.setLinked(true);

        PartModelLink linked = new PartModelLink();
        linked.createFromMpLink(mpLinked);
        partViewerDetails.setLinkedModel(linked);
    }

    private void augmentWithMasterPartNonModeledPart(PartViewerDetails partViewerDetails,
                                                     Language defaultLanguage,
                                                     MasterPart masterPart,
                                                     User currentUser,
                                                     Integer languageId,
                                                     Integer priceListIdentifierId,
                                                     Integer dealerEntityId,
                                                     Integer warehouseId) {
        NonModelled nonModeledMasterPart = masterPartExtensionsDao.getNonModelledForMasterPartId(masterPart.getMasterPartId(), languageId, defaultLanguage);

        partViewerDetails.setNonModelled(true);

        List<Part> nonModelledPartList = new ArrayList<>();

        for (NonModelledPart nonModelledPart : nonModeledMasterPart.getNonModelledPart()) {
            Part mpPart = new Part();
            mpPart.setPartNumber(nonModelledPart.getPartNumber());
            mpPart.setPartDescription(nonModelledPart.getDescription());
            mpPart.setMasterPartId(nonModelledPart.getMasterPartId());

            if (currentUser.getManufacturerSettings().isPreviewPricingEnabled()) {
                Float price = masterPartDao.getMasterPartPriceById(mpPart.getMasterPartId(), priceListIdentifierId, dealerEntityId);
                mpPart.setPrice(price);
            }
            if (currentUser.getManufacturerSettings().isPreviewStockLevelEnabled()) {
                if (warehouseId != null) {
                    Double stock = masterPartDao.getMasterPartStockById(mpPart.getMasterPartId(), warehouseId);
                    mpPart.setStock(stock);
                } else {
                    mpPart.setStock(nonModelledPart.getStock());
                }
            }
            nonModelledPartList.add(mpPart);
        }
        partViewerDetails.setNonModelledPart(nonModelledPartList);
    }

    private void augmentWithMasterPart(PartViewerDetails partViewerDetails,
                                       Part part,
                                       MasterPart masterPart,
                                       Integer priceListIdentifierId,
                                       Integer dealerEntityId,
                                       Integer warehouseId) {
        part.setNote(masterPart.getNote());
        Float price = masterPartDao.getMasterPartPriceById(masterPart.getMasterPartId(), priceListIdentifierId, dealerEntityId);
        partViewerDetails.getPart().setPrice(price);

        if (warehouseId != null) {
            Double stock = masterPartDao.getMasterPartStockById(masterPart.getMasterPartId(), warehouseId);
            partViewerDetails.getPart().setStock(stock);
            partViewerDetails.getPart().setDefaultWarehouseId(warehouseId);
        }
    }
}
