Feature: Place Orders
  Dealers and Customers place orders for Manufacturers and DealerPlus to fulfill

  @ignore
  Scenario Outline: Place Enquiry
    Given I am a Dealer with <NAME_EMAIL>
    And a <listOfParts> I want to purchase are available
    When I place an enquiry for this <listOfParts>
    Then an enquiry should be created with this <listOfParts> as line items
    Examples:
      | listOfParts                            |
      | Cat-part-101,Cat-part-102,Cat-part-103 |

  @ignore
  Scenario: List Enquiries for Manufacturer
    Given I am a Manufacturer with <NAME_EMAIL>
    And I shouldn't see Orders placed with the DealerPlus
    When a number of Enquiries have been placed with this Manufacturer and DealerPlus
    Then I can only list the Enquiries for this Manufacturer

  Scenario Outline:Add Parts to Enquiry
    Given I am a Dealer with <NAME_EMAIL>
    And I have already placed an enquiry with <orderId> for this <listOfParts>
    When I add <quantity> of part with number <newPart> to the enquiry with <orderId>
    Then the enquiry with <orderId> should have this <listOfPartsWithNewPart>
    Examples:
      | orderId | listOfParts                            | newPart      | quantity | listOfPartsWithNewPart                              |
      | 1       | Cat-part-104,Cat-part-105,Cat-part-106 | Cat-part-107 | 3        | Cat-part-104,Cat-part-105,Cat-part-106,Cat-part-107 |

  Scenario Outline:Add Kits to Enquiry
    Given I am a Dealer with <NAME_EMAIL>
    And I have already placed an enquiry with <orderId> for this <listOfParts>
    When I add <quantity> of kit with number <newKit> to the enquiry with <orderId>
    Then the enquiry with <orderId> should have this <listOfPartsWithNewKit>
    Examples:
      | orderId | listOfParts                            | newKit      | quantity | listOfPartsWithNewKit                              |
      | 2       | Cat-part-104,Cat-part-105,Cat-part-106 | Cat-kit-108 | 3        | Cat-part-104,Cat-part-105,Cat-part-106,Cat-kit-108 |

  Scenario Outline:Add Extra Quantity of Existing Parts to Enquiry
    Given I am a Dealer with <NAME_EMAIL>
    And I have already placed an enquiry with <orderId> for this <listOfParts>
    And the enquiry with <orderId> with this <existingPart> should have <existingQuantity>
    When I add <quantity> of part with number <existingPart> to the enquiry with <orderId>
    Then the enquiry with <orderId> should have this <listOfParts>
    And the enquiry with <orderId> with this <existingPart> should have <updatedQuantity>
    Examples:
      | orderId | listOfParts                            | existingPart | existingQuantity | quantity | updatedQuantity |
      | 6       | Cat-part-104,Cat-part-105,Cat-part-106 | Cat-part-106 | 2                | 3        | 5               |

  Scenario Outline:Add Extra Quantity of Existing Kits to Enquiry
    Given I am a Dealer with <NAME_EMAIL>
    And I have already placed an enquiry with <orderId> for this <listOfParts>
    And the enquiry with <orderId> with this <existingPart> should have <existingQuantity>
    When I add <quantity> of kit with number <existingPart> to the enquiry with <orderId>
    Then the enquiry with <orderId> should have this <listOfParts>
    And the enquiry with <orderId> with this <existingPart> should have <updatedQuantity>
    Examples:
      | orderId | listOfParts | existingPart | existingQuantity | quantity | updatedQuantity |
      | 7       | Cat-kit-108 | Cat-kit-108  | 2                | 4        | 6               |