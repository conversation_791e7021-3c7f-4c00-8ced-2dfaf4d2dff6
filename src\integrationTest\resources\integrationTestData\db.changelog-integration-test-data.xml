<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog/1.9"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog/1.9
         http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-1.9.xsd">

    <include file="db.changelog-1.1-create-manufacturers.xml" relativeToChangelogFile="true"/>
    <include file="db.changelog-1.2-create-users.xml" relativeToChangelogFile="true"/>
    <include file="db.changelog-1.3-create-manufacturer-users.xml" relativeToChangelogFile="true"/>
    <include file="db.changelog-1.4-create-user-settings.xml" relativeToChangelogFile="true"/>
    <include file="db.changelog-1.5-create-user-roles.xml" relativeToChangelogFile="true"/>
    <include file="db.changelog-1.6-create-manufacturer-settings.xml" relativeToChangelogFile="true"/>
    <include file="db.changelog-1.7-create-user-addresses.xml" relativeToChangelogFile="true"/>
    <include file="db.changelog-1.8-create-purchasers-and-warehouses.xml" relativeToChangelogFile="true"/>
    <include file="db.changelog-1.9-create-purchaser-users.xml" relativeToChangelogFile="true"/>
    <include file="db.changelog-1.11-create-purchaser-settings.xml" relativeToChangelogFile="true"/>
    <include file="db.changelog-1.12-create-manufacturer-languages.xml" relativeToChangelogFile="true"/>
    <include file="db.changelog-2.0-create-ranges.xml" relativeToChangelogFile="true"/>
    <include file="db.changelog-2.1-create-products.xml" relativeToChangelogFile="true"/>
    <include file="db.changelog-2.2-create-models.xml" relativeToChangelogFile="true"/>
    <include file="db.changelog-2.3-create-parts.xml" relativeToChangelogFile="true"/>
    <include file="db.changelog-2.4-create-master-parts.xml" relativeToChangelogFile="true"/>
    <include file="db.changelog-2.5-create-master-part-translations.xml" relativeToChangelogFile="true"/>
    <include file="db.changelog-2.6-create-master-part-stocks.xml" relativeToChangelogFile="true"/>
    <include file="db.changelog-2.7-create-publications.xml" relativeToChangelogFile="true"/>
    <include file="db.changelog-2.8-create-viewables-and-snapshots.xml" relativeToChangelogFile="true"/>
    <include file="db.changelog-2.9-create-kits.xml" relativeToChangelogFile="true"/>
    <include file="db.changelog-3.0-create-orders.xml" relativeToChangelogFile="true"/>
    <include file="db.changelog-3.1-create-order-items.xml" relativeToChangelogFile="true"/>
</databaseChangeLog>