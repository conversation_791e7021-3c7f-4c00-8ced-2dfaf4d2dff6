package co.cadshare.modelMgt.viewables.adapters.api.web;

import co.cadshare.modelMgt.models.core.Model;
import co.cadshare.modelMgt.shared.core.Product;
import co.cadshare.modelMgt.shared.core.Range;
import co.cadshare.modelMgt.shared.core.Viewable;
import org.junit.Test;

import static org.junit.Assert.assertEquals;

public class ViewableMapperTest {

    @Test
    public void testModelToViewableListItemDtoMapsCorrectly(){

	    Range range = new Range();
	    range.setId(1);
	    range.setName("Sample Range");

	    Product product = new Product();
		product.setId(1);
		product.setName("Sample Product");
		product.setRange(range);

        Viewable viewable = new Viewable();
	    viewable.setId(1);
	    viewable.setName("Sample Viewable");
		viewable.setProduct(product);

	    ViewableListItemDto dto = ViewableMapper.Instance.viewableToViewableListItemDto(viewable);

        assertEquals(viewable.getName(), dto.getName());
        assertEquals(viewable.getId(), dto.getId().intValue());
		assertEquals(viewable.getProduct().getId(), dto.getProduct().getId().intValue());
		assertEquals(viewable.getProduct().getName(), dto.getProduct().getName());
		assertEquals(viewable.getProduct().getRange().getId(), dto.getRange().getId().intValue());
		assertEquals(viewable.getProduct().getRange().getName(), dto.getRange().getName());
    }
}
