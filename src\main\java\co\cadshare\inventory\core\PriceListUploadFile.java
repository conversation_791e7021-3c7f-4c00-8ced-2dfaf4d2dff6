package co.cadshare.inventory.core;

import co.cadshare.domainmodel.priceListIdentifier.PriceListIdentifier;
import lombok.Data;

import java.text.Collator;
import java.util.ArrayList;
import java.util.List;

@Data
public class PriceListUploadFile {
    PriceListFileHeaders headers;
    List<String []> dataRows;
    List<PriceListIdentifier> identifiers;

    public PriceListUploadFile(List<String[]> input, List<PriceListIdentifier> identifiers, String partNumberColumn) throws UnparseablePriceListFileException {

        this.headers = new PriceListFileHeaders(input.get(0), partNumberColumn);
        this.dataRows = input.subList(1, input.size());
        this.identifiers = identifiers;
    }

    @Data
    public class PriceListFileHeaders {

        private List<PriceListIdentifier> identifiers = new ArrayList<>();

        public PriceListFileHeaders(String[] headers, String partNumberColumn) throws UnparseablePriceListFileException {
            if (headers.length < 2) {
                throw new UnparseablePriceListFileException("CSV must contain at least 2 columns");
            }
            if (!isSame(headers[0].trim(),partNumberColumn)) {
                throw new UnparseablePriceListFileException("Header of first column should be " + partNumberColumn);
            }
        }

        public boolean isSame(String a, String b) {
            Collator insenstiveStringComparator = Collator.getInstance();
            insenstiveStringComparator.setStrength(Collator.PRIMARY);
            return insenstiveStringComparator.compare(a, b) == 0;
        }

    }

    public static class UnparseablePriceListFileException extends Exception {
        public UnparseablePriceListFileException(String message){
            super(message);
        }
    }
}
