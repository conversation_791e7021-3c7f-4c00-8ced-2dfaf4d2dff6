package co.cadshare.modelMgt.viewables.adapters.database;

import co.cadshare.modelMgt.publications.adapters.database.PublicationsModelEntity;
import co.cadshare.modelMgt.publications.adapters.database.PublicationsViewableEntityView;
import co.cadshare.shared.adapters.database.BlazeQueryRepo;
import com.blazebit.persistence.CriteriaBuilder;
import com.blazebit.persistence.CriteriaBuilderFactory;
import com.blazebit.persistence.view.EntityViewManager;
import com.blazebit.persistence.view.EntityViewSetting;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import javax.persistence.EntityManager;
import java.util.List;

@Repository
public class ViewableQueryRepo extends BlazeQueryRepo {

    @Autowired
    public ViewableQueryRepo(EntityManager entityManager,
                             EntityViewManager entityViewManager,
                             CriteriaBuilderFactory factory) {
	    super(entityManager, entityViewManager, factory);
    }

    public PublicationsViewableEntityView getById(Integer id) {

        CriteriaBuilder<PublicationsModelEntity> criteriaBuilder =
                factory.create(entityManager, PublicationsModelEntity.class)
                        .where("modelId")
                        .eq(id);

		return entityViewManager.applySetting(EntityViewSetting.create(PublicationsViewableEntityView.class), criteriaBuilder)
			.getSingleResult();
    }

	public List<PublicationsViewableEntityView> getAllViewablesForManufacturer(int manufacturerId) {
		CriteriaBuilder<PublicationsModelEntity> criteriaBuilder = getPublicationsModelEntityCriteriaBuilder(manufacturerId);
		return entityViewManager.applySetting(EntityViewSetting.create(PublicationsViewableEntityView.class), criteriaBuilder)
				.getResultList();
	}

	public List<PublicationsViewableEntityView> searchViewables(int manufacturerId, String searchTerm) {
		CriteriaBuilder<PublicationsModelEntity> criteriaBuilder = getPublicationsModelEntityCriteriaBuilder(manufacturerId);
		criteriaBuilder.where("modelName")
				.like(false).value(wrapSearchCriteria(searchTerm)).noEscape();
		return entityViewManager.applySetting(EntityViewSetting.create(PublicationsViewableEntityView.class), criteriaBuilder)
				.getResultList();
	}

	public List<PublicationsViewableEntityView> filterViewablesByProduct(int manufacturerId, int productId) {
		CriteriaBuilder<PublicationsModelEntity> criteriaBuilder = getPublicationsModelEntityCriteriaBuilder(manufacturerId);
		criteriaBuilder.where("product.id").eq(productId);
		return entityViewManager.applySetting(EntityViewSetting.create(PublicationsViewableEntityView.class), criteriaBuilder)
				.getResultList();
	}

	private CriteriaBuilder<PublicationsModelEntity> getPublicationsModelEntityCriteriaBuilder(int manufacturerId) {
		return factory.create(entityManager, PublicationsModelEntity.class)
						.where("deleted").eq(false)
						.where("product.range.manufacturerId").eq(manufacturerId);
	}
}
