// get session

# @name session
POST {{VIS_SESSION_URL}}
Content-Type: text/xml

<?xml version="1.0" encoding="utf-8"?>
<soap:Envelope xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">
    <soap:Body>
        <GetSession xmlns="http://visibility.com/">
            <strUser>{{VIS_SESSION_USER}}</strUser>
            <strPassword>{{$dotenv VIS_SESSION_PASSWORD}}</strPassword>
        </GetSession>
    </soap:Body>
</soap:Envelope>

###

POST {{VIS_SESSION_URL}}
Content-Type: text/xml

<?xml version="1.0" encoding="utf-8"?>
<soap:Envelope
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xmlns:xsd="http://www.w3.org/2001/XMLSchema"
	xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">
	<soap:Body>
		<SetEntity xmlns="http://visibility.com/">
			<sessionId>{{secureId}}</sessionId>
			<strEntity_CODE>SUI</strEntity_CODE>
		</SetEntity>
	</soap:Body>
</soap:Envelope>


###

//Create Order

@secureId = {{session.response.body./*[local-name()='Envelope']/*[local-name()='Body']/*[local-name()='GetSessionResponse']/*[local-name()='GetSessionResult']/*[local-name()='sReturnMessage']}}

# @name salesOrderResponse
POST {{VIS_SESSION_URL}}
Content-Type: text/xml

<?xml version="1.0" encoding="utf-8"?>
<soap:Envelope 
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" 
	xmlns:xsd="http://www.w3.org/2001/XMLSchema" 
	xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">
	<soap:Body>
		<CreateSupremeSalesOrder xmlns="http://visibility.com/">
			<strSecureID>{{secureId}}</strSecureID>
			<strCustomerNo>BACKCOUNTRY</strCustomerNo>
			<strCustomerPO>online</strCustomerPO>
			<strWarehouse>11</strWarehouse>
			<strOrderType>01</strOrderType>
			<BilltoAddress>
				<STREET_ADDR_1></STREET_ADDR_1>
                <STREET_ADDR_2></STREET_ADDR_2>
                <CITY></CITY>
                <strSTATE_PROV></strSTATE_PROV>
                <ZIP_POSTAL_CODE></ZIP_POSTAL_CODE>
                <COUNTRY></COUNTRY>
				<CONTACT_ID></CONTACT_ID>
                <CONTACT_NAME></CONTACT_NAME>
				<TP_ADDRESS_TO_ID></TP_ADDRESS_TO_ID>
			</BilltoAddress>
			<SoldtoAddress>
				<STREET_ADDR_1></STREET_ADDR_1>
                <STREET_ADDR_2></STREET_ADDR_2>
                <CITY></CITY>
                <strSTATE_PROV></strSTATE_PROV>
                <ZIP_POSTAL_CODE></ZIP_POSTAL_CODE>
                <COUNTRY></COUNTRY>
				<CONTACT_ID></CONTACT_ID>
                <CONTACT_NAME></CONTACT_NAME>
				<TP_ADDRESS_TO_ID></TP_ADDRESS_TO_ID>
			</SoldtoAddress>
			<ShiptoAddress>
				<STREET_ADDR_1></STREET_ADDR_1>
                <STREET_ADDR_2></STREET_ADDR_2>
                <CITY></CITY>
                <strSTATE_PROV></strSTATE_PROV>
                <ZIP_POSTAL_CODE></ZIP_POSTAL_CODE>
                <COUNTRY></COUNTRY>
				<TRADE_PARTNER_ADDR_ID>134</TRADE_PARTNER_ADDR_ID>
				<CONTACT_NAME_X></CONTACT_NAME_X>
                <CONTACT_ID>611</CONTACT_ID>
				<TP_ADDRESS_TO_ID></TP_ADDRESS_TO_ID>
			</ShiptoAddress>
			<strQuote></strQuote>
		</CreateSupremeSalesOrder>
	</soap:Body>
</soap:Envelope>


###

<soap:Envelope xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">
    <soap:Body>
        <CreateSupremeSalesOrder xmlns="http://visibility.com/">
            <strSecureID>420F3E658D6DB4620FCBFE94076020079D219999</strSecureID>
            <strCustomerNo>INTERMOUNTAIN</strCustomerNo>
            <strCustomerPO>PT08013</strCustomerPO>
            <strWarehouse>11</strWarehouse>
            <strOrderType>01</strOrderType>
            <BilltoAddress>
                <STREET_ADDR_1>1935 Kimberly Road</STREET_ADDR_1>
                <STREET_ADDR_2></STREET_ADDR_2>
                <CITY>Twin Falls</CITY>
                <strSTATE_PROV>ID</strSTATE_PROV>
                <ZIP_POSTAL_CODE>83301</ZIP_POSTAL_CODE>
                <COUNTRY>US </COUNTRY>
                <CONTACT_NAME>accouting</CONTACT_NAME>
            </BilltoAddress>
            <SoldtoAddress>
                <STREET_ADDR_1>1935 Kimberly Road</STREET_ADDR_1>
                <STREET_ADDR_2></STREET_ADDR_2>
                <CITY>Twin Falls</CITY>
                <strSTATE_PROV>ID</strSTATE_PROV>
                <ZIP_POSTAL_CODE>83301</ZIP_POSTAL_CODE>
                <COUNTRY>US </COUNTRY>
                <CONTACT_NAME>parts</CONTACT_NAME>
            </SoldtoAddress>
            <ShiptoAddress>
                <STREET_ADDR_1>1935 Kimberly Road</STREET_ADDR_1>
                <STREET_ADDR_2></STREET_ADDR_2>
                <CITY>Twin Falls</CITY>
                <strSTATE_PROV>ID</strSTATE_PROV>
                <ZIP_POSTAL_CODE>83301</ZIP_POSTAL_CODE>
                <COUNTRY>US </COUNTRY>
                <CONTACT_NAME>parts</CONTACT_NAME>
            </ShiptoAddress>
            <strQuote></strQuote>
        </CreateSupremeSalesOrder>
    </soap:Body>
</soap:Envelope>



###

<?xml version="1.0" encoding="utf-8"?>
<soap:Envelope
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xmlns:xsd="http://www.w3.org/2001/XMLSchema"
	xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">
	<soap:Body>
		<CreateSupremeSalesOrder
			xmlns="http://visibility.com/">
			<strSecureID>{{secureId}}</strSecureID>
			<strCustomerNo>customerNumber123</strCustomerNo>
			<strCustomerPO>purchaseOrderNumber12345</strCustomerPO>
			<strWarehouse>wareHouseId123</strWarehouse>
			<strOrderType>01</strOrderType>
			<BilltoAddress>
				<STREET_ADDR_1>Billing Address Line 1</STREET_ADDR_1>
				<CITY>Billing Address City</CITY>
				<strSTATE_PROV>Billing Address State</strSTATE_PROV>
				<COUNTRY>CA</COUNTRY>
				<CONTACT_NAME>Contact Name</CONTACT_NAME>
			</BilltoAddress>
			<SoldtoAddress>
				<STREET_ADDR_1>Shipping Address Line 1</STREET_ADDR_1>
				<CITY>Shipping Address City</CITY>
				<strSTATE_PROV>Shipping Address State</strSTATE_PROV>
				<COUNTRY>CA</COUNTRY>
				<CONTACT_NAME>Delivery Name</CONTACT_NAME>
			</SoldtoAddress>
			<ShiptoAddress>
				<STREET_ADDR_1>Shipping Address Line 1</STREET_ADDR_1>
				<CITY>Shipping Address City</CITY>
				<strSTATE_PROV>Shipping Address State</strSTATE_PROV>
				<COUNTRY>CA</COUNTRY>
				<CONTACT_NAME>Delivery Name</CONTACT_NAME>
			</ShiptoAddress>
			<strAuthorization>GlobalPaymentCode123</strAuthorization>
			<strTransactionID>TransactionID123</strTransactionID>
			<strQuote>Y</strQuote>
		</CreateSupremeSalesOrder>
	</soap:Body>
</soap:Envelope>


###

//Create Sales Order Line Item

POST {{VIS_SESSION_URL}}
Content-Type: text/xml

<soap:Envelope
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xmlns:xsd="http://www.w3.org/2001/XMLSchema"
	xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">
	<soap:Body>
		<CreateSalesOrderLine
			xmlns="http://visibility.com/">
			<strSecureID>{{secureId}}}</strSecureID>
			<strSalesOrderNo>SalesOrderNumber123</strSalesOrderNo>
			<strPart>null</strPart>
			<strQty> 0</strQty>
			<strAlloc>Y</strAlloc>
			<strPRICE>0.0</strPRICE>
		</CreateSalesOrderLine>
	</soap:Body>
</soap:Envelope>

