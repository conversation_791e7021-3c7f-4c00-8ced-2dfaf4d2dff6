<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                   http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.0.xsd
">

    <changeSet author="AndyB" id="1.7-integration-test-data-create-user-addresses-1">
        <sql stripComments="true">
            INSERT INTO public.address(id, addressline1, addressline2, city, state, postcode, country, archived, createddate, createdbyuserid)
            VALUES (
            1,                      -- id
            '29 Acacia Road',       -- addressline1
            'Nuttytown',            -- addressline2
            'Banana City',          -- city
            'Banana State',         -- state,
            'NT1 1NT',              -- postcode
            'United Kingdom',       -- country,
            FALSE,                  -- archived,
            '2017-05-07 13:58:36',  -- createddate,
            1                       -- createdbyuserid
            );
        </sql>
    </changeSet>
    <changeSet author="AndyB" id="1.7-integration-test-data-create-user-address-map-1">
        <sql stripComments="true">
            INSERT INTO public.useraddressmap(
            id, addressid, userid, archived, createddate, createdbyuserid, modifieddate, modifiedbyuserid)
            VALUES (1, 1, 1, false, '2017-05-07 13:58:36', 1, '2017-05-07 13:58:36', 1);
        </sql>
    </changeSet>

    <changeSet author="AndyB" id="1.7-integration-test-data-create-user-addresses-2">
        <sql stripComments="true">
            INSERT INTO public.address(id, addressline1, addressline2, city, state, postcode, country, archived, createddate, createdbyuserid)
            VALUES (
            2,                      -- id
            'Red Post Box',         -- addressline1
            '221B Baker Street',    -- addressline2
            'London',               -- city
            'No State',             -- state,
            'NW1 6XE',              -- postcode
            'United Kingdom',       -- country,
            FALSE,                  -- archived,
            '2017-05-07 13:58:36',  -- createddate,
            1                       -- createdbyuserid
            );
        </sql>
    </changeSet>
    <changeSet author="AndyB" id="1.7-integration-test-data-create-user-address-map-2">
        <sql stripComments="true">
            INSERT INTO public.useraddressmap(
            id, addressid, userid, archived, createddate, createdbyuserid, modifieddate, modifiedbyuserid)
            VALUES (2, 2, 2, false, '2017-05-07 13:58:36', 1, '2017-05-07 13:58:36', 1);
        </sql>
    </changeSet>
</databaseChangeLog>
