package co.cadshare.shared.core.manufacturer;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.util.List;

import java.sql.Timestamp;

@Data
public class ManufacturerDetails {

    @ApiModelProperty(hidden = true)
    private int manufacturerId;
    
    private String emailSignature;
    private String logoUrl;
    private String supportEmail;
    private String phone;
    private String viewerColour;
    private boolean edgingEnabledDefault;
    private boolean contactUsPageEnabled;

    private Timestamp modifiedDate;
    private Integer modifiedByUserId;

    private List<AdditionalEmail> additionalEmails;
}

