package co.cadshare.masterParts.adapters.api.web;

import co.cadshare.masterParts.core.TranslationFile;
import co.cadshare.masterParts.core.TranslationFile.UnparseableTranslationFileException;
import co.cadshare.shared.boundary.LanguageQueryPort;
import com.opencsv.CSVReader;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStreamReader;
import java.util.HashMap;
import java.util.List;

@Component
@Slf4j
public class TranslationCsvFileProcessor {

    private LanguageQueryPort languageQuery;

    @Autowired
    public TranslationCsvFileProcessor(LanguageQueryPort languageQuery) {
        this.languageQuery = languageQuery;
    }
    private List<String[]> readAll(MultipartFile file) throws IOException {

        try (CSVReader reader = new CSVReader(new InputStreamReader(file.getInputStream(), "UTF-8"))){
            List<String[]> list;
            list = reader.readAll();
            return list;
        }
    }

    public TranslationFile convert(MultipartFile file) throws UnparseableTranslationFileException, IOException {
        HashMap<String, Integer> languages = languageQuery.getAllLanguages();
        TranslationFile translationFile = new TranslationFile(readAll(file), languages);
        return translationFile;
    }
}
