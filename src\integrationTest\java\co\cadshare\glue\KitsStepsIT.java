package co.cadshare.glue;

import co.cadshare.main.Application;
import co.cadshare.utils.ObjectUtilsExtension;
import io.cucumber.java.en.And;
import io.cucumber.java.en.Given;
import io.cucumber.java.en.Then;
import io.cucumber.java.en.When;
import io.cucumber.spring.CucumberContextConfiguration;
import io.restassured.RestAssured;
import io.restassured.http.ContentType;
import io.restassured.response.Response;
import lombok.experimental.ExtensionMethod;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.context.SpringBootContextLoader;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.web.server.LocalServerPort;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.ContextConfiguration;

import static io.restassured.RestAssured.given;
import static org.junit.Assert.*;

@ExtensionMethod(ObjectUtilsExtension.class)
public class KitsStepsIT {

    @LocalServerPort
    String port;

    String baseUrl = "http://localhost:";

    @Value("${foo}")
    String foo;

    private static String token;
    private static Response response;
    private static String jsonString;
    private static String kitId;

    @When("I create a Kit")
    public void iCreateAKit() {

        RestAssured.baseURI = baseUrl.concat(port);
        given().header("Site-Url", baseUrl.concat(port))
                .contentType(ContentType.JSON)
                .auth().oauth2(token)
                .body("")
                .when()
                .post("/manufacturers/1/master-part-kits")
                .then()
                .statusCode(200);

    }

    @Then("I should see it in the list of Kits")
    public void iShouldSeeItInTheListOfKits() {
        assertEquals(foo, "bar");
    }

    @Given("a MasterPart with part number {string} exists")
    public void aMasterPartWithPartNumberExists(String arg0) {
        assertEquals(foo, "bar");

    }

    @And("a selection of MasterParts exist not including {string}")
    public void aSelectionOfMasterPartsExistNotIncluding(String arg0) {
    }

    @And("the list of Kits doesn't contain a Kit with the Kit master part Number {string}")
    public void theListOfKitsDoesnTContainAKitWithTheKitMasterPartNumber(String arg0) {
        assertEquals(foo, "bar");
    }

    @When("I create a Kit, using the MasterPart {string} as the Kit MasterPart")
    public void iCreateAKitUsingTheMasterPartAsTheKitMasterPart(String arg0) {
        assertEquals(foo, "bar");
    }


}
