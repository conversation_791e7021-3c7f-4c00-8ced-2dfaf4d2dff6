package co.cadshare.masterParts.adapters.api.web;

import co.cadshare.aspects.access.languages.CanUseLanguage;
import co.cadshare.masterParts.core.extensions.kit.Kit;
import co.cadshare.shared.core.Language;
import co.cadshare.shared.core.user.User;
import co.cadshare.masterParts.boundary.MasterPartKitService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Slf4j
@Controller
public class MasterPartKitController {

  private final MasterPartKitService masterPartKitService;

  public MasterPartKitController(MasterPartKitService masterPartKitService) {
    this.masterPartKitService = masterPartKitService;
  }

	@GetMapping(value = "/kit")
	public HttpEntity<List<Kit>> getKitByManufacturerId(@AuthenticationPrincipal User currentUser) {

		log.info("ACCESS: User [{}], getKitByManufacturerId", currentUser.accessDetails());
		List<Kit> kits = masterPartKitService.getKitsDetailsByManufacturerId(currentUser.getManufacturerId());
		return new ResponseEntity<>(kits, HttpStatus.OK);
	}


  //Kits Services
  @RequestMapping(value = "/masterPart/{masterpartId}/kit", method = RequestMethod.GET)
  public HttpEntity<List<Kit>> getKitsForPart(@AuthenticationPrincipal User currentUser,
                                              @PathVariable int masterpartId) {

    log.info("ACCESS: User [{}], getKitsForPart, partId [{}]", currentUser.accessDetails(), masterpartId);

    List<Kit> kits = masterPartKitService.getKitsForPart(masterpartId);
    return new ResponseEntity<>(kits, HttpStatus.OK);
  }

  @RequestMapping(value = "/masterPart/kit/{kitId}", method = RequestMethod.GET)
  @CanUseLanguage
  public HttpEntity<Kit> getKitForPartById(@AuthenticationPrincipal User currentUser,
                                           @PathVariable int kitId,
                                           @RequestParam(value = "language", required = false) Language language) throws Exception {

    log.info("ACCESS: User [{}], getKitForPartById, kitId [{}]", currentUser.accessDetails(), kitId);
	Integer languageId = language != null ? currentUser.findLanguage(language).getLanguageId() : null;
	Kit kit = masterPartKitService.getKitForPartById(kitId, languageId, currentUser.obtainDefaultLanguage());
    return new ResponseEntity<>(kit, HttpStatus.OK);
  }


  @PostMapping(value = "/masterPart/{masterpartId}/kit", consumes = "application/json")
  public HttpEntity<Integer> createKit(@AuthenticationPrincipal User currentUser,
                                       @PathVariable int masterpartId,
                                       @RequestBody Kit kit) {

    log.info("ACCESS: User [{}], createKit, for masterpart Id [{}]", currentUser.accessDetails(), masterpartId);
    kit.setId(masterpartId);
    int kitId = masterPartKitService.createKit(kit);
    log.info("Kit with id [{}] created", kitId);
    return new ResponseEntity<>(kitId, HttpStatus.OK);
  }

  @PutMapping(value = "/masterPart/kit/{kitId}", consumes = "application/json")
  public HttpEntity<Boolean> updateKit(@AuthenticationPrincipal User currentUser,
                                       @PathVariable int kitId,
                                       @RequestBody Kit kit) {

    log.info("ACCESS: User [{}], updateKit, linkedPartId [{}]", currentUser.accessDetails(), kitId);
    kit.setId(kitId);
    boolean response = masterPartKitService.updateKit(kit);
    return new ResponseEntity<>(response, HttpStatus.OK);
  }

  @DeleteMapping(value = "/masterPart/kit/{kitId}")
  public HttpEntity<Boolean> deleteKit(@AuthenticationPrincipal User currentUser,
                                       @PathVariable int kitId) {

    log.info("ACCESS: User [{}], deleteKit, id [{}]", currentUser.accessDetails(), kitId);
    Boolean isDeleted = masterPartKitService.deleteKit(kitId);
    return new ResponseEntity<>(isDeleted, HttpStatus.OK);
  }

  @DeleteMapping(value = "/masterPart/kit/{kitId}/masterPart/{masterPartId}")
  public HttpEntity<Boolean> deleteMasterPartFromKit(@AuthenticationPrincipal User currentUser,
                                                     @PathVariable int kitId,
                                                     @PathVariable int masterPartId) {

    log.info("ACCESS: User [{}], deleteMasterPartFromKit, kitId [{}], masterPartId [{}]", currentUser.accessDetails(), kitId, masterPartId);
    Boolean isDeleted = masterPartKitService.deleteMasterPartFromKit(kitId, masterPartId);
    return new ResponseEntity<>(isDeleted, HttpStatus.OK);
  }

}
