<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                   http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.0.xsd
">

    <changeSet author="AndyB" id="1.4-integration-test-data-create-user-settings-1a">
        <sql>
            INSERT INTO public.usersettings(userid, disablediscountediting)
            VALUES (1, false);
        </sql>
    </changeSet>
    <changeSet author="AndyB" id="1.4-integration-test-data-create-user-settings-1b">
        <sql>
            INSERT INTO public.usersettings(userid, disablediscountediting)
            VALUES (2, false);
        </sql>
    </changeSet>
    <changeSet author="AndyB" id="1.4-integration-test-data-create-user-settings-1c">
        <sql>
            INSERT INTO public.usersettings(userid, disablediscountediting)
            VALUES (3, false);
        </sql>
    </changeSet>

    <changeSet author="AndyB" id="1.4-integration-test-data-create-user-settings-1d">
        <sql>
            INSERT INTO public.usersettings(userid, disablediscountediting)
            VALUES (4, false);
        </sql>
    </changeSet>

    <changeSet author="AndyB" id="1.4-integration-test-data-create-user-settings-1e">
        <sql>
            INSERT INTO public.usersettings(userid, disablediscountediting)
            VALUES (5, false);
        </sql>
    </changeSet>

    <changeSet author="AndyB" id="1.4-integration-test-data-create-user-settings-1f">
        <sql>
            INSERT INTO public.usersettings(userid, disablediscountediting)
            VALUES (6, false);
        </sql>
    </changeSet>

    <changeSet author="AndyB" id="1.4-integration-test-data-create-user-settings-1g">
        <sql>
            INSERT INTO public.usersettings(userid, disablediscountediting)
            VALUES (7, false);
        </sql>
    </changeSet>

    <changeSet author="AndyB" id="1.4-integration-test-data-create-user-settings-1h">
        <sql>
            INSERT INTO public.usersettings(userid, disablediscountediting)
            VALUES (8, false);
        </sql>
    </changeSet>

    <changeSet author="AndyB" id="1.4-integration-test-data-create-user-settings-1i">
        <sql>
            INSERT INTO public.usersettings(userid, disablediscountediting)
            VALUES (9, false);
        </sql>
    </changeSet>

    <changeSet author="AndyB" id="1.4-integration-test-data-create-user-settings-1j">
        <sql>
            INSERT INTO public.usersettings(userid, disablediscountediting)
            VALUES (10, false);
        </sql>
    </changeSet>

    <changeSet author="AndyB" id="1.4-integration-test-data-create-user-settings-1k">
        <sql>
            INSERT INTO public.usersettings(userid, disablediscountediting)
            VALUES (11, false);
        </sql>
    </changeSet>

    <changeSet author="AndyB" id="1.4-integration-test-data-create-user-settings-1l">
        <sql>
            INSERT INTO public.usersettings(userid, disablediscountediting)
            VALUES (12, false);
        </sql>
    </changeSet>

    <changeSet author="AndyB" id="1.4-integration-test-data-create-user-settings-1m">
        <sql>
            INSERT INTO public.usersettings(userid, disablediscountediting)
            VALUES (13, false);
        </sql>
    </changeSet>

    <changeSet author="AndyB" id="1.4-integration-test-data-create-user-settings-1n">
        <sql>
            INSERT INTO public.usersettings(userid, disablediscountediting)
            VALUES (14, false);
        </sql>
    </changeSet>

    <changeSet author="AndyB" id="1.4-integration-test-data-create-user-settings-1o">
        <sql>
            INSERT INTO public.usersettings(userid, disablediscountediting)
            VALUES (15, false);
        </sql>
    </changeSet>

    <changeSet author="AndyB" id="1.4-integration-test-data-create-user-settings-1p">
        <sql>
            INSERT INTO public.usersettings(userid, disablediscountediting)
            VALUES (16, false);
        </sql>
    </changeSet>

</databaseChangeLog>
