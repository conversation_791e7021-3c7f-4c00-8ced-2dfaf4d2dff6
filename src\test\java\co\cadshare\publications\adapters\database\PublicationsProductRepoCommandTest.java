package co.cadshare.publications.adapters.database;

import co.cadshare.main.Application;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.EntityManager;

import java.math.BigInteger;

import static org.junit.Assert.assertEquals;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class)
@Transactional
public class PublicationsProductRepoCommandTest {

    @Autowired
    private EntityManager entityManager;

    @Autowired
    private PublicationsProductRepo sut;

    PublicationsProductEntity product;

    @Before
    public void before() {
        product = new PublicationsProductEntity();
        product.setName("Publications Product");
        sut.save(product);
    }

    @Test
    public void save() {
        sut.save(product);
        String sql = String.format("select count(*) from machine where name = '%s'", product.getName());
        int count = ((BigInteger)entityManager.createNativeQuery(sql).getSingleResult()).intValue();
        assertEquals(1, count);
    }

    @Test
    public void update() {
        sut.save(product);
        product.setThumbnailUrl("thumbnailUrl");
        sut.save(product);
        String sql = String.format("select count(*) from machine where thumbnailurl = '%s'", product.getThumbnailUrl());
        int count = ((BigInteger)entityManager.createNativeQuery(sql).getSingleResult()).intValue();
        assertEquals(1, count);
    }
}