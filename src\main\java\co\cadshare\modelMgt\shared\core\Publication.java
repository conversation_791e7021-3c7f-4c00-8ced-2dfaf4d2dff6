package co.cadshare.modelMgt.shared.core;

import co.cadshare.media.core.Image;
import co.cadshare.modelMgt.publicationCategories.core.PublicationCategory;
import co.cadshare.modelMgt.publications.core.Kit;
import co.cadshare.modelMgt.publications.core.ManualStatus;
import co.cadshare.modelMgt.publications.core.TechDoc;
import co.cadshare.modelMgt.publications.core.Video;
import lombok.Data;

import java.sql.Timestamp;
import java.util.List;

@Data
public class Publication {

    private Integer id;
    private String name;
	private String serialNumber;
	private PublicationCategory publicationCategory;
	private Integer featuredViewableId;
    private Image coverImage;
	private Image featuredViewableImage;
    private List<Purchaser> purchasers;
    private List<Viewable> viewables;
    private ManualStatus.Status status;
	private boolean deleted;
	private List<TechDoc> techDocs;
	private List<Video> videos;
	private List<Kit> kits;
	private Integer manufacturerId;
	private Integer createdByUserId;
	private Integer modifiedByUserId;
	private Timestamp createdDate;


	public void delete() {
		this.deleted = true;
	}

	public void publish() {
		status = ManualStatus.Status.PUBLISHED;
	}

	public void unpublish() {
		status = ManualStatus.Status.UNPUBLISHED;
	}
}
