package co.cadshare.masterKits.adapters.api.web;

import co.cadshare.aspects.access.languages.CanUseLanguage;
import co.cadshare.shared.core.Language;
import co.cadshare.masterKits.core.MasterKitSearchResult;
import co.cadshare.shared.core.user.User;
import co.cadshare.masterKits.boundary.*;
import co.cadshare.masterKits.core.MasterKit;
import io.swagger.v3.oas.annotations.Operation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.*;

import java.util.List;


@RestController
@RequestMapping("/purchasers/{purchaser-id}/master-part-kits")
public class PurchaserMasterKitsController {

    private final GetMasterKitService getMasterKitService;
    private final SearchMasterKitService searchMasterKitService;
    private static final Logger logger = LoggerFactory.getLogger(PurchaserMasterKitsController.class);

    @Autowired
    public PurchaserMasterKitsController(GetMasterKitService getMasterKitService,
                                         SearchMasterKitService searchMasterKitService){
        this.getMasterKitService = getMasterKitService;
        this.searchMasterKitService = searchMasterKitService;
    }

    @GetMapping(path = "/{master-part-kit-id}", produces = "application/json")
    @PreAuthorize("hasRole('ROLE_MANUFACTURER_SUB_ENTITY_DEALER') or " +
		    "hasRole('ROLE_MANUFACTURER_SUB_ENTITY_CUSTOMER') or " +
		    "hasRole('ROLE_MANUFACTURER_SUB_ENTITY_DEALER_PLUS') or hasRole('ROLE_MANUFACTURER')")
    @ResponseStatus(HttpStatus.OK)
    @Operation(summary = "Get the details of a specific MasterKit")
    public ResponseEntity<GetMasterKitResponseDto> getMasterKit(@PathVariable("purchaser-id") int purchaserId,
                                                                @PathVariable("master-part-kit-id") Integer masterKitId,
                                                                @AuthenticationPrincipal User currentUser) {

        MasterKit masterKit = this.getMasterKitService.get(masterKitId);
        GetMasterKitResponseDto getResponseDto = MasterKitMapper.Instance.masterKitToGetResponseDto(masterKit);
        return new ResponseEntity<>(getResponseDto, HttpStatus.OK);
    }

    @PreAuthorize(" (hasRole('ROLE_MANUFACTURER_SUB_ENTITY_DEALER') or " +
            " hasRole('ROLE_MANUFACTURER_SUB_ENTITY_CUSTOMER') or " +
            " hasRole('ROLE_MANUFACTURER') or " +
            " hasRole('ROLE_MANUFACTURER_SUB_ENTITY_DEALER_PLUS') and hasRole('PartSearch'))")
    @PostMapping ("/search")
    @CanUseLanguage
    public ResponseEntity<PostMasterKitsSearchResponseDto> searchMasterKitsForPurchaser(@PathVariable("purchaser-id") int purchaserId,
                                                                                        @AuthenticationPrincipal User currentUser,
                                                                                        @RequestBody PostMasterKitsSearchRequestDto dto,
                                                                                        @RequestParam(value = "language") Language language) {

        if (dto.isSearchStringEmptyOrNull()) {
            logger.error("Kit search failed no PartNumber or Description to search");
            return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
        }

        PurchaserMasterKitSearchRequest searchRequest = PurchaserMasterKitSearchRequest.build(purchaserId, dto.getPartNumber(),
                dto.getPartDescription(),
		        currentUser.findLanguage(language));

        List<MasterKitSearchResult> results = this.searchMasterKitService.searchForPurchaser(currentUser, searchRequest);
        PostMasterKitsSearchResponseDto response = new PostMasterKitsSearchResponseDto();
        response.setKits(MasterKitSearchResultMapper.Instance.coresToDtos(results));
        response.setTotalResults(results.size());
        return new ResponseEntity<>(response, HttpStatus.OK);
    }

}