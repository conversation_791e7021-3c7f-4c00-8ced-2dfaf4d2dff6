package co.cadshare.inventory.adapters.api.web;

import co.cadshare.inventory.core.InventoryFile;
import co.cadshare.shared.core.user.User;
import co.cadshare.inventory.boundary.InventoryService;
import com.google.gson.JsonObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;

@RestController
@RequestMapping("/inventory")
@Slf4j
public class InventoryController {

    @Autowired
    private InventoryService inventoryService;

    @Autowired
    private InventoryFileProcessor csvProcessor;

    @PostMapping()
    public ResponseEntity<String> inventoryManagementUpload(@AuthenticationPrincipal User user, @RequestParam("file") MultipartFile file) {
        log.info("Received inventory price file [{}] for processing for manufacturer id", file.getName(), user.getManufacturerId());
        // We will do some quick validation and then hand back to the client. In the background
        // we'll process the file and commit to database
        try {
            InventoryFile inventoryFile = csvProcessor.convert(file);
            inventoryService.save(user.getManufacturerId(), inventoryFile);
        } catch (InventoryFile.UnparseableInventoryFileException | IOException e) {
            JsonObject response = new JsonObject();
            response.addProperty("message", "Error processing file: " + e.getMessage());
            return new ResponseEntity<>(response.toString(), HttpStatus.BAD_REQUEST);
        }
        return new ResponseEntity<>( HttpStatus.ACCEPTED);
    }

    @PostMapping(value = "/stock")
    public ResponseEntity<String> inventoryManagementStockFileUpload(@AuthenticationPrincipal User user, @RequestParam("file") MultipartFile file) {
        log.info("Received inventory stock file [{}] for processing for manufacturer id", file.getName(), user.getManufacturerId());
        // We will do some quick validation and then hand back to the client. In the background
        // we'll process the file and commit to database
        try {
            InventoryFile inventoryFile = csvProcessor.convert(file);
            inventoryService.saveStock(user.getManufacturerId(), inventoryFile);
        } catch (InventoryFile.UnparseableInventoryFileException | IOException e) {
            JsonObject response = new JsonObject();
            response.addProperty("message", "Error processing file: " + e.getMessage());
            return new ResponseEntity<>(response.toString(), HttpStatus.BAD_REQUEST);
        }
        return new ResponseEntity<>( HttpStatus.ACCEPTED);
    }

    @PostMapping(value = "/price")
    public ResponseEntity<String> inventoryManagementPriceFileUpload(@AuthenticationPrincipal User user, @RequestParam("file") MultipartFile file) {
        log.info("Received inventory price file [{}] for processing for manufacturer id", file.getName(), user.getManufacturerId());
        // We will do some quick validation and then hand back to the client. In the background
        // we'll process the file and commit to database
        try {
            InventoryFile inventoryFile = csvProcessor.convert(file);
            inventoryService.savePrice(user.getManufacturerId(), inventoryFile);
        } catch (InventoryFile.UnparseableInventoryFileException | IOException e) {
            JsonObject response = new JsonObject();
            response.addProperty("message", "Error processing file: " + e.getMessage());
            return new ResponseEntity<>(response.toString(), HttpStatus.BAD_REQUEST);
        }
        return new ResponseEntity<>( HttpStatus.ACCEPTED);
    }

    @GetMapping
    @PreAuthorize("hasRole('ROLE_MANUFACTURER') and hasRole('Parts')")
    public ResponseEntity inventoryManagementDownload(@AuthenticationPrincipal User currentUser) {
        log.info("Masterparts data download request for manufacturer id [{}]", currentUser.getManufacturerId());
        String masterPartCSV = null;
        try {
            inventoryService.downloadMasterPartInventory(currentUser.getManufacturerId());
        } catch (Exception e) {
            JsonObject response = new JsonObject();
            response.addProperty("message", "Error processing file: " + e.getMessage());
            return new ResponseEntity<>(response.toString(), HttpStatus.BAD_REQUEST);
        }
        return new ResponseEntity<>(masterPartCSV, HttpStatus.OK);
    }

    @GetMapping(value = "/headers", produces = MediaType.APPLICATION_OCTET_STREAM_VALUE)
    @PreAuthorize("hasRole('ROLE_MANUFACTURER') and hasRole('Parts')")
    public ResponseEntity masterPartFileTemplate(@AuthenticationPrincipal User currentUser) {
        log.info("Masterparts inventory management template for manufacturer id [{}]", currentUser.getManufacturerId());
        String inventoryCSV = null;
        try {
            inventoryCSV = inventoryService.downloadMasterPartInventoryTemplate();
        } catch (Exception e) {
            JsonObject response = new JsonObject();
            response.addProperty("message", "Error processing request: " + e.getMessage());
            return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
        }
        return new ResponseEntity<>(inventoryCSV, HttpStatus.OK);
    }
}
