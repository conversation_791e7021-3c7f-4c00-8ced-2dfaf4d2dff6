package co.cadshare.publications.adapters.database;

import co.cadshare.publications.core.Snapshot;
import co.cadshare.publications.core.Viewable;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface PublicationsSnapshotEntityViewMapper {

    PublicationsSnapshotEntityViewMapper Instance = Mappers.getMapper(PublicationsSnapshotEntityViewMapper.class);

    Snapshot entityToCore(PublicationsSnapshotEntityView entityView);
}
