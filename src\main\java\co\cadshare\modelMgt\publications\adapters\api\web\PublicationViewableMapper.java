package co.cadshare.modelMgt.publications.adapters.api.web;

import co.cadshare.modelMgt.shared.core.Publication;
import co.cadshare.modelMgt.shared.core.Viewable;
import org.mapstruct.Context;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;
import org.mapstruct.factory.Mappers;

@Mapper
public interface PublicationViewableMapper {

	PublicationViewableMapper Instance = Mappers.getMapper(PublicationViewableMapper.class);

	@Mapping(source="product.name", target="productName")
	@Mapping(source="product.id", target="productId")
	@Mapping(source="product.range.name", target="rangeName")
	@Mapping(source = ".",target="featuredViewable", qualifiedByName="viewableWithContext")
	public PublicationViewableListItemDto viewableToViewableListItemDto(Viewable viewable, @Context Publication context);

	@Named("viewableWithContext")
	default boolean viewableWithContext(Viewable viewable, @Context Publication context) {
		if(context.getFeaturedViewableId() == null) return false;
		return context.getFeaturedViewableId().equals(viewable.getId());
	}
}
