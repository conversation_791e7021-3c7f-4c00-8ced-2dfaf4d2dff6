package co.cadshare.modelMgt.publications.adapters.database;

import co.cadshare.modelMgt.publications.core.Customer;
import co.cadshare.modelMgt.publications.core.Dealer;
import co.cadshare.modelMgt.publications.core.DealerPlus;
import co.cadshare.modelMgt.publications.core.RegionalOffice;
import co.cadshare.modelMgt.shared.core.Purchaser;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.util.List;
import java.util.Set;

@Mapper(uses = {  PublicationsUserEntityMapper.class }, subclassExhaustiveStrategy = SubclassExhaustiveStrategy.RUNTIME_EXCEPTION)
public interface PublicationsPurchaserEntityMapper {

    PublicationsPurchaserEntityMapper Instance = Mappers.getMapper(PublicationsPurchaserEntityMapper.class);

	@Named("coreToEntity")
	@SubclassMapping(source= Customer.class, target=PublicationsCustomerEntity.class)
	@SubclassMapping(source= Dealer.class, target=PublicationsDealerEntity.class)
	@SubclassMapping(source= RegionalOffice.class, target= PublicationsRegionalOfficeEntity.class)
	@SubclassMapping(source= DealerPlus.class, target= PublicationsDealerPlusEntity.class)
	PublicationsPurchaserEntity purchaserCoreToEntity(Purchaser core);

	@Named("coresToEntities")
	@IterableMapping(qualifiedByName="coreToEntity")
	Set<PublicationsPurchaserEntity> purchaserCoresToEntities(List<Purchaser> cores);

	@Named("entityToCore")
	@SubclassMapping(source = PublicationsDealerEntity.class, target = Dealer.class)
	@SubclassMapping(source = PublicationsCustomerEntity.class, target = Customer.class)
	@SubclassMapping(source= PublicationsRegionalOfficeEntity.class, target= RegionalOffice.class)
	@SubclassMapping(source= PublicationsDealerPlusEntity.class, target= DealerPlus.class)
	Purchaser entityToPurchaserCore(PublicationsPurchaserEntity entity);

	@Named("entitiesToCores")
	@IterableMapping(qualifiedByName="entityToCore")
	List<Purchaser> entitiesToPurchaserCores(Set<PublicationsPurchaserEntity> entities);

/*


	@CommonPurchaserMappings
	Dealer entityToDealer(PublicationsDealerEntity entity);
	List<Dealer> entitiesToDealers(Set<PublicationsDealerEntity> entities);

	@CommonPurchaserMappings
	PublicationsDealerEntity dealerToEntity(Dealer dealer);
	List<PublicationsDealerEntity> dealersToEntities(List<Dealer> dealers);

	@CommonPurchaserMappings
	Customer entityToCustomer(PublicationsCustomerEntity entity);
	List<Customer> entitiesToCustomers(Set<PublicationsCustomerEntity> entities);

	@CommonPurchaserMappings
	PublicationsCustomerEntity customerToEntity(Customer customer);
	List<PublicationsCustomerEntity> customersToEntities(List<Customer> customers);

	@CommonPurchaserMappings
	RegionalOffice entityToRegionalOffice(PublicationsRegionalOfficeEntity entity);
	List<RegionalOffice> entitiesToRegionalOffices(Set<PublicationsRegionalOfficeEntity> entities);

	@CommonPurchaserMappings
	PublicationsRegionalOfficeEntity regionalOfficeToEntity(RegionalOffice regionalOffice);
	List<PublicationsRegionalOfficeEntity> regionalOfficesToEntities(List<RegionalOffice> regionalOffices);

	@CommonPurchaserMappings
	DealerPlus entityToDealerPlus(PublicationsDealerPlusEntity entity);
	List<DealerPlus> entitiesToDealerPlus(Set<PublicationsDealerPlusEntity> entities);

	@CommonPurchaserMappings
	PublicationsDealerPlusEntity dealerPlusToEntity(DealerPlus dealerPlus);
	List<PublicationsDealerPlusEntity> dealerPlusToEntities(List<DealerPlus> dealerPlus);

	@Mapping(source = "id", target = "id")
	@Mapping(source = "name", target = "name")
	@interface CommonPurchaserMappings{}


 */

}
