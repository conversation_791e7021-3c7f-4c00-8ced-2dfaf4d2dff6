package co.cadshare.masterKits.adapters.api.web;

import co.cadshare.masterKits.core.MasterPartPriceListItem;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;
import org.mapstruct.factory.Mappers;

@Mapper
public interface MasterPartPriceListItemMapper {

    MasterPartPriceListItemMapper Instance = Mappers.getMapper(MasterPartPriceListItemMapper.class);

    @Mapping(source="identifier", target="currencyIdentifier")
    @Mapping(source=".", target="value", qualifiedByName="priceMapping")
    GetMasterKitPriceResponseDto coreToGetResponseDto(MasterPartPriceListItem priceListItem);

    @Named("priceMapping")
    static String priceMapping(MasterPartPriceListItem priceListItem) {
        return priceListItem.getDisplayPrice();
    }
}
