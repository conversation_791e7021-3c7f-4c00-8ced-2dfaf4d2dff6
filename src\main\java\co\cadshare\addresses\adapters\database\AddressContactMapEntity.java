package co.cadshare.addresses.adapters.database;

import co.cadshare.shared.adapters.database.EntityAudit;
import co.cadshare.shared.adapters.database.addresses.BaseAddressEntity;
import lombok.Data;
import lombok.ToString;
import org.hibernate.annotations.NotFound;
import org.hibernate.annotations.NotFoundAction;

import javax.persistence.*;
import java.sql.Timestamp;

@Data
@Entity
@Table(name="addresscontactmap")
public class AddressContactMapEntity implements EntityAudit {

    @Id
    @GeneratedValue (strategy = GenerationType.IDENTITY)
    private Integer id;

    @ToString.Exclude
    @ManyToOne(optional = false)
    @NotFound(action = NotFoundAction.IGNORE)
    @JoinColumn(name="addressid", referencedColumnName="id")
    private BaseAddressEntity address;

    @ToString.Exclude
    @ManyToOne(optional = false, cascade=CascadeType.REFRESH)
    @JoinColumn(name="contactid", referencedColumnName="id")
    private AddressesContactEntity contact;

    @Column(name="deleted")
    private boolean deleted;

	@Column(name="compositeexternalrefid")
	private String compositeExternalRefId;

    @Column(name="createddate")
    private Timestamp createdDate;

    @Column(name="createdbyuserid")
    private Integer createdByUserId;

    @Column(name="modifiedbyuserid")
    private Integer modifiedByUserId;

    @Column(name="modifieddate")
    private Timestamp modifiedDate;
}

