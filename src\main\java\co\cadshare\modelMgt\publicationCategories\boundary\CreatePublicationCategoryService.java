package co.cadshare.modelMgt.publicationCategories.boundary;

import co.cadshare.modelMgt.publicationCategories.core.PublicationCategory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import co.cadshare.shared.core.user.User;
import co.cadshare.aspects.logging.Log;

@Service
public class CreatePublicationCategoryService {

    private final PublicationCategoryCommandPort publicationCategoryCommandPort;

    @Autowired
    public CreatePublicationCategoryService(PublicationCategoryCommandPort publicationCategoryCommandPort) {
        this.publicationCategoryCommandPort = publicationCategoryCommandPort;
    }

    @Log
    public Integer create(User user, PublicationCategory publicationCategory) {
        return this.publicationCategoryCommandPort.create(user, publicationCategory);
    }
}
