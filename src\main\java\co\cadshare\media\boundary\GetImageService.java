package co.cadshare.media.boundary;


import co.cadshare.media.core.Image;
import co.cadshare.shared.boundary.MediaQueryPort;
import org.springframework.stereotype.Service;

@Service
public class GetImageService {

	private final MediaQueryPort mediaPort;

	public GetImageService(MediaQueryPort mediaPort) {
		this.mediaPort = mediaPort;
	}

	public Image getImage(int id) throws Exception {
		return mediaPort.get(id);
	}
}
