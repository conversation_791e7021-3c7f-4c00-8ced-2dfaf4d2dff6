/*
 * Copyright 2016 Bell.
 */
package co.cadshare.masterParts.adapters.database;

import co.cadshare.shared.core.Language;
import co.cadshare.domainmodel.part.Part;
import co.cadshare.masterParts.core.MasterPart;
import co.cadshare.masterParts.core.Price;
import co.cadshare.masterParts.core.SupersededMasterPart;
import co.cadshare.masterParts.core.Translation;
import co.cadshare.masterParts.boundary.MasterPartSearchRequest;
import co.cadshare.masterParts.adapters.api.web.MasterPartPrice;
import co.cadshare.shared.boundary.MasterPartQueryPort;
import co.cadshare.shared.core.manufacturer.subentity.ManufacturerSubEntity;
import co.cadshare.utils.ObjectExtension;
import lombok.experimental.ExtensionMethod;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.EmptyResultDataAccessException;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.namedparam.BeanPropertySqlParameterSource;
import org.springframework.jdbc.core.namedparam.MapSqlParameterSource;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.jdbc.support.GeneratedKeyHolder;
import org.springframework.jdbc.support.KeyHolder;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Repository
@ExtensionMethod(ObjectExtension.class)
public class MasterPartDao implements MasterPartQueryPort {

  private final NamedParameterJdbcTemplate namedParamJdbcTemplate;
  private final static String SEARCH_PARTNUMBER = " AND LOWER(vmpl.partnumber) LIKE LOWER(:partNumber) ";
  private final static String SEARCH_DESCRIPTION_START = " AND LOWER(";
  private final static String SEARCH_DESCRIPTION_END = ") LIKE LOWER(:description) ";

  @Autowired
  public MasterPartDao(NamedParameterJdbcTemplate namedParamJdbcTemplate) {
    this.namedParamJdbcTemplate = namedParamJdbcTemplate;
  }

  @Override
  public String getPartTranslationForMasterPartId(int masterPartId, Integer languageId) {
    Map<String, Object> parameters = new HashMap<String, Object>();
    parameters.put("masterPartId", masterPartId);
    parameters.put("languageId", languageId);
    String description = null;
    try {
      description = namedParamJdbcTemplate.queryForObject(GET_MASTERPART_TRANSLATION_FOR_MPID, parameters, String.class);
    } catch (EmptyResultDataAccessException ex) {
      //Master part uploaded via inventory management no default description found
    }
    return description;
  }

  public int createMasterPart(String partNumber, Integer manufacturerId) {
    KeyHolder keyHolder = new GeneratedKeyHolder();

    MapSqlParameterSource parameters = new MapSqlParameterSource();
    parameters.addValue("partNumber", partNumber);
    parameters.addValue("manufacturerId", manufacturerId);

    int result = namedParamJdbcTemplate.update(CREATE_MASTER_PART, parameters, keyHolder, new String[]{"id"});
    return keyHolder.getKey().intValue();
  }

  @Override
  public MasterPart getMasterPartForMasterPartId(int masterPartId, Integer languageId) {
    Map<String, Object> parameters = new HashMap<>();
    parameters.put("masterPartId", masterPartId);

    MasterPart part = namedParamJdbcTemplate.queryForObject(GET_MASTER_PART_FOR_ID, parameters, new BeanPropertyRowMapper<>(MasterPart.class));
    if (part.isNotNull() && languageId.isNotNull()) {
      part.setDescription(getPartTranslationForMasterPartId(part.getMasterPartId(), languageId));
    }
    return part;
  }

  @Override
  public MasterPart getDealerMasterPartForMasterPartId(int masterPartId, int dealerEntityId, Integer languageId) {
    Map<String, Object> parameters = new HashMap<String, Object>();
    parameters.put("masterPartId", masterPartId);
    parameters.put("dealerEntityId", dealerEntityId);

    MasterPart part = namedParamJdbcTemplate.queryForObject(GET_DEALER_MASTER_PART_FOR_ID, parameters, new BeanPropertyRowMapper<>(MasterPart.class));
    if (part != null && languageId != null) {
      part.setDescription(getPartTranslationForMasterPartId(part.getMasterPartId(), languageId));
    }
    return part;
  }

  @Override
  public MasterPart getPriceListMasterPartForMasterPartId(Integer masterPartId, Integer languageId, Integer priceListIdentifierId) {
    Map<String, Object> parameters = new HashMap<String, Object>();
    parameters.put("masterPartId", masterPartId);
    parameters.put("priceId", priceListIdentifierId);

    MasterPart part = namedParamJdbcTemplate.queryForObject(GET_PRICE_LIST_MASTER_PART_FOR_ID, parameters, new BeanPropertyRowMapper<>(MasterPart.class));
    if (part != null && languageId != null) {
      part.setDescription(getPartTranslationForMasterPartId(part.getMasterPartId(), languageId));
    }
    return part;
  }

  private final static String GET_MASTER_PART_DEALER_PRICE = "SELECT price "
          + "FROM dealerplusmasterpart  "
          + "WHERE masterpartid = :masterPartId "
          + "AND manufacturersubentityid = :dealerEntityId "
          + "LIMIT 1 ";

  private final static String GET_MASTER_PART_PRICE_LIST_PRICE = "SELECT price "
          + "FROM masterpartprice "
          + "WHERE masterpartid = :masterPartId "
          + "AND priceid = :priceListIdentifierId "
          + "LIMIT 1 ";

  @Override
  public Float getMasterPartPriceById(int masterPartId, Integer priceListIdentifierId, Integer dealerEntityId) {
    Map<String, Object> parameters = new HashMap<String, Object>();
    parameters.put("masterPartId", masterPartId);
    Float price = null;
    try {
      if (masterPartId > 0) {
        if (dealerEntityId != null) {
          parameters.put("dealerEntityId", dealerEntityId);
          price = namedParamJdbcTemplate.queryForObject(GET_MASTER_PART_DEALER_PRICE, parameters, Float.class);
        } else if (priceListIdentifierId != null) {
          parameters.put("priceListIdentifierId", priceListIdentifierId);
          price = namedParamJdbcTemplate.queryForObject(GET_MASTER_PART_PRICE_LIST_PRICE, parameters, Float.class);
        } else {
          MasterPart part = getMasterPartForMasterPartId(masterPartId, null);
          if (part != null && part.getPrice() != null) {
            price = part.getPrice();
          }
        }
      }
    } catch (EmptyResultDataAccessException e) {
      return price;
    }
    return price;

  }

  @Override
  public MasterPart getMasterPartForMasterPartId(int masterPartId, Integer languageId, Language defaultLanguage) {
    Map<String, Object> parameters = new HashMap<String, Object>();
    parameters.put("masterPartId", masterPartId);

    MasterPart part = namedParamJdbcTemplate.queryForObject(GET_MASTER_PART_FOR_ID, parameters, new BeanPropertyRowMapper<>(MasterPart.class));

    String description = null;
    if (part != null && languageId != null) {
      description = getPartTranslationForMasterPartId(part.getMasterPartId(), languageId);
      if (description == null && languageId != defaultLanguage.getLanguageId()) {
        description = getPartTranslationForMasterPartId(part.getMasterPartId(), defaultLanguage.getLanguageId());
      }
    }
    part.setDescription(description);
    return part;
  }

  @Override
  public MasterPart getBasicMasterPartByPartNumber(String partNumber, int manufacturerId) {
    Map<String, Object> parameters = new HashMap<String, Object>();
    parameters.put("partNumber", partNumber);
    parameters.put("manufacturerId", manufacturerId);
    try {
      MasterPart part = namedParamJdbcTemplate.queryForObject(GET_SANITISED_MASTER_PART_FOR_PARTNUMBER, parameters, new BeanPropertyRowMapper<>(MasterPart.class));
      return part;
    } catch (EmptyResultDataAccessException e) {
      return null;
    }
  }

  @Override
  public MasterPart getMasterPartByPartNumber(String partNumber, int manufacturerId, Integer languageId) {

    Map<String, Object> parameters = new HashMap<>();
    parameters.put("partNumber", partNumber);
    parameters.put("manufacturerId", manufacturerId);
    try {
      MasterPart part = namedParamJdbcTemplate.queryForObject(GET_MASTER_PART_FOR_PARTNUMBER, parameters, new BeanPropertyRowMapper<>(MasterPart.class));
      if (part != null && languageId != null)
          part.setDescription(getPartTranslationForMasterPartId(part.getMasterPartId(), languageId));

      return part;
    } catch (EmptyResultDataAccessException e) {
      return null;
    }
  }

  public SupersededMasterPart getSupersedingMasterPart(String partNumber, int manufacturerId) {
    String GET_SUPERSESSION_PARTNUMBER = "SELECT id, " +
            "partnumber, " +
            "maxsupersessionid, " +
            "maxsupersessionpartnumber " +
            "FROM v_superseded_masterpart "+
            "WHERE manufacturerid = :manufacturerId " +
            "AND partnumber = :partNumber";

    Map<String, Object> parameters = new HashMap<String, Object>();
    parameters.put("manufacturerId", manufacturerId);
    parameters.put("partNumber", partNumber);

    SupersededMasterPart supersededMasterPart = null;
    try {
      supersededMasterPart = namedParamJdbcTemplate.queryForObject(GET_SUPERSESSION_PARTNUMBER, parameters, new BeanPropertyRowMapper<>(SupersededMasterPart.class));
    } catch (EmptyResultDataAccessException ex) {
      log.info("No supersession part number found for partNumber " + partNumber);
    }
    return supersededMasterPart;
  }

  public List<SupersededMasterPart> getSupersedingMasterPartHistory(String partNumber, int manufacturerId) {
    String GET_SUPERSESSION_PARTNUMBER = "SELECT id, " +
            "partnumber, " +
            "maxsupersessionid, " +
            "maxsupersessionpartnumber " +
            "FROM v_superseded_masterpart "+
            "WHERE manufacturerid = :manufacturerId " +
            "AND maxsupersessionpartnumber = :partNumber";

    Map<String, Object> parameters = new HashMap<String, Object>();
    parameters.put("manufacturerId", manufacturerId);
    parameters.put("partNumber", partNumber);

    List<SupersededMasterPart> supersededMasterParts = null;
    try {
      supersededMasterParts = namedParamJdbcTemplate.query(GET_SUPERSESSION_PARTNUMBER, parameters, new BeanPropertyRowMapper<>(SupersededMasterPart.class));
    } catch (EmptyResultDataAccessException ex) {
      log.error("No supersession part number history found for partNumber " + partNumber);
    }
    return supersededMasterParts;
  }

  @Override
  public List<MasterPart> getMasterPartsForManufacturerId(Integer manufacturerId) {
    Map<String, Object> parameters = new HashMap<String, Object>();
    parameters.put("manufacturerId", manufacturerId);

    List<MasterPart> parts = namedParamJdbcTemplate.query(String.format(GET_ALL_MASTER_PARTS), parameters, new BeanPropertyRowMapper<>(MasterPart.class));
    return parts;
  }

  @Override
  public List<MasterPart> getDealerMasterPartsForManufacturerAndDealerId(int manufacturerId, Integer dealerEntityId) {
    Map<String, Object> parameters = new HashMap<String, Object>();
    parameters.put("manufacturerId", manufacturerId);
    parameters.put("dealerEntityId", dealerEntityId);

    List<MasterPart> parts = namedParamJdbcTemplate.query(String.format(GET_ALL_DEALER_MASTER_PARTS), parameters, new BeanPropertyRowMapper<>(MasterPart.class));
    return parts;
  }

  @Override
  public List<Translation> getMasterPartTranslationsForMasterPartId(Integer masterPartId) {
    Map<String, Object> parameters = new HashMap<String, Object>();
    parameters.put("masterPartId", masterPartId);

    List<Translation> translations = namedParamJdbcTemplate.query(GET_TRANSLATIONS_FOR_MASTER_PART, parameters, new BeanPropertyRowMapper<>(Translation.class));
    return translations;
  }

  @Override
  public List<MasterPartPrice> getMasterPartPriceListsForMasterPartId(int masterpartId) {
    Map<String, Object> parameters = new HashMap<String, Object>();
    parameters.put("masterPartId", masterpartId);

    List<MasterPartPrice> priceList = namedParamJdbcTemplate.query(GET_PRICE_LIST_FOR_MASTER_PART, parameters, new BeanPropertyRowMapper<>(MasterPartPrice.class));
    return priceList;
  }

  private static final String CREATE_PART_LIST_PRICE = "INSERT INTO masterpartprice (priceid, price, masterpartid) "
          + "VALUES(:priceId, :price, :masterPartId)";

  public Boolean createPartListPrice(Price price) {
    BeanPropertySqlParameterSource namedParameters = new BeanPropertySqlParameterSource(price);
    KeyHolder keyHolder = new GeneratedKeyHolder();

    namedParamJdbcTemplate.update(CREATE_PART_LIST_PRICE, namedParameters, keyHolder);
    return true;
  }

  private static final String UPDATE_PART_LIST_PRICE = "UPDATE masterpartprice SET price = :price " +
          "WHERE priceid = :priceId AND masterpartid = :masterPartId";

  public Boolean updatePartPrice(Price price) {
    BeanPropertySqlParameterSource namedParameters = new BeanPropertySqlParameterSource(price);
    KeyHolder keyHolder = new GeneratedKeyHolder();

    namedParamJdbcTemplate.update(UPDATE_PART_LIST_PRICE, namedParameters, keyHolder);
    return true;
  }

  private static final String DELETE_PART_LIST_PRICE = "DELETE FROM masterpartprice " +
          "WHERE identifierId = :identifierId AND masterpartid = :masterPartId";

  public void deletePartPrice(int masterpartId, int identifierId) {
    MapSqlParameterSource mapSqlParameterSource = new MapSqlParameterSource();
    mapSqlParameterSource.addValue("masterPartId", masterpartId);
    mapSqlParameterSource.addValue("identifierId", identifierId);

    namedParamJdbcTemplate.update(DELETE_PART_LIST_PRICE, mapSqlParameterSource);
  }

  @Override
  public List<MasterPart> searchMasterPartsForManufacturer(MasterPartSearchRequest request, int manufacturerId) {
    Map<String, Object> parameters = new HashMap<String, Object>();

    parameters.put("manufacturerId", manufacturerId);

    int size = request.getSize();
    int offset = (request.getPage() * size) - size;

    parameters.put("offset", offset);
    parameters.put("limit", size);

    String searchParams = "";
    if (request.getPartNumber() != null) {
      parameters.put( "partNumber", "%" + request.getPartNumber() + "%");
      searchParams = searchParams.concat(SEARCH_PARTNUMBER);
    }
    Language language = request.getLanguage();
    if (request.getDescription() != null) {
      searchParams = searchParams.concat(SEARCH_DESCRIPTION_START + "vmpl." + language.getLanguageName().toLowerCase() + "description" + SEARCH_DESCRIPTION_END);
      parameters.put("description", "%" + request.getDescription() + "%");
    }

    List<MasterPart> parts = namedParamJdbcTemplate.query(String.format(SEARCH_MASTER_PARTS_FOR_MANUFACTURER, searchParams), parameters, new BeanPropertyRowMapper<>(MasterPart.class));

    setLanguageDescriptionForParts(language, parts);
    return parts;
  }

  @Override
  public List<MasterPart> searchMasterPartsForPurchaser(MasterPartSearchRequest request,
                                                        int manufacturerId,
                                                        ManufacturerSubEntity purchaser,
                                                        boolean priceListEnabled,
                                                        Integer priceListIdentifierId,
                                                        boolean warehousesEnabled,
                                                        Integer warehouseId) {

    Map<String, Object> parameters = new HashMap<String, Object>();

    parameters.put("manufacturerId", manufacturerId);
    parameters.put("mseId", purchaser.getManufacturerSubEntityId());

    int size = request.getSize();
    int offset = (request.getPage() * size) - size;

    parameters.put("offset", offset);
    parameters.put("limit", size);

    String searchParams = "";
    if (request.getPartNumber() != null) {
      parameters.put( "partNumber", "%" + request.getPartNumber() + "%");
      searchParams = searchParams.concat(SEARCH_PARTNUMBER);
    }
    Language language = request.getLanguage();
    if (request.getDescription() != null) {
      searchParams = searchParams.concat(SEARCH_DESCRIPTION_START + "vmpl." + language.getLanguageName().toLowerCase() + "description" + SEARCH_DESCRIPTION_END);
      parameters.put("description", "%" + request.getDescription() + "%");
    }

    List<MasterPart> parts;
    if (priceListEnabled && priceListIdentifierId != null) {
      parameters.put("priceId", priceListIdentifierId);
      if (warehousesEnabled && warehouseId != null) {
        parameters.put("warehouseId", warehouseId);
        parts = namedParamJdbcTemplate.query(String.format(SEARCH_PRICE_LIST_AND_WAREHOUSE_MASTER_PARTS_FOR_PURCHASER, searchParams), parameters, new BeanPropertyRowMapper<>(MasterPart.class));
      } else {
        parts = namedParamJdbcTemplate.query(String.format(SEARCH_PRICE_LIST_MASTER_PARTS_FOR_PURCHASER, searchParams), parameters, new BeanPropertyRowMapper<>(MasterPart.class));
      }
    } else if (purchaser.isDealerPlusCustomer() || purchaser.isDealerPlusDealer()) {
	    parameters.put("dealerEntityId", purchaser.dealerPlusId());
	    parts = namedParamJdbcTemplate.query(String.format(SEARCH_DEALER_MASTER_PARTS_FOR_PURCHASER, searchParams), parameters, new BeanPropertyRowMapper<>(MasterPart.class));
    } else {
      parts = namedParamJdbcTemplate.query(String.format(SEARCH_MASTER_PARTS_FOR_PURCHASER, searchParams), parameters, new BeanPropertyRowMapper<>(MasterPart.class));
    }
    setLanguageDescriptionForParts(language, parts);
    return parts;
  }

	@Override
	public List<MasterPart> searchMasterPartsForDealerPlusPrice(MasterPartSearchRequest request,
		                                                      ManufacturerSubEntity purchaser) {

		Map<String, Object> parameters = new HashMap<>();

		parameters.put("manufacturerId", purchaser.getManufacturerId());
		parameters.put("mseId", purchaser.getManufacturerSubEntityId());

		int size = request.getSize();
		int offset = (request.getPage() * size) - size;

		parameters.put("offset", offset);
		parameters.put("limit", size);

		String searchParams = "";
		if (request.getPartNumber() != null) {
			parameters.put( "partNumber", "%" + request.getPartNumber() + "%");
			searchParams = searchParams.concat(SEARCH_PARTNUMBER);
		}
		Language language = request.getLanguage();
		if (request.getDescription() != null) {
			searchParams = searchParams.concat(SEARCH_DESCRIPTION_START + "vmpl." + language.getLanguageName().toLowerCase() + "description" + SEARCH_DESCRIPTION_END);
			parameters.put("description", "%" + request.getDescription() + "%");
		}

		List<MasterPart> parts;
		parameters.put("dealerEntityId", purchaser.dealerPlusId());
		parts = namedParamJdbcTemplate.query(String.format(SEARCH_DEALER_MASTER_PARTS_FOR_PURCHASER, searchParams), parameters, new BeanPropertyRowMapper<>(MasterPart.class));
		setLanguageDescriptionForParts(language, parts);
		return parts;
	}

  private static final String UPDATE_MASTER_PART_PRICE =
          "UPDATE masterpart SET price = :price "
                  + "WHERE id = :masterPartId";

  public void updateMasterPartPrice(Part partInventory) {
    BeanPropertySqlParameterSource namedParameters = new BeanPropertySqlParameterSource(partInventory);
    int result = namedParamJdbcTemplate.update(UPDATE_MASTER_PART_PRICE, namedParameters);
  }

  private static final String UPDATE_MASTER_PART_NOTE =
          "UPDATE masterpart SET note = :note "
                  + "WHERE id = :masterPartId";

  public void updateMasterPartNote(MasterPart note) {
    BeanPropertySqlParameterSource namedParameters = new BeanPropertySqlParameterSource(note);
    int result = namedParamJdbcTemplate.update(UPDATE_MASTER_PART_NOTE, namedParameters);
  }

  private static final String DELETE_PART_NOTE_SQL =
          "UPDATE public.masterpart SET note = NULL WHERE id = :masterPartId";

  public Boolean deleteMasterPartNote(int masterpartId) {
    MapSqlParameterSource parameters = new MapSqlParameterSource();
    parameters.addValue("masterPartId", masterpartId);
    int result = namedParamJdbcTemplate.update(DELETE_PART_NOTE_SQL, parameters);
    return null;
  }

  private static final String UPDATE_DEALER_MASTER_PART_PRICE = "INSERT INTO dealerplusmasterpart (masterpartid, manufacturersubentityid, price) "
          + "VALUES(:masterPartId, :dealerEntityId, :price) " + "ON CONFLICT (masterpartid, manufacturersubentityid) DO UPDATE SET price = :price";

  public void updateDealerMasterPartPrice(int dealerEntityId, Part partInventory) {
    MapSqlParameterSource mapSqlParameterSource = new MapSqlParameterSource();
    mapSqlParameterSource.addValue("masterPartId", partInventory.getMasterPartId());
    mapSqlParameterSource.addValue("price", partInventory.getPrice());
    mapSqlParameterSource.addValue("dealerEntityId", dealerEntityId);

    namedParamJdbcTemplate.update(UPDATE_DEALER_MASTER_PART_PRICE, mapSqlParameterSource);
  }

  private static final String UPDATE_MASTER_PART_STOCK =
          "UPDATE masterpart SET stocklevel = :stock "
                  + "WHERE id = :masterPartId";

  public void updateMasterPartStock(Part partInventory) {
    BeanPropertySqlParameterSource namedParameters = new BeanPropertySqlParameterSource(partInventory);
    int result = namedParamJdbcTemplate.update(UPDATE_MASTER_PART_STOCK, namedParameters);
  }

  private static final String CREATE_TRANSLATION = "INSERT INTO parttranslation (languageid, description, masterpartid) "
          + "VALUES(:languageId, :description, :masterPartId)";

  public Boolean createPartTranslation(Translation translation) {
    BeanPropertySqlParameterSource namedParameters = new BeanPropertySqlParameterSource(translation);
    KeyHolder keyHolder = new GeneratedKeyHolder();

    namedParamJdbcTemplate.update(CREATE_TRANSLATION, namedParameters, keyHolder);
    return true;
  }

  private static final String UPDATE_TRANSLATION = "UPDATE parttranslation SET description = :description " +
          "WHERE languageid = :languageId AND masterpartid = :masterPartId";

  public Boolean updatePartTranslation(Translation translation) {
    BeanPropertySqlParameterSource namedParameters = new BeanPropertySqlParameterSource(translation);
    KeyHolder keyHolder = new GeneratedKeyHolder();

    namedParamJdbcTemplate.update(UPDATE_TRANSLATION, namedParameters, keyHolder);
    return true;
  }

  private static final String DELETE_TRANSLATION = "DELETE FROM parttranslation " +
          "WHERE languageid = :languageId AND masterpartid = :masterPartId";

  public void deletePartTranslation(int masterpartId, int languageId) {
    MapSqlParameterSource mapSqlParameterSource = new MapSqlParameterSource();
    mapSqlParameterSource.addValue("masterPartId", masterpartId);
    mapSqlParameterSource.addValue("languageId", languageId);

    namedParamJdbcTemplate.update(DELETE_TRANSLATION, mapSqlParameterSource);
  }

  private static final String DELETE_ALL_TRANSLATIONS_FOR_MASTERPART = "DELETE FROM parttranslation " +
          "WHERE masterpartid = :masterPartId";

  public void deleteTranslationsForMasterPart(int masterpartId) {
    MapSqlParameterSource mapSqlParameterSource = new MapSqlParameterSource();
    mapSqlParameterSource.addValue("masterPartId", masterpartId);

    namedParamJdbcTemplate.update(DELETE_ALL_TRANSLATIONS_FOR_MASTERPART, mapSqlParameterSource);
  }

   public Double getWarehouseStockForMasterPart(Integer masterPartId, Integer warehouseId) {
      String sql = "SELECT stock FROM masterpartwarehousestock WHERE masterpartid = :masterPartId AND warehouseid = :warehouseId";
      Map<String, Object> params = new HashMap<>();
      params.put("masterPartId", masterPartId);
      params.put("warehouseId", warehouseId);
      try {
          return namedParamJdbcTemplate.queryForObject(sql, params, Double.class);
      } catch (EmptyResultDataAccessException e) {
          return 0.0;
      }
  }

  private static final String DELETE_ALL_PRICE_LISTS_FOR_MASTERPART = "DELETE FROM masterpartprice " +
          "WHERE masterpartid = :masterPartId";

  public void deletePartPriceForMasterPart(int masterpartId) {
    MapSqlParameterSource mapSqlParameterSource = new MapSqlParameterSource();
    mapSqlParameterSource.addValue("masterPartId", masterpartId);

    namedParamJdbcTemplate.update(DELETE_ALL_PRICE_LISTS_FOR_MASTERPART, mapSqlParameterSource);
  }

  private static final String DELETE_ALL_WAREHOUSE_STOCK_FOR_MASTERPART = "DELETE FROM masterpartwarehousestock " +
          "WHERE masterpartid = :masterPartId";

  public void deletePartStockForMasterPart(int masterpartId) {
    MapSqlParameterSource mapSqlParameterSource = new MapSqlParameterSource();
    mapSqlParameterSource.addValue("masterPartId", masterpartId);

    namedParamJdbcTemplate.update(DELETE_ALL_WAREHOUSE_STOCK_FOR_MASTERPART, mapSqlParameterSource);
  }

  private static final String DELETE_MASTER_PART_BY_ID = "DELETE FROM masterpart " +
          "WHERE id = :masterPartId";

  public void deleteMasterPartById(int masterpartId) {
    MapSqlParameterSource mapSqlParameterSource = new MapSqlParameterSource();
    mapSqlParameterSource.addValue("masterPartId", masterpartId);

    namedParamJdbcTemplate.update(DELETE_MASTER_PART_BY_ID, mapSqlParameterSource);
  }

  private final static String UPDATE_MASTER_PART_WEIGHT_AND_MASSUNIT = "UPDATE masterpart SET massunit = :massUnit, weight = :weight " +
          "WHERE id = :id";
  public int updateMasterPartMassUnitAndWeightById(Integer masterPartId, String massUnit, String weight) throws Exception {
    Map<String, Object> params = new HashMap<>();
    params.put("id", masterPartId);
    params.put("massUnit", massUnit);
    params.put("weight", weight);
    return namedParamJdbcTemplate.update(UPDATE_MASTER_PART_WEIGHT_AND_MASSUNIT, params);
  }

  private final static String CREATE_PART_TRANSLATION = "INSERT INTO parttranslation (description, languageid, masterpartid) "
          + "VALUES( :description, :languageId, :masterPartId)";

  public void createPartTranslation(String description, Integer languageId, Integer masterPartId) {
    MapSqlParameterSource parameters = new MapSqlParameterSource();
    parameters.addValue("description", description);
    parameters.addValue("languageId", languageId);
    parameters.addValue("masterPartId", masterPartId);
    int result = namedParamJdbcTemplate.update(CREATE_PART_TRANSLATION, parameters);
  }


  private final static String UPDATE_PART_TRANSLATION = "UPDATE parttranslation SET description = :description "
          + "WHERE languageid = :languageId AND masterpartid = :masterPartId";

  public void updatePartTranslation(String description, Integer languageId, Integer masterPartId) {
    MapSqlParameterSource parameters = new MapSqlParameterSource();
    parameters.addValue("description", description);
    parameters.addValue("languageId", languageId);
    parameters.addValue("masterPartId", masterPartId);
    int result = namedParamJdbcTemplate.update(UPDATE_PART_TRANSLATION, parameters);
  }

  private final static String GET_MASTER_PART_ID_FOR_PART_TRANSLATION = "SELECT masterpartid FROM parttranslation pt "
          + "WHERE pt.masterpartid = :masterPartId "
          + "AND pt.languageid = :languageId ";

  @Override
  public Integer getMasterPartForPartTranslation(Integer masterPartId, Integer languageId) {
    Map<String, Object> parameters = new HashMap<String, Object>();
    parameters.put("masterPartId", masterPartId);
    parameters.put("languageId", languageId);
    try {
      return namedParamJdbcTemplate.queryForObject(GET_MASTER_PART_ID_FOR_PART_TRANSLATION, parameters, Integer.class);
    } catch (EmptyResultDataAccessException e) {
      return null;
    }
  }

  private final static String CREATE_MASTER_PART_WAREHOUSE_STOCK = "INSERT INTO masterpartwarehousestock (masterpartid, warehouseid, stock) "
          + "VALUES( :masterPartId, :warehouseId, :stock) "
          + "ON CONFLICT (masterpartid, warehouseid) DO UPDATE SET stock = :stock";

  public void createMasterPartWarehouseStock (Integer masterPartId, Integer warehouseId, Double stock) {
    try {
      MapSqlParameterSource parameters = new MapSqlParameterSource();
      parameters.addValue("masterPartId", masterPartId);
      parameters.addValue("warehouseId", warehouseId);
      parameters.addValue("stock", stock);
      int result = namedParamJdbcTemplate.update(CREATE_MASTER_PART_WAREHOUSE_STOCK, parameters);
    }catch (Exception e) {
      log.error("Error - createMasterPartWarehouseStock: ".concat(e.getMessage()));
    }
  }

  private final static String GET_MASTER_PART_WAREHOUSE_STOCK = "SELECT stock FROM masterpartwarehousestock "
          + "WHERE masterpartid = :masterPartId  AND warehouseid = :warehouseId";

  @Override
  public Double getMasterPartStockById(Integer masterPartId, Integer warehouseId) {
    Map<String, Object> parameters = new HashMap<String, Object>();
    parameters.put("masterPartId", masterPartId);
    parameters.put("warehouseId", warehouseId);
    try {
      return namedParamJdbcTemplate.queryForObject(GET_MASTER_PART_WAREHOUSE_STOCK, parameters, Double.class);
    } catch (EmptyResultDataAccessException ea) {
      return null;
    }
  }

  @Override
  public List<MasterPart> getMasterPartsWeight(List<Integer> masterPartIds) {
    List<MasterPart> masterParts = new ArrayList<>();
    for (Integer masterPartId : masterPartIds) {
      MasterPart part = getMasterPartForMasterPartId(masterPartId, null);
      masterParts.add(part);
    }
    return masterParts;
  }

  private static void setLanguageDescriptionForParts(Language language, List<MasterPart> parts) {
    for (MasterPart part : parts) {
      if ("EN".equalsIgnoreCase(language.getLanguageCode())) {
        part.setDescription((part.getEnglishDescription() != null) ? part.getEnglishDescription() : part.getDefaultDescription());
      } else if ("FR".equalsIgnoreCase(language.getLanguageCode())) {
        part.setDescription((part.getFrenchDescription() != null) ? part.getFrenchDescription() : part.getDefaultDescription());
      } else if ("DE".equalsIgnoreCase(language.getLanguageCode())) {
        part.setDescription((part.getGermanDescription() != null) ? part.getGermanDescription() : part.getDefaultDescription());
      } else if ("RU".equalsIgnoreCase(language.getLanguageCode())) {
        part.setDescription((part.getRussianDescription() != null) ? part.getRussianDescription() : part.getDefaultDescription());
      } else if ("ES".equalsIgnoreCase(language.getLanguageCode())) {
        part.setDescription((part.getSpanishDescription() != null) ? part.getSpanishDescription() : part.getDefaultDescription());
      } else {
        part.setDescription(part.getDefaultDescription());
      }
    }
  }

  private final static String GET_ALL_MASTER_PARTS = "SELECT *, mp.id AS masterpartid, mp.stocklevel AS stock "
          + "FROM masterpart mp "
          + "WHERE mp.manufacturerid = :manufacturerId "
          + "ORDER BY mp.id asc";

  private final static String GET_ALL_DEALER_MASTER_PARTS = "SELECT mp.id AS masterpartid, mp.partnumber, mp.manufacturerid, dmp.price AS price "
          + "FROM masterpart mp "
          + "LEFT JOIN dealerplusmasterpart dmp ON mp.id = dmp.masterpartid "
          + "AND dmp.manufacturersubentityid = :dealerEntityId "
          + "WHERE mp.manufacturerid = :manufacturerId "
          + "ORDER BY mp.id asc";

  private final static String CREATE_MASTER_PART = "INSERT INTO masterpart (partnumber, manufacturerid) "
          + "VALUES( :partNumber, :manufacturerId)";

  private final static String GET_MASTER_PART_FOR_ID = "SELECT *, mp.id AS masterpartid, mp.stocklevel AS stock "
          + "FROM masterpart mp "
          + "WHERE mp.id = :masterPartId";

  private final static String GET_DEALER_MASTER_PART_FOR_ID = "SELECT mp.id AS masterpartid, mp.partnumber, mp.manufacturerid, dmp.price AS price "
          + "FROM masterpart mp "
          + "LEFT JOIN dealerplusmasterpart dmp ON mp.id = dmp.masterpartid "
          + "AND dmp.manufacturersubentityid = :dealerEntityId "
          + "WHERE mp.id = :masterPartId";

  private final static String GET_PRICE_LIST_MASTER_PART_FOR_ID = "SELECT mp.id AS masterpartid, mp.partnumber, mp.manufacturerid, mpp.price AS price, mp.stocklevel AS stock "
          + "FROM masterpart mp "
          + "LEFT JOIN masterpartprice mpp ON mp.id = mpp.masterpartid "
          + "AND mpp.priceid = :priceId "
          + "WHERE mp.id = :masterPartId";

  private final static String GET_MASTER_PART_FOR_PARTNUMBER = "SELECT *, mp.id AS masterpartid, mp.stocklevel AS stock "
          + "FROM masterpart mp "
          + "WHERE mp.partnumber = :partNumber "
          + "AND mp.manufacturerid = :manufacturerId ";

  private final static String GET_SANITISED_MASTER_PART_FOR_PARTNUMBER = "SELECT mp.id AS masterpartid, mp.partnumber, mp.manufacturerid "
          + "FROM masterpart mp "
          + "WHERE mp.partnumber = :partNumber "
          + "AND mp.manufacturerid = :manufacturerId ";

  private final static String GET_MASTERPART_TRANSLATION_FOR_MPID = "SELECT pt.description "
          + "FROM parttranslation pt "
          + "WHERE pt.masterpartid = :masterPartId AND pt.languageid = :languageId ";

  private final static String GET_TRANSLATIONS_FOR_MASTER_PART = "SELECT pt.*, l.display AS displayText, l.code AS languageCode "
          + "FROM parttranslation pt "
          + "INNER JOIN language l ON l.id = pt.languageid "
          + "WHERE  masterpartid = :masterPartId "
          + "ORDER BY languageid asc ";

  private final static String GET_PRICE_LIST_FOR_MASTER_PART = "SELECT mpp.*, pli.identifier, c.name AS currencyName, c.code AS currencyCode, c.symbol AS currencySymbol "
          + "FROM masterpartprice mpp "
          + "INNER JOIN pricelistidentifier pli ON pli.id = mpp.priceid "
          + "INNER JOIN currency c ON c.id = pli.currencyid "
          + "WHERE  masterpartid = :masterPartId "
          + "ORDER BY c.id asc ";

  private final static String SEARCH_MASTER_PARTS_FOR_MANUFACTURER = "SELECT "
          + "masterpartid, partNumber, price, stock, defaultdescription, englishdescription, frenchdescription, germandescription, russiandescription, spanishdescription, "
          + "superseded, "
          + "supersessionpartnumber as \"supersessionPartNumber\", "
          + "maxsupersessionpartid as \"maxSupersessionPartId\", "
          + "maxsupersessionpartnumber as \"maxSupersessionPartNumber\", "
          + "insupersession as \"inSupersession\", "
          + "supersessionreverseindex as \"supersessionReverseIndex\" "
          + "FROM v_manumasterpartlanguage vmpl "
          + "WHERE vmpl.manufacturerid = :manufacturerId "
          + "%s "
          + "ORDER BY LOWER(vmpl.partNumber) ASC, vmpl.masterpartid ASC "
          + "LIMIT :limit OFFSET :offset";

  private final static String SEARCH_DEALER_MASTER_PARTS_FOR_PURCHASER = "SELECT vmpl.masterpartid, vmpl.partnumber, vmpl.manufacturerid, dmp.price AS price, vmpl.note as note, "
          + "vmpl.defaultdescription, vmpl.englishdescription, vmpl.germandescription, vmpl.frenchdescription, vmpl.russiandescription, vmpl.spanishdescription, "
          + "vmpl.superseded, "
          + "vmpl.supersessionpartnumber as \"supersessionPartNumber\", "
          + "vmpl.maxsupersessionpartid as \"maxSupersessionPartId\", "
          + "vmpl.maxsupersessionpartnumber as \"maxSupersessionPartNumber\", "
          + "vmpl.insupersession as \"inSupersession\", "
          + "vmpl.supersessionreverseindex as \"supersessionReverseIndex\" "
          + "FROM v_msemasterpartlanguage vmpl "
          + "LEFT JOIN dealerplusmasterpart dmp ON vmpl.masterpartid = dmp.masterpartid "
          + "AND dmp.manufacturersubentityid = :dealerEntityId "
          + "WHERE vmpl.manufacturerid = :manufacturerId "
          + "AND (vmpl.mseid = :mseId or vmpl.mseid = 0) "
          + "%s "
          + "GROUP BY vmpl.masterpartid, vmpl.partnumber,  vmpl.manufacturerid, dmp.price, vmpl.note, vmpl.defaultdescription, vmpl.englishdescription, vmpl.germandescription, vmpl.frenchdescription, vmpl.russiandescription, vmpl.spanishdescription, vmpl.superseded, vmpl.supersessionpartnumber, vmpl.maxsupersessionpartid, vmpl.maxsupersessionpartnumber, vmpl.insupersession, vmpl.supersessionreverseindex  "
          + "ORDER BY LOWER(vmpl.partNumber) ASC, vmpl.masterpartid ASC "
          + "LIMIT :limit OFFSET :offset";

  private final static String SEARCH_MASTER_PARTS_FOR_PURCHASER = "SELECT masterpartid, partNumber, price, stock, note, defaultdescription, englishdescription, frenchdescription, germandescription, russiandescription, spanishdescription, "
          + "superseded, "
          + "supersessionpartnumber as \"supersessionPartNumber\", "
          + "maxsupersessionpartid as \"maxSupersessionPartId\", "
          + "maxsupersessionpartnumber as \"maxSupersessionPartNumber\", "
          + "insupersession as \"inSupersession\", "
          + "supersessionreverseindex as \"supersessionReverseIndex\" "
          + "FROM v_msemasterpartlanguage vmpl "
          + "WHERE vmpl.manufacturerid = :manufacturerId "
          + "AND (vmpl.mseid = :mseId or vmpl.mseid = 0) "
          + "%s "
          + "GROUP BY vmpl.masterpartid, vmpl.partnumber, vmpl.price, vmpl.stock, vmpl.note, vmpl.defaultdescription, vmpl.englishdescription, vmpl.frenchdescription, vmpl.germandescription, vmpl.russiandescription, vmpl.spanishdescription, vmpl.superseded, vmpl.supersessionpartnumber, vmpl.maxsupersessionpartid, vmpl.maxsupersessionpartnumber, vmpl.insupersession, vmpl.supersessionreverseindex "
          + "ORDER BY LOWER(vmpl.partNumber) ASC, vmpl.masterpartid ASC "
          + "LIMIT :limit OFFSET :offset";

  private final static String SEARCH_PRICE_LIST_MASTER_PARTS_FOR_PURCHASER = "SELECT vmpl.masterpartid as masterpartid, vmpl.partnumber as partNumber, vmpl.manufacturerid as manufacturerId, mpp.price AS price, vmpl.stock as stock, vmpl.note as note, "
          + "vmpl.defaultdescription as defaultdescription, vmpl.englishdescription as englishdescription, vmpl.germandescription as germandescription, vmpl.frenchdescription as frenchdescription, vmpl.russiandescription as russiandescription, vmpl.spanishdescription as spanishdescription, "
          + "vmpl.superseded, "
          + "vmpl.supersessionpartnumber as \"supersessionPartNumber\", "
          + "vmpl.maxsupersessionpartid as \"maxSupersessionPartId\", "
          + "vmpl.maxsupersessionpartnumber as \"maxSupersessionPartNumber\", "
          + "vmpl.insupersession as \"inSupersession\", "
          + "vmpl.supersessionreverseindex as \"supersessionReverseIndex\" "
          + "FROM v_msemasterpartlanguage vmpl "
          + "LEFT JOIN masterpartprice mpp ON vmpl.masterpartid = mpp.masterpartid "
          + "AND mpp.priceid = :priceId "
          + "WHERE vmpl.manufacturerid = :manufacturerId "
          + "AND (vmpl.mseid = :mseId or vmpl.mseid = 0) "
          + "%s "
          + "GROUP BY vmpl.masterpartid, vmpl.partnumber, vmpl.manufacturerid, vmpl.note, mpp.price, vmpl.stock, vmpl.defaultdescription, vmpl.englishdescription, vmpl.germandescription, vmpl.frenchdescription, vmpl.russiandescription, vmpl.spanishdescription, vmpl.superseded, vmpl.supersessionpartnumber, vmpl.maxsupersessionpartid, vmpl.maxsupersessionpartnumber, vmpl.insupersession, vmpl.supersessionreverseindex "
          + "ORDER BY vmpl.masterpartid asc "
          + "LIMIT :limit OFFSET :offset";

  private final static String SEARCH_PRICE_LIST_AND_WAREHOUSE_MASTER_PARTS_FOR_PURCHASER = "SELECT vmpl.masterpartid as masterpartid, vmpl.partnumber as partNumber, vmpl.manufacturerid as manufacturerId, vmpl.note as note, "
          + "mpp.price AS price, mpws.stock AS stock, mp.weight AS weight, mp.massunit AS massUnit, "
          + "vmpl.defaultdescription as defaultdescription, vmpl.englishdescription as englishdescription, vmpl.germandescription as germandescription, vmpl.frenchdescription as frenchdescription, vmpl.russiandescription as russiandescription, vmpl.spanishdescription as spanishdescription, "
          + "vmpl.superseded, "
          + "vmpl.supersessionpartnumber as \"supersessionPartNumber\", "
          + "vmpl.maxsupersessionpartid as \"maxSupersessionPartId\", "
          + "vmpl.maxsupersessionpartnumber as \"maxSupersessionPartNumber\", "
          + "vmpl.insupersession as \"inSupersession\", "
          + "vmpl.supersessionreverseindex as \"supersessionReverseIndex\" "
          + "FROM v_msemasterpartlanguage vmpl "
          + "LEFT JOIN masterpartprice mpp ON vmpl.masterpartid = mpp.masterpartid "
          + "AND mpp.priceid = :priceId "
          + "LEFT JOIN masterpart mp ON mp.id = mpp.masterpartid "
          + "LEFT JOIN masterpartwarehousestock mpws ON vmpl.masterpartid = mpws.masterpartid "
          + "AND mpws.warehouseid = :warehouseId "
          + "WHERE vmpl.manufacturerid = :manufacturerId "
          + "AND (vmpl.mseid = :mseId or vmpl.mseid = 0) "
          + "%s "
          + "GROUP BY vmpl.masterpartid, vmpl.partnumber, vmpl.manufacturerid, vmpl.note, mpp.price, mpws.stock, mp.weight, mp.massunit, vmpl.defaultdescription, vmpl.englishdescription, vmpl.germandescription, vmpl.frenchdescription, vmpl.russiandescription, vmpl.spanishdescription, vmpl.superseded, vmpl.supersessionpartnumber, vmpl.maxsupersessionpartid, vmpl.maxsupersessionpartnumber, vmpl.insupersession, vmpl.supersessionreverseindex "
          + "ORDER BY vmpl.masterpartid asc "
          + "LIMIT :limit OFFSET :offset";

}
