package co.cadshare.services;

import co.cadshare.domainmodel.video.Video;
import co.cadshare.persistence.VideoDao;
import co.cadshare.publications.boundary.ManualQueryPort;
import co.cadshare.publications.boundary.ManualService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.dao.EmptyResultDataAccessException;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Slf4j
@Service
public class VideoService {

    private VideoDao videoDao;
    private ManualService manualService;
    private ManualQueryPort manualDao;


    public VideoService(VideoDao videoDao, ManualService manualService, ManualQueryPort manualDao) {
        this.videoDao = videoDao;
        this.manualService = manualService;
        this.manualDao = manualDao;
    }

    public List<Video> getVideoByManufacturerId(int manufacturerId) {
        List<Video> videos = videoDao.getVideoByManufacturerId(manufacturerId);
        return videos;
    }

    public Video getVideoById(int videoId) {
        return videoDao.getVideoById(videoId);
    }

    public int createVideo(Video video) {
        return videoDao.createVideo(video);
    }

    public boolean updateVideo(Video video) {
        videoDao.updateVideo(video);
        return true;
    }

    public Boolean deleteVideo(int videoId) {
        manualService.deleteVideoFromManualVideoMap(videoId);
        return videoDao.deleteVideo(videoId);
    }

    public List<Video> getVideoByManualId(int manualId) {
        List<Integer> videoIds = manualDao.getVideoIdsByManualId(manualId);

        List<Video> videos = new ArrayList<>();
        for (Integer id : videoIds) {
            try {
                videos.add(videoDao.getVideoById(id));
            } catch (EmptyResultDataAccessException e){
                log.error("Video with id [{}] not found", id);
            }
        }
        return videos;
    }
}
