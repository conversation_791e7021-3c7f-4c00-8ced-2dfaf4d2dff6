package co.cadshare.oauth.services;

import java.util.List;

import co.cadshare.oauth.domainmodel.OAuthUserMapper;
import co.cadshare.users.boundary.UsersService;
import co.cadshare.utils.ObjectUtilsExtension;
import lombok.experimental.ExtensionMethod;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.IncorrectResultSizeDataAccessException;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;
import co.cadshare.shared.core.user.User;
import co.cadshare.users.core.UserPermission;

@Service
@ExtensionMethod(ObjectUtilsExtension.class)
public class CadshareUserDetailsService implements UserDetailsService {

    private static final Logger logger = LoggerFactory.getLogger(CadshareUserDetailsService.class);
    private final UsersService usersService;

    @Autowired
    public CadshareUserDetailsService(UsersService usersService) {
        this.usersService = usersService;
    }

    @Override
    public UserDetails loadUserByUsername(String userIdString) throws UsernameNotFoundException {
        try {
            // TODO - IN HERE DO SOMETHING TO CHECK IF MFA IS IN USE AND RETURN THIS TO THE FRONTEND FOR HANDLING
            User user = usersService.findDetailsByUserid(Integer.parseInt(userIdString));
            if (user.isNull())
              throw new UsernameNotFoundException(String.format("Error looking up userId %s", userIdString));
            usersService.setUserMFASettings(user);
            return OAuthUserMapper.Instance.buildFromUser(user);

      } catch (IncorrectResultSizeDataAccessException e) {
        logger.info("Incorrect number of results returned for userid {}. {}", userIdString, e.getMessage());
        throw new UsernameNotFoundException(String.format("Error looking up userId %s", userIdString));
      } catch (NumberFormatException e) {
        logger.error("Error parsing userId {} to int", userIdString);
        throw new UsernameNotFoundException(String.format("Error looking up userId %s", userIdString));
      }
    }
}