//@autodeskModelUrn=dXJuOmFkc2sub2JqZWN0czpvcy5vYmplY3Q6d2ViYXBwLmNhZHNoYXJlLmNvLzE3MTgyNjY3MjczNzI0NjAtMDAwLUEwMDBCcmVha291dCgyKS56aXA
@autodeskModelUrn=dXJuOmFkc2sub2JqZWN0czpvcy5vYmplY3Q6d2ViYXBwLmNhZHNoYXJlLmNvLzE3MTY5OTQxNTgwOTI0NjAtMDAwLUEwMDAyLnppcA
//@autodeskModelUrn="dXJuOmFkc2sub2JqZWN0czpvcy5vYmplY3Q6ZGV2MWJ1Y2tldC5jYWRzaGFyZS5jby8xNjg4NzMzOTA3MTUzQzQ5NTY0MyUyMFN0YXRpYyUyMHNjcmVlbi5qdA=="
//@autodeskModelUrnEncoded="ZFhKdU9tRmtjMnN1YjJKcVpXTjBjenB2Y3k1dlltcGxZM1E2WkdWMk1XSjFZMnRsZEM1allXUnphR0Z5WlM1amJ5OHhOamc0TnpNek9UQTNNVFV6UXpRNU5UWTBNeVV5TUZOMFlYUnBZeVV5TUhOamNtVmxiaTVxZEE9PQ=="

// Basis Auth Encoded is base64 encoding of client_id:secret from env variables
# @name autodeskToken
POST {{AUTODESK_HOST_URL}}/{{AUTODESK_AUTH_API}}
Content-Type: application/x-www-form-urlencoded
Accept: application/json
Authorization: {{$dotenv BASIC_AUTH_BASE64_ENCODED_2}}

grant_type=client_credentials&scope=data:read

###

//Get Metadata

GET {{AUTODESK_HOST_URL}}/{{AUTODESK_DERIVATIVES_API}}/designdata/{{autodeskModelUrn}}/metadata
Content-Type: application/json
Authorization: Bearer {{autodeskToken.response.body.access_token}}


###

//Get Manifest

# @name manifest

GET {{AUTODESK_HOST_URL}}/{{AUTODESK_DERIVATIVES_API}}/designdata/{{autodeskModelUrn}}/manifest
Content-Type: application/json
Authorization: Bearer {{autodeskToken.response.body.access_token}}


###

//Get PropertyDatabase

GET {{AUTODESK_HOST_URL}}/{{AUTODESK_DERIVATIVES_API}}/designdata/{{autodeskModelUrn}}/manifest/urn%3Aadsk.viewing%3Afs.file%3AdXJuOmFkc2sub2JqZWN0czpvcy5vYmplY3Q6d2ViYXBwLmNhZHNoYXJlLmNvLzE3MTgyNjY3MjczNzI0NjAtMDAwLUEwMDBCcmVha291dCgyKS56aXA%2Foutput%2F1%2Fproperties.db/signedcookies
Authorization: Bearer {{autodeskToken.response.body.access_token}}

###

GET {{AUTODESK_HOST_URL}}/{{AUTODESK_DERIVATIVES_API}}/designdata/{{autodeskModelUrn}}/manifest/urn%3Aadsk.viewing%3Afs.file%3AdXJuOmFkc2sub2JqZWN0czpvcy5vYmplY3Q6d2ViYXBwLmNhZHNoYXJlLmNvLzE3MTgyNjY3MjczNzI0NjAtMDAwLUEwMDBCcmVha291dCgyKS56aXA%2Foutput%2FResource%2Fmodel.sdb/signedcookies  
Authorization: Bearer {{autodeskToken.response.body.access_token}}