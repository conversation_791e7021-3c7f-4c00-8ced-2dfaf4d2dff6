package co.cadshare.masterKits.adapters.database;

import co.cadshare.masterParts.adapters.database.MasterPartTranslationEntityView;
import co.cadshare.masterParts.core.MasterPartType;
import co.cadshare.shared.adapters.database.MasterPartKitMapEntity;
import com.blazebit.persistence.view.EntityView;
import com.blazebit.persistence.view.IdMapping;
import com.blazebit.persistence.view.Mapping;

import java.util.List;

@EntityView(MasterPartKitMapEntity.class)
public interface MasterPartEntityView {
    @IdMapping
    Integer getId();

    @Mapping
    int getQuantity();

    @Mapping(value = "masterPartDetail.id")
    Integer getKitMasterPartId();

    @Mapping(value = "masterPartDetail.partNumber")
    String getKitPartNumber();

	@Mapping(value="masterPartDetail.translations")
	List<MasterPartTranslationEntityView> getTranslations();

	@Mapping(value = "masterPartDetail.stock")
    Double getStock();

    @Mapping(value="masterPartDetail.manufacturer.settings.stockWarehousesEnabled")
    boolean isStockWarehousesEnabled();

    @Mapping(value="masterPartDetail.warehouseStocks")
    List<MasterPartWarehouseStockEntityView> getWarehouseStocks();

    @Mapping(value="masterPartDetail.manufacturer.settings.priceListsEnabled")
    boolean isPriceListsEnabled();

    @Mapping(value = "masterPartDetail.price")
    Float getPrice();

    @Mapping(value = "masterPartDetail.prices")
    List<MasterPartPriceEntityView> getPrices();

    @Mapping(value = "masterPartDetail.type")
    MasterPartType getType();

}
