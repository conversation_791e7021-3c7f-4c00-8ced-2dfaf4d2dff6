package co.cadshare.inventory.boundary;

import co.cadshare.inventory.core.InventoryPriceListUploadProgress;
import co.cadshare.shared.adapters.database.manufacturer.ManufacturerProgressDao;
import co.cadshare.shared.boundary.LanguageQueryPort;
import co.cadshare.shared.core.Audit;
import co.cadshare.shared.core.manufacturer.ManufacturerProgress;
import co.cadshare.inventory.core.PriceListUploadFile;
import co.cadshare.shared.core.user.User;
import co.cadshare.persistence.*;
import co.cadshare.response.PriceListUploadAudit;
import co.cadshare.users.adapters.database.UserDetailsDao;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.sql.Timestamp;
import java.time.Instant;
import java.util.Calendar;
import java.util.HashMap;

@Slf4j
@Service
public class PriceListService {

    private final LanguageQueryPort languageQuery;
    private final UserDetailsDao userDetailsDao;
    private final PriceListDao priceListDao;
    private final ManufacturerProgressDao manufacturerProgressDao;
    private final MasterPartAuditDao masterPartAuditDao;

    @Autowired
    public PriceListService(LanguageQueryPort languageQuery,
                            UserDetailsDao userDetailsDao,
                            PriceListDao priceListDao,
                            ManufacturerProgressDao manufacturerProgressDao,
                            MasterPartAuditDao masterPartAuditDao) {
        this.languageQuery = languageQuery;
        this.userDetailsDao = userDetailsDao;
        this.priceListDao = priceListDao;
        this.manufacturerProgressDao = manufacturerProgressDao;
        this.masterPartAuditDao = masterPartAuditDao;
    }

    @Async
    public void save(User user, PriceListUploadFile priceListFile) {
        log.info("Processing price list file with headers [{}] and [{}] parts", priceListFile.getHeaders(),
                priceListFile.getDataRows().size());
        Integer manufacturerId = user.getManufacturerId();
        HashMap<String, Integer> allPriceListIdentifiers = languageQuery.getAllLanguages();
        ManufacturerProgress progress = new InventoryPriceListUploadProgress(manufacturerId);
        Timestamp now = new java.sql.Timestamp(Calendar.getInstance().getTime().getTime());
        try {
            int progressId = manufacturerProgressDao.createManufacturerProgress(progress);
            progress.setId(progressId);

            Instant startDbInsert = Instant.now();
            priceListDao.insertPriceList(priceListFile,manufacturerId,allPriceListIdentifiers, now);
            log.info("Price List file processed. Db operation took {} milliseconds",
                    Instant.now().minusMillis(startDbInsert.toEpochMilli()).toEpochMilli());
            String oldStatus = progress.getStatus();
            progress.complete();
            Audit audit = new Audit(manufacturerId, "priceListUpload", progress.getStatus(), oldStatus, user.getUserId());
            masterPartAuditDao.createOrderAudit(audit);
        } catch (Exception ex) {
            progress.error();
            throw ex;
        }
        finally {
            manufacturerProgressDao.updateManufacturerProgress(progress);
        }
        priceListDao.cleanUpRemovedPriceLists(manufacturerId, now);
    }

    public PriceListUploadAudit getPriceListUploadHistory(User currentUser) {
        Audit audit = masterPartAuditDao.getPriceListUploadAudit(currentUser.getManufacturerId());
        PriceListUploadAudit response = new PriceListUploadAudit();;

        if (audit != null) {
            User user = userDetailsDao.findByUserId(audit.getUserId());
            response.setFirstName(user.getFirstName());
            response.setLastName(user.getLastName());
            response.setUpdatedDate(audit.getTimestamp());
        }
        return response;
    }

    public HashMap<String, Integer> getPriceIdentifiersManufacturerId(int manufacturerId) {
        return priceListDao.getPriceIdentifiers(manufacturerId);
    }
}
