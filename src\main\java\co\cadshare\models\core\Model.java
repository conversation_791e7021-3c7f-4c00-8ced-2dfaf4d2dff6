package co.cadshare.models.core;

import co.cadshare.shared.core.manufacturer.ManufacturerSettings;

import co.cadshare.models.core.model.AutodeskStatus;
import co.cadshare.models.core.model.FileType;
import co.cadshare.models.core.model.TranslateType;
import lombok.Data;

import java.sql.Timestamp;

import static co.cadshare.models.core.model.AutodeskStatus.*;
import static co.cadshare.models.core.model.AutodeskStatus.PROPERTIES_PROCESSED;

@Data
public class Model {

    private int modelId;
    private String modelName;
    private String modelDescription;
    private Integer machineId;
    private String machineName;
    private Timestamp createdDate;
    private int createdByUserId;
    private String createdByUserFirstName;
    private String createdByUserLastName;
    private String createdByUserFullName;
    private FileType fileType;
    private TranslateType translateType;
    private String topLevelAssembly;
    private AutodeskStatus autodeskStatus;
    private String autodeskStatusDisplay;
    private String filename;
    private String originalFilename;
    private String autodeskUrn;
    private String autodeskProgress;
    private Timestamp modifiedDate;
    private Integer modifiedByUserId;
    private Boolean is2d;
    private Integer linkedPartCount;
    private int viewerSettingsId;
    private String leafNodes;

    private Boolean isSetupComplete;
    private int retries;
    private String reasonForFailure;

    public Model(){}

    public void setAutodeskStatus(AutodeskStatus status) {
        this.autodeskStatus = status;
        this.autodeskStatusDisplay = status.customerReadable();
    }

    public void retriedConnectionIssue(String reasonForFailure) {
        retries++;
        if(retries >= 15) {
            setAutodeskStatus(FAILED);
            setReasonForFailure(reasonForFailure);
        }
    }

    public void partValidationFailed() {
        setAutodeskStatus(FAILED);
        setReasonForFailure("Part batch update failure - part name, number, mass unit or weight length is too large for CADshare database or the part name is not present.");
    }

    public void retriedPartsMissing(String reasonForFailure) {
        retries++;
        if(retries >= 50) {
            setAutodeskStatus(FAILED);
            setReasonForFailure(reasonForFailure);
        }
    }

    public void configureForCreation(ManufacturerSettings settings) {
        TranslateType translateType = TranslateType.SVF;
        if (settings.isSvf2Enabled())
            translateType = TranslateType.SVF2;
        setIsSetupComplete(false);
        setTranslateType(translateType);
        if(getOriginalFilename() != null)
            setFilename(Long.toString(System.currentTimeMillis()).concat(getOriginalFilename()));
    }

    public boolean notFinishedTranslating() {
        return getAutodeskStatus() != AutodeskStatus.PROPERTIES_PROCESSED  &&
                getAutodeskStatus() != AutodeskStatus.PROPERTIES_PROCESSED_WITH_WARNINGS;
    }

    public void partsProcessed(ModelManifestWrapper manifest) {
        try {
            if (manifest.hasWarnings())
                setAutodeskStatus(PROPERTIES_PROCESSED_WITH_WARNINGS);
            else
                setAutodeskStatus(PROPERTIES_PROCESSED);
        } catch (NullPointerException ex) {
            //In here as it is a PDF
            setAutodeskStatus(PROPERTIES_PROCESSED);
            setModel2dIfNull();
        }
    }

    public boolean readyForPartsUpload() {
        return isUploaded() && is3d();
    }

    public boolean partsUpdateNotRequired() { return isUploaded() && getIs2d(); }

    private void setModel2dIfNull() { if (getIs2d() == null) setIs2d(true); }

    private boolean isUploaded() {
        return autodeskStatus.equals(UPLOADED);
    }

    private boolean is3d(){
        return getIs2d() == null || !getIs2d();
    }

}