package co.cadshare.publications.adapters.database;

import co.cadshare.main.Application;
import co.cadshare.models.adapters.database.ModelsUserEntity;
import co.cadshare.models.core.model.FileType;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.EntityManager;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class)
@Transactional
public class PublicationsViewableQueryRepoTest {

    @Autowired
    private EntityManager entityManager;
    @Autowired
    private PublicationsViewableQueryRepo sut;

    ModelsUserEntity user;
    List<PublicationsPurchaserEntity> purchasers;
    PublicationsModelEntity viewable;
    PublicationsProductEntity product;
    PublicationsRangeEntity range;
    PublicationsDealerEntity dealer;
    PublicationsCustomerEntity customer;
    PublicationsViewableConfigEntity viewableConfig;
    PublicationsSnapshotEntity snapshot;

    @Before
    public void Before() {
        buildAndSaveUser();
        buildRange();
        buildProduct();
        buildViewable();
        buildViewableConfig();
        buildSnapshot();
    }

    @Test
    public void shouldReturnViewableWithMultiplePurchasers() {
        buildDealer();
        buildCustomer();
        purchasers.add(dealer);
        purchasers.add(customer);
        persistAll();
        PublicationsViewableEntityView viewableEntityView = sut.getById(viewable.getModelId());
        assertNotNull(viewableEntityView.getPurchasersAssignedToRange());
        assertEquals(2, viewableEntityView.getPurchasersAssignedToRange().size());
    }

    @Test
    public void shouldReturnViewableWithSingleCustomer() {
        buildCustomer();
        purchasers.add(customer);
        persistAll();
        PublicationsViewableEntityView viewableEntityView = sut.getById(viewable.getModelId());
        assertNotNull(viewableEntityView.getPurchasersAssignedToRange());
        assertEquals(1, viewableEntityView.getPurchasersAssignedToRange().size());
    }

    @Test
    public void shouldReturnViewableWithSingleDealer() {
        buildDealer();
        purchasers.add(dealer);
        persistAll();
        PublicationsViewableEntityView viewableEntityView = sut.getById(viewable.getModelId());
        assertNotNull(viewableEntityView.getPurchasersAssignedToRange());
        assertEquals(1, viewableEntityView.getPurchasersAssignedToRange().size());
    }

    @Test
    public void shouldReturnViewableWithNoPurchasers() {
        persistAll();
        PublicationsViewableEntityView viewableEntityView = sut.getById(viewable.getModelId());
        assertNotNull(viewableEntityView.getPurchasersAssignedToRange());
        assertEquals(0, viewableEntityView.getPurchasersAssignedToRange().size());
    }

    @Test
    public void shouldReturnViewableWithSnapshots() {
        persistAll();
        PublicationsViewableEntityView viewableEntityView = sut.getById(viewable.getModelId());
        assertNotNull(viewableEntityView.getSnapshots());
        assertEquals(1, viewableEntityView.getSnapshots().size());
        assertEquals(snapshot.getImgUrl(), viewableEntityView.getSnapshots().get(0).getImgUrl());
    }

    @Test
    public void shouldReturnViewableWithProductThumbnail() {
        persistAll();
        PublicationsViewableEntityView viewableEntityView = sut.getById(viewable.getModelId());
        assertNotNull(viewableEntityView.getProduct().getThumbnailUrl());
        assertEquals(product.getThumbnailUrl(), viewableEntityView.getProduct().getThumbnailUrl());
    }

    private void buildProduct() {
        product = new PublicationsProductEntity();
        product.setName("Publications Product");
        product.setRange(range);
        product.setThumbnailUrl("thumbnailUrl");
    }

    private void buildRange() {
        range = new PublicationsRangeEntity();
        purchasers = new ArrayList<>();
        range.setAssignedPurchasers(purchasers);
    }

    private void buildCustomer() {
        customer = new PublicationsCustomerEntity();
        customer.setName("Publications Customer");
    }

    private void buildDealer() {
        dealer = new PublicationsDealerEntity();
        dealer.setName("Publications Dealer");
    }

    private void buildAndSaveUser() {
        user = new ModelsUserEntity();
        entityManager.persist(user);
    }

    private void buildViewable() {
        viewable = new PublicationsModelEntity();
        viewable.setAutodeskUrn("AUTODESKURN");
        viewable.setCreatedDate(new Timestamp(System.currentTimeMillis()));
        viewable.setCreatedBy(user);
        viewable.setFileType(FileType.ARCHIVE);
        viewable.setFilename("filename");
        viewable.setRetries(0);
        viewable.setIsSetupComplete(false);
        viewable.setProduct(product);
        viewable.setModelName("Publications Viewable");
    }

    private void buildViewableConfig(){
        viewableConfig = new PublicationsViewableConfigEntity();
        viewableConfig.setViewable(viewable);
    }

    private void buildSnapshot() {
        snapshot = new PublicationsSnapshotEntity();
        snapshot.setStateId("ROOT");
        snapshot.setImgUrl("imgurl");
        snapshot.setViewableConfig(viewableConfig);
    }

    private void persistAll() {
        entityManager.persist(range);
        entityManager.persist(product);
        entityManager.persist(viewable);
        entityManager.persist(viewableConfig);
        entityManager.persist(snapshot);
    }
}
