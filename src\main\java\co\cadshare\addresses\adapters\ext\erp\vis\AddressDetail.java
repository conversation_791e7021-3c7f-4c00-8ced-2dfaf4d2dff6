package co.cadshare.addresses.adapters.ext.erp.vis;

import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty;
import lombok.Builder;
import lombok.Data;

import java.util.List;

@Data
@Builder
public class AddressDetail {
	@JacksonXmlProperty(localName = "TRADE_PARTNER_ADDR_ID")
    private final String tradePartnerAddressId;
	@JacksonXmlProperty(localName = "STREET_ADDR_1")
	private final String streetAddress1;
	@JacksonXmlProperty(localName = "STREET_ADDR_2")
	private final String streetAddress2;
	@JacksonXmlProperty(localName = "STREET_ADDR_3")
	private final String streetAddr3;
	@JacksonXmlProperty(localName = "CITY")
	private final String city;
	@JacksonXmlProperty(localName = "strSTATE_PROV")
	private final String stateProvince;
	@JacksonXmlProperty(localName = "STATE_PROV_ID")
	private final String stateProvinceId;
	@JacksonXmlProperty(localName = "ZIP_POSTAL_CODE")
	private final String zipPostalCode;
	@JacksonXmlProperty(localName = "COUNTRY")
	private final String country;
	@JacksonXmlProperty(localName = "COUNTRY_ID")
	private final String countryId;
	@JacksonXmlProperty(localName = "SUBTAX_EXEMPT_X")
	private final String subtaxExemptX;
	@JacksonXmlProperty(localName = "SUBTAX_LICENSE_X")
	private final String subtaxLicenseX;
	@JacksonXmlProperty(localName = "ContactList")
	private List<ContactDetail> contactList;
}
