package co.cadshare.products.adapters.database;

import co.cadshare.exceptions.NotFoundException;
import co.cadshare.products.core.Product;
import co.cadshare.products.boundary.ProductCommandPort;
import co.cadshare.products.boundary.ProductQueryPort;
import co.cadshare.aspects.logging.Log;
import co.cadshare.shared.core.user.User;

import java.sql.Timestamp;
import java.util.Calendar;
import java.util.List;

import co.cadshare.shared.adapters.database.ProductEntity;
import co.cadshare.shared.adapters.database.ProductEntityMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class ProductDataGateway implements ProductQueryPort, ProductCommandPort {

    private ProductRepo productRepo;
    private ProductComplexQueryRepo productQueryRepo;

    @Autowired
    public ProductDataGateway(ProductRepo productRepo,
                                  ProductComplexQueryRepo productQueryRepo) {
        this.productRepo = productRepo;
        this.productQueryRepo = productQueryRepo;
    }

    @Override
    public Product get(Integer productId) {
         ProductEntity entity = this.productRepo.getOne(productId);
         if(entity.isDeleted())
            throw new NotFoundException("Product does not exist");
         return ProductEntityMapper.Instance.entityToCore(entity);
    }

    public List<Product> getListForRange(Integer rangeId) {
         List<ProductEntity> entities = this.productQueryRepo.getProductsForRange(rangeId);
         return ProductEntityMapper.Instance.entitiesToCores(entities);
    }

    @Override
    public List<Product> getList() {
        List<ProductEntity> entities = this.productRepo.findAll();
        return ProductEntityMapper.Instance.entitiesToCores(entities);
    }

    @Override
    @Log
    public Integer create(User user, Product product) {
        product.setCreatedByUserId(user.getUserId());
        product.setCreatedDate(new Timestamp(Calendar.getInstance().getTime().getTime()));
        ProductEntity productEntity = ProductEntityMapper.Instance.coreToEntity(product);
        ProductEntity savedEntity = this.productRepo.save(productEntity);
        return savedEntity.getId();
    }

    @Override
    @Log
    public void update(User user, Product product) throws Exception {
        product.setModifiedByUserId(user.getUserId());
        product.setModifiedDate(new Timestamp(Calendar.getInstance().getTime().getTime()));
        this.productRepo.save(ProductEntityMapper.Instance.coreToEntity(product));
    }

    @Override
    @Log
    public void delete(User user, Product product) throws Exception {
        product.setModifiedByUserId(user.getUserId());
        product.setModifiedDate(new Timestamp(Calendar.getInstance().getTime().getTime()));
        this.productRepo.save(ProductEntityMapper.Instance.coreToEntity(product));
    }
}