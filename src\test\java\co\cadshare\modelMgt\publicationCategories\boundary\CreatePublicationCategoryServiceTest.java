package co.cadshare.modelMgt.publicationCategories.boundary;

import co.cadshare.modelMgt.publicationCategories.boundary.CreatePublicationCategoryService;
import co.cadshare.shared.core.user.User;
import co.cadshare.aspects.logging.ServiceLoggingAspect;
import co.cadshare.modelMgt.publicationCategories.boundary.PublicationCategoryCommandPort;
import co.cadshare.modelMgt.publicationCategories.core.PublicationCategory;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.MockitoAnnotations;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.when;

@EnableAspectJAutoProxy
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(classes = {CreatePublicationCategoryService.class, ServiceLoggingAspect.class})
public class CreatePublicationCategoryServiceTest {

    @MockBean
    private PublicationCategoryCommandPort commandPort;
    @Autowired
    private CreatePublicationCategoryService out;
    private User user;
    private PublicationCategory publicationCategory;
    private PublicationCategory errorPublicationCategory = new PublicationCategory();

    @Before
    public void Before() {
        MockitoAnnotations.initMocks(this);
        publicationCategory = buildPublicationCategory();
        user = new User() {{
            setUserId(1);
            setFirstName("firstName");
            setLastName("lastName");
        }};
    }

    @Test
    public void CreatePublicationCategorySuccess() {
        Integer returnVal = Integer.valueOf(1);
        when(commandPort.create(user, publicationCategory)).thenReturn(returnVal);
        Integer result = out.create(user, publicationCategory);
        assertEquals(returnVal, result);
    }

    @Test(expected = RuntimeException.class)
    public void CreatePublicationCategoryFailureException() throws Exception {
        when(commandPort.create(user, errorPublicationCategory)).thenThrow(new RuntimeException("terrible"));
        out.create(user, errorPublicationCategory);
    }

    private PublicationCategory buildPublicationCategory() {
        PublicationCategory publicationCategory = new PublicationCategory();
        return publicationCategory;
    }
}
