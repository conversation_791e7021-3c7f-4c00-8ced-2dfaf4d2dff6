package co.cadshare.addresses.adapters.api.web;

import co.cadshare.aspects.BaseAspect;
import co.cadshare.exceptions.ForbiddenException;
import co.cadshare.shared.core.user.User;
import co.cadshare.users.boundary.UsersService;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Aspect
@Component
public class CanMaintainAddressAspect extends BaseAspect {

    private final UsersService usersService;

    @Autowired
    public CanMaintainAddressAspect(UsersService usersService) {
        this.usersService = usersService;
    }

    @Pointcut("@annotation(CanMaintainAddress)")
    public void serviceLogPointcut(){}

    @Before("serviceLogPointcut()")
    public void checkRbacPermissions(JoinPoint joinPoint) {
        configureInterception(joinPoint);
        int userId = (int)getArgumentByName(joinPoint, "userId");
        User nominatedUser = usersService.findDetailsByUserid(userId);
        boolean validUser = (nominatedUser.isPurchaserUser() ||
                (nominatedUser.isManufacturerUser() && nominatedUser.getManufacturerId().equals(user.getManufacturerId())));
        if(!validUser)
            throw new ForbiddenException("User does not have permission to maintain address");
    }
}
