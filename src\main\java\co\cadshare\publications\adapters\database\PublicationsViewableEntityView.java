package co.cadshare.publications.adapters.database;

import com.blazebit.persistence.view.EntityView;
import com.blazebit.persistence.view.IdMapping;
import com.blazebit.persistence.view.Mapping;

import java.util.List;

@EntityView(PublicationsModelEntity.class)
public interface PublicationsViewableEntityView {

    @IdMapping("modelId")
    int getId();

    @Mapping("modelName")
    String getName();

    @Mapping(value = "product.range.assignedPurchasers")
    List<PublicationsPurchaserEntityView> getPurchasersAssignedToRange();

    @Mapping("isSetupComplete")
    boolean isSetupComplete();

    @Mapping(value="viewableConfigs.snapshots")
    List<PublicationsSnapshotEntityView> getSnapshots();

    @Mapping(value="product")
    PublicationsProductEntityView getProduct();

}
