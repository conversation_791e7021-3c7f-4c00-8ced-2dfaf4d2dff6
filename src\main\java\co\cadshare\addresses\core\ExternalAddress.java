package co.cadshare.addresses.core;

import co.cadshare.utils.ObjectUtilsExtension;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.ExtensionMethod;

import java.util.ArrayList;
import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
@ExtensionMethod(ObjectUtilsExtension.class)
public class ExternalAddress extends Address {

    private String externalRefId;
    private int manufacturerId;
    private int purchaserId;
    @ToString.Exclude
    private List<UserAddressMap> userMaps;

	private List<ExternalAddressContact> contactMaps;

    public ExternalAddress() {
		this.userMaps = new ArrayList<>();
		this.contactMaps = new ArrayList<>();
    }

	@Override
	@JsonIgnore
	public boolean isValid() {
		String errorElement = this.validateAttributes();
		if(errorElement.isNotNull()) {
			String error = String.format(errorTemplate, errorElement);
			this.logger.error(String.format("External address failed CADshare address validation, externalRefId [ %s ] | Cause: %s",
					externalRefId,
					error));
		}
		return errorElement.isNull();
	}

    public boolean matchesExternally(ExternalAddress address) {
        if(externalRefId.isNull() || address.getExternalRefId().isNull())
            return false;
        return (externalRefId.equals(address.getExternalRefId()));
    }

    public boolean appliesTo(ExternalPurchaser purchaser) {
        return this.purchaserId == purchaser.getId();
    }

	@JsonIgnore
    public boolean isNew() {
        return (this.getId() == 0);
    }

    public void shareWithUser(UserAddressMap userMap) {
        if(userMap.getAddress().matchesExternally(this) && !userMaps.contains(userMap))
            userMaps.add(userMap);
    }

	public void delete() {
		setArchived(true);
		contactMaps.forEach(ExternalAddressContact::delete);
	}

	public void refresh(ExternalAddress addressChanges) {
		super.updateAddressDetails(addressChanges);
		contactMaps.forEach(cm -> cm.setAddress(this));
		List<ExternalAddressContact> newContactMaps = addressChanges.getContactMaps().stream()
				.filter(contact -> contactMaps.stream()
						.noneMatch(cm -> cm.getCompositeExternalRefId().equals(contact.getCompositeExternalRefId())))
				.collect(java.util.stream.Collectors.toList());
		newContactMaps.forEach(cm -> cm.setAddress(this));
		contactMaps.addAll(newContactMaps);

		contactMaps.forEach(cm -> cm.refresh(addressChanges));
	}
}
