package co.cadshare.modelMgt.publicationCategories.adapters.database;

import co.cadshare.shared.core.user.User;
import co.cadshare.aspects.logging.ServiceLoggingAspect;
import co.cadshare.modelMgt.publicationCategories.core.PublicationCategory;
import co.cadshare.modelMgt.publicationCategories.boundary.PublicationCategoryCommandPort;
import co.cadshare.modelMgt.publicationCategories.boundary.PublicationCategoryQueryPort;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.MockitoAnnotations;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.*;

@EnableAspectJAutoProxy
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(classes = {PublicationCategoryDataFacade.class, ServiceLoggingAspect.class})
public class PublicationCategoryDataFacadeTest {

    @MockBean
    private PublicationCategoryRepo publicationCategoryRepo;
    @MockBean
    private PublicationCategoryComplexQueryRepo publicationCategoryQueryRepo;
	@MockBean
	private PublicationCategoryDao publicationCategoryDao;
    @Autowired
    PublicationCategoryCommandPort cmdOut;

    private User user;
    private PublicationCategory publicationCategory;
    private PublicationCategoryEntity publicationCategoryEntity;
    private PublicationCategoryEntity publicationCategoryEntityWithId;
    private PublicationCategory errorPublicationCategory;
    private PublicationCategoryEntity errorPublicationCategoryEntity;

    @Before
    public void Before() {
        MockitoAnnotations.initMocks(this);
        publicationCategory = buildPublicationCategory();
        errorPublicationCategory = buildPublicationCategory();
        user = new User() {{
            setUserId(1);
            setFirstName("firstName");
            setLastName("lastName");
        }};
        publicationCategoryEntity = PublicationCategoryEntityMapper.Instance.coreToEntity(publicationCategory);
        errorPublicationCategoryEntity = PublicationCategoryEntityMapper.Instance.coreToEntity(errorPublicationCategory);
        publicationCategoryEntityWithId = PublicationCategoryEntityMapper.Instance.coreToEntity(publicationCategory);
        publicationCategoryEntityWithId.setId(Integer.valueOf(1));
    }

    @Test
    public void CreatePublicationCategorySuccess() {
        when(publicationCategoryRepo.save(any(PublicationCategoryEntity.class))).thenReturn(publicationCategoryEntityWithId);
        Integer result = cmdOut.create(user, publicationCategory);
        verify(publicationCategoryRepo, times(1)).save(argThat(new PublicationCategoryEntityMatcher(publicationCategoryEntity)));
        assertEquals(1, result);
    }

    @Test(expected = RuntimeException.class)
    public void CreatePublicationCategoryFailureException() throws Exception {
        when(publicationCategoryRepo.save(any(PublicationCategoryEntity.class))).thenThrow(new RuntimeException("terrible"));
        cmdOut.create(user, errorPublicationCategory);
    }

    @Test
    public void UpdatePublicationCategorySuccess()  throws Exception {
        when(publicationCategoryRepo.save(publicationCategoryEntity)).thenReturn(publicationCategoryEntityWithId);
        cmdOut.update(user, publicationCategory);
        verify(publicationCategoryRepo, times(1)).save(argThat(new PublicationCategoryEntityMatcher(publicationCategoryEntity)));
    }

    @Test(expected = RuntimeException.class)
    public void UpdatePublicationCategoryFailureException() throws Exception {
        doThrow(new RuntimeException("terrible")).when(publicationCategoryRepo).save(any(PublicationCategoryEntity.class));
        cmdOut.update(user, errorPublicationCategory);
        verify(publicationCategoryRepo, times(1)).save(argThat(new PublicationCategoryEntityMatcher(errorPublicationCategoryEntity)));
    }

    @Test
    public void DeletePublicationCategorySuccess()  throws Exception {
        doNothing().when(publicationCategoryRepo).delete(publicationCategoryEntity);
        cmdOut.delete(user, publicationCategory);
    }

    @Test(expected = RuntimeException.class)
    public void DeletePublicationCategoryFailureException() throws Exception {
        doThrow(new RuntimeException("terrible")).when(publicationCategoryRepo).save(any(PublicationCategoryEntity.class));
        cmdOut.delete(user, errorPublicationCategory);
        verify(publicationCategoryRepo, times(1)).save(argThat(new PublicationCategoryEntityMatcher(errorPublicationCategoryEntity)));
    }

    private PublicationCategory buildPublicationCategory() {
        PublicationCategory publicationCategory = new PublicationCategory();
        return publicationCategory;
    }
}

