package co.cadshare.models.adapters.database;

import co.cadshare.models.core.Model;
import co.cadshare.shared.adapters.database.ProductEntityMapper;
import org.mapstruct.AfterMapping;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper(uses = {ProductEntityMapper.class, ModelsPublicationEntityMapper.class, ModelsPartEntityMapper.class})
public interface ModelsModelEntityMapper {

    ModelsModelEntityMapper Instance = Mappers.getMapper(ModelsModelEntityMapper.class);

    Model clone(Model model);

    @Mapping(source="product.id", target="machineId")
    @Mapping(source="product.name", target="machineName")
    @Mapping(source="createdBy.lastName", target="createdByUserLastName")
    @Mapping(source="createdBy.firstName", target="createdByUserFirstName")
    @Mapping(source="createdBy.id", target="createdByUserId")
    Model entityToCore(ModelsModelEntity entity);

    ModelCommandEntity coreToEntity(Model model);

    @AfterMapping
    default void setFullName(@MappingTarget ModelsModelEntity entity, Model model) {
        model.setCreatedByUserFullName(entity.getCreatedBy().getFirstName() + " " + entity.getCreatedBy().getLastName());
    }

    List<Model> entitiesToCores(List<ModelsModelEntity> entities);

    List<ModelsModelEntity> coresToEntities(List<Model> models);

}
