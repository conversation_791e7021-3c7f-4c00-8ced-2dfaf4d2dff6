package co.cadshare.modelMgt.shared.core;

import co.cadshare.utils.ObjectUtilsExtension;
import lombok.Data;
import lombok.experimental.ExtensionMethod;

@Data
@ExtensionMethod(ObjectUtilsExtension.class)
public class Product {
    private int id;
    private String name;
    private Range range;
    private String thumbnailUrl;

    public boolean thumbnailUrlNotSet() {
        return thumbnailUrl.isNull() || thumbnailUrl.equals("images/placeholder.jpg");
    }
}
