package co.cadshare.controller;

import co.cadshare.shared.core.user.User;
import co.cadshare.domainmodel.warehouse.Warehouse;
import co.cadshare.response.MasterPartWarehouseStock;
import co.cadshare.services.WarehouseService;
import co.cadshare.utils.ManufacturerFinder;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Controller
@PreAuthorize("hasRole('ROLE_MANUFACTURER') or hasRole('ROLE_MANUFACTURER_SUB_ENTITY_DEALER')")
public class WarehouseController {

    @Autowired
    private WarehouseService wareHouseService;

    @Autowired
    private ManufacturerFinder manufacturerFinder;

    @RequestMapping(path = "/manufacturers/{manufacturer-id}/warehouses",
            produces = "application/json",
            method = RequestMethod.GET)
    @Operation(summary = "Get the list of Warehouses for a specified Manufacturer")
    public HttpEntity<List<Warehouse>> getWarehousesForManufacturer(@AuthenticationPrincipal User currentUser,
                                                         @PathVariable("manufacturer-id") int manufacturerId) {
        int userManufacturerId = manufacturerFinder.getUserManufacturerId(currentUser);
        if(manufacturerId == userManufacturerId) {
            List<Warehouse> warehouses = wareHouseService.getWarehousesForManufacturerId(manufacturerId);
            return new ResponseEntity<>(warehouses, HttpStatus.OK);
        } else {
            return new ResponseEntity<>(HttpStatus.FORBIDDEN);
        }
    }

    @RequestMapping(path = "/manufacturers/{manufacturer-id}/masterPartStock",
            consumes = "application/json",
            produces = "application/json",
            method = RequestMethod.PUT)
    @Operation(summary = "Get the warehouse stock for a specified Manufacturer")
    public HttpEntity<List<MasterPartWarehouseStock>> getWarehouseStockForMasterParts(@AuthenticationPrincipal User currentUser,
                                                                                       @PathVariable("manufacturer-id") int manufacturerId,
                                                                                       @RequestBody List<Integer> masterpartIds) {
        int userManufacturerId = manufacturerFinder.getUserManufacturerId(currentUser);
        if(manufacturerId == userManufacturerId) {
            List<MasterPartWarehouseStock> response = wareHouseService.getWareHouseStockForMasterParts(masterpartIds, manufacturerId);
            return new ResponseEntity<>(response, HttpStatus.OK);
        } else {
            return new ResponseEntity<>(HttpStatus.FORBIDDEN);
        }
    }
}
