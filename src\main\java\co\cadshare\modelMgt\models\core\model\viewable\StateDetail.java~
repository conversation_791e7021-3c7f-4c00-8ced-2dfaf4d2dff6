package co.cadshare.models.core.model.viewable;

import co.cadshare.models.core.model.viewable.softCopyDetail.SoftCopyDetail;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import java.sql.Timestamp;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@JsonInclude(Include.NON_NULL)
@Data
@NoArgsConstructor
public class StateDetail {
    private Integer id;
    private int viewableId;
    private String stateId;
    private String parentId;
    private String childrenIds;
    private String visibleDbIds;
    private String state;
    private Integer sequence;
    private String stateName;
    private String imgUrl;
    private String notes;
    private String explodeAxis;
    private Integer createdByUserId;
    private Timestamp createdDate;
    private Integer modifiedByUserId;
    private Timestamp modifiedDate;
    @JsonProperty("softCopyDetail")
    private SoftCopyDetail softCopyDetails;

    public static StateDetail createFrom(StateDetail stateDetail) {
        return StateDetail.builder()
            .stateId(stateDetail.getStateId())
            .parentId(stateDetail.getParentId())
            .childrenIds(stateDetail.getChildrenIds())
            .visibleDbIds(stateDetail.getVisibleDbIds())
            .state(stateDetail.getState())
            .sequence(stateDetail.getSequence())
            .stateName(stateDetail.getStateName())
            .imgUrl(stateDetail.getImgUrl())
            .notes(stateDetail.getNotes())
            .explodeAxis(stateDetail.getExplodeAxis())
            .build();
    }

    @Builder
    public StateDetail(Integer id, int viewableId, String stateId, String parentId, String childrenIds, String visibleDbIds, String state,
        Integer sequence, String stateName, String imgUrl, String notes, String explodeAxis, Integer createdByUserId, Timestamp createdDate, Integer modifiedByUserId,
        Timestamp modifiedDate, SoftCopyDetail softCopyDetails) {
        this.id = id;
        this.viewableId = viewableId;
        this.stateId = stateId;
        this.parentId = parentId;
        this.childrenIds = childrenIds;
        this.visibleDbIds = visibleDbIds;
        this.state = state;
        this.sequence = sequence;
        this.stateName = stateName;
        this.imgUrl = imgUrl;
        this.notes = notes;
        this.explodeAxis = explodeAxis;
        this.createdByUserId = createdByUserId;
        this.createdDate = createdDate;
        this.modifiedByUserId = modifiedByUserId;
        this.modifiedDate = modifiedDate;
        this.softCopyDetails = softCopyDetails;
    }
}


