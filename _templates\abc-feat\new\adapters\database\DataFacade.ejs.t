---
to: src/main/java/co/cadshare/<%= h.inflection.pluralize(name) %>/adapters/database/<%= Name %>DataFacade.java
---

package co.cadshare.<%= h.inflection.pluralize(name) %>.adapters.database;

import co.cadshare.exceptions.NotFoundException;
import co.cadshare.<%= h.inflection.pluralize(name) %>.core.<%= Name %>;
import co.cadshare.shared.boundary.QueryFilter;
import co.cadshare.<%= h.inflection.pluralize(name) %>.boundary.<%= Name %>CommandPort;
import co.cadshare.<%= h.inflection.pluralize(name) %>.boundary.<%= Name %>QueryPort;
import co.cadshare.aspects.logging.Log;
import co.cadshare.shared.core.user.User;

import java.sql.Timestamp;
import java.util.Calendar;
import java.util.List;

import org.apache.commons.lang3.NotImplementedException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class <%= Name %>DataFacade implements <%= Name %>QueryPort, <%= Name %>CommandPort {

    private <%= Name %>Repo <%= name %>Repo;
    private <%= Name %>ComplexQueryRepo <%= name %>QueryRepo;

    @Autowired
    public <%= Name %>DataFacade(<%= Name %>Repo <%= name %>Repo,
                                  <%= Name %>ComplexQueryRepo <%= name %>QueryRepo) {
        this.<%= name %>Repo = <%= name %>Repo;
        this.<%= name %>QueryRepo = <%= name %>QueryRepo;
    }

    @Override
    public <%= Name %> get(<%= idtype %> <%= name %>Id) {
         <%= Name %>Entity entity = this.<%= name %>Repo.getOne(<%= name %>Id);
         if(entity.isDeleted())
            throw new NotFoundException("<%= Name %> does not exist");
         return <%= Name %>EntityMapper.Instance.entityToCore(entity);
    }

    public List<<%= Name %>> getListForManufacturer(Integer manufacturerId) {
         List<<%= Name %>Entity> entities = this.<%= name %>QueryRepo.get<%= h.inflection.pluralize(Name) %>ForManufacturer(manufacturerId);
         return <%= Name %>EntityMapper.Instance.entitiesToCores(entities);
    }

    @Override
    public List<<%= Name %>> getList() {
        List<<%= Name %>Entity> entities = this.<%= name %>Repo.findAll();
        return <%= Name %>EntityMapper.Instance.entitiesToCores(entities);
    }

    @Override
    @Log
    public <%= idtype %> create(User user, <%= Name %> <%= name %>) {
        <%= name %>.setCreatedByUserId(user.getUserId());
        <%= name %>.setCreatedDate(new Timestamp(Calendar.getInstance().getTime().getTime()));
        <%= Name %>Entity <%= name %>Entity = <%= Name %>EntityMapper.Instance.coreToEntity(<%= name %>);
        <%= Name %>Entity savedEntity = this.<%= name %>Repo.save(<%= name %>Entity);
        return savedEntity.getId();
    }

    @Override
    @Log
    public void update(User user, <%= Name %> <%= name %>) throws Exception {
        <%= name %>.setModifiedByUserId(user.getUserId());
        <%= name %>.setModifiedDate(new Timestamp(Calendar.getInstance().getTime().getTime()));
        this.<%= name %>Repo.save(<%= Name %>EntityMapper.Instance.coreToEntity(<%= name %>));
    }

    @Override
    @Log
    public void delete(User user, <%= Name %> <%= name %>) throws Exception {
        <%= name %>.setModifiedByUserId(user.getUserId());
        <%= name %>.setModifiedDate(new Timestamp(Calendar.getInstance().getTime().getTime()));
        this.<%= name %>Repo.save(<%= Name %>EntityMapper.Instance.coreToEntity(<%= name %>));
    }
}