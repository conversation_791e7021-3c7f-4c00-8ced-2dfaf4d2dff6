package co.cadshare.masterKits.adapters.database;

import co.cadshare.shared.adapters.database.MasterKitEntity;
import com.blazebit.persistence.view.EntityView;
import com.blazebit.persistence.view.IdMapping;
import com.blazebit.persistence.view.Mapping;

import java.util.List;

@EntityView(MasterKitEntity.class)
public interface MasterKitEntityView {

    @IdMapping
    Integer getId();

    @Mapping
    String getDescription();

    @Mapping
    String getTitle();

    @Mapping
    boolean getDeleted();

    @Mapping
    List<MasterPartEntityView> getMasterParts();
}
