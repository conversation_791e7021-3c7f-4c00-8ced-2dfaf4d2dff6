package co.cadshare.oauth.domainmodel;

import java.util.ArrayList;
import java.util.Collection;
import java.util.HashSet;
import java.util.Set;

import co.cadshare.users.core.UserPermission;
import co.cadshare.utils.ObjectUtilsExtension;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.ExtensionMethod;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.userdetails.UserDetails;

import co.cadshare.shared.core.user.User;

@ExtensionMethod(ObjectUtilsExtension.class)
public class OAuthUser extends User implements UserDetails {

  private static final long serialVersionUID = 1L;
  private Set<Role> roles;

  public void buildAuthRoles() {
    roles = new HashSet<>();
    roles.add(new Role("ROLE_" + getUserType().name()));
    if(getUserPermissions().isNotNull())
      for (UserPermission permission : getUserPermissions())
          roles.add(new Role("ROLE_" + permission.name()));
  }

  @Override
  public Collection<? extends GrantedAuthority> getAuthorities() {
    Collection<GrantedAuthority> authorities = new ArrayList<>();
    if (roles.isNotNull()) {
      for (Role role : roles) {
        SimpleGrantedAuthority authority = new SimpleGrantedAuthority(role.getRoleName());
        authorities.add(authority);
      }
    }
    return authorities;
  }

  @Override
  public String getUsername() {
    return String.valueOf(super.getUserId());
  }

  @Override
  public boolean isAccountNonExpired() {
    return true;
  }

  @Override
  public boolean isAccountNonLocked() {
    boolean active = false;
    if (this.getUserStatus().name().equalsIgnoreCase(UserStatus.ACTIVE.name()) || this.getUserStatus().name().equalsIgnoreCase(UserStatus.ACTIVATION_REQUIRED.name())) {
      active = true;
    }
    return active;
  }

  @Override
  public boolean isCredentialsNonExpired() {
    return true;
  }

  @Override
  public boolean isEnabled() {
    return true;
  }

}
