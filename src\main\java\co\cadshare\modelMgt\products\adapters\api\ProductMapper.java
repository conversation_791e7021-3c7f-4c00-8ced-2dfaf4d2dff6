package co.cadshare.modelMgt.products.adapters.api;

import co.cadshare.modelMgt.products.adapters.api.ext.PostProductRequestDto;
import co.cadshare.modelMgt.products.adapters.api.ext.PutProductRequestDto;
import co.cadshare.modelMgt.products.adapters.api.web.GetManufacturerProductResponseDto;
import co.cadshare.modelMgt.products.core.Product;
import org.mapstruct.IterableMapping;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;
import org.mapstruct.factory.Mappers;
import java.util.List;

@Mapper
public interface ProductMapper {

    ProductMapper Instance = Mappers.getMapper(ProductMapper.class);

    Product postRequestDtoToProduct(PostProductRequestDto postRequestDto);

    Product putRequestDtoToProduct(PutProductRequestDto putRequestDto);

	@Named("productToGetResponseDto")
    GetProductResponseDto productToGetResponseDto(Product product);

	@IterableMapping(qualifiedByName="productToGetResponseDto")
    List<GetProductResponseDto> productToGetListResponseDto(List<Product> products);

	@Named("productToGetManufacturerResponseDto")
	@Mapping(source="rangeName", target="range")
	GetManufacturerProductResponseDto productToGetManufacturerResponseDto(Product product);

	@IterableMapping(qualifiedByName="productToGetManufacturerResponseDto")
	List<GetManufacturerProductResponseDto> productToGetManufacturerListResponseDto(List<Product> products);
}
