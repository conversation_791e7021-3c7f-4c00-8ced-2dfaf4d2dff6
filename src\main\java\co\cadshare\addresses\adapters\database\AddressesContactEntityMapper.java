package co.cadshare.addresses.adapters.database;

import co.cadshare.addresses.core.ExternalContact;
import co.cadshare.addresses.core.ContactName;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper(uses={ UserContactMapEntityMapper.class })
public interface AddressesContactEntityMapper {

	AddressesContactEntityMapper Instance = Mappers.getMapper(AddressesContactEntityMapper.class);

	//CoreToEntity
	AddressesContactEntity coreToEntity(ExternalContact core);

	List<AddressesContactEntity> coresToEntities(List<ExternalContact> cores);


	//Entity To Core
	ExternalContact entityToExternalCore(AddressesContactEntity entity);

	List<ExternalContact > entitiesToExternalCores(List<AddressesContactEntity> entities);


	@Mapping(source="name", target="contactName")
	ContactName entityToCore(AddressesContactEntity entity);

	List<ContactName > entitiesToCores(List<AddressesContactEntity> entities);

}
