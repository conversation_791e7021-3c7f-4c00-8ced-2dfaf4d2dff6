package co.cadshare.modelMgt.publications.adapters.database;

import lombok.Data;

import javax.persistence.*;

@Entity
@Table(name="video")
@Data
public class PublicationsVideoEntity {
	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name="id")
	private Integer id;

	@Column(name="manufacturerid")
	private int manufacturerId;

	@Column(name="name")
	private String name;

	@Column(name="url")
	private String url;
}
