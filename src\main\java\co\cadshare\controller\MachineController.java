package co.cadshare.controller;

import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

import co.cadshare.domainmodel.machine.Machine;
import co.cadshare.modelMgt.models.core.model.AutodeskStatus;
import co.cadshare.modelMgt.models.core.Model;
import co.cadshare.shared.core.user.User;
import co.cadshare.services.MachineService;
import co.cadshare.services.PermissionsService;

@Controller
@RequestMapping("/machine")
public class MachineController {

    @Autowired
    MachineService machineService;

    @Autowired
    private PermissionsService permissionsService;
    
    private static final Logger logger = LoggerFactory.getLogger(MachineController.class);

  @PreAuthorize("hasRole('ROLE_MANUFACTURER')")
    @RequestMapping(value = "/{machineId}/models", method = RequestMethod.GET)
    public HttpEntity<List<Model>> getModelsForMachine(@AuthenticationPrincipal User currentUser, @PathVariable int machineId, 
        @RequestParam(value = "status", required = false) AutodeskStatus status, @RequestParam(value = "setupComplete", required = false) boolean setupComplete) {
        logger.info("ACCESS: User [{}]", currentUser.accessDetails());

        try {
          userHasPermissionsForMachine(currentUser.getUserId(), machineId);
        } catch (Exception ex) {
          return new ResponseEntity<>(null, HttpStatus.FORBIDDEN);
        }
        
        List<Model> machineList = machineService.getAllModelsForMachine(machineId, status, setupComplete);

        return new ResponseEntity<>(machineList, HttpStatus.OK);
    }

  @PreAuthorize("hasRole('ROLE_MANUFACTURER')")
    @RequestMapping(method = RequestMethod.POST, consumes = "application/json")
    public HttpEntity<Integer> createMachine(@AuthenticationPrincipal User currentUser, @RequestBody Machine machine) throws Exception {

        logger.info("ACCESS: User [{}], createMachine, machine [{}]", currentUser.accessDetails(), machine.toString());

        machine.setCreatedByUserId(currentUser.getUserId());
        machine.setModifiedByUserId(currentUser.getUserId());
        int machineId = machineService.createMachine(machine);

        return new ResponseEntity<>(machineId, HttpStatus.OK);
    }

    @RequestMapping(value="/{machineId}", method = RequestMethod.PUT, consumes = "application/json")
    @PreAuthorize("hasRole('ROLE_MANUFACTURER') and @permissionsDao.hasAccessToMachine(#currentUser.getUserId(), #machineId)")
    public HttpEntity<Integer> updateMachine(@AuthenticationPrincipal User currentUser, @RequestBody Machine machine, @PathVariable int machineId) throws Exception {

        logger.info("ACCESS: User [{}], updateMachine, machine [{}]", currentUser.accessDetails(), machine.toString());

        machine.setModifiedByUserId(currentUser.getUserId());
        machine.setMachineId(machineId);
        machineService.updateMachine(machine);

        return new ResponseEntity<>(HttpStatus.OK);
    }

    @RequestMapping(value="/{machineId}", method = RequestMethod.DELETE)
    @PreAuthorize("hasRole('ROLE_MANUFACTURER') and @permissionsDao.hasAccessToMachine(#currentUser.getUserId(), #machineId)")
    public HttpEntity<Boolean> deleteMachine(@AuthenticationPrincipal User currentUser, @PathVariable int machineId) throws Exception {

        logger.info("ACCESS: User [{}], deleteMachine, machine id [{}]", currentUser.accessDetails(), machineId);
        machineService.deleteMachine(currentUser, machineId);

        return new ResponseEntity<>(true, HttpStatus.OK);
    }
    
  private boolean userHasPermissionsForMachine(int userId, int machineId) throws Exception {
    boolean permissions = permissionsService.userHasPermissionsToViewMachine(userId, machineId);
      return permissions;
    }
}
