
Feature: Assign to Purchasers

  Scenario Outline: Assign PublicationCategory attached to Publications as Manufacturer
    Given I am a Manufacturer with email address <manufacturerEmailAddress>
    And I create PublicationCategories named <publicationCategoryNames>
    And I create Publications named <publicationNames> with PublicationCategories named <publicationCategoryNames>
    And I publish this existing Publications named <publicationNames>
    When I assign PublicationCategories named <publicationCategoryNames> to Dealer named <dealerName>
    Then I am a Dealer with email address <dealerEmailAddress>
    And I can see Publications named <publicationNames> in the list of Publications
    Examples:
      | manufacturerEmailAddress     | dealerName           | dealerEmailAddress     | publicationCategoryNames | publicationNames |
      | <EMAIL> | Caterpillar Dealer 1 | <EMAIL> | PubCat1,PubCat2          | Pub1,Pub2        |

  @assign
  Scenario Outline: Assign Publications to Dealer as Manufacturer
    Given I am a Manufacturer with email address <manufacturerEmailAddress>
    And I start with a new Publication
    And I create this new Publication named <publicationName1>
    And I publish this existing Publication named <publicationName1>
    And I start with a new Publication
    And I create this new Publication named <publicationName2>
    And I publish this existing Publication named <publicationName2>
    And I can see a single Publication named <publicationName1>
    And I can see a single Publication named <publicationName2>
    When I assign Publications named <publicationNames> to Purchaser named <dealerName>
    Then I am a Dealer with email address <dealerEmailAddress>
    And I can see Publications named <publicationNames> in the list of Publications
    Examples:
      | manufacturerEmailAddress     | dealerName           | dealerEmailAddress      | publicationNames | publicationName1 | publicationName2 |
      | <EMAIL> | Caterpillar Dealer-2 | <EMAIL> | Pub1,Pub2        | Pub1             | Pub2             |

  Scenario Outline: Assign Publications to Customer as DealerPlus
    Given I am a Manufacturer with email address <manufacturerEmailAddress>
    And I start with a new Publication
    And I create this new Publication named <publicationName1>
    And I publish this existing Publication named <publicationName1>
    And I start with a new Publication
    And I create this new Publication named <publicationName2>
    And I publish this existing Publication named <publicationName2>
    And I can see a single Publication named <publicationName1>
    And I can see a single Publication named <publicationName2>
    And I assign Publications named <publicationNames> to Purchaser named <dealerPlusName>
    When I am a DealerPlus with email address <dealerPlusEmailAddress>
    And I assign Publications named <publicationNames> to Purchaser named <customerName>
    Then I am a Dealer with email address <customerEmailAddress>
    And I can see Publications named <publicationNames> in the list of Publications
    Examples:
      | manufacturerEmailAddress     | dealerPlusEmailAddress      | dealerPlusName           | customerName                          | customerEmailAddress                | publicationNames | publicationName1 | publicationName2 |
      | <EMAIL> | <EMAIL> | Caterpillar DealerPlus-2 | Caterpillar Customer for DealerPlus-2 | <EMAIL> | Pub1,Pub2        | Pub1             | Pub2             |