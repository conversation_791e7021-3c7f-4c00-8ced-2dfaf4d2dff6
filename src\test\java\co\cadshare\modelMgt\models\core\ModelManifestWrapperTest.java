package co.cadshare.modelMgt.models.core;

import co.cadshare.modelMgt.models.core.Model;
import co.cadshare.modelMgt.models.core.ModelManifestWrapper;
import co.cadshare.modelMgt.models.core.model.AutodeskStatus;
import com.autodesk.client.model.*;
import org.junit.Before;
import org.junit.Test;

import static co.cadshare.modelMgt.models.core.model.AutodeskStatus.UPLOADED;
import static org.junit.Assert.*;

import java.util.ArrayList;
import java.util.List;

public class ModelManifestWrapperTest {

    private Manifest manifest;
    private Model model;
    private ModelManifestWrapper out;
    private List<ManifestDerivative> derivatives;
    ManifestDerivative derivative;
    List<ManifestChildren> children;
    ManifestChildren child;
    Messages warnings;

    @Before
    public void Before() {
        manifest = new Manifest();
        model = new Model();
        derivatives = new ArrayList<>();
        derivative = new ManifestDerivative();
        children = new ArrayList<>();
        child = new ManifestChildren();
        children.add(child);
        derivative.setChildren(children);
        derivatives.add(derivative);
        manifest.setDerivatives(derivatives);
        warnings = new Messages();
        manifest.getDerivatives().get(0).getChildren().get(0).setMessages(warnings);
    }

    @Test
    public void manifestIsNull() {
        out = new ModelManifestWrapper(null);
        assertFalse(out.isNotNull());
    }

    @Test
    public void manifestIsNotNull() {
        out = new ModelManifestWrapper(manifest);
        assertTrue(out.isNotNull());
    }

    @Test
    public void statusHasChanged() {
        model.setAutodeskProgress("ProgressTest");
        model.setAutodeskStatus(AutodeskStatus.PENDING);

        manifest.setProgress("ProgressTest");
        manifest.setStatus("InProgress");

        child.setRole(ManifestChildren.RoleEnum._2D);

        out = new ModelManifestWrapper(manifest);

        assertTrue(out.statusOrProgressHasChanged(model));
        assertEquals(AutodeskStatus.INPROGRESS, model.getAutodeskStatus());
        assertEquals("ProgressTest", model.getAutodeskProgress());
        assertEquals(true, model.getIs2d());
    }

    @Test
    public void progressHasChanged() {
        model.setAutodeskProgress("ProgressTest");
        model.setAutodeskStatus(AutodeskStatus.PENDING);

        manifest.setProgress("ProgressAdvanced");
        manifest.setStatus("Pending");

        child.setRole(ManifestChildren.RoleEnum._3D);

        out = new ModelManifestWrapper(manifest);

        assertTrue(out.statusOrProgressHasChanged(model));
        assertEquals(AutodeskStatus.PENDING, model.getAutodeskStatus());
        assertEquals("ProgressAdvanced", model.getAutodeskProgress());
        assertEquals(false, model.getIs2d());
    }

    @Test
    public void statusOrProgressHasChangedWhenUploaded() {
        model.setAutodeskProgress("ProgressTest");
        model.setAutodeskStatus(UPLOADED);

        manifest.setProgress("ProgressAdvanced");
        manifest.setStatus("Uploaded");

        manifest.setDerivatives(null);

        out = new ModelManifestWrapper(manifest);

        assertTrue(out.statusOrProgressHasChanged(model));
        assertEquals(UPLOADED, model.getAutodeskStatus());
        assertEquals("ProgressAdvanced", model.getAutodeskProgress());
    }

    @Test
    public void modelHasntChanged() {
        model.setAutodeskProgress("ProgressTest");
        model.setAutodeskStatus(AutodeskStatus.PENDING);

        manifest.setProgress("ProgressTest");
        manifest.setStatus("Pending");

        out = new ModelManifestWrapper(manifest);

        assertFalse(out.statusOrProgressHasChanged(model));
        assertEquals(AutodeskStatus.PENDING, model.getAutodeskStatus());
        assertEquals("ProgressTest", model.getAutodeskProgress());
    }

    @Test
    public void hasWarnings(){
        warnings.add(new Message());
        out = new ModelManifestWrapper(manifest);
        assertTrue(out.hasWarnings());
    }

    @Test
    public void hasNoWarnings(){
        out = new ModelManifestWrapper(manifest);
        assertFalse(out.hasWarnings());
    }

    @Test
    public void getWarnings(){
        Message message = new Message();
        message.setCode("code");
        message.setMessage("message");
        warnings.add(message);
        out = new ModelManifestWrapper(manifest);
        assertEquals(message, out.getWarnings().get(0));
        assertEquals(1, out.getWarnings().size());
    }

    @Test
    public void getDbUrnSuccess() {
        child.setType(ManifestChildren.TypeEnum.RESOURCE);
        child.setRole(ManifestChildren.RoleEnum.PROPERTYDB);
        child.setUrn("urn:adsk.viewing:fs.file:dXJuOmZvbw/output/Resource/model.sdb");
        out = new ModelManifestWrapper(manifest);
        assertEquals(child.getUrn(), out.getEncodedPropertiesDbUrn());
    }

    @Test
    public void getDbUrnFailed() {
        child.setType(ManifestChildren.TypeEnum.GEOMETRY);
        child.setRole(ManifestChildren.RoleEnum._3D);
        child.setUrn("urn:adsk.viewing:fs.file:dXJuOmZvbw/output/Resource/model.sdb");
        out = new ModelManifestWrapper(manifest);
        assertNull(out.getEncodedPropertiesDbUrn());
    }

}
