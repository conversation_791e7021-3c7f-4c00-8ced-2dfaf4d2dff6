package co.cadshare.modelMgt.shared.boundary;

import co.cadshare.modelMgt.shared.core.Viewable;
import co.cadshare.shared.boundary.QueryPort;

import java.util.List;

public interface ViewableQueryPort extends QueryPort<Viewable, Integer> {

	Viewable getViewable(int viewableId);

	List<Viewable> getListForManufacturer(int manufacturerId);

	List<Viewable> searchViewables(int manufacturerId, String searchTerm);

	List<Viewable> filterViewablesByProduct(int manufacturerId, int productId);
}