package co.cadshare.aspects;

import co.cadshare.shared.core.user.User;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Component;

@Slf4j
@Aspect
@Component
public class AccessLogger {

    @Before("execution(public * co.cadshare.controller.*.*(..))")
    public void logExecutionTime(JoinPoint joinPoint) {
        User user;
        try {
            user = (User) SecurityContextHolder.getContext().getAuthentication().getPrincipal();
        } catch (Exception e) {
            log.info(" User: unknown, JoinPoint: {},Args: {}", joinPoint, joinPoint.getArgs());
            return;
        }

        if (user != null) {
            log.info(" User: {}, JoinPoint: {},Args: {}", user.accessDetails(), joinPoint, joinPoint.getArgs());
        } else {
            log.info("JoinPoint: {}, Args: {}", joinPoint, joinPoint.getArgs());
        }
    }
}
