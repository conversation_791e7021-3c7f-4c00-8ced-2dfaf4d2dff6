package co.cadshare.modelMgt.models.boundary;

import co.cadshare.modelMgt.models.core.Model;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.dao.EmptyResultDataAccessException;

import co.cadshare.exceptions.NotFoundException;
import co.cadshare.shared.core.user.User;
import co.cadshare.aspects.logging.Log;

@Service
public class DeleteModelService {

    private final ModelCommandPort commandPort;
    private final ModelQueryPort queryPort;

    @Autowired
    public DeleteModelService(ModelCommandPort commandPort,
                                    ModelQueryPort queryPort) {
        this.commandPort = commandPort;
        this.queryPort = queryPort;
    }

    @Log
    public void delete(User user, Integer modelId) throws Exception {
        try {
            Model model = this.queryPort.get(modelId);
            this.commandPort.delete(user, model);
        } catch (EmptyResultDataAccessException ex) {
            throw new NotFoundException("Model does not exist");
        }
    }
}
