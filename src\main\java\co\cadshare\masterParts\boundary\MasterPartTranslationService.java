package co.cadshare.masterParts.boundary;

import co.cadshare.masterParts.adapters.database.MasterPartDao;
import co.cadshare.masterParts.core.MasterPartTranslationUploadProgress;
import co.cadshare.shared.boundary.LanguageQueryPort;
import co.cadshare.masterParts.core.Translation;
import co.cadshare.shared.core.manufacturer.ManufacturerProgress;
import co.cadshare.masterParts.core.TranslationFile;
import co.cadshare.shared.adapters.database.manufacturer.ManufacturerProgressDao;
import co.cadshare.persistence.TranslationDao;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.time.Instant;
import java.util.HashMap;
import java.util.List;

@Slf4j
@Service
public class MasterPartTranslationService {

    private final LanguageQueryPort languageQuery;
    private final TranslationDao translationDao;
    private final ManufacturerProgressDao manufacturerProgressDao;
    private final MasterPartDao masterPartDao;

    @Autowired
    public MasterPartTranslationService(LanguageQueryPort languageQuery,
                                              TranslationDao translationDao,
                                              ManufacturerProgressDao manufacturerProgressDao,
                                              MasterPartDao masterPartDao) {
        this.languageQuery = languageQuery;
        this.translationDao = translationDao;
        this.manufacturerProgressDao = manufacturerProgressDao;
	    this.masterPartDao = masterPartDao;
    }

    @Async
    public void save(Integer manufacturerId, TranslationFile translationFile) {
        log.info("Processing translation file with headers [{}] and [{}] translations", translationFile.getHeaders(),
                translationFile.getDataRows().size());
        ManufacturerProgress progress = new MasterPartTranslationUploadProgress(manufacturerId);
        try {
            int progressId = manufacturerProgressDao.createManufacturerProgress(progress);
            progress.setId(progressId);
            Instant startDbInsert = Instant.now();
            HashMap<String, Integer> allLanguages = languageQuery.getAllLanguages();
            translationDao.insertTranslations(translationFile, manufacturerId, allLanguages);
            log.info("Translation file processed. Db operation took {} milliseconds",
                    Instant.now().minusMillis(startDbInsert.toEpochMilli()).toEpochMilli());
            progress.complete();
        } catch (Exception ex) {
            progress.error();
        } finally {
            manufacturerProgressDao.updateManufacturerProgress(progress);
        }
    }

    public List<Translation> getPartTranslations(int masterPartId) {
        return masterPartDao.getMasterPartTranslationsForMasterPartId(masterPartId);
    }

    public Boolean createPartTranslation(Translation translation) {
        return masterPartDao.createPartTranslation(translation);
    }

    public boolean updatePartTranslation(Translation translation) {
        return masterPartDao.updatePartTranslation(translation);
    }

    public boolean deletePartTranslation(int masterPartId, int languageId) {
        masterPartDao.deletePartTranslation(masterPartId, languageId);
        return true;
    }
}
