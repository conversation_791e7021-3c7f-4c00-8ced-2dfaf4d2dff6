package co.cadshare.publications.boundary;

import co.cadshare.publications.core.Manual;
import co.cadshare.publications.core.ManualLatestUpdateResponse;
import co.cadshare.response.CustomerModel;
import java.sql.Timestamp;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import co.cadshare.models.boundary.ModelService;
import co.cadshare.services.ModelUpdateService;
import org.springframework.stereotype.Service;

@Service
public class LatestChangesService {

    private ModelService modelService;
    private ModelUpdateService modelUpdateService;
    private ManualService manualService;

    public LatestChangesService(ModelService modelService, ModelUpdateService modelUpdateService, ManualService manualService) {
        this.modelService = modelService;
        this.modelUpdateService = modelUpdateService;
        this.manualService = manualService;
    }


    public ManualLatestUpdateResponse getManualLatestChanges(int manualId) {
        Manual manual = manualService.getManual(manualId);
        Map<Integer, Timestamp> modelIdToLatestUpdate = getLatestModelUpdatesForManual(manualId);

        return ManualLatestUpdateResponse.builder()
            .manual(manual)
            .modelToLatestUpdate(modelIdToLatestUpdate)
            .build();
    }

    private Map<Integer,Timestamp> getLatestModelUpdatesForManual(int manualId) {
        List<Integer> modelIds = modelService.getModelsByManualId(manualId).stream()
            .map(CustomerModel::getModelId)
            .collect(Collectors.toList());

        return modelIds.size() == 0 ? Collections.EMPTY_MAP : modelUpdateService.getLatestUpdateTimestamps(modelIds);
    }
}
