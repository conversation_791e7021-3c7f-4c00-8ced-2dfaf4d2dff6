// log in

# @name cadshareAuthResponse
POST {{CADSHARE_URL}}/oauth/token
Content-Type: application/x-www-form-urlencoded
Authorization: {{$dotenv BASIC_AUTH_BASE64_ENCODED}}

username={{DEALER_USER_ID}}
&password={{STANDARD_PWD}}
&grant_type=password

###

// search for kits

@cadshareToken = Bearer {{cadshareAuthResponse.response.body.access_token}}

POST {{CADSHARE_URL}}/purchasers/{{DEALER_ID}}/master-part-kits/search?language=EN
Content-Type: application/json
Authorization: {{cadshareToken}}

{
	"partNumber": "123"
}