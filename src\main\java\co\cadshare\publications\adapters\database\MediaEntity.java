package co.cadshare.publications.adapters.database;

import lombok.Data;

import javax.persistence.*;

@Data
@Entity(name="media")
@Inheritance(strategy = InheritanceType.SINGLE_TABLE)
@DiscriminatorColumn(name="mediatype", discriminatorType = DiscriminatorType.STRING)
public abstract class MediaEntity {
    @Id
    @GeneratedValue
    @Column(name="mediaId")
    private long id;
    private String description;
    @Column(name="locationUrl")
    private String locationUrl;
    //private String mediaType;
}
