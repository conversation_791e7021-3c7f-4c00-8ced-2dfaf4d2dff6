package co.cadshare.modelMgt.publications.boundary;

import co.cadshare.modelMgt.shared.core.Publication;
import co.cadshare.modelMgt.shared.boundary.ViewableQueryPort;
import co.cadshare.modelMgt.shared.core.Viewable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service
public class ViewableHydrator implements PublicationAttributeHydrator {

	private final ViewableQueryPort viewableQueryPort;

	@Autowired
	public ViewableHydrator(ViewableQueryPort viewableQueryPort) {
		this.viewableQueryPort = viewableQueryPort;
	}

	@Override
	public void hydrate(PublicationCommand command, Publication publication) {
				List<Viewable> viewableList = new ArrayList<>();
				if(command.hasViewables())
					command.getViewables().forEach(v -> viewableList.add(viewableQueryPort.getViewable(v.getId())));
				publication.setViewables(viewableList);

		}

}
