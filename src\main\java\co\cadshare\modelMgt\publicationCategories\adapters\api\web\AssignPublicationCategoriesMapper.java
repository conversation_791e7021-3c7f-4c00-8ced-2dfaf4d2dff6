package co.cadshare.modelMgt.publicationCategories.adapters.api.web;

import co.cadshare.modelMgt.publicationCategories.boundary.AssignPublicationCategoriesCommand;
import org.mapstruct.Context;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

@Mapper
public interface AssignPublicationCategoriesMapper {

	AssignPublicationCategoriesMapper Instance = Mappers.getMapper(AssignPublicationCategoriesMapper.class);

	AssignPublicationCategoriesCommand dtoToCommand(PostPublicationCategoriesDto dto);
}
