<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                   http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.0.xsd
">

    <changeSet author="AndyB" id="1.12-integration-test-data-create-manufacturer-languages-1">
        <sql stripComments="true">
            INSERT INTO public.manufacturerlanguage(
            id, manufacturerid, languageid, "default")
            VALUES (1, 1, 1, true);
        </sql>
    </changeSet>
    <changeSet author="AndyB" id="1.12-integration-test-data-create-manufacturer-languages-2">
        <sql stripComments="true">
            INSERT INTO public.manufacturerlanguage(
            id, manufacturerid, languageid, "default")
            VALUES (2, 1, 2, false);
        </sql>
    </changeSet>
    <changeSet author="AndyB" id="1.12-integration-test-data-create-manufacturer-languages-3">
        <sql stripComments="true">
            INSERT INTO public.manufacturerlanguage(
            id, manufacturerid, languageid, "default")
            VALUES (3, 2, 1, true);
        </sql>
    </changeSet>
    <changeSet author="AndyB" id="1.12-integration-test-data-create-manufacturer-languages-4">
        <sql stripComments="true">
            INSERT INTO public.manufacturerlanguage(
            id, manufacturerid, languageid, "default")
            VALUES (4, 3, 1, true);
        </sql>
    </changeSet>
    <changeSet author="AndyB" id="1.12-integration-test-data-create-manufacturer-languages-5">
        <sql stripComments="true">
            INSERT INTO public.manufacturerlanguage(
            id, manufacturerid, languageid, "default")
            VALUES (5, 4, 1, true);
        </sql>
    </changeSet>
</databaseChangeLog>
