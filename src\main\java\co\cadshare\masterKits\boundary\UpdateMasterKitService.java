package co.cadshare.masterKits.boundary;

import co.cadshare.aspects.logging.Log;
import co.cadshare.shared.core.user.User;
import co.cadshare.exceptions.NotFoundException;
import co.cadshare.masterKits.core.KitMasterPart;
import co.cadshare.masterKits.core.MasterKit;
import co.cadshare.masterKits.core.PartMasterPart;
import co.cadshare.utils.ObjectUtilsExtension;
import lombok.experimental.ExtensionMethod;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.EmptyResultDataAccessException;
import org.springframework.stereotype.Service;

import javax.transaction.Transactional;
import java.util.ArrayList;
import java.util.List;

@Service
@ExtensionMethod({ObjectUtilsExtension.class})
public class UpdateMasterKitService {

    private final MasterKitCommandPort commandPort;
    private final MasterKitQueryPort queryPort;

    @Autowired
    public UpdateMasterKitService(MasterKitCommandPort commandPort,
                                  MasterKitQueryPort queryPort) {
        this.commandPort = commandPort;
        this.queryPort = queryPort;
    }

    @Log
    @Transactional
    public void update(User user, UpdateMasterKitCommand update) throws Exception {
        try {
            MasterKit masterKit = queryPort.get(update.getMasterKitId());
            boolean kitMasterPartIsChanging = masterKit.kitMasterPartIsChanging(update.getKitMasterPartId());
            KitMasterPart changingKitMasterPart =  kitMasterPartIsChanging ?
                    queryPort.getKitMasterPartForKit(update.getKitMasterPartId()) :
                    null;
            List<PartMasterPart> hydratedMasterPartForUpdate =
                    queryPort.getMasterPartsForKit(new ArrayList<>(update.getMasterParts().keySet()));

            masterKit.update(update, changingKitMasterPart, hydratedMasterPartForUpdate);
            masterKit.validate();

            commandPort.update(user, masterKit);
            if(kitMasterPartIsChanging && masterKit.isNotLegacy())
                commandPort.update(user, masterKit.getFormerKitMasterPart());
        } catch (EmptyResultDataAccessException ex) {
            throw new NotFoundException("MasterKit does not exist");
        }
    }
}
