package co.cadshare.modelMgt.publications.boundary;

import co.cadshare.modelMgt.shared.core.Publication;
import co.cadshare.shared.boundary.CommandPort;

import java.util.List;

public interface PublicationCommandPort extends CommandPort<Publication, Integer> {
	void unassignPublicationsFromDealerPlusCustomers(List<Integer> publicationsToBeUnassigned, Integer purchaserId);

	void assignPublicationsToPurchaser(Integer purchaserId, List<Integer> publications);

	void unassignPublicationsFromPurchaser(int purchaserId);
}
