package co.cadshare.modelMgt.publications.adapters.api.web;

import co.cadshare.modelMgt.publications.core.Dealer;
import org.mapstruct.Mapper;

import java.util.ArrayList;
import java.util.List;

@Mapper
public abstract class CustomerMapper {
    public String dealerToCustomerString(Dealer dealer) {
        return dealer.getName();
    }

    public List<String> dealersToCustomers(List<Dealer> dealers){
        List<String> customers = new ArrayList<>();
        dealers.forEach(dealer -> customers.add(dealerToCustomerString(dealer)));
        return customers;
    }
}
