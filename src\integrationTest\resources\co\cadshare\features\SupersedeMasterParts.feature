Feature: Supersede MasterParts
  MasterParts can be superseded by other MasterParts, meaning when you identify this MasterPart,
  the MasterPart that supersedes it is offered instead

  Scenario Outline: Get Supersession History for Dealer
    Given I am a Dealer with <NAME_EMAIL>
    When I identify a <supersededMasterPart> MasterPart
    Then I am offered a <supersessionMasterPart> as a replacement
    And there is a Supersession History from <supersessionMasterPart> that goes right back to <supersededMasterPart>
    Examples:
      | supersededMasterPart | supersessionMasterPart |
      | "Cat-part-1"         | "Cat-part-3"           |

  Scenario Outline: Get MasterPart for Manufacturer
    Given I am a Manufacturer with <NAME_EMAIL>
    When I identify a <supersededMasterPart> MasterPart
    Then I am offered a <supersededMasterPart> as a replacement
    Examples:
      | supersededMasterPart |  |
      | "Cat-part-1"         |  |

  Scenario Outline: Get Supersession History for Manufacturer
    Given I am a Manufacturer with <NAME_EMAIL>
    When I select a <selectedMasterPart>
    Then there is a Supersession History from <supersessionOriginMasterPart> to <supersessionMasterPart>
    And there are <number> of MasterParts in that Supersession History
    Examples:
      | selectedMasterPart | supersessionOriginMasterPart | supersessionMasterPart | number |
      | "Cat-part-1"       | "Cat-part-1"                 | "Cat-part-3"           | 3      |
      | "Cat-part-2"       | "Cat-part-1"                 | "Cat-part-3"           | 3      |
      | "Cat-part-3"       | "Cat-part-1"                 | "Cat-part-3"           | 3      |

  Scenario: Do Not Get Supersession History for Manufacturer
    Given I am a Manufacturer with <NAME_EMAIL>
    When I select a "Cat-part-18"
    Then there is no Supersession History

  Scenario Outline: Supersede MasterPart with no supersession History
    Given I am a Manufacturer with <NAME_EMAIL>
    And the <selectedMasterPart> has no supersession history
    When I supersede a <selectedMasterPart> by <supersessionMasterPart>
    And I select a <selectedMasterPart>
    Then there is a Supersession History from <selectedMasterPart> to <supersessionMasterPart>
    And there are <number> of MasterParts in that Supersession History
    Examples:
      | selectedMasterPart | supersessionMasterPart | number |
      | "Cat-part-5"       | "Cat-part-6"           | 2      |

  Scenario Outline: Supersede MasterPart with supersession History
    Given I am a Manufacturer with <NAME_EMAIL>
    And I select a <selectedMasterPart>
    And there is a Supersession History from <originMasterPart> to <selectedMasterPart>
    When I supersede a <selectedMasterPart> by <supersessionMasterPart>
    And the <selectedMasterPart> has supersession history starting at <originMasterPart>
    Then there is a Supersession History from <originMasterPart> to <supersessionMasterPart>
    And there are <number> of MasterParts in that Supersession History
    Examples:
      | originMasterPart | selectedMasterPart | supersessionMasterPart | number |
      | "Cat-part-1"     | "Cat-part-3"         | "Cat-part-4"           | 4      |

  Scenario Outline: Split supersession History
    Given I am a Manufacturer with <NAME_EMAIL>
    And I select a <originMasterPart>
    And there is a Supersession History from <originMasterPart> to <supersessionMasterPart>
    When I split the supersession at <splitMasterPart>
    And I select a <originMasterPart>
    Then there is a Supersession History from <originMasterPart> to <splitMasterPart>
    And there are <beforeSplitNumber> of MasterParts in that Supersession History
    And I select a <afterSplitMasterPart>
    And there is a Supersession History from <afterSplitMasterPart> to <supersessionMasterPart>
    And there are <afterSplitNumber> of MasterParts in that Supersession History

    Examples:
      | originMasterPart | splitMasterPart | afterSplitMasterPart | supersessionMasterPart | beforeSplitNumber | afterSplitNumber |
      | "Cat-part-7"     | "Cat-part-8"    | "Cat-part-9"         | "Cat-part-11"          | 2                 | 3                |

  Scenario Outline: Split supersession History and Orphan Last MasterPart on Split MasterPart
    Given I am a Manufacturer with <NAME_EMAIL>
    And I select a <originMasterPart>
    And there is a Supersession History from <originMasterPart> to <supersessionMasterPart>
    When I split the supersession at <splitMasterPart>
    And I select a <originMasterPart>
    Then there is a Supersession History from <originMasterPart> to <splitMasterPart>
    And there are <beforeSplitNumber> of MasterParts in that Supersession History
    And <supersessionMasterPart> is no longer in supersession history
    Examples:
      | originMasterPart | splitMasterPart | supersessionMasterPart | beforeSplitNumber |
      | "Cat-part-35"    | "Cat-part-36"   | "Cat-part-37"          | 2                 |

  Scenario Outline: Split supersession History and Orphan Last MasterPart on Max MasterPart
    Given I am a Manufacturer with <NAME_EMAIL>
    And I select a <originMasterPart>
    And there is a Supersession History from <originMasterPart> to <supersessionMasterPart>
    When I split the supersession at <supersessionMasterPart>
    And I select a <originMasterPart>
    Then there is a Supersession History from <originMasterPart> to <splitMasterPart>
    And there are <beforeSplitNumber> of MasterParts in that Supersession History
    And <supersessionMasterPart> is no longer in supersession history
    Examples:
      | originMasterPart | splitMasterPart | supersessionMasterPart | beforeSplitNumber |
      | "Cat-part-42"    | "Cat-part-43"   | "Cat-part-44"          | 2                 |

  Scenario Outline: Split supersession History and Orphan First MasterPart
    Given I am a Manufacturer with <NAME_EMAIL>
    And I select a <originMasterPart>
    And there is a Supersession History from <originMasterPart> to <supersessionMasterPart>
    When I split the supersession at <originMasterPart>
    And I select a <originMasterPart>
    Then <originMasterPart> is no longer in supersession history
    And I select a <afterSplitMasterPart>
    And there is a Supersession History from <afterSplitMasterPart> to <supersessionMasterPart>
    And there are <afterSplitNumber> of MasterParts in that Supersession History
    Examples:
      | originMasterPart | afterSplitMasterPart | supersessionMasterPart | afterSplitNumber |
      | "Cat-part-38"    | "Cat-part-39"        | "Cat-part-41"          | 3                |

  Scenario Outline: Do Not Supersede MasterPart with MasterPart that already has supersession History
    Given I am a Manufacturer with <NAME_EMAIL>
    And I select a <originMasterPart>
    And there is a Supersession History from <originMasterPart> to <splitMasterPart>
    And I select a <afterSplitMasterPart>
    And there is a Supersession History from <afterSplitMasterPart> to <supersessionMasterPart>
    When I fail to supersede <splitMasterPart> by <supersessionMasterPart>
    And I select a <afterSplitMasterPart>
    Then there is a Supersession History from <afterSplitMasterPart> to <supersessionMasterPart>
    Examples:
      | originMasterPart | splitMasterPart | afterSplitMasterPart | supersessionMasterPart |
      | "Cat-part-27"    | "Cat-part-28"   | "Cat-part-29"        | "Cat-part-30"          |

  Scenario Outline: Remove MasterPart from Supersession History
    Given I am a Manufacturer with <NAME_EMAIL>
    And I select a <originMasterPart>
    And there is a Supersession History from <originMasterPart> to <supersessionMasterPart>
    And there are <number> of MasterParts in that Supersession History
    When I revise the supersession at <reviseMasterPart>
    And I select a <originMasterPart>
    Then there is a Supersession History from <originMasterPart> to <supersessionMasterPart>
    And there are <revisedNumber> of MasterParts in that Supersession History
    Examples:
      | originMasterPart | reviseMasterPart | supersessionMasterPart | number | revisedNumber |
      | "Cat-part-12"    | "Cat-part-13"    | "Cat-part-17"          | 6      | 5             |

  Scenario Outline: Remove MasterPart from Supersession History And Get New Supersession History
    Given I am a Manufacturer with <NAME_EMAIL>
    And I select a <selectedMasterPart>
    And there is a Supersession History from <supersessionOriginMasterPart> to <supersessionMasterPart>
    When I revise the supersession at <reviseMasterPart>
    And I select a <selectedMasterPart>
    Then there is a Supersession History from <supersessionOriginMasterPart> to <supersessionMasterPart>
    And there are <number> of MasterParts in that Supersession History
    Examples:
      | supersessionOriginMasterPart | reviseMasterPart | selectedMasterPart | supersessionMasterPart | number |
      | "Cat-part-21"                | "Cat-part-22"    | "Cat-part-21"      | "Cat-part-23"          | 2      |
      | "Cat-part-24"                | "Cat-part-25"    | "Cat-part-26"      | "Cat-part-26"          | 2      |

  Scenario: Do Not Remove MasterPart from Supersession History With Only 2 MasterParts
    Given I am a Manufacturer with <NAME_EMAIL>
    And I select a "Cat-part-19"
    And there is a Supersession History from "Cat-part-19" to "Cat-part-20"
    And there are 2 of MasterParts in that Supersession History
    When I fail to revise the supersession at "Cat-part-19"
    And I select a "Cat-part-19"
    Then there is a Supersession History from "Cat-part-19" to "Cat-part-20"