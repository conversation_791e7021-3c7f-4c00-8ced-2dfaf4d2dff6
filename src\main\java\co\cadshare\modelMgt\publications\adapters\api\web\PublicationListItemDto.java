package co.cadshare.modelMgt.publications.adapters.api.web;

import co.cadshare.modelMgt.viewables.adapters.api.web.ViewableListItemDto;
import co.cadshare.shared.adapters.api.web.CommonItemDto;
import co.cadshare.shared.adapters.api.web.ListItemDto;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
public class PublicationListItemDto extends ListItemDto {

	private String serialNumber;
	private boolean published;
	private int publicationCategoryId;
	private String publicationCategoryName;
	private List<PublicationViewableListItemDto> viewables;
	private PublicationImageDto featuredViewableImage;
	private PublicationImageDto coverImage;
	private List<PublicationCustomerDto> customers;
	private List<PublicationKitDto> kits;
	private List<PublicationTechDocDto> techDocs;
	private List<PublicationVideoDto> videos;
	private Date lastPublishedDateTime;
	private String publicationStatus;
}
