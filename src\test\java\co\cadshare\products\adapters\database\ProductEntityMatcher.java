package co.cadshare.products.adapters.database;

import co.cadshare.shared.adapters.database.ProductEntity;
import org.mockito.ArgumentMatcher;

public class ProductEntityMatcher implements ArgumentMatcher<ProductEntity> {

    private ProductEntity left;

    public ProductEntityMatcher(ProductEntity left) {
        this.left = left;
    }

    @Override
    public boolean matches(ProductEntity right) {
        boolean returnVal = (left.getId() == null && right.getId() == null) ||
                left.getId() == right.getId();
        return returnVal;
    }
}
