package co.cadshare.masterKits.adapters.database;

import co.cadshare.masterKits.core.MasterKitPriceSearchResult;
import co.cadshare.masterKits.core.MasterKitSearchResult;
import co.cadshare.masterParts.core.MasterPartType;
import co.cadshare.shared.core.Language;
import co.cadshare.shared.core.user.User;
import co.cadshare.utils.ObjectExtension;
import lombok.experimental.ExtensionMethod;
import org.mapstruct.Context;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;
import org.mapstruct.factory.Mappers;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Mapper(uses = MasterPartPriceEntityViewMapper.class)
public interface MasterKitEntityViewMapper {
    MasterKitEntityViewMapper Instance = Mappers.getMapper(MasterKitEntityViewMapper.class);

    @Mapping(source="id", target="kitId")
    @Mapping(source="masterParts", target="masterPartId", qualifiedByName="masterPartIdMapping" )
    @Mapping(source="masterParts", target="partNumber", qualifiedByName="partNumberMapping" )
    @Mapping(source="masterParts", target="partDescription", qualifiedByName="partDescMapping" )
    @Mapping(source="masterParts", target="price", qualifiedByName="priceMapping")
    @Mapping(source="masterParts", target="stock", qualifiedByName="stockMapping")
    MasterKitSearchResult entityToCore(@Context User user, @Context Language language, MasterKitEntityView entityView);
    List<MasterKitSearchResult> entitiesToCores(@Context User user, @Context Language language, List<MasterKitEntityView> entities);

    @Named("masterPartIdMapping")
    static Integer masterPartIdMapping(List<MasterPartEntityView> masterPartEntityViews) {
        Optional<MasterPartEntityView> kitPart = getKitMasterPart(masterPartEntityViews);
        return kitPart.map(MasterPartEntityView::getKitMasterPartId).orElse(null);
    }

    @Named("partNumberMapping")
    static String partNumberMapping(List<MasterPartEntityView> masterPartEntityViews) {
        Optional<MasterPartEntityView> kitPart = getKitMasterPart(masterPartEntityViews);
        return kitPart.map(MasterPartEntityView::getKitPartNumber).orElse(null);
    }

	@Named("partDescMapping")
	static String partDescMapping(@Context Language language, List<MasterPartEntityView> masterPartEntityViews) {
		Optional<MasterPartEntityView> kitPart = getKitMasterPart(masterPartEntityViews);
		return kitPart.map(masterPartEntityView -> masterPartEntityView.getTranslations()
				.stream()
				.filter(trans -> trans.getCode() != null && trans.getCode().equals(language.getLanguageCode()))
				.findFirst()
				.map(trans -> trans.getDescription())
				.orElse(null)).orElse(null);
	}

    @Named("stockMapping")
    static Double stockMapping(@Context User user, List<MasterPartEntityView> masterPartEntityViews) {
        Optional<MasterPartEntityView> kitPart = getKitMasterPart(masterPartEntityViews);
        if(kitPart.isPresent()){
            MasterPartEntityView foundKitPart = kitPart.get();
            if(foundKitPart.isStockWarehousesEnabled() && user.isPurchaserUser()){
               MasterPartWarehouseStockEntityView selected = foundKitPart.getWarehouseStocks()
                        .stream()
                        .filter(warehouseStock ->
                                (warehouseStock.getWarehouseId() == user.getManufacturerSubEntitySettings().getWareHouseId()))
                        .findAny().orElse(null);
                assert selected != null;
                return selected.getStock();
            } else {
                return foundKitPart.getStock();
            }
        } else
            throw new IllegalStateException("Kit part not found");
    }

    @Named("priceMapping")
    static MasterKitPriceSearchResult priceMapping(@Context User user, List<MasterPartEntityView> masterPartEntityViews) {
        Optional<MasterPartEntityView> kitPart = getKitMasterPart(masterPartEntityViews);
        if(kitPart.isPresent()) {
            MasterPartEntityView foundKitPart = kitPart.get();
            if (foundKitPart.isPriceListsEnabled() && user.isPurchaserUser()) {
                if(user.getManufacturerSubEntitySettings().getPriceListIdentifierId() != null) {
                    Optional<MasterPartPriceEntityView> foundPrice =
                            foundKitPart.getPrices().stream()
                                    .filter(price -> price.getPriceListIdentifierId() == user.getManufacturerSubEntitySettings().getPriceListIdentifierId())
                                    .findFirst();
                    return foundPrice.map(MasterPartPriceEntityViewMapper.Instance::entityToCore).orElse(null);
                }
                else
                    return null;
            }
            if(foundKitPart.getPrice() != null)
                return new MasterKitPriceSearchResult(kitPart.get().getPrice(),
                    user.obtainDefaultCurrency().getSymbol());
            else
                return null;
        }
        throw new IllegalStateException("Kit part not found");
    }

    @Named("pricesMapping")
    static List<MasterKitPriceSearchResult> pricesMapping(List<MasterPartEntityView> masterPartEntityViews) {
        Optional<MasterPartEntityView> kitPart = getKitMasterPart(masterPartEntityViews);
        return kitPart.map(masterPartEntityView ->
                MasterPartPriceEntityViewMapper.Instance.entitiesToCores(masterPartEntityView.getPrices()))
                .orElse(null);
    }

    static Optional<MasterPartEntityView> getKitMasterPart(List<MasterPartEntityView> masterPartEntityViews) {
        Optional<MasterPartEntityView> kitPart = masterPartEntityViews.stream().filter(mp -> mp.getType().equals(MasterPartType.KIT)).findFirst();
        return kitPart;
    }

}
