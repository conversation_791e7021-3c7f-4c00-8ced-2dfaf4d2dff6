package co.cadshare.modelMgt.publications.adapters.database;

import co.cadshare.main.Application;
import co.cadshare.modelMgt.models.adapters.database.ModelsModelEntity;
import co.cadshare.modelMgt.models.adapters.database.ModelsUserEntity;
import co.cadshare.modelMgt.models.core.model.FileType;
import co.cadshare.modelMgt.viewables.adapters.database.ViewableQueryRepo;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.EntityManager;
import javax.persistence.TemporalType;
import java.math.BigInteger;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class)
@Transactional
public class PublicationsViewableQueryRepoTest {

    @Autowired
    private EntityManager entityManager;
    @Autowired
    private ViewableQueryRepo sut;

    ModelsUserEntity user;
    List<PublicationsPurchaserEntity> purchasers;
    PublicationsModelEntity viewable;
    PublicationsProductEntity product;
    PublicationsRangeEntity range;
    PublicationsDealerEntity dealer;
    PublicationsCustomerEntity customer;
    PublicationsViewableConfigEntity viewableConfig;
    PublicationsSnapshotEntity snapshot;

    @Before
    public void Before() {
        buildAndSaveUser();
        buildRange();
        buildProduct();
    }

    @Test
    public void shouldReturnViewableWithMultiplePurchasers() {
        buildDealer();
        buildCustomer();
        purchasers.add(dealer);
        purchasers.add(customer);
	    int viewableId = persistAll();
	    PublicationsViewableEntityView viewableEntityView = sut.getById(viewableId);
        assertNotNull(viewableEntityView.getPurchasersAssignedToRange());
        assertEquals(2, viewableEntityView.getPurchasersAssignedToRange().size());
    }

    @Test
    public void shouldReturnViewableWithSingleCustomer() {
        buildCustomer();
        purchasers.add(customer);
	    int viewableId = persistAll();
	    PublicationsViewableEntityView viewableEntityView = sut.getById(viewableId);
        assertNotNull(viewableEntityView.getPurchasersAssignedToRange());
        assertEquals(1, viewableEntityView.getPurchasersAssignedToRange().size());
    }

    @Test
    public void shouldReturnViewableWithSingleDealer() {
        buildDealer();
        purchasers.add(dealer);
	    int viewableId = persistAll();
	    PublicationsViewableEntityView viewableEntityView = sut.getById(viewableId);
        assertNotNull(viewableEntityView.getPurchasersAssignedToRange());
        assertEquals(1, viewableEntityView.getPurchasersAssignedToRange().size());
    }

    @Test
    public void shouldReturnViewableWithNoPurchasers() {
	    int viewableId = persistAll();
	    PublicationsViewableEntityView viewableEntityView = sut.getById(viewableId);
        assertNotNull(viewableEntityView.getPurchasersAssignedToRange());
        assertEquals(0, viewableEntityView.getPurchasersAssignedToRange().size());
    }

    @Test
    public void shouldReturnViewableWithProductThumbnail() {
        int viewableId = persistAll();
        PublicationsViewableEntityView viewableEntityView = sut.getById(viewableId);
        assertNotNull(viewableEntityView.getProduct().getThumbnailUrl());
        assertEquals(product.getThumbnailUrl(), viewableEntityView.getProduct().getThumbnailUrl());
    }

    private void buildProduct() {
        product = new PublicationsProductEntity();
        product.setName("Publications Product");
        product.setRange(range);
        product.setThumbnailUrl("thumbnailUrl");
    }

    private void buildRange() {
        range = new PublicationsRangeEntity();
        purchasers = new ArrayList<>();
        range.setAssignedPurchasers(purchasers);
    }

    private void buildCustomer() {
        customer = new PublicationsCustomerEntity();
        customer.setName("Publications Customer");
    }

    private void buildDealer() {
        dealer = new PublicationsDealerEntity();
        dealer.setName("Publications Dealer");
    }

    private void buildAndSaveUser() {
        user = new ModelsUserEntity();
        entityManager.persist(user);
    }

    private void buildViewable() {
        viewable = new PublicationsModelEntity();
	    viewable.setCreatedDate(new Timestamp(System.currentTimeMillis()));
		/*
        viewable.setAutodeskUrn("AUTODESKURN");
        viewable.setCreatedBy(user);
        viewable.setFileType(FileType.ARCHIVE);
        viewable.setFilename("filename");
        viewable.setRetries(0);
        viewable.setIsSetupComplete(false);
        */
        viewable.setProduct(product);
        viewable.setModelName("Publications Viewable");
    }

    private int persistAll() {
        entityManager.persist(range);
        entityManager.persist(product);
	    return persistViewable();
    }

	private int persistViewable() {
		//PublicationsModelEntity reduced to only properties required for
		// reading a Viewable in the context of Publications. If full entity configured for hibernate,
		// then need to make sure full object is synced with database - not required for publications.
		String sql = "INSERT INTO public.model(\n" +
				"modelname, machineid, archived, autodeskurn, createddate, createdbyuserid, filename, filetype, issetupcomplete, retries) \n" +
				"VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";

		entityManager.createNativeQuery(sql)
				.setParameter(1,"Publications Viewable")
				.setParameter(2,product.getId())
				.setParameter(3,false)
				.setParameter(4,"autodeskurn")
				.setParameter(5,new Timestamp(System.currentTimeMillis()), TemporalType.DATE)
				.setParameter(6,user.getId())
				.setParameter(7,"filename")
				.setParameter(8,"ARCHIVE")
				.setParameter(9,false)
				.setParameter(10,0)
				.executeUpdate();
		BigInteger viewableId = (BigInteger) entityManager.createNativeQuery("SELECT SCOPE_IDENTITY()").getSingleResult();
		return viewableId.intValue();
	}
}
