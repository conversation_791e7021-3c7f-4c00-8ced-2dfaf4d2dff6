package co.cadshare.publications.adapters.database;

import com.querydsl.jpa.impl.JPAQueryFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import javax.persistence.EntityManager;
import java.util.List;

@Repository
public class PublicationsComplexQueryRepo {

    private JPAQueryFactory queryFactory;

    @Autowired
    public PublicationsComplexQueryRepo(EntityManager entityManager) {
        this.queryFactory = new JPAQueryFactory(entityManager);
    }

    public List<PublicationsPublicationEntity> getPublicationsForManufacturer(Integer manufacturerId) {
        QPublicationsPublicationEntity publication = QPublicationsPublicationEntity.publicationsPublicationEntity;
        return queryFactory.selectFrom(publication)
                .where(publication.manufacturerId.eq(manufacturerId)
                        .and(publication.archived.eq(false)))
                .fetch();
    }

}
