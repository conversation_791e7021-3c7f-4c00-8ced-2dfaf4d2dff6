package co.cadshare.masterKits.adapters.database;

import co.cadshare.exceptions.NotFoundException;
import co.cadshare.masterKits.boundary.ManufacturerMasterKitSearchRequest;
import co.cadshare.masterKits.core.MasterKitSearchResult;
import co.cadshare.masterKits.boundary.PurchaserMasterKitSearchRequest;
import co.cadshare.masterKits.core.KitMasterPart;
import co.cadshare.masterKits.core.MasterKit;
import co.cadshare.masterKits.boundary.MasterKitCommandPort;
import co.cadshare.masterKits.boundary.MasterKitQueryPort;
import co.cadshare.aspects.logging.Log;
import co.cadshare.shared.core.user.User;

import java.sql.Timestamp;
import java.util.Calendar;
import java.util.Collections;
import java.util.List;

import co.cadshare.masterKits.core.PartMasterPart;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.persistence.EntityNotFoundException;

@Service
public class MasterKitDataFacade implements MasterKitQueryPort, MasterKitCommandPort {

    private final String NOT_FOUND = "MasterKit does not exist";
    private final MasterKitRepo masterKitRepo;
    private final MasterKitComplexQueryRepo masterKitComplexQueryRepo;
    private final MkMasterPartRepo masterPartRepo;
    private final MkMasterPartComplexQueryRepo masterPartQueryRepo;
    private final MasterKitQueryRepo masterKitQueryRepo;

    @Autowired
    public MasterKitDataFacade(MasterKitRepo masterKitRepo,
                               MasterKitComplexQueryRepo masterKitQueryRepo,
                               MkMasterPartRepo masterPartRepo,
                               MkMasterPartComplexQueryRepo masterPartQueryRepo,
                               MasterKitQueryRepo masterKitQueryRepo1) {
        this.masterKitRepo = masterKitRepo;
        this.masterKitComplexQueryRepo = masterKitQueryRepo;
        this.masterPartRepo = masterPartRepo;
        this.masterPartQueryRepo = masterPartQueryRepo;
        this.masterKitQueryRepo = masterKitQueryRepo1;
    }

    @Override
    public MasterKit get(Integer masterKitId) {
        try {
            MkMasterKitEntity masterKitEntity = this.masterKitRepo.getOne(masterKitId);
            if(masterKitEntity.isDeleted())
                throw new NotFoundException(NOT_FOUND);
            MasterKit masterKit = MasterKitEntityMapper.Instance.entityToCore(masterKitEntity);
            return masterKit;
        } catch (EntityNotFoundException e) {
            throw new NotFoundException(NOT_FOUND);
        }
    }

    public List<MasterKit> getListForManufacturer(Integer manufacturerId) {
         List<MkMasterKitEntity> entities = this.masterKitComplexQueryRepo.getMasterKitsForManufacturer(manufacturerId);
        List<MasterKit> cores = MasterKitEntityMapper.Instance.entitiesToCores(entities);
        return cores;
    }

    @Override
    public List<MasterKit> getList() {
        List<MkMasterKitEntity> entities = this.masterKitRepo.findAll();
        return MasterKitEntityMapper.Instance.entitiesToCores(entities);
    }

    @Override
    public List<PartMasterPart> getMasterPartsForKit(List<Integer> ids) {
        List<MkMasterPartEntity> entities = this.masterPartQueryRepo.getMasterPartsForKit(ids);
        return MkMasterPartEntityMapper.Instance.entitiesToCores(entities);
    }

    @Override
    public KitMasterPart getKitMasterPartForKit(Integer id) {
        MkMasterPartEntity entity = this.masterPartRepo.getOne(id);
        KitMasterPart core = MkMasterPartEntityMapper.Instance.entityToCoreForKit(entity);
        return core;
    }

	@Override
    @Log
    public Integer create(User user, MasterKit masterKit) {
        masterKit.setCreatedByUserId(user.getUserId());
        masterKit.setCreatedDate(new Timestamp(Calendar.getInstance().getTime().getTime()));
        MkMasterKitEntity masterKitEntity = MasterKitEntityMapper.Instance.coreToEntity(masterKit);
        MkMasterKitEntity savedEntity = this.masterKitRepo.save(masterKitEntity);
        return savedEntity.getId();
    }

    @Override
    @Log
    public void update(User user, MasterKit masterKit) throws Exception {
        masterKit.setModifiedByUserId(user.getUserId());
        masterKit.setModifiedDate(new Timestamp(Calendar.getInstance().getTime().getTime()));
        MkMasterKitEntity mkEntity = MasterKitEntityMapper.Instance.coreToEntity(masterKit);
        this.masterKitRepo.save(mkEntity);
    }

    @Override
    @Log
    public void delete(User user, MasterKit masterKit) throws Exception {
        masterKit.setModifiedByUserId(user.getUserId());
        masterKit.setModifiedDate(new Timestamp(Calendar.getInstance().getTime().getTime()));
        MkMasterKitEntity entity = MasterKitEntityMapper.Instance.coreToEntity(masterKit);
        this.masterKitRepo.save(entity);
    }

    @Override
    @Log
    public void update(User user, PartMasterPart masterPart) throws Exception {
        MkMasterPartEntity entity = MkMasterPartEntityMapper.Instance.coreToEntity(masterPart);
        this.masterPartRepo.save(entity);
    }

    @Override
    public List<MasterKitSearchResult> searchForMasterKitsForPurchaser(User user, PurchaserMasterKitSearchRequest searchRequest) {
        List<MasterKitEntityView> entities = masterKitQueryRepo.getKitsByPartNumberOrDescriptionForPurchaser(searchRequest.getPartNumber(),
                searchRequest.getDescription(),
                searchRequest.getPurchaserId(),
		        searchRequest.getLanguage());
        return MasterKitEntityViewMapper.Instance.entitiesToCores(user, searchRequest.getLanguage(), entities);
    }

	@Override
	public List<MasterKitSearchResult> searchForMasterKitsForManufacturer(User user, ManufacturerMasterKitSearchRequest searchRequest) {
		List<MasterKitEntityView> entities = masterKitQueryRepo.getKitsByPartNumberOrDescriptionForManufacturer(searchRequest.getPartNumber(),
				searchRequest.getDescription(),
				searchRequest.getManufacturerId(),
				searchRequest.getLanguage());
		return MasterKitEntityViewMapper.Instance.entitiesToCores(user, searchRequest.getLanguage(), entities);
	}
}