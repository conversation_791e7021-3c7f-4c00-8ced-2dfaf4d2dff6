package co.cadshare.domainmodel.visibility;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;
import java.util.List;

@XmlRootElement(name = "List")
@XmlAccessorType(XmlAccessType.FIELD)
public class PartAvailListDetailList {

    @XmlElement(name = "PartAvailListDetail")
    private List<PartAvailListDetail> partAvailListDetailList;

    public List<PartAvailListDetail> getPartAvailListDetailList() {
        return partAvailListDetailList;
    }

    public void setPartAvailListDetailList(List<PartAvailListDetail> partAvailListDetailList) {
        this.partAvailListDetailList = partAvailListDetailList;
    }
}
