package co.cadshare.modelMgt.publications.boundary;

import co.cadshare.exceptions.UnprocessableEntityException;
import org.junit.Test;

public class CreatePublicationCommandTest {


	@Test(expected = UnprocessableEntityException.class)
	public void validateShouldThrowExceptionOnNoName() {
		CreatePublicationCommand command = new CreatePublicationCommand();
		command.validate();
	}

	@Test
	public void validateShouldNotThrowExceptionOnName() {
		CreatePublicationCommand command = new CreatePublicationCommand();
		command.setName("name");
		command.validate();
	}
}
