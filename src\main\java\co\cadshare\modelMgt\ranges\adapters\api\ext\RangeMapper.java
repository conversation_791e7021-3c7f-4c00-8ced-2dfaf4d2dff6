package co.cadshare.modelMgt.ranges.adapters.api.ext;

import co.cadshare.modelMgt.ranges.core.Range;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import java.util.List;

@Mapper
public interface RangeMapper {

    RangeMapper Instance = Mappers.getMapper(RangeMapper.class);

    Range postRequestDtoToRange(PostRangeRequestDto postRequestDto);

    Range putRequestDtoToRange(PutRangeRequestDto putRequestDto);

    GetRangeResponseDto RangeToGetResponseDto(Range range);

    List<GetRangeResponseDto> RangeToGetListResponseDto(List<Range> ranges);
}
