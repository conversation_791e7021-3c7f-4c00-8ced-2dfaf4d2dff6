package co.cadshare.glue;

import co.cadshare.media.adapters.api.web.GetImageResponseDto;
import io.cucumber.java.en.Then;
import io.cucumber.java.en.When;

import static org.junit.Assert.assertNotNull;

public class MediaStepsIT {

	private final CadshareIT cadshare;

	public MediaStepsIT(CadshareIT cadshare) {
		this.cadshare = cadshare;
	}

	@Then("I get an image with id {int} I should see the image")
	public void iGetAnImageWithId(int imageId) {
		GetImageResponseDto response = cadshare.loggedInUser().getImage(imageId);
		assertNotNull(response);
	}

}
