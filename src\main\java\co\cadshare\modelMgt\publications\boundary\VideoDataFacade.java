package co.cadshare.modelMgt.publications.boundary;

import co.cadshare.modelMgt.publications.adapters.database.VideoDao;
import co.cadshare.modelMgt.publications.core.Video;
import org.springframework.stereotype.Service;

@Service
public class VideoDataFacade implements VideoQueryPort {

	private final VideoDao videoDao;

	public VideoDataFacade(VideoDao videoDao) {
		this.videoDao = videoDao;
	}

	@Override
	public Video getVideoById(int videoId) {
		return videoDao.getVideoById(videoId);
	}
}
