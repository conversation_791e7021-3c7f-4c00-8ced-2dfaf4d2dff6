package co.cadshare.modelMgt.publicationCategories.boundary;

import co.cadshare.aspects.logging.ServiceLoggingAspect;
import co.cadshare.modelMgt.publicationCategories.boundary.UpdatePublicationCategoryService;
import co.cadshare.shared.core.user.User;
import co.cadshare.modelMgt.publicationCategories.boundary.PublicationCategoryCommandPort;
import co.cadshare.modelMgt.publicationCategories.boundary.PublicationCategoryQueryPort;
import co.cadshare.modelMgt.publicationCategories.core.PublicationCategory;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.MockitoAnnotations;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import static org.mockito.Mockito.*;

@EnableAspectJAutoProxy
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(classes = {UpdatePublicationCategoryService.class, ServiceLoggingAspect.class})
public class UpdatePublicationCategoryServiceTest {

    @MockBean
    private PublicationCategoryCommandPort commandPort;
    @MockBean
    private PublicationCategoryQueryPort queryPort;
    @Autowired
    private UpdatePublicationCategoryService out;
    private User user;
    private PublicationCategory publicationCategory;
    private PublicationCategory errorPublicationCategory = new PublicationCategory();

    @Before
    public void Before() {
        MockitoAnnotations.initMocks(this);
        publicationCategory = buildPublicationCategory();
        user = new User() {{
            setUserId(1);
            setFirstName("firstName");
            setLastName("lastName");
        }};
    }

    @Test
    public void UpdatePublicationCategorySuccess() throws Exception {
		when(queryPort.get(publicationCategory.getId())).thenReturn(publicationCategory);
        doNothing().when(commandPort).update(user, publicationCategory);
        out.update(user, publicationCategory);
    }

    @Test(expected = RuntimeException.class)
    public void UpdatePublicationCategoryFailureException() throws Exception {
        doThrow(new RuntimeException("terrible")).when(commandPort).update(user, errorPublicationCategory);
        out.update(user, errorPublicationCategory);
    }

    private PublicationCategory buildPublicationCategory() {
        PublicationCategory publicationCategory = new PublicationCategory();
        return publicationCategory;
    }
}
