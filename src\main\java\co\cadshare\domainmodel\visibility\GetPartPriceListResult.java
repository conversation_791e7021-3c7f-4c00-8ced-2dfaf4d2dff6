package co.cadshare.domainmodel.visibility;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;

@XmlRootElement(name = "GetPartPriceListResult", namespace = "http://visibility.com/")
@XmlAccessorType(XmlAccessType.FIELD)
public class GetPartPriceListResult {

    @XmlElement(name = "ENTITY_CODE", namespace = "http://visibility.com/")
    private String ENTITY_CODE;
    @XmlElement(name = "ErrorMsg", namespace = "http://visibility.com/")
    private String ErrorMsg;
    @XmlElement(name = "ErrorC", namespace = "http://visibility.com/")
    private String ErrorC;
    @XmlElement(name = "List", namespace = "http://visibility.com/")
    private PartPriceListDetailList partPriceListDetailList;

    public String getENTITY_CODE() {
        return ENTITY_CODE;
    }

    public void setENTITY_CODE(String ENTITY_CODE) {
        this.ENTITY_CODE = ENTITY_CODE;
    }

    public String getErrorMsg() {
        return ErrorMsg;
    }

    public void setErrorMsg(String errorMsg) {
        ErrorMsg = errorMsg;
    }

    public String getErrorC() {
        return ErrorC;
    }

    public void setErrorC(String errorC) {
        ErrorC = errorC;
    }

    public PartPriceListDetailList getPartPriceListDetailList() {
        return partPriceListDetailList;
    }

    public void setPartPriceListDetailList(PartPriceListDetailList partPriceListDetailList) {
        this.partPriceListDetailList = partPriceListDetailList;
    }
}
