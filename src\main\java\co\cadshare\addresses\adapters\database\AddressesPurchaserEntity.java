package co.cadshare.addresses.adapters.database;

import co.cadshare.shared.adapters.database.BasePurchaserEntity;
import co.cadshare.users.adapters.database.UsersManufacturerEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DiscriminatorOptions;

import javax.persistence.*;
import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
@Entity
@Table(name="manufacturersubentity")
@Inheritance(strategy = InheritanceType.SINGLE_TABLE)
@DiscriminatorColumn(name="manufacturersubentitytypeid", discriminatorType = DiscriminatorType.INTEGER)
@DiscriminatorOptions(force = true)  //this is due to a bug in Hibernate 5.2.x versions - once upgraded, this can be removed
public abstract class AddressesPurchaserEntity extends BasePurchaserEntity {

    @Column(name="viscustomercode")
    private String externalRefIdentifier;

    @ManyToMany(cascade = { CascadeType.PERSIST },  fetch = FetchType.EAGER)
    @JoinTable(
            name = "manufacturersubentityusers",
            joinColumns = { @JoinColumn(name = "manufacturersubentityid") },
            inverseJoinColumns = { @JoinColumn(name = "userid") })
    private List<AddressesUserEntity> users;


    @ManyToOne
    @JoinColumn(name="manufacturerid")
    private UsersManufacturerEntity manufacturer;
}
