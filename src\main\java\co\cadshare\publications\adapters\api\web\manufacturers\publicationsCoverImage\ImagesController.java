package co.cadshare.publications.adapters.api.web.manufacturers.publicationsCoverImage;

import co.cadshare.shared.core.user.User;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;

@RestController
@RequestMapping("/manufacturers/{manufacturer-id}/images")
public class ImagesController {

    @GetMapping(produces = "application/json")
    @PreAuthorize("hasRole('ROLE_SUPER_USER') or hasRole('ROLE_MANUFACTURER') and #currentUser.manufacturerId == manufacturerId")
    @ResponseStatus(HttpStatus.OK)
    @Operation(summary = "Get the summary details of the list of all Images belonging to the specified Manufacturer")
    public ResponseEntity<GetImageListResponseDto> getImages(@PathVariable("manufacturer-id") int manufacturerId,
                                                               @AuthenticationPrincipal User currentUser) {

        List<ImageListItemDto> images = new ArrayList<>();
        for (Integer i = 1; i < manufacturerId  + 1; i++) {
            Integer finalI = i;
            ImageListItemDto image = new ImageListItemDto() {{
                setId(finalI);
                setDescription("Sample Media Description".concat(finalI.toString()));
                setLocationUrl("https://www.wallchimp.co.uk/images/farm-machinery-image-featuring-a-cat-challenger-tractor-p3259-8873_image.jpg");
                setTags(new ArrayList<String>(){{
                    add("tag1-".concat(finalI.toString()));
                    add("tag2-".concat(finalI.toString()));
                    add("tag3-".concat(finalI.toString()));
                }});
            }};
            images.add(image);
        }
        GetImageListResponseDto response = new GetImageListResponseDto()
        {{ setImages(images);}};

        return new ResponseEntity<>(response, HttpStatus.OK);
    }
}
