/*
 * Copyright 2016 Bell.
 */
package co.cadshare.controller.dealer;

import co.cadshare.orders.core.Comment;
import co.cadshare.orders.core.CommentThread;
import co.cadshare.shared.core.user.User;
import co.cadshare.services.CommentService;
import co.cadshare.services.PermissionsService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Controller
@PreAuthorize("hasRole('ROLE_MANUFACTURER_SUB_ENTITY_DEALER_PLUS')")
@RequestMapping("/dealerplus/commentthread")
public class DealerCommentController {

  private static final Logger logger = LoggerFactory.getLogger(DealerCommentController.class);

  @Autowired
  private CommentService commentService;

  @Autowired
  private PermissionsService permissionsService;

  @RequestMapping(value = "", method = RequestMethod.POST, consumes = "application/json")
  public HttpEntity<Integer> createCommentThread(@AuthenticationPrincipal User currentUser, @RequestBody CommentThread commentThread) {

    logger.info("ACCESS: User [{}],  dealerplus - createCommentThread", currentUser.accessDetails());
    try {
      userHasPermissionsToViewOrder(currentUser.getUserId(), commentThread.getOrderId());
    } catch (Exception ex) {
      return new ResponseEntity<>(null, HttpStatus.FORBIDDEN);
    }
    //Check if comment thread already exists for order
    CommentThread existingThread = commentService.getCommentThread(currentUser, commentThread.getOrderId(),  commentThread.getOrderItemId());
    int threadId = 0;
    if (existingThread != null) {
      threadId = existingThread.getId();
      logger.error("Comment Thread with id [{}] already exists, returning existing thread", threadId);
      return new ResponseEntity<>(threadId, HttpStatus.CONFLICT);
    } else {
      threadId = commentService.createCommentThread(commentThread, currentUser.getUserId());
      logger.info("Comment Thread with id [{}] created", threadId);
    }
    return new ResponseEntity<>(threadId, HttpStatus.OK);
  }

  @RequestMapping(value = "/{commentThreadId}/message", method = RequestMethod.POST, consumes = "application/json")
  public HttpEntity<Integer> saveComment(@AuthenticationPrincipal User currentUser, @RequestHeader(value = "Site-Url") String siteUrl,
                                         @PathVariable int commentThreadId, @RequestBody Comment comment) {

    logger.info("ACCESS: User [{}],  dealerplus - saveComment", currentUser.accessDetails());
    CommentThread commentThread = commentService.getCommentThreadById(commentThreadId);
    // Comment thread to be fetch to use order ID and confirm user has
    // permissions to comment on the order
    try {
      userHasPermissionsToViewOrder(currentUser.getUserId(), commentThread.getOrderId());
    } catch (Exception ex) {
      return new ResponseEntity<>(null, HttpStatus.FORBIDDEN);
    }

    int commentId = commentService.saveComment(comment, commentThread, currentUser.getUserId(), siteUrl);

    logger.info("Comment with id [{}] created", commentId);
    return new ResponseEntity<>(commentId, HttpStatus.OK);
  }

  @RequestMapping(value = "/{commentThreadId}", method = RequestMethod.GET)
  public HttpEntity<List<Comment>> getThreadComments(@AuthenticationPrincipal User currentUser, @PathVariable int commentThreadId) throws Exception {

    CommentThread commentThread = commentService.getCommentThreadById(commentThreadId);
    // Comment thread to be fetch to use order ID and confirm user has
    // permissions to comment on the order
    try {
      userHasPermissionsToViewOrder(currentUser.getUserId(), commentThread.getOrderId());
    } catch (Exception ex) {
      return new ResponseEntity<>(null, HttpStatus.FORBIDDEN);
    }
    
    List<Comment> comments = commentService.getCommentsByThread(currentUser, commentThreadId);
    commentService.commentRead(commentThreadId, currentUser.getUserId());

    return new ResponseEntity<>(comments, HttpStatus.OK);
  }

  private boolean userHasPermissionsToViewOrder(int userId, int orderId) throws Exception {
    boolean permissions = permissionsService.hasDealerHasPermissionsToViewOrder(userId, orderId);
    return permissions;
  }
}
