package co.cadshare.api.orders;

import co.cadshare.orders.core.Order;
import co.cadshare.orders.core.OrderStatus;
import co.cadshare.orders.core.OrderUnreadCounts;
import co.cadshare.shared.core.user.User;
import co.cadshare.services.ManufacturerSubEntityService;
import java.time.LocalDate;
import java.util.Arrays;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.format.annotation.DateTimeFormat.ISO;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("api/orders")
public class OrdersApiController {

    private ManufacturerSubEntityService manufacturerSubEntityService;

    public OrdersApiController(ManufacturerSubEntityService manufacturerSubEntityService) {
        this.manufacturerSubEntityService = manufacturerSubEntityService;
    }

    @GetMapping("/metadata")
    public OrdersMetadata getOrdersMetadataForCurrentUser(@AuthenticationPrincipal User currentUser,
        @RequestParam(required = false) @DateTimeFormat(iso = ISO.DATE) LocalDate fromDate,
        @RequestParam(required = false) @DateTimeFormat(iso = ISO.DATE) LocalDate toDate) {
        Integer manufacturerSubEntityId = currentUser.getManufacturerSubEntityId();
        int totalEnquiries = manufacturerSubEntityService
            .getFilteredOrderTotalsForManufacturerSubEntityAndOrderType(manufacturerSubEntityId, OrderStatus.SUBMITTED, fromDate, toDate)
            + manufacturerSubEntityService
            .getFilteredOrderTotalsForManufacturerSubEntityAndOrderType(manufacturerSubEntityId, OrderStatus.PREPARING, fromDate, toDate);
        int totalQuotes = manufacturerSubEntityService
            .getFilteredOrderTotalsForManufacturerSubEntityAndOrderType(manufacturerSubEntityId, OrderStatus.QUOTE, fromDate, toDate);
        int totalLiveOrders = manufacturerSubEntityService
            .getFilteredOrderTotalsForManufacturerSubEntityAndOrderType(manufacturerSubEntityId, OrderStatus.PROCESSED, fromDate, toDate) +
            manufacturerSubEntityService
                .getFilteredOrderTotalsForManufacturerSubEntityAndOrderType(manufacturerSubEntityId, OrderStatus.SHIPPED, fromDate, toDate) +
            manufacturerSubEntityService
                .getFilteredOrderTotalsForManufacturerSubEntityAndOrderType(manufacturerSubEntityId, OrderStatus.RECEIVED, fromDate, toDate);
        int totalCompletedOrders = manufacturerSubEntityService
            .getFilteredOrderTotalsForManufacturerSubEntityAndOrderType(manufacturerSubEntityId, OrderStatus.CLOSED, fromDate, toDate);

        return OrdersMetadata.builder()
            .totalEnquiriesCount(totalEnquiries)
            .totalQuotesCount(totalQuotes)
            .totalLiveOrdersCount(totalLiveOrders)
            .totalCompletedOrdersCount(totalCompletedOrders)
            .totalOrdersCount(totalEnquiries + totalQuotes + totalLiveOrders + totalCompletedOrders)
            .build();
    }

    @GetMapping("/unreadCounts")
    public OrdersUnread getOrderUnreadCountsForCurrentUser(@AuthenticationPrincipal User currentUser) {
        Integer manufacturerSubEntityId = currentUser.getManufacturerSubEntityId();
        OrderUnreadCounts orderUnreadCounts = manufacturerSubEntityService.getOrderCountsForManufacturerSubEntity(manufacturerSubEntityId);

        boolean isUnreadComments = manufacturerSubEntityService
            .getOrdersForManufacturerSubEntity(manufacturerSubEntityId, Arrays.asList(OrderStatus.values()), currentUser.getUserId()).stream()
            .anyMatch(Order::isUnreadComment);

        return OrdersUnread.builder()
            .unreadQuote(orderUnreadCounts.getEnquiryCount())
            .unreadLiveOrders(orderUnreadCounts.getLiveCount())
            .isUnreadComments(isUnreadComments)
            .build();
    }
}
