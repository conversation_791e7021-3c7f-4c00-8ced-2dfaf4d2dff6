Feature: Manage Publications
  Background: As a Manufacturer I should be able to manage Publications by viewing and editing them.

  Scenario Outline: View Publication as Manufacturer
    Given I am a Manufacturer with email address <emailAddress>
    Then I can see a single Publication named <publicationName>
    Examples:
      | emailAddress                 | publicationName             |
      | <EMAIL> | Publication for Caterpillar |

  Scenario Outline: View All Publications for Manufacturer
    Given I am a Manufacturer with email address <emailAddress>
    Then I can view all Publications for my Manufacturer
    Examples:
      | emailAddress                 |
      | <EMAIL> |

  Scenario Outline: View Publication as Dealer
    Given I am a Dealer with email address <emailAddress>
    Then I can see a single Publication named <publicationName>
    Examples:
      | emailAddress           | publicationName             |
      | <EMAIL> | Publication for Caterpillar |

    #View all Publications for Dealer tested in AssignToPurchasers

 Scenario Outline: Create a new Publication
    Given I am a Manufacturer with email address <emailAddress>
    And I create a new PublicationCategory named <publicationCategoryName>
    And a Viewable with name <newViewableName> exists
    And a Purchaser exists
    And I start with a new Publication
    When I select the PublicationCategory named <publicationCategoryName> for my Publication
    And I select the Viewable named <newViewableName> for my Publication
    And I select any existing Purchaser for my new Publication
    And I create this new Publication named <publicationName>
    Then I can see a Publication named <publicationName> in the list of Publications
    Examples:
      | emailAddress                 | publicationName                 | publicationCategoryName | newViewableName     | purchaserName        |
      | <EMAIL> | New Publication for Caterpillar | PubCat for Publication  | Caterpillar Model 1 | Caterpillar Dealer 1 |

  Scenario Outline: Update an existing Publication
    Given I am a Manufacturer with email address <emailAddress>
    And I start with a new Publication
    And I create this new Publication named <publicationName>
    And I can see a Publication named <publicationName> in the list of Publications
    When I update this existing Publication named <publicationName> with <updatedPublicationName>
    Then I can see a Publication named <updatedPublicationName> in the list of Publications
    And I update this existing Publication named <updatedPublicationName> with <publicationName>
    Examples:
      | emailAddress                 | publicationName                              | updatedPublicationName              |
      | <EMAIL> | Ready for Update Publication for Caterpillar | Updated Publication for Caterpillar |

  Scenario Outline: Delete a Publication
    Given I am a Manufacturer with email address <emailAddress>
    And I start with a new Publication
    And I create this new Publication named <publicationName>
    And I can see a Publication named <publicationName> in the list of Publications
    When I delete this existing Publication named <publicationName>
    Then I can't see a Publication named <publicationName> in the list of Publications
    Examples:
      | emailAddress                 | publicationName                    |
      | <EMAIL> | Delete Publication for Caterpillar |

  Scenario Outline:  Publish a Publication
    Given I am a Manufacturer with email address <emailAddress>
    And I start with a new Publication
    And I create this new Publication named <publicationName>
    And I can see a Publication named <publicationName> in the list of Publications
    When I publish this existing Publication named <publicationName>
    Then the Publication named <publicationName> is published
    Examples:
      | emailAddress                 | publicationName                     |
      | <EMAIL> | Publish Publication for Caterpillar |


  Scenario Outline: Unpublish a Publication
    Given I am a Manufacturer with email address <emailAddress>
    And I start with a new Publication
    And I create this new Publication named <publicationName>
    And I can see a Publication named <publicationName> in the list of Publications
    When I unpublish this existing Publication named <publicationName>
    Then the Publication named <publicationName> is unpublished
    Examples:
      | emailAddress                 | publicationName                       |
      | <EMAIL> | Unpublish publication for Caterpillar |