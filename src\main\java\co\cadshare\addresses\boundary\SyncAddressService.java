package co.cadshare.addresses.boundary;

import co.cadshare.addresses.core.*;
import co.cadshare.utils.ObjectUtilsExtension;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.ExtensionMethod;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service
@ExtensionMethod(ObjectUtilsExtension.class)
public class SyncAddressService {

    private final String START_MSG = "Sync Addresses Task started";
    private final String END_MSG = "Sync Addresses Task completed";
    private final String ERROR_MSG = "An error occurred while syncing Addresses for Manufacturer [%s: %s] | Cause: %s";
    private final String SAVE_ADDR_ERROR_MSG = "An error occurred while saving Address id [%s], externalRefId [%s] | Cause: %s";
	private final String SAVE_CONTACT_ERROR_MSG = "An error occurred while saving Contact id [%s], externalRefId [%s] | Cause: %s";
    private final String COMPLETE_MSG = "Address sync completed for for Purchaser [%s: %s] - %s address inserts, %s address updates, %s contact inserts, %s contact updates";
    private final Logger logger = LoggerFactory.getLogger(getClass());
    @Getter(AccessLevel.NONE)
    @Setter(AccessLevel.NONE)
    private int addressInsertCount = 0;
    @Getter(AccessLevel.NONE)
    @Setter(AccessLevel.NONE)
    private int addressUpdateCount = 0;
	@Getter(AccessLevel.NONE)
	@Setter(AccessLevel.NONE)
	private int contactInsertCount = 0;
	@Getter(AccessLevel.NONE)
	@Setter(AccessLevel.NONE)
	private int contactUpdateCount = 0;

    private final ManufacturerQueryPort manufacturerQuery;
    private final ExternalAddressQueryPort externalAddressQuery;
    private final AddressCommandPort addressCommand;

    @Autowired
    public SyncAddressService(ManufacturerQueryPort manufacturerQuery,
                              ExternalAddressQueryPort externalAddressQuery,
                              AddressCommandPort addressCommand) {
        this.manufacturerQuery = manufacturerQuery;
        this.externalAddressQuery = externalAddressQuery;
        this.addressCommand = addressCommand;
    }

    public void execute() {
        logger.info(START_MSG);
        List<Manufacturer> manufacturers = manufacturerQuery.getManufacturersWithExternalAddresses();
        manufacturers.forEach(this::syncAddressesForManufacturer);
        logger.info(END_MSG);
    }


    private void syncAddressesForManufacturer(Manufacturer manufacturer) {
        try {
            List<ExternalPurchaser> purchasers = manufacturer.getExternalPurchasers()
                    .stream()
                    .filter(purchaser -> purchaser.getExternalRefIdentifier().isNotNull())
                    .collect(Collectors.toList());
            for (ExternalPurchaser purchaser : purchasers)
				syncAddressesForPurchaser(purchaser);
        }
        catch (Exception e) {
            logger.error(String.format(ERROR_MSG,
                    manufacturer.getManufacturerId(), manufacturer.getName(), e.getMessage()));
        }
    }

    private void syncAddressesForPurchaser(ExternalPurchaser purchaser) throws Exception {
        addressInsertCount = 0;
        addressUpdateCount = 0;
		contactInsertCount = 0;
		contactUpdateCount = 0;
        List<ExternalAddress> externalAddresses = externalAddressQuery.getExternalAddresses(purchaser);
	    List<ExternalContact> upsertedContacts = purchaser.upsertContacts(externalAddresses);
	    List<ExternalContact> savedContacts = saveContacts(upsertedContacts);
	    List<ExternalAddress> allAddresses = purchaser.upsertAllAddresses(externalAddresses, savedContacts);
        allAddresses.forEach(this::saveAddress);
        logger.info(String.format(COMPLETE_MSG,
		        purchaser.getId(),
		        purchaser.getName(),
		        addressInsertCount,
		        addressUpdateCount,
		        contactInsertCount,
		        contactUpdateCount));
    }

	private List<ExternalContact> saveContacts(List<ExternalContact> upsertedContacts) {
		List<ExternalContact> savedContacts = new ArrayList<>();
		upsertedContacts.forEach(contact -> {
			ExternalContact savedContact = saveContact(contact);
				if(savedContact.isNotNull())
					savedContacts.add(savedContact);
		});
		return savedContacts;
	}

	private ExternalContact saveContact(ExternalContact contact) {
		if (contact == null) return null;
		ExternalContact returnContact = null;
		try {
			if (contact.isNew()) {
				returnContact = addressCommand.createContact(contact);
				contactInsertCount++;
			} else {
				returnContact = addressCommand.createContact(contact);
				contactUpdateCount++;
			}
		} catch (Exception e) {
			logger.error(String.format(SAVE_CONTACT_ERROR_MSG,
					contact.getId(), contact.getExternalRefId(), e.getMessage()));
		}
		return returnContact;
	}

	private List<ExternalAddress> overwriteDeletedAddresses(List<ExternalAddress> deletedAddresses,
                                                            List<ExternalAddress> syncedAddresses) {
        Map<String, ExternalAddress> map = new HashMap<>();
        for (ExternalAddress deletedAddress : deletedAddresses)
            map.put(deletedAddress.getExternalRefId(), deletedAddress);
        for (ExternalAddress syncedAddress : syncedAddresses)
            map.put(syncedAddress.getExternalRefId(), syncedAddress);
        return new ArrayList<>(map.values());
    }

    private void saveAddress(ExternalAddress address) {
        if (address == null) return;
        try {
            if (address.isNew()) {
                addressCommand.createAddress(address);
                addressInsertCount++;
            } else {
                addressCommand.updateAddress(address);
                addressUpdateCount++;
            }
        }
        catch (Exception e) {
            logger.error(String.format(SAVE_ADDR_ERROR_MSG,
                    address.getId(), address.getExternalRefId(), e.getMessage()));
        }
    }
}
