package co.cadshare.inventory.adapters.database;

import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import com.opencsv.CSVReader;

import co.cadshare.inventory.core.ModelPart;
import co.cadshare.inventory.core.BomUpload;
import co.cadshare.inventory.core.BomUpload.UnparseableModelPartsFileException;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.util.ArrayList;
import java.util.List;

@Component
public class CsvProcessor {
    
    private List<String[]> readAll(MultipartFile file) throws IOException {

        try (CSVReader reader = new CSVReader(new InputStreamReader(file.getInputStream(), "UTF-8"))){
            List<String[]> list;
            list = reader.readAll();
            return list;
        }
    }

    public BomUpload parseCsv(MultipartFile file) throws IOException, UnparseableModelPartsFileException {
        BomUpload modelPartsFile = new BomUpload(readAll(file));
        List<ModelPart> modelParts = new ArrayList<>();

        try (BufferedReader br = new BufferedReader(new InputStreamReader(file.getInputStream()))) {
            String line;
            boolean headerSkipped = false;

            while ((line = br.readLine()) != null) {
                // Skip the header row
                if (!headerSkipped) {
                    headerSkipped = true;
                    continue;
                }

                String[] values = line.split(","); // Assuming the CSV is separated by semicolons

                // Assuming the CSV structure is: Artikel Sohn;Ausfallwahrscheinlichkeit
                String artikelSohn = values[0].trim();
                String ausfallwahrscheinlichkeit = values[1].trim();

                ModelPart modelPart = new ModelPart();
                modelPart.setArtikelSohn(artikelSohn);
                modelPart.setAusfallwahrscheinlichkeit(ausfallwahrscheinlichkeit);

                modelParts.add(modelPart);
            }
        }

        modelPartsFile.setModelParts(modelParts);
        return modelPartsFile;
    }

    public static BomUpload convert(MultipartFile file, Class<BomUpload> class1) {
        return null;
    }
}

