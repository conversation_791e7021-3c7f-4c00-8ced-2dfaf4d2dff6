package co.cadshare.modelMgt.publicationCategories.adapters.database;

import co.cadshare.exceptions.NoCadshareDataFoundException;
import co.cadshare.exceptions.NotFoundException;
import co.cadshare.modelMgt.publicationCategories.boundary.PublicationCategoryCommandPort;
import co.cadshare.modelMgt.publicationCategories.boundary.PublicationCategoryQueryPort;
import co.cadshare.modelMgt.publicationCategories.core.PublicationCategory;
import co.cadshare.aspects.logging.Log;
import co.cadshare.shared.core.user.User;

import java.sql.Timestamp;
import java.util.Calendar;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.EntityNotFoundException;

@Service
public class PublicationCategoryDataFacade implements PublicationCategoryQueryPort, PublicationCategoryCommandPort {

    private final PublicationCategoryRepo publicationCategoryRepo;
    private final PublicationCategoryComplexQueryRepo publicationCategoryQueryRepo;
	private final PublicationCategoryDao publicationCategoryDao;

    @Autowired
    public PublicationCategoryDataFacade(PublicationCategoryRepo publicationCategoryRepo,
                                         PublicationCategoryComplexQueryRepo publicationCategoryQueryRepo,
                                         PublicationCategoryDao publicationCategoryDao) {
        this.publicationCategoryRepo = publicationCategoryRepo;
        this.publicationCategoryQueryRepo = publicationCategoryQueryRepo;
	    this.publicationCategoryDao = publicationCategoryDao;
    }

    @Override
    public PublicationCategory get(Integer publicationCategoryId) {
		try {
			PublicationCategoryEntity entity = this.publicationCategoryRepo.getOne(publicationCategoryId);
			if (entity.isDeleted())
				throw new NotFoundException("PublicationCategory does not exist");
			return PublicationCategoryEntityMapper.Instance.entityToCore(entity);
		} catch (EntityNotFoundException ex) {
			throw new NoCadshareDataFoundException("PublicationCategory does not exist");
		}
    }

    public List<PublicationCategory> getListForManufacturer(Integer manufacturerId) {
         List<PublicationCategoryEntity> entities = this.publicationCategoryQueryRepo.getPublicationCategoriesForManufacturer(manufacturerId);
         return PublicationCategoryEntityMapper.Instance.entitiesToCores(entities);
    }

    @Override
    public List<PublicationCategory> getList() {
        List<PublicationCategoryEntity> entities = this.publicationCategoryRepo.findAll();
        return PublicationCategoryEntityMapper.Instance.entitiesToCores(entities);
    }

    @Override
    @Log
    public Integer create(User user, PublicationCategory publicationCategory) {
        publicationCategory.setCreatedByUserId(user.getUserId());
        publicationCategory.setCreatedDate(new Timestamp(Calendar.getInstance().getTime().getTime()));
        PublicationCategoryEntity publicationCategoryEntity = PublicationCategoryEntityMapper.Instance.coreToEntity(publicationCategory);
        PublicationCategoryEntity savedEntity = this.publicationCategoryRepo.save(publicationCategoryEntity);
        return savedEntity.getId();
    }

    @Override
    @Log
    public void update(User user, PublicationCategory publicationCategory) throws Exception {
        publicationCategory.setModifiedByUserId(user.getUserId());
        publicationCategory.setModifiedDate(new Timestamp(Calendar.getInstance().getTime().getTime()));
        this.publicationCategoryRepo.save(PublicationCategoryEntityMapper.Instance.coreToEntity(publicationCategory));
    }

    @Override
    @Log
    public void delete(User user, PublicationCategory publicationCategory) throws Exception {
        publicationCategory.setModifiedByUserId(user.getUserId());
        publicationCategory.setModifiedDate(new Timestamp(Calendar.getInstance().getTime().getTime()));
        this.publicationCategoryRepo.save(PublicationCategoryEntityMapper.Instance.coreToEntity(publicationCategory));
    }

	@Override
	public void assignPublicationCategoriesToPurchaser(Integer purchaserId, List<Integer> publicationCategoryIds, User user) throws Exception {
		publicationCategoryDao.assignPublicationCategoriesToPurchaser(purchaserId, publicationCategoryIds, user);
	}

	@Override
	public void clearPublicationCategoriesForPurchaser(Integer purchaserId) {
		publicationCategoryDao.clearPublicationCategoriesForPurchaser(purchaserId);
	}

	@Override
	public List<PublicationCategory> getAssignedPublicationCategoriesForPurchaser(int purchaserId) {
		List<PublicationCategoryEntity> entities = publicationCategoryDao.getAssignedPublicationCategoriesForPurchaser(purchaserId);
		return PublicationCategoryEntityMapper.Instance.entitiesToCores(entities);
	}
}