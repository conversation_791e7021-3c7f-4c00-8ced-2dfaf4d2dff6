package co.cadshare.inventory.core;

import co.cadshare.domainmodel.part.PartDetails;
import co.cadshare.utils.ObjectExtension;
import lombok.experimental.ExtensionMethod;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.List;

@ExtensionMethod(ObjectExtension.class)
public class BomParser {

    private final int modelId;
    private final BomUploadConfiguration bomUploadConfiguration;
    private final BomUpload bomUpload;
    private static final Logger logger = LoggerFactory.getLogger(BomParser.class);

    public BomParser(int modelId, BomUploadConfiguration bomUploadConfiguration, BomUpload bomUpload) {
        this.modelId = modelId;
        this.bomUploadConfiguration = bomUploadConfiguration;
        this.bomUpload = bomUpload;
    }

    public List<PartDetails> parseBomToPartDetails() {

        List<PartDetails> partsDetails = new ArrayList<>();
        for (int i = 0; i < bomUpload.getDataRows().size(); i++) {
            try {
                String name = null;
                Integer objectId = null;
                String description = null;
                String number = null;
                String[] rowData = bomUpload.getDataRows().get(i);
                if (bomUploadConfiguration.getNameColumn().isNotNull()) {
                    name = rowData[bomUploadConfiguration.getNameColumn()].trim();
                    name = (name.isEmpty()) ? null : name;
                }
                if (bomUploadConfiguration.getObjectIdColumn().isNotNull()) {
                    objectId = Integer.valueOf(rowData[bomUploadConfiguration.getObjectIdColumn()]);
                }
                if (bomUploadConfiguration.getDescriptionColumn().isNotNull()) {
                    description = rowData[bomUploadConfiguration.getDescriptionColumn()].trim();
                    description = (description.isEmpty()) ? null : description;
                }
                if (bomUploadConfiguration.getNumberColumn().isNotNull()) {
                    number = rowData[bomUploadConfiguration.getNumberColumn()].trim();
                    number = (number.isEmpty()) ? null : number;
                }
                PartDetails details = new PartDetails(name, objectId, description, number, bomUploadConfiguration, modelId);
                partsDetails.add(details);
            } catch (Exception ex) {
                logger.error("Error occurred uploading csv file data in row " + i, ex);
            }
        }
        return partsDetails;
    }

    public static List<String> parseBomHeader(String file) throws Exception {
        List<String> headerList = new ArrayList<>();
        String[] headerFromFile = file.split("\r|\n");

        if (headerFromFile.length > 0) {
            String[] data = headerFromFile[0].split(",");
            for (String heading : data)
                headerList.add(heading.trim());
        } else {
            throw new Exception("File headers are invalid");
        }
        return headerList;
    }
}
