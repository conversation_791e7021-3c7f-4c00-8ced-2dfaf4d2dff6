<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog/1.9"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog/1.9
         http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-1.9.xsd">

    <!--
    this is required when you are applying the changelog in environment where the database already exists
    if you want to apply the changelog in a database that does *not* exist, use db.changelog-master-bootstrapped.xml
    -->

    <include file="db.changelog-8.0.xml" relativeToChangelogFile="true"/>
    <include file="db.changelog-8.1.xml" relativeToChangelogFile="true"/>
    <include file="db.changelog-8.2.xml" relativeToChangelogFile="true"/>
    <include file="db.changelog-8.3.xml" relativeToChangelogFile="true"/>
    <include file="db.changelog-8.4.xml" relativeToChangelogFile="true"/>
    <include file="db.changelog-7.10.xml" relativeToChangelogFile="true"/>
    <include file="db.changelog-8.5.xml" relativeToChangelogFile="true"/>
    <include file="db.changelog-8.6.xml" relativeToChangelogFile="true"/>
    <include file="db.changelog-8.7.xml" relativeToChangelogFile="true"/>
    <include file="db.changelog-8.8.xml" relativeToChangelogFile="true"/>
    <include file="db.changelog-8.9.xml" relativeToChangelogFile="true"/>
    <include file="db.changelog-8.10.xml" relativeToChangelogFile="true"/>
    <include file="db.changelog-8.11.xml" relativeToChangelogFile="true"/>
    <include file="db.changelog-8.12.xml" relativeToChangelogFile="true"/>
    <include file="db.changelog-8.13.xml" relativeToChangelogFile="true"/>
    <include file="db.changelog-8.14.xml" relativeToChangelogFile="true"/>
    <include file="db.changelog-8.15.xml" relativeToChangelogFile="true"/>
    <include file="db.changelog-8.16.xml" relativeToChangelogFile="true"/>
    <include file="db.changelog-8.17.xml" relativeToChangelogFile="true"/>
    <include file="db.changelog-8.18.xml" relativeToChangelogFile="true"/>
    <include file="db.changelog-8.19.xml" relativeToChangelogFile="true"/>
    <include file="9.0/db.changelog-9.0.xml" relativeToChangelogFile="true"/>
    <include file="9.0/db.changelog-9.1.xml" relativeToChangelogFile="true"/>
    <include file="9.0/db.changelog-9.2.xml" relativeToChangelogFile="true"/>
    <include file="9.0/db.changelog-9.3.xml" relativeToChangelogFile="true"/>
    <include file="9.0/db.changelog-9.4.xml" relativeToChangelogFile="true"/>
    <include file="9.0/db.changelog-9.5.xml" relativeToChangelogFile="true"/>
    <include file="9.0/db.changelog-9.6.xml" relativeToChangelogFile="true"/>
    <include file="9.0/db.changelog-9.7.xml" relativeToChangelogFile="true"/>
    <include file="9.0/db.changelog-9.8.xml" relativeToChangelogFile="true"/>
    <include file="9.0/db.changelog-9.9.xml" relativeToChangelogFile="true"/>
    <include file="9.0/db.changelog-9.10.xml" relativeToChangelogFile="true"/>
    <include file="9.0/db.changelog-9.11.xml" relativeToChangelogFile="true"/>
    <include file="9.0/db.changelog-9.12.xml" relativeToChangelogFile="true"/>
    <include file="9.0/db.changelog-9.14.xml" relativeToChangelogFile="true"/>
    <include file="9.0/db.changelog-9.13.xml" relativeToChangelogFile="true"/>
    <include file="9.0/db.changelog-9.15.xml" relativeToChangelogFile="true"/>
    <include file="9.0/db.changelog-9.16.xml" relativeToChangelogFile="true"/>
    <include file="9.0/db.changelog-9.17.xml" relativeToChangelogFile="true"/>
    <include file="9.0/db.changelog-9.18.xml" relativeToChangelogFile="true"/>
    <include file="10.0/db.changelog-10.0.xml" relativeToChangelogFile="true"/>
    <include file="10.0/db.changelog-10.1.xml" relativeToChangelogFile="true"/>
    <include file="10.0/db.changelog-10.2.xml" relativeToChangelogFile="true"/>
    <include file="11.0/db.changelog-11.0.xml" relativeToChangelogFile="true"/>
    <include file="11.0/db.changelog-11.1.xml" relativeToChangelogFile="true"/>
    <include file="12/db.changelog-12.0.xml" relativeToChangelogFile="true"/>
    <include file="13/db.changelog-13.0.xml" relativeToChangelogFile="true"/>
    <include file="14/db.changelog-14.0.xml" relativeToChangelogFile="true"/>
    <include file="14/db.changelog-14.1.xml" relativeToChangelogFile="true"/>
    <include file="15/db.changelog-15.0.xml" relativeToChangelogFile="true"/>
    <include file="16/db.changelog-16.0.xml" relativeToChangelogFile="true"/>
    <include file="16/db.changelog-16.1.xml" relativeToChangelogFile="true"/>
    <include file="16/db.changelog-16.2.xml" relativeToChangelogFile="true"/>
    <include file="17/db.changelog-17.0.xml" relativeToChangelogFile="true"/>
    <include file="18/db.changelog-18.0.xml" relativeToChangelogFile="true"/>
    <include file="19/db.changelog-19.0.xml" relativeToChangelogFile="true"/>
    <include file="20/db.changelog-20.0.xml" relativeToChangelogFile="true"/>
    <include file="21/db.changelog-21.0.xml" relativeToChangelogFile="true"/>
    <include file="22/db.changelog-22.0.xml" relativeToChangelogFile="true"/>
    <include file="22/db.changelog-22.1.xml" relativeToChangelogFile="true"/>
    <include file="23/db.changelog-23.0.xml" relativeToChangelogFile="true"/>
    <include file="24/db.changelog-24.0.xml" relativeToChangelogFile="true"/>
    <include file="24/db.changelog-24.1.xml" relativeToChangelogFile="true"/>
    <include file="24/db.changelog-24.2.xml" relativeToChangelogFile="true"/>
    <include file="24/db.changelog-24.3.xml" relativeToChangelogFile="true"/>
    <include file="24/db.changelog-24.4.xml" relativeToChangelogFile="true"/>
    <include file="24/db.changelog-24.5.xml" relativeToChangelogFile="true"/>
    <include file="24/db.changelog-24.6.xml" relativeToChangelogFile="true"/>
    <include file="24/db.changelog-24.7.xml" relativeToChangelogFile="true"/>
    <include file="24/db.changelog-24.8.xml" relativeToChangelogFile="true"/>
    <include file="24/db.changelog-24.9.xml" relativeToChangelogFile="true"/>
    <include file="25/db.changelog-25.0.xml" relativeToChangelogFile="true"/>
    <include file="26/db.changelog-26.0.xml" relativeToChangelogFile="true"/>
    <include file="27/db.changelog-27.0.xml" relativeToChangelogFile="true"/>
    <include file="28/db.changelog-28.0.xml" relativeToChangelogFile="true"/>
    <include file="29/db.changelog-29.0.xml" relativeToChangelogFile="true"/>
    <include file="29/db.changelog-29.1.xml" relativeToChangelogFile="true"/>
    <include file="30/db.changelog-30.0.xml" relativeToChangelogFile="true"/>
    <include file="30/db.changelog-30.1.xml" relativeToChangelogFile="true"/>
    <include file="32/db.changelog-32.0.xml" relativeToChangelogFile="true"/>
    <include file="33/db.changelog-33.0.xml" relativeToChangelogFile="true"/>
    <include file="34/db.changelog-34.0.xml" relativeToChangelogFile="true"/>
    <include file="35/db.changelog-35.0.xml" relativeToChangelogFile="true"/>
    <include file="36/db.changelog-36.0.xml" relativeToChangelogFile="true"/>
    <include file="36/db.changelog-36.1.xml" relativeToChangelogFile="true"/>
    <include file="36/db.changelog-36.2.xml" relativeToChangelogFile="true"/>
    <include file="37/db.changelog-37.0.xml" relativeToChangelogFile="true"/>
    <include file="38/db.changelog-38.0.xml" relativeToChangelogFile="true"/>
    <include file="38/db.changelog-38.1.xml" relativeToChangelogFile="true"/>
    <include file="39/db.changelog-39.0.xml" relativeToChangelogFile="true"/>
    <include file="40/db.changelog-40.0.xml" relativeToChangelogFile="true"/>
    <include file="41/db.changelog-41.0.xml" relativeToChangelogFile="true"/>
    <include file="41/db.changelog-41.1.xml" relativeToChangelogFile="true"/>
    <include file="42/db.changelog-42.0.xml" relativeToChangelogFile="true"/>
    <include file="42/db.changelog-42.1.xml" relativeToChangelogFile="true"/>
    <include file="42/db.changelog-42.2.xml" relativeToChangelogFile="true"/>
    <include file="42/db.changelog-42.3.xml" relativeToChangelogFile="true"/>
    <include file="43/db.changelog-43.0.xml" relativeToChangelogFile="true"/>
    <include file="44/db.changelog-44.0.xml" relativeToChangelogFile="true"/>
    <include file="45/db.changelog-45.0.xml" relativeToChangelogFile="true"/>
    <include file="46/db.changelog-46.0.xml" relativeToChangelogFile="true"/>
    <include file="47/db.changelog-47.0.xml" relativeToChangelogFile="true"/>
    <include file="48/db.changelog-48.0.xml" relativeToChangelogFile="true"/>
    <include file="49/db.changelog-49.xml" relativeToChangelogFile="true"/>
    <include file="50/db.changelog-50.0.xml" relativeToChangelogFile="true"/>
    <include file="51/db.changelog-51.xml" relativeToChangelogFile="true"/>
    <include file="52/db.changelog-52.xml" relativeToChangelogFile="true"/>
    <include file="53/db.changelog-53.xml" relativeToChangelogFile="true"/>
    <include file="55/db.changelog-55.0.xml" relativeToChangelogFile="true"/>
    <include file="56/db.changelog-56.xml" relativeToChangelogFile="true"/>
    <include file="57/db.changelog-57.xml" relativeToChangelogFile="true"/>
    <include file="60/db.changelog-60.xml" relativeToChangelogFile="true"/>
</databaseChangeLog>