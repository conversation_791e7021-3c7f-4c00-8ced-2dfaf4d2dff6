package co.cadshare.modelMgt.ranges.adapters.database;

import co.cadshare.modelMgt.ranges.adapters.database.RangeEntity;
import org.mockito.ArgumentMatcher;

public class RangeEntityMatcher implements ArgumentMatcher<RangeEntity> {

    private RangeEntity left;

    public RangeEntityMatcher(RangeEntity left) {
        this.left = left;
    }

    @Override
    public boolean matches(RangeEntity right) {
        boolean returnVal = (left.getId() == null && right.getId() == null) ||
                left.getId() == right.getId();
        return returnVal;
    }
}
