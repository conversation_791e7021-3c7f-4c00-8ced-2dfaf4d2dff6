<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                   http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.0.xsd
">
    <!-- matching part for simulation of part identification/selection from viewer -->
    <changeSet author="AndyB" id="2.3-integration-test-data-create-parts-1">
        <sql stripComments="true">
            INSERT INTO public.part (partid, filename, partdescription, modelid, partnumber, modifiedbyuserid, modifieddate, createdbyuserid, createddate, objectid, parentobjectid, massunit, weight, itemnumber, sparepart, alternatepartnumber, criticalsparepart, sparepartidentifier, sellablepart)
            VALUES(
            1, 	                        --partid,
            'Caterpillar Part 1', 	    --filename,
            '', 	                    --partdescription,
            1, 	                        --modelid,
            'Cat-part-1', 	            --partnumber,
            1, 	                        --modifiedbyuserid,
            '2017-06-09 10:17:25.019', 	--modifieddate,
            1, 	                        --createdbyuserid,
            '2017-06-09 10:17:25.019', 	--createddate,
            1, 	                        --objectid,
            NULL, 	                    --parentobjectid,
            NULL, 	                    --massunit,
            NULL, 	                    --weight,
            NULL, 	                    --itemnumber,
            true, 	                    --sparepart,
            NULL, 	                    --alternatepartnumber,
            false, 	                    --criticalsparepart,
            NULL, 	                    --sparepartidentifier,
            FALSE);	                    --sellablepart)
        </sql>
    </changeSet>



    <changeSet author="AndyB" id="2.3-integration-test-data-create-parts-101">
        <sql stripComments="true">
            INSERT INTO public.part (partid, filename, partdescription, modelid, partnumber, modifiedbyuserid, modifieddate, createdbyuserid, createddate, objectid, parentobjectid, massunit, weight, itemnumber, sparepart, alternatepartnumber, criticalsparepart, sparepartidentifier, sellablepart)
            VALUES(
            101, 	                        --partid,
            'Caterpillar Part 1', 	    --filename,
            '', 	                    --partdescription,
            1, 	                        --modelid,
            'Cat-part-101', 	            --partnumber,
            1, 	                        --modifiedbyuserid,
            '2017-06-09 10:17:25.019', 	--modifieddate,
            1, 	                        --createdbyuserid,
            '2017-06-09 10:17:25.019', 	--createddate,
            101, 	                    --objectid,
            NULL, 	                    --parentobjectid,
            NULL, 	                    --massunit,
            NULL, 	                    --weight,
            NULL, 	                    --itemnumber,
            true, 	                    --sparepart,
            NULL, 	                    --alternatepartnumber,
            false, 	                    --criticalsparepart,
            NULL, 	                    --sparepartidentifier,
            FALSE);	                    --sellablepart)
        </sql>
    </changeSet>
    <changeSet author="AndyB" id="2.3-integration-test-data-create-parts-102">
        <sql stripComments="true">
            INSERT INTO public.part (partid, filename, partdescription, modelid, partnumber, modifiedbyuserid, modifieddate, createdbyuserid, createddate, objectid, parentobjectid, massunit, weight, itemnumber, sparepart, alternatepartnumber, criticalsparepart, sparepartidentifier, sellablepart)
            VALUES(
            102, 	                        --partid,
            'Caterpillar Part 102', 	    --filename,
            '', 	                    --partdescription,
            1, 	                        --modelid,
            'Cat-part-102', 	            --partnumber,
            1, 	                        --modifiedbyuserid,
            '2017-06-09 10:17:25.019', 	--modifieddate,
            1, 	                        --createdbyuserid,
            '2017-06-09 10:17:25.019', 	--createddate,
            102, 	                    --objectid,
            NULL, 	                    --parentobjectid,
            NULL, 	                    --massunit,
            NULL, 	                    --weight,
            NULL, 	                    --itemnumber,
            true, 	                    --sparepart,
            NULL, 	                    --alternatepartnumber,
            false, 	                    --criticalsparepart,
            NULL, 	                    --sparepartidentifier,
            FALSE);	                    --sellablepart)
        </sql>
    </changeSet>
    <changeSet author="AndyB" id="2.3-integration-test-data-create-parts-103">
        <sql stripComments="true">
            INSERT INTO public.part (partid, filename, partdescription, modelid, partnumber, modifiedbyuserid, modifieddate, createdbyuserid, createddate, objectid, parentobjectid, massunit, weight, itemnumber, sparepart, alternatepartnumber, criticalsparepart, sparepartidentifier, sellablepart)
            VALUES(
            103, 	                        --partid,
            'Caterpillar Part 103', 	    --filename,
            '', 	                    --partdescription,
            1, 	                        --modelid,
            'Cat-part-103', 	            --partnumber,
            1, 	                        --modifiedbyuserid,
            '2017-06-09 10:17:25.019', 	--modifieddate,
            1, 	                        --createdbyuserid,
            '2017-06-09 10:17:25.019', 	--createddate,
            103, 	                    --objectid,
            NULL, 	                    --parentobjectid,
            NULL, 	                    --massunit,
            NULL, 	                    --weight,
            NULL, 	                    --itemnumber,
            true, 	                    --sparepart,
            NULL, 	                    --alternatepartnumber,
            false, 	                    --criticalsparepart,
            NULL, 	                    --sparepartidentifier,
            FALSE);	                    --sellablepart)
        </sql>
    </changeSet>
    <changeSet author="AndyB" id="2.3-integration-test-data-create-parts-104">
        <sql stripComments="true">
            INSERT INTO public.part (partid, filename, partdescription, modelid, partnumber, modifiedbyuserid, modifieddate, createdbyuserid, createddate, objectid, parentobjectid, massunit, weight, itemnumber, sparepart, alternatepartnumber, criticalsparepart, sparepartidentifier, sellablepart)
            VALUES(
            104, 	                        --partid,
            'Caterpillar Part 104', 	    --filename,
            '', 	                    --partdescription,
            1, 	                        --modelid,
            'Cat-part-104', 	            --partnumber,
            1, 	                        --modifiedbyuserid,
            '2017-06-09 10:17:25.019', 	--modifieddate,
            1, 	                        --createdbyuserid,
            '2017-06-09 10:17:25.019', 	--createddate,
            104, 	                    --objectid,
            NULL, 	                    --parentobjectid,
            NULL, 	                    --massunit,
            NULL, 	                    --weight,
            NULL, 	                    --itemnumber,
            true, 	                    --sparepart,
            NULL, 	                    --alternatepartnumber,
            false, 	                    --criticalsparepart,
            NULL, 	                    --sparepartidentifier,
            FALSE);	                    --sellablepart)
        </sql>
    </changeSet>
    <changeSet author="AndyB" id="2.3-integration-test-data-create-parts-105">
        <sql stripComments="true">
            INSERT INTO public.part (partid, filename, partdescription, modelid, partnumber, modifiedbyuserid, modifieddate, createdbyuserid, createddate, objectid, parentobjectid, massunit, weight, itemnumber, sparepart, alternatepartnumber, criticalsparepart, sparepartidentifier, sellablepart)
            VALUES(
            105, 	                        --partid,
            'Caterpillar Part 105', 	    --filename,
            '', 	                    --partdescription,
            1, 	                        --modelid,
            'Cat-part-105', 	            --partnumber,
            1, 	                        --modifiedbyuserid,
            '2017-06-09 10:17:25.019', 	--modifieddate,
            1, 	                        --createdbyuserid,
            '2017-06-09 10:17:25.019', 	--createddate,
            105, 	                    --objectid,
            NULL, 	                    --parentobjectid,
            NULL, 	                    --massunit,
            NULL, 	                    --weight,
            NULL, 	                    --itemnumber,
            true, 	                    --sparepart,
            NULL, 	                    --alternatepartnumber,
            false, 	                    --criticalsparepart,
            NULL, 	                    --sparepartidentifier,
            FALSE);	                    --sellablepart)
        </sql>
    </changeSet>
    <changeSet author="AndyB" id="2.3-integration-test-data-create-parts-106">
        <sql stripComments="true">
            INSERT INTO public.part (partid, filename, partdescription, modelid, partnumber, modifiedbyuserid, modifieddate, createdbyuserid, createddate, objectid, parentobjectid, massunit, weight, itemnumber, sparepart, alternatepartnumber, criticalsparepart, sparepartidentifier, sellablepart)
            VALUES(
            106, 	                        --partid,
            'Caterpillar Part 106', 	    --filename,
            '', 	                    --partdescription,
            1, 	                        --modelid,
            'Cat-part-106', 	            --partnumber,
            1, 	                        --modifiedbyuserid,
            '2017-06-09 10:17:25.019', 	--modifieddate,
            1, 	                        --createdbyuserid,
            '2017-06-09 10:17:25.019', 	--createddate,
            106, 	                    --objectid,
            NULL, 	                    --parentobjectid,
            NULL, 	                    --massunit,
            NULL, 	                    --weight,
            NULL, 	                    --itemnumber,
            true, 	                    --sparepart,
            NULL, 	                    --alternatepartnumber,
            false, 	                    --criticalsparepart,
            NULL, 	                    --sparepartidentifier,
            FALSE);	                    --sellablepart)
        </sql>
    </changeSet>
    <changeSet author="AndyB" id="2.3-integration-test-data-create-parts-107">
        <sql stripComments="true">
            INSERT INTO public.part (partid, filename, partdescription, modelid, partnumber, modifiedbyuserid, modifieddate, createdbyuserid, createddate, objectid, parentobjectid, massunit, weight, itemnumber, sparepart, alternatepartnumber, criticalsparepart, sparepartidentifier, sellablepart)
            VALUES(
            107, 	                        --partid,
            'Caterpillar Part 107', 	    --filename,
            '', 	                    --partdescription,
            1, 	                        --modelid,
            'Cat-part-107', 	            --partnumber,
            1, 	                        --modifiedbyuserid,
            '2017-06-09 10:17:25.019', 	--modifieddate,
            1, 	                        --createdbyuserid,
            '2017-06-09 10:17:25.019', 	--createddate,
            107, 	                    --objectid,
            NULL, 	                    --parentobjectid,
            NULL, 	                    --massunit,
            NULL, 	                    --weight,
            NULL, 	                    --itemnumber,
            true, 	                    --sparepart,
            NULL, 	                    --alternatepartnumber,
            false, 	                    --criticalsparepart,
            NULL, 	                    --sparepartidentifier,
            FALSE);	                    --sellablepart)
        </sql>
    </changeSet>
</databaseChangeLog>
