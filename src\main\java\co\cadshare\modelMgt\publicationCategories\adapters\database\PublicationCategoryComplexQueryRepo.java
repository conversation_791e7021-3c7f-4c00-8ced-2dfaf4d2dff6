package co.cadshare.modelMgt.publicationCategories.adapters.database;

import com.querydsl.jpa.impl.JPAQueryFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import javax.persistence.EntityManager;
import java.util.List;

@Repository
public class PublicationCategoryComplexQueryRepo {

    private final JPAQueryFactory queryFactory;

    @Autowired
    public PublicationCategoryComplexQueryRepo(EntityManager entityManager) {
        this.queryFactory = new JPAQueryFactory(entityManager);
    }

    public List<PublicationCategoryEntity> getPublicationCategoriesForManufacturer(Integer manufacturerId) {
        QPublicationCategoryEntity publicationCategory = QPublicationCategoryEntity.publicationCategoryEntity;
        return queryFactory.selectFrom(publicationCategory)
                .where(publicationCategory.manufacturerId.eq(manufacturerId)
                        .and(publicationCategory.deleted.eq(false)))
                .fetch();
    }

}

