/*
 * Copyright 2016 Bell.
 */
package co.cadshare.domainmodel.serialnumber;

import java.sql.Timestamp;
import java.util.List;

import lombok.Data;

/**
 * TODO(dallanmc) Description of class.
 */
@Data
public class SerialNumber {

    private int serialNumberId;
    private String serialNumber;

    private int manualId;
    private String manualName;
    private String manualStatus;

    private List<Integer> modelId;
    private List<String> modelName;
    private List<Integer> techDocId;
    private List<String> techDocName;
    private List<Integer> videoId;
    private List<String> videoName;
    private List<Integer> kitId;
    private List<String> kitTitle;

    private Integer featuredModelId;
    private String featuredModelUrl;

    private int rangeId;
    private String rangeName;

    private int machineId;
    private String machineName;

    private Timestamp createdDate;
    private Integer createdByUserId;

    private List<Integer> manufacturerSubEntityIds;
    private List<String> manufacturerSubEntityNames;
    private boolean useViewableImage;

    @Override
    public boolean equals(Object o){
        if (o instanceof SerialNumber){
          SerialNumber temp = (SerialNumber)o;
            if (this.getSerialNumberId() == (temp.getSerialNumberId()) && this.getManualId() == (temp.getManualId()))
                return true;
        }
        return false;
    }
}
