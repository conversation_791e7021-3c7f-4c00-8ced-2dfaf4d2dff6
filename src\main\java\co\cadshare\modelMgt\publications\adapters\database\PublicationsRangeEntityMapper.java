package co.cadshare.modelMgt.publications.adapters.database;

import co.cadshare.modelMgt.shared.core.Product;
import co.cadshare.modelMgt.shared.core.Range;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper()
public interface PublicationsRangeEntityMapper {

    PublicationsRangeEntityMapper Instance = Mappers.getMapper(PublicationsRangeEntityMapper.class);

	Range entityToCore(PublicationsRangeEntity entity);

    List<Range> entitiesToCores(List<PublicationsRangeEntity> publication);

}
