package co.cadshare.masterKits.adapters.database;

import co.cadshare.exceptions.NotFoundException;
import co.cadshare.masterKits.core.KitMasterPart;
import co.cadshare.masterKits.core.MasterKit;
import co.cadshare.masterKits.core.PartMasterPart;
import co.cadshare.masterParts.core.MasterPartType;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;
import org.mapstruct.factory.Mappers;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import static co.cadshare.utils.CustomStreamCollector.collectSingle;

@Mapper(uses=MasterPartKitMapEntityMapper.class)
public interface MasterKitEntityMapper {

    MasterKitEntityMapper Instance = Mappers.getMapper(MasterKitEntityMapper.class);

    @Mapping(source=".", target="kitMasterPart", qualifiedByName="mapToKitMasterPart")
    @Mapping(source=".", target="partMasterParts", qualifiedByName="partMasterParts")
    MasterKit entityToCore(MkMasterKitEntity masterKitEntity);

    @Mapping(source=".", target="masterParts", qualifiedByName="mapToMasterPartEntities")
    MkMasterKitEntity coreToEntity(MasterKit masterKit);


    List<MasterKit> entitiesToCores(List<MkMasterKitEntity> entities);

    List<MkMasterKitEntity> coresToEntities(List<MasterKit> masterKits);

    @Named("mapToMasterPartEntities")
    public static List<MkMasterPartKitMapEntity> mapToMasterPartEntities(MasterKit masterKit) {
        List<MkMasterPartKitMapEntity> masterParts = new ArrayList<>();
        if(masterKit.getPartMasterParts() == null) return null;
        masterKit.getPartMasterParts().forEach(part ->
            masterParts.add(MasterPartKitMapEntityMapper.Instance.coreToEntity(part, masterKit.getId())));

        if(masterKit.hasKitMasterPart())
            masterParts.add(MasterPartKitMapEntityMapper.Instance.coreToEntity(masterKit.getKitMasterPart(), masterKit.getId()));
        return masterParts;
    }

    @Named("mapToKitMasterPart")
    public static KitMasterPart mapToKitMasterPart(MkMasterKitEntity masterKitEntity) {
        MkMasterPartKitMapEntity kitEntity = getKitMasterPartEntity(masterKitEntity);
        return MasterPartKitMapEntityMapper.Instance.entityToCoreForKit(kitEntity);
    }

    @Named("partMasterParts")
    public static List<PartMasterPart> partMasterParts(MkMasterKitEntity masterKit) {
        List<MkMasterPartKitMapEntity> entities = masterKit.getMasterParts()
                .stream()
                .filter(mp -> mp.getMasterPartDetail().getType().equals(MasterPartType.PART))
                .collect(Collectors.toList());
        return MasterPartKitMapEntityMapper.Instance.entitiesToCores(entities);
    }


    static MkMasterPartKitMapEntity getKitMasterPartEntity(MkMasterKitEntity masterKitEntity) {
        try {
            return masterKitEntity.getMasterParts()
                    .stream()
                    .filter(mp -> mp.getMasterPartDetail().getType().equals(MasterPartType.KIT))
                    .collect(collectSingle());
        }
        catch (IllegalStateException stateException) {
            throw new NotFoundException("Kit Master Part cannot be found: ".concat(masterKitEntity.getId().toString()));
        }
    }
}
