package co.cadshare.modelMgt.models.boundary;

import co.cadshare.modelMgt.models.core.Model;
import co.cadshare.shared.core.manufacturer.ManufacturerSettings;
import co.cadshare.shared.adapters.database.manufacturer.ManufacturerDao;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import co.cadshare.shared.core.user.User;
import co.cadshare.aspects.logging.Log;

@Service
public class CreateModelService {

    private final ModelCommandPort modelCommand;
    private final AutodeskPort autodesk;
    private ManufacturerDao manufacturerDao;

    private final UserQueryPort userQuery;

    @Autowired
    public CreateModelService(ModelCommandPort modelCommand,
                              AutodeskPort autodesk,
                              ManufacturerDao manufacturerDao,
                              UserQueryPort userQuery) {
        this.modelCommand = modelCommand;
        this.autodesk = autodesk;
        this.manufacturerDao = manufacturerDao;
        this.userQuery = userQuery;
    }

    @Log
    public int createModel(User user, Model model) throws Exception {
        ManufacturerSettings settings = manufacturerDao.getManufacturerSettingsById(user.getManufacturerId());
        model.configureForCreation(settings);
        int modelId = modelCommand.create(user, model);
        model.setModelId(modelId);
        if(model.notFinishedTranslating()) {
            autodesk.translateModel(model, model.getTranslateType());
            modelCommand.update(user, model);
        }
        return modelId;
    }

    public int createModel(Model model, int manufacturerId) throws Exception {
        User apiUser = this.userQuery.getApiUserForManufacturer(manufacturerId);
        return  createModel(apiUser, model);
    }
}
