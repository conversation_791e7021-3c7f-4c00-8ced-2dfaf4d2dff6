package co.cadshare.masterParts.adapters.database;

import co.cadshare.masterParts.core.SupersededMasterPart;
import co.cadshare.masterParts.core.SupersessionHistoryItem;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface SupersededMasterPartCteMapper {

    SupersededMasterPartCteMapper Instance = Mappers.getMapper(SupersededMasterPartCteMapper.class);

    @Mapping(source="masterPartId", target="id")
    SupersededMasterPart entityToCore(SupersededMasterPartEntityCte entity);

    List<SupersededMasterPart> entitiesToCores(List<SupersededMasterPartEntityCte> entities);
}
