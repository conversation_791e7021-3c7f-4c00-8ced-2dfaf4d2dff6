package co.cadshare.publications.adapters.database;

import co.cadshare.publications.core.Product;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface PublicationsProductEntityViewMapper {

    PublicationsProductEntityViewMapper Instance = Mappers.getMapper(PublicationsProductEntityViewMapper.class);

    Product entityToCore(PublicationsProductEntityView entityView);
}
