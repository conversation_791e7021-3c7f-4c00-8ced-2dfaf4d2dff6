package co.cadshare.modelMgt.publications.boundary;

import co.cadshare.exceptions.UnprocessableEntityException;
import co.cadshare.modelMgt.publications.core.ManualStatus;
import co.cadshare.modelMgt.shared.core.Publication;
import co.cadshare.modelMgt.shared.core.Viewable;
import co.cadshare.utils.ObjectUtilsExtension;
import lombok.Data;
import lombok.experimental.ExtensionMethod;

import java.util.List;

@Data
@ExtensionMethod(ObjectUtilsExtension.class)
public abstract class PublicationCommand {

	private String name;
	private String serialNumber;
	private int publicationCategoryId;
	private List<Viewable> viewables;
	private int coverImageId;
	private int featuredViewableImageId;
	private List<Integer> techDocs;
	private List<Integer> videos;
	private List<Integer> kits;
	private List<Integer> customers;
	private Integer featuredViewableId;
	private Integer manufacturerId;

	protected Publication build(Publication publication) {
		publication.setName(name);
		publication.setSerialNumber(serialNumber);
		publication.setManufacturerId(manufacturerId);
		publication.setFeaturedViewableId(featuredViewableId);
		publication.setStatus(ManualStatus.Status.UNPUBLISHED);
		return publication;
	}

	public void validate() {
		if (name.isNull() || name.isEmpty())
			throw new UnprocessableEntityException("Invalid, a name must be provided for the Publication");
	}

	public boolean hasViewables() {
		return viewables != null && !viewables.isEmpty();
	}

	public boolean hasKits() {
		return kits != null && !kits.isEmpty();
	}

	public boolean hasVideos() {
		return videos != null && !videos.isEmpty();
	}

	public boolean hasTechDocs() {
		return techDocs != null && !techDocs.isEmpty();
	}

	public boolean hasCustomers() {
		return customers != null && !customers.isEmpty();
	}

	public boolean hasCoverImage() {
		return coverImageId != 0;
	}

	public boolean hasFeaturedViewableImage() {
		return featuredViewableImageId != 0;
	}

	public boolean hasPublicationCategory() {
		return publicationCategoryId != 0;
	}

}
