package co.cadshare.modelMgt.publications.boundary;

import co.cadshare.modelMgt.shared.core.Publication;
import co.cadshare.modelMgt.publications.core.TechDoc;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service
public class TechDocHydrator implements PublicationAttributeHydrator {

	private final TechDocQueryPort techDocQueryPort;

	@Autowired
	public TechDocHydrator(TechDocQueryPort techDocQueryPort) {
		this.techDocQueryPort = techDocQueryPort;
	}

	@Override
	public void hydrate(PublicationCommand command, Publication publication) {
		List<TechDoc> techDocs = new ArrayList<>();
		if(command.hasTechDocs())
			command.getTechDocs().forEach(td -> techDocs.add(techDocQueryPort.getTechDocById(td)));
		publication.setTechDocs(techDocs);
	}
}
