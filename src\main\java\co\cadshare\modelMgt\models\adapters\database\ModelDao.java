/*
 * Copyright 2016 Bell.
 */
package co.cadshare.modelMgt.models.adapters.database;

import java.sql.Timestamp;
import java.sql.Types;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import co.cadshare.persistence.PartModelLinkDao;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.namedparam.BeanPropertySqlParameterSource;
import org.springframework.jdbc.core.namedparam.MapSqlParameterSource;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.jdbc.support.GeneratedKeyHolder;
import org.springframework.jdbc.support.KeyHolder;
import org.springframework.stereotype.Repository;

import co.cadshare.modelMgt.models.core.Model;
import co.cadshare.response.CustomerModel;

@Repository
public class ModelDao {

  private static final Logger logger = LoggerFactory.getLogger(ModelDao.class);
  @Autowired
  JdbcTemplate jdbcTemplate;

  @Autowired
  NamedParameterJdbcTemplate namedParamJdbcTemplate;

  @Autowired
  PartModelLinkDao partModelLinkDao;

  private final static String CREATE_MODEL = "INSERT INTO model (modelName, modelDescription, machineId, createdDate, autodeskStatus, autodeskProgress, autodeskUrn, createdByUserId, filename, originalfilename, fileType, translateType, is2d, isSetupComplete) "
      + "VALUES( :modelName, :modelDescription, :machineId, :createdDate, :autodeskStatus, :autodeskProgress, :autodeskUrn, :createdByUserId, :filename, :originalFilename, :fileType, :translateType, :is2d, false)";

  private static final String UPDATE_MODEL = "UPDATE model "
          .concat("SET modelName = :modelName, ")
          .concat("modelDescription = :modelDescription, ")
          .concat("machineId = :machineId, ")
          .concat("createdDate = :createdDate, ")
          .concat("autodeskStatus = :autodeskStatus, ")
          .concat("autodeskProgress = :autodeskProgress, ")
          .concat("autodeskUrn = :autodeskUrn, ")
          .concat("topLevelAssembly = :topLevelAssembly, ")
          .concat("fileType = :fileType, ")
          .concat("is2d = :is2d, ")
          .concat("leafNodes = to_jsonb(:leafNodes::JSONB), ")
          .concat("isSetupComplete = :isSetupComplete, ")
          .concat("retries = :retries, ")
          .concat("reasonforfailure = :reasonForFailure ")
          .concat("WHERE modelId = :modelId");

  private static final String GET_MODEL_FOR_MODEL_ID = "Select m.*, u.firstname as createdByUserFirstName, u.lastname as createdByUserLastName from model m INNER JOIN users u ON u.userid = m.createdByUserId WHERE modelId = :modelId AND m.archived = FALSE ";

  private static final String GET_MODELS_FOR_MACHINE_ID = "SELECT DISTINCT(m.modelid) as modelid, m.*, u.firstname as createdByUserFirstName, u.lastname as createdByUserLastName, "
          + " (SELECT COUNT(*) FROM partlinkedmodelmap pl WHERE pl.modelid = m.modelid AND pl.archived=FALSE) as linkedPartCount"
          + " FROM model m INNER JOIN users u ON u.userid = m.createdByUserId " + "%s" + " AND m.archived = FALSE ";// interceptor

  private final static String AUTODESK_STATUS_FILTER = "LEFT JOIN viewable view ON view.modelid = m.modelid AND view.type = 'CORE' "
          + "LEFT JOIN pdfpage pdf ON pdf.modelid = m.modelid WHERE machineId = :machineId AND m.autodeskstatus LIKE :autoDeskStatus ";
  private final static String NO_STATUS_FILTER = "WHERE machineId = :machineId ";
  private final static String SETUP_COMPLETE_FILTER = "AND m.isSetupComplete = :setupComplete ";


  private static final String GET_MODELS_FOR_MANUFACTURER_ID = "SELECT m.*, u.firstname as createdByUserFirstName, u.lastname AS createdByUserLastName, machine.name AS machineName FROM model m "
      + "INNER JOIN users u ON u.userid = m.createdByUserId " + "INNER JOIN machine ON machine.machineid = m.machineId " + "INNER JOIN range ON range.rangeid = machine.rangeid "
      + "INNER JOIN manufacturer ON manufacturer.manufacturerid = range.manufacturerid " + "%s" + // interceptor
      "WHERE manufacturer.manufacturerId = :manufacturerId AND (m.autodeskstatus = 'PROPERTIES_PROCESSED' OR m.autodeskstatus = 'PROPERTIES_PROCESSED_WITH_WARNINGS') AND m.archived = false";

  private static final String UPLOADED_SINCE_FILTER = "AND m.createdDate > :uploadedSinceDateTimestamp ";

  private final static String GET_MODELS_FOR_STATUS = "SELECT * FROM model where autodeskStatus IN (:autodeskStatusList) AND model.archived = FALSE";

  private static final String DELETE_MODEL = "UPDATE model SET archived = TRUE where modelid = :modelid";

  public int createModel(Model model) {

    Timestamp now = new Timestamp(Calendar.getInstance().getTime().getTime());
    model.setCreatedDate(now);

    KeyHolder keyHolder = new GeneratedKeyHolder();

    BeanPropertySqlParameterSource namedParameters = new BeanPropertySqlParameterSource(model);
    namedParameters.registerSqlType("autodeskStatus", Types.VARCHAR);
    namedParameters.registerSqlType("fileType", Types.VARCHAR);
    namedParameters.registerSqlType("translateType", Types.VARCHAR);

    int result = namedParamJdbcTemplate.update(CREATE_MODEL, namedParameters, keyHolder, new String[] { "modelid" });
    return keyHolder.getKey().intValue();
  }

  public void updateModel(Model model) {

    BeanPropertySqlParameterSource namedParameters = new BeanPropertySqlParameterSource(model);
    namedParameters.registerSqlType("autodeskStatus", Types.VARCHAR);
    namedParameters.registerSqlType("fileType", Types.VARCHAR);
    namedParameters.registerSqlType("translateType", Types.VARCHAR);
    int result = namedParamJdbcTemplate.update(UPDATE_MODEL, namedParameters);

    logger.info("Updated model. result from database [{}]", result);
  }

  public List<Model> getModels(List<String> statusList) {

    MapSqlParameterSource parameters = new MapSqlParameterSource();
    parameters.addValue("autodeskStatusList", statusList);

    List<Model> modelList = namedParamJdbcTemplate.query(GET_MODELS_FOR_STATUS, parameters, new BeanPropertyRowMapper<Model>(Model.class));

    logger.info("getModels with status [{}] executed. Number of models returned [{}]", statusList, modelList.size());
    return modelList;
  }

  private static final String UPDATE_MODEL_NAME = "UPDATE model SET modelName = :modelName, modelDescription = :modelDescription, modifiedDate = :modifiedDate, modifiedByUserId = :modifiedByUserId where modelId = :modelId";

  public void updateModelName(Model model) {

    BeanPropertySqlParameterSource namedParameters = new BeanPropertySqlParameterSource(model);
    namedParamJdbcTemplate.update(UPDATE_MODEL_NAME, namedParameters);
  }

  public List<Model> getModelsForManufacturer(int manufacturerId, Timestamp uploadedSinceDateTimestamp, Integer resultSize) {
    MapSqlParameterSource mapSqlParameterSource = new MapSqlParameterSource();
    mapSqlParameterSource.addValue("manufacturerId", manufacturerId);

    String filter = "";
    if (uploadedSinceDateTimestamp != null) {
      mapSqlParameterSource.addValue("uploadedSinceDateTimestamp", uploadedSinceDateTimestamp);
      mapSqlParameterSource.registerSqlType("uploadedSinceDateTimestamp", Types.TIMESTAMP);
      filter = filter.concat(UPLOADED_SINCE_FILTER);
    }
    String sql = GET_MODELS_FOR_MANUFACTURER_ID;
    if (resultSize != null) {
      sql = GET_MODELS_FOR_MANUFACTURER_ID.concat(" LIMIT " + resultSize);
    }
    List<Model> modelList = namedParamJdbcTemplate.query(String.format(sql, filter), mapSqlParameterSource, new BeanPropertyRowMapper<Model>(Model.class));

    return modelList;
  }

  private final static String GET_MODELS_BY_MANUAL = "SELECT mod.*, man.manualid, man.manualname, mac.name AS machineName "
          + "FROM model mod INNER JOIN manualmodelmap mmm ON mmm.modelid = mod.modelid "
          + "INNER JOIN manual man ON mmm.manualid = man.manualid "
          + "INNER JOIN machine mac ON mod.machineid = mac.machineid "
          + "WHERE man.manualid = :manualid "
          + "AND mod.archived = FALSE AND mmm.archived = FALSE ";

  private final static String GET_ALL_MODELS = "SELECT * FROM model WHERE archived = FALSE ORDER BY (CASE WHEN modelname ~ '^[0-9]+' THEN SUBSTRING(modelname FROM '^[0-9]+')::BIGINT ELSE 0 END) ASC, modelname";

  private final static String GET_MODELS_TOP_SNAPSHOT_IMG = "SELECT sd.imgurl FROM statedetail sd "
          + "INNER JOIN viewable v ON v.id = sd.viewableid "
          + "INNER JOIN model mod ON mod.modelid = v.modelid "
          + "WHERE sd.stateid = 'ROOT' "
          + "AND v.type = 'CORE' "
          + "AND mod.modelid = :modelid ";

  public List<CustomerModel> getModelsByManualId(int manualId) {
    MapSqlParameterSource parameters = new MapSqlParameterSource();
    parameters.addValue("manualid", manualId);

	List<CustomerModel> modelList = namedParamJdbcTemplate.query(GET_MODELS_BY_MANUAL,
			parameters,
			new BeanPropertyRowMapper<>(CustomerModel.class));

	modelList = modelList.stream()
			.sorted(Comparator.comparing((CustomerModel model) -> {
				String modelName = model.getModelName();
				Matcher matcher = Pattern.compile("^(\\d+)").matcher(modelName);
				if (matcher.find())
					return Integer.parseInt(matcher.group());
				return Integer.MAX_VALUE;
			}).thenComparing(CustomerModel::getModelName))
			.collect(Collectors.toList());

	for(CustomerModel model : modelList) {
      MapSqlParameterSource parameters2 = new MapSqlParameterSource();
      parameters2.addValue("modelid", model.getModelId());

      try {
        if (!model.getIs2d()) {
          String imgUrl = namedParamJdbcTemplate.queryForObject(GET_MODELS_TOP_SNAPSHOT_IMG, parameters2, String.class);
          model.setThumbnailUrl(imgUrl);
        }
      } catch (Exception ex) {
        logger.debug("No Thumbnail URL found for model id [{}]", model.getModelId());
      }
    }


    logger.info("getModels with for manualid [{}] executed. Number of models returned [{}]", manualId, modelList.size());
    return modelList;
  }

  private final static String COUNT_PARTS_IN_MODEL = "SELECT COUNT(*) FROM part WHERE modelid = :modelId";

  public int getPartCountForModel(int modelId) {

    Map<String, Object> parameters = new HashMap<String, Object>();
    parameters.put("modelId", modelId);

    int count = namedParamJdbcTemplate.queryForObject(COUNT_PARTS_IN_MODEL, parameters, Integer.class);

    return count;
  }

  private final static String GET_MANUFACTURER_FOR_PART_ID = "SELECT r.manufacturerid FROM range r " +
          "INNER JOIN machine mac ON mac.rangeid = r.rangeid " +
          "INNER JOIN model mod ON mod.machineid = mac.machineid " +
          "WHERE mod.modelid = :modelId";
  public int getManufacturerIdForModelId(int modelId) {
    Map<String, Object> parameters = new HashMap<String, Object>();
    parameters.put("modelId", modelId);

    return namedParamJdbcTemplate.queryForObject(GET_MANUFACTURER_FOR_PART_ID, parameters, Integer.class);

  }
}
