package co.cadshare.modelMgt.models.core;

import co.cadshare.modelMgt.models.core.Model;
import co.cadshare.modelMgt.models.core.ModelManifestWrapper;
import co.cadshare.shared.core.user.User;
import co.cadshare.shared.core.manufacturer.ManufacturerSettings;
import co.cadshare.modelMgt.models.core.model.AutodeskStatus;
import co.cadshare.modelMgt.models.core.model.TranslateType;
import com.flextrade.jfixture.rules.FixtureRule;
import org.junit.Before;
import org.junit.Rule;
import org.junit.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import static co.cadshare.modelMgt.models.core.model.AutodeskStatus.*;
import static org.mockito.Mockito.*;

import static org.junit.Assert.*;
import static org.junit.Assert.assertNotEquals;

public class ModelTest {

    @Mock
    private ModelManifestWrapper mockManifest;


    private Model out;

    @Rule
    public FixtureRule fr = FixtureRule.initFixtures();

    @Before
    public void Before() {
        MockitoAnnotations.initMocks(this);
        out = new Model();
        out.setRetries(0);
    }

    @Test
    public void CheckModelIsNotNullTest() {
        assertTrue(out != null);
    }

    @Test
    public void RetryConnectionIssuesIncrement() {
        out.retriedConnectionIssue("reason");
        assertEquals(1, out.getRetries());
        assertNotEquals(out.getAutodeskStatus(), AutodeskStatus.FAILED);
    }

    @Test
    public void RetryConnectionIssuesIncrementFails() {
        out.setRetries(14);
        out.retriedConnectionIssue("reason");
        assertEquals(15, out.getRetries());
        assertEquals(out.getAutodeskStatus(), AutodeskStatus.FAILED);
    }

    @Test
    public void RetryPartsMissingIncrement() {
        out.retriedPartsMissing("reason");
        assertEquals(1, out.getRetries());
        assertNotEquals(out.getAutodeskStatus(), AutodeskStatus.FAILED);
    }

    @Test
    public void RetryPartsMissingIncrementFails() {
        out.setRetries(99);
        out.retriedPartsMissing("reason");
        assertEquals(100, out.getRetries());
        assertEquals(out.getAutodeskStatus(), AutodeskStatus.FAILED);
    }

    @Test
    public void ConfigureForCreationSetFilename() {
        out.setOriginalFilename("filename.txt");
        out.configureForCreation(new ManufacturerSettings());
        assertTrue(out.getFilename().length() > out.getOriginalFilename().length());
    }

    @Test
    public void ConfigureForCreationSVFType() {
        ManufacturerSettings settings = new ManufacturerSettings(){{
            setSvf2Enabled(false);
        }};
        out.configureForCreation(settings);
        assertEquals(TranslateType.SVF, out.getTranslateType());
    }

    @Test
    public void ConfigureForCreationSVF2Type() {
        ManufacturerSettings settings = new ManufacturerSettings(){{
            setSvf2Enabled(true);
        }};
        User user = new User() {{
            setManufacturerId(1);
        }};
        out.configureForCreation(settings);
        assertEquals(TranslateType.SVF2, out.getTranslateType());
    }

    @Test
    public void ConfigureForCreationSetUpIncomplete() {

        out.configureForCreation(new ManufacturerSettings());
        assertFalse(out.getIsSetupComplete());
    }

    @Test
    public void PropertiesProcessedSoFinishedTranslating() {
        out.setAutodeskStatus(PROPERTIES_PROCESSED);
        assertFalse(out.notFinishedTranslating());
    }

    @Test
    public void PropertiesProcessedWithWarningsSoFinishedTranslating() {
        out.setAutodeskStatus(PROPERTIES_PROCESSED_WITH_WARNINGS);
        assertFalse(out.notFinishedTranslating());
    }

    @Test
    public void PropertiesProcessingInProgressSoNotFinishedTranslating() {
        out.setAutodeskStatus(AutodeskStatus.INPROGRESS);
        assertTrue(out.notFinishedTranslating());
    }

    @Test
    public void partsProcessedWithWarnings() {
        when(mockManifest.hasWarnings()).thenReturn(true);
        out.setAutodeskStatus(PENDING);
        out.partsProcessed(mockManifest);
        assertEquals(PROPERTIES_PROCESSED_WITH_WARNINGS, out.getAutodeskStatus());
    }

    @Test
    public void partsProcessedWithoutWarnings() {
        when(mockManifest.hasWarnings()).thenReturn(false);
        out.setAutodeskStatus(PENDING);
        out.partsProcessed(mockManifest);
        assertEquals(PROPERTIES_PROCESSED, out.getAutodeskStatus());
    }

    @Test
    public void partsProcessedWithoutWarningsAs2d() {
        when(mockManifest.hasWarnings()).thenThrow(NullPointerException.class);
        out.setAutodeskStatus(PENDING);
        out.partsProcessed(mockManifest);
        assertEquals(PROPERTIES_PROCESSED, out.getAutodeskStatus());
    }

    @Test
    public void notReadyForPartsUploadAsStillPending() {
        out.setAutodeskStatus(PENDING);
        assertFalse(out.readyForPartsUpload());
        assertFalse(out.partsUpdateNotRequired());
    }

    @Test
    public void readyForPartsUpload() {
        out.setAutodeskStatus(UPLOADED);
        out.setIs2d(false);
        assertTrue(out.readyForPartsUpload());
        assertFalse(out.partsUpdateNotRequired());
    }

    @Test
    public void partsUpdateNotRequired() {
        out.setAutodeskStatus(UPLOADED);
        out.setIs2d(true);
        assertFalse(out.readyForPartsUpload());
        assertTrue(out.partsUpdateNotRequired());
    }
}