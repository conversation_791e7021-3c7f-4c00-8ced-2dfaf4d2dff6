package co.cadshare.controller;

import co.cadshare.aspects.access.languages.CanUseLanguage;
import co.cadshare.shared.core.Language;
import co.cadshare.masterParts.core.extensions.kit.Kit;

import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import co.cadshare.shared.core.user.User;
import co.cadshare.services.KitService;
import co.cadshare.services.PermissionsService;
import org.springframework.web.bind.annotation.RequestParam;

@Controller
@RequestMapping("/kit")
public class KitController {

  private static final Logger logger = LoggerFactory.getLogger(KitController.class);

  private final KitService kitService;
  private final PermissionsService permissionsService;

  @Autowired
  public KitController (KitService kitService,
                        PermissionsService permissionsService) {
    this.kitService = kitService;
    this.permissionsService = permissionsService;
  }

  @RequestMapping(value = "model/{modelId}", method = RequestMethod.GET)
  @CanUseLanguage
  public HttpEntity<List<Kit>> getKitsByModel(@AuthenticationPrincipal User currentUser,
                                              @PathVariable int modelId,
                                              @RequestParam(value = "language", required = false) Language language) {

    logger.info("ACCESS: User [{}], getKitsByModel, modelId [{}]", currentUser.accessDetails(), modelId);
    List<Kit> kit = kitService.getKitsByModelId(modelId);
    return new ResponseEntity<>(kit, HttpStatus.OK);
  }

  @RequestMapping(value = "/{kitId}", method = RequestMethod.GET)
  @CanUseLanguage
  public HttpEntity<Kit> getKitById(@AuthenticationPrincipal User currentUser,
                                    @PathVariable int kitId,
                                    @RequestParam(value = "language", required = false) Language language) {

    logger.info("ACCESS: User [{}], getKitById, kitId [{}]", currentUser.accessDetails(), kitId);
    Kit kit = kitService.getKitById(kitId);
    return new ResponseEntity<>(kit, HttpStatus.OK);
  }

  @RequestMapping(value = "part/{partId}", method = RequestMethod.GET)
  public HttpEntity<List<Kit>> getKitsByPart(@AuthenticationPrincipal User currentUser, @PathVariable int partId) throws Exception {

    logger.info("ACCESS: User [{}], getKitsByPart, partId [{}]", currentUser.accessDetails(), partId);
    List<Kit> kit = kitService.getKitsByPartId(partId);
    return new ResponseEntity<>(kit, HttpStatus.OK);
  }

  @RequestMapping(value = "model/{modelId}", method = RequestMethod.POST, consumes = "application/json")
  public HttpEntity<Integer> createKit(@AuthenticationPrincipal User currentUser, @PathVariable int modelId, @RequestBody Kit kit) {

    logger.info("ACCESS: User [{}], createKit, for model Id [{}]", currentUser.accessDetails(), modelId);

    try {
      userHasPermissionsForModel(currentUser.getUserId(), modelId);
    } catch (Exception ex) {
      return new ResponseEntity<>(null, HttpStatus.FORBIDDEN);
    }

    kit.setCreatedByUserId(currentUser.getUserId());
    int kitId = kitService.createKit(modelId, kit);

    logger.info("Kit with id [{}] created", kitId);
    return new ResponseEntity<>(kitId, HttpStatus.OK);
  }

  @RequestMapping(method = RequestMethod.PUT, consumes = "application/json")
  public HttpEntity<Boolean> updateKit(@AuthenticationPrincipal User currentUser, @RequestBody Kit kit) {

    logger.info("ACCESS: User [{}], updateKit, kitId [{}]", currentUser.accessDetails(), kit.getId());

    kit.setModifiedByUserId(currentUser.getUserId());
    if (kit.getId() != 0) {
      kitService.updateKit(kit);
    }
    return new ResponseEntity<>(HttpStatus.OK);
  }

  @RequestMapping(value = "/{kitId}", method = RequestMethod.DELETE)
  public HttpEntity<Boolean> deleteKit(@AuthenticationPrincipal User currentUser, @PathVariable int kitId) {

    logger.info("ACCESS: User [{}], deleteKit, kitId [{}]", currentUser.accessDetails(), kitId);
    Boolean isDeleted = kitService.deleteKit(kitId);

    return new ResponseEntity<Boolean>(isDeleted, HttpStatus.OK);
  }


  private boolean userHasPermissionsForModel(int userId, int modelId) throws Exception {
    boolean permissions = permissionsService.userHasPermissionsToViewModel(userId, modelId);
    return permissions;
  }
}
