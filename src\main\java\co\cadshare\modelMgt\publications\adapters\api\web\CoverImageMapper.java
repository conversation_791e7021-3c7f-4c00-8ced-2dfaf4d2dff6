package co.cadshare.modelMgt.publications.adapters.api.web;

import co.cadshare.modelMgt.publications.core.CoverImage;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

@Mapper
public interface CoverImageMapper {

    public final CoverImageMapper Instance = Mappers.getMapper(CoverImageMapper.class);

    @Mapping(source = "imageUrl", target = "locationUrl")
    CoverImage dtoToCoverImage(CoverImageDto dto);

}
