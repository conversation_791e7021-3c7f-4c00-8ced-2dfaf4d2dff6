package co.cadshare.models.core.processor.fileproperties;

import co.cadshare.models.core.MetadataObjectExtended;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.LinkedHashMap;
import java.util.List;

@Component
public class DescriptionPropertyProcessor extends AbstractPropertiesProcessor implements FilePropertiesProcessor, InitializingBean {

    @Value("#{'${properties.processor.description.synonyms}'.split(',')}")
    List<String> descriptionSynonyms;

    @Override
    public void setProperties(LinkedHashMap properties, MetadataObjectExtended object) {
        object.setDescription(getPropertyValue(properties));
    }

    @Override
    public List<String> getSynonyms() {
        return descriptionSynonyms;
    }

    @Override
    public void setSynonyms(List<String> synonyms) {
        descriptionSynonyms = synonyms;
    }
}
