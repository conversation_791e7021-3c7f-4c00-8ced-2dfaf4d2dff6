package co.cadshare.models.adapters.ext.autodesk;

import co.cadshare.models.adapters.database.ModelDao;
import co.cadshare.models.boundary.UploadModelService;
import co.cadshare.models.core.*;
import co.cadshare.models.core.model.AutodeskStatus;
import co.cadshare.models.core.model.FileType;
import co.cadshare.models.core.model.TranslateType;
import co.cadshare.models.core.processor.PropertiesProcessor;
import co.cadshare.shared.adapters.aws.s3.S3StorageClient;
import co.cadshare.shared.adapters.database.manufacturer.ManufacturerConfigDao;
import co.cadshare.shared.adapters.ext.autodesk.CustomOauth2TwoLegged;
import co.cadshare.shared.adapters.ext.autodesk.assetsretriever.CadshareAutodeskAssetsRetrieverClient;
import co.cadshare.models.boundary.AutodeskPort;
import co.cadshare.domainmodel.autodesk.AutodeskResource;
import co.cadshare.shared.core.manufacturer.ManufacturerConfig;
import co.cadshare.utils.HttpRequestHelper;
import com.amazonaws.services.apigateway.model.NotFoundException;
import com.autodesk.client.ApiResponse;
import com.autodesk.client.api.DerivativesApi;
import com.autodesk.client.auth.Credentials;
import com.autodesk.client.model.*;
import lombok.val;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import java.sql.*;
import java.util.*;
import java.util.stream.Collectors;


@Service
public class AutodeskGateway implements AutodeskPort {
    private static final Logger logger = LoggerFactory.getLogger(AutodeskGateway.class);
    public static final String FETCH_DERIVATIVE_DOWNLOAD_URL = "https://developer.api.autodesk.com/modelderivative/v2/designdata/%1$s/manifest/%2$s/signedcookies";
    public static final String COOKIE_EXCESS = " Path=/; Domain=cdn.derivative.autodesk.com; HTTPOnly";
    public static final String PROPERTIES_DB_QUERY = "SELECT sub.entity_id, ov.value\n" +
            "FROM _objects_eav oe,\n" +
            "_objects_attr oa,\n" +
            "_objects_val ov\n" +
            "INNER JOIN\n" +
            "(SELECT suboe.entity_id, subov.value\n" +
            "FROM _objects_eav suboe,\n" +
            "_objects_attr suboa,\n" +
            "_objects_val subov\n" +
            "WHERE suboa.id = suboe.attribute_id\n" +
            "AND suboe.value_id = subov.id\n" +
            "AND suboa.name = 'Active Configuration'\n" +
            ") AS sub ON\n" +
            "oe.entity_id = sub.value\n" +
            "WHERE oa.id = oe.attribute_id\n" +
            "AND oe.value_id = ov.id\n" +
            "AND oa.name = '%s'";
    public static final String UNABLE_TO_UPLOAD_PROPERTIES_DB_TO_S3 = "Unable to upload properties db to s3: ";
    public static final String UNABLE_TO_READ_FROM_PROPERTIES_DB = "Unable to read from properties db: ";
    private static final String UNABLE_TO_DOWNLOAD_PROPERTIES_DB = "Unable to download properties db";
    private static final String DOWNLOADING_PROPERTIES_DB = "Downloading properties db..";
    private static final String PROPERTIES_DB_SUCCESSFULLY_DOWNLOADED = "Properties db successfully downloaded";
    private final CustomOauth2TwoLegged oAuth2TwoLegged;
    private final CustomOauth2TwoLegged oAuth2TwoLeggedWebClient;

    private final DerivativesApi derivativesApi;
    private final CadshareAutodeskAssetsRetrieverClient client;
    private final HttpRequestHelper httpRequestHelper;
    private final S3StorageClient s3StorageClient;
    private final ManufacturerConfigDao manufacturerConfigDao;
    private final ModelDao modelDao;
    private final PropertiesProcessor propertiesProcessor;

    @Autowired
    public AutodeskGateway(CustomOauth2TwoLegged oAuth2TwoLegged,
                           DerivativesApi derivativesApi,
                           CustomOauth2TwoLegged oAuth2TwoLeggedWebClient,
                           CadshareAutodeskAssetsRetrieverClient client,
                           HttpRequestHelper httpRequestHelper,
                           S3StorageClient s3StorageClient,
                           ManufacturerConfigDao manufacturerConfigDao,
                           ModelDao modelDao,
                           PropertiesProcessor propertiesProcessor) {
        this.oAuth2TwoLegged = oAuth2TwoLegged;
        this.oAuth2TwoLeggedWebClient = oAuth2TwoLeggedWebClient;
        this.derivativesApi = derivativesApi;
        this.client = client;
        this.httpRequestHelper = httpRequestHelper;
        this.s3StorageClient = s3StorageClient;
        this.manufacturerConfigDao = manufacturerConfigDao;
        this.modelDao = modelDao;
        this.propertiesProcessor = propertiesProcessor;
    }

    public Credentials getAutodeskToken() throws Exception {

        if (!oAuth2TwoLegged.isAccessTokenExpired()) {
            oAuth2TwoLegged.authenticate();
        }
        return oAuth2TwoLegged.getCredentials();
    }

    // Creates Autodesk token for frontend application use
    public Credentials getAutodeskTokenForWebClient() throws Exception {

        if (!oAuth2TwoLeggedWebClient.isAccessTokenExpired()) {
            oAuth2TwoLeggedWebClient.authenticate();
        }
        return oAuth2TwoLeggedWebClient.getCredentials();
    }

    public List<AutodeskResource> getAllAutodeskModelResources(Model model) {
        String urn = model.getAutodeskUrn().replace("=", "");

        List<String> urls = client.getAutodeskAssets(urn);

        if (urls != null) {
            return urls.stream()
                    .map(AutodeskResource::new)
                    .collect(Collectors.toList());
        }
        return Collections.emptyList();
    }

    public void translateModel(Model model, TranslateType translateType)  throws Exception {
        JobPayload jobPayload = new JobPayload();
        JobPayloadInput input = new JobPayloadInput();
        if (model.getFileType().equals(FileType.ARCHIVE)) {
            input.setRootFilename(model.getTopLevelAssembly());
            input.setCompressedUrn(true);
        }

        input.setUrn(model.getAutodeskUrn());

        JobPayloadOutput output = new JobPayloadOutput();
        JobPayloadItem outputItem = new JobPayloadItem();
        output.setFormats(Arrays.asList(outputItem));

        JobPayloadItem.TypeEnum payload = JobPayloadItem.TypeEnum.SVF;
        if (TranslateType.SVF2.equals(translateType))
            payload = JobPayloadItem.TypeEnum.SVF2;
        outputItem.setType(payload);
        List<JobPayloadItem.ViewsEnum> views = new ArrayList<>();
        views.add(JobPayloadItem.ViewsEnum._2D);
        views.add(JobPayloadItem.ViewsEnum._3D);
        outputItem.setViews(views);

        jobPayload.setInput(input);
        jobPayload.setOutput(output);

        try {
            derivativesApi.translate(jobPayload,
                    true,
                    oAuth2TwoLegged,
                    oAuth2TwoLegged.getCredentials());
            model.setAutodeskStatus(AutodeskStatus.PENDING);

        } catch (Exception e) {
            model.setAutodeskStatus(AutodeskStatus.FAILED);
        }
    }

    public ModelManifestWrapper getManifest(Model model) throws Exception {
        Manifest manifest = derivativesApi.getManifest(model.getAutodeskUrn(),
                        null,
                        oAuth2TwoLegged,
                        oAuth2TwoLegged.getCredentials())
                .getData();
        return new ModelManifestWrapper(manifest);
    }

    public ParsedMetadata extractMetadata(Model model, ModelManifestWrapper manifest) throws Exception {
        MetadataWrapper metadata = getMetadata(model);
        MetadataParser metadataParser = new MetadataParser(model, metadata, propertiesProcessor);
        if (metadataParser.isGuidProvidedByMetadata()) {
            String guid = metadataParser.retrieveGuidFromMetadata();
            MetadataWrapper guidMetaData = getMetadataForGuid(model, guid);
            if (metadataParser.guidMetadataIsParseable(guidMetaData)) {
                MetadataWrapper propertiesForGuid = getPropertiesForGuid(model, guid);
                ManufacturerConfig config = getManufacturerConfig(model);
                ParsedMetadata parsedMetadata = metadataParser.parseMetadataForGuid(propertiesForGuid,
                        guidMetaData,
                        config,
                        guid);
               HashMap<Integer, List<String>> extendedProperties = getPropertiesDb(model.getAutodeskUrn(),
                        manifest.getEncodedPropertiesDbUrn(),
                        config);
                parsedMetadata.overrideExtendedProperties(config, extendedProperties);
                return parsedMetadata;
            }
        }
        return null;
    }

    private MetadataWrapper getMetadata(Model model) throws Exception {
        ApiResponse<Metadata> metadata = derivativesApi.getMetadata(model.getAutodeskUrn(),
                null,
                oAuth2TwoLegged,
                oAuth2TwoLegged.getCredentials());
        return new MetadataWrapper(metadata, model);
    }

    private MetadataWrapper getPropertiesForGuid(Model model, String guid) throws Exception {
        ApiResponse<Metadata> metadata = derivativesApi.getModelviewProperties(model.getAutodeskUrn(),
                guid,
                "gzip",
                oAuth2TwoLegged,
                oAuth2TwoLegged.getCredentials());
        return new MetadataWrapper(metadata, model);
    }

    private MetadataWrapper getMetadataForGuid(Model model, String guid) throws Exception {
        ApiResponse<Metadata> metadata = derivativesApi.getModelviewMetadata(model.getAutodeskUrn(),
                guid,
                "gzip",
                null,
                oAuth2TwoLegged,
                oAuth2TwoLegged.getCredentials());
        return new MetadataWrapper(metadata, model);
    }

    private ManufacturerConfig getManufacturerConfig(Model model) {
        int manufacturerId = modelDao.getManufacturerIdForModelId(model.getModelId());
        return manufacturerConfigDao.getManufacturerConfigById(manufacturerId);
    }

    private HashMap<Integer, List<String>> getPropertiesDb(String urn, String derivativeUrn, ManufacturerConfig config) throws Exception {
        HashMap<Integer, List<String>> extendedPropertiesForOverride = new HashMap<>();
        if(config.isUseExtendedMetadata()) {
            ResponseEntity<Map> response = getDerivativeDownloadUrl(urn, derivativeUrn);
            ResponseEntity<Resource> fileResponse = downloadPropertiesDb(response);

            String downloadedPropertiesDbLocation = "";
            try {
                downloadedPropertiesDbLocation = s3StorageClient.uploadPropertiesDb(fileResponse.getBody().getInputStream(), config.getManufacturerId());
                pullExtendedMetadataFromDb(downloadedPropertiesDbLocation, config, extendedPropertiesForOverride);
            } catch (NotFoundException notFoundException) {
                throw new Exception(UNABLE_TO_UPLOAD_PROPERTIES_DB_TO_S3, notFoundException);
            } catch (SQLException ex) {
                throw new Exception(UNABLE_TO_READ_FROM_PROPERTIES_DB, ex);
            } finally {
                if(!downloadedPropertiesDbLocation.isEmpty())
                    s3StorageClient.deleteS3File(downloadedPropertiesDbLocation);
            }
        }
        return extendedPropertiesForOverride;
    }

    private static void pullExtendedMetadataFromDb(String downloadedPropertiesDbLocation,
                                                   ManufacturerConfig config,
                                                   HashMap<Integer, List<String>> extendedPropertiesForOverride) throws SQLException {
        try (Connection connection = DriverManager.getConnection("jdbc:sqlite::resource:".concat(downloadedPropertiesDbLocation))) {
            Statement statement = connection.createStatement();
            String[] extendedMetadataSynonyms = config.getExtendedMetadataSynonyms().split(",");
            int columnIndex = 0;
            List<String> valuesList = null;
            for (String ems : extendedMetadataSynonyms) {
                ResultSet rs = statement.executeQuery(String.format(PROPERTIES_DB_QUERY, ems));
                do {
                    if (extendedPropertiesForOverride.get(rs.getInt("entity_id")) == null) {
                        valuesList = new ArrayList<>();
                        for (int i = 0; i < columnIndex; i++)
                            valuesList.add(null);
                    }
                    else {
                        valuesList = extendedPropertiesForOverride.get(rs.getInt("entity_id"));
                        extendedPropertiesForOverride.remove(rs.getInt("entity_id"));
                    }
                    valuesList.add(rs.getString("value"));
                    extendedPropertiesForOverride.put(rs.getInt("entity_id"), valuesList);
                } while (rs.next());
                columnIndex++;
            }
        }
    }

    private ResponseEntity<Resource> downloadPropertiesDb(ResponseEntity<Map> response) throws Exception {
        logger.info(DOWNLOADING_PROPERTIES_DB);
        Map<String, String> requestHeaders = getHeadersForPropertiesDbDownload(response.getHeaders());
        Map<String, Object> result = response.getBody();
        assert result != null;
        String dbUrl = result.get("url").toString();
        ResponseEntity<Resource> fileResponse = httpRequestHelper.getObject(dbUrl, requestHeaders, byte[].class);
        if(fileResponse.getStatusCodeValue() == 200) {
            logger.info(PROPERTIES_DB_SUCCESSFULLY_DOWNLOADED);
            return fileResponse;
        }
        else {
            logger.error(UNABLE_TO_DOWNLOAD_PROPERTIES_DB);
            throw new Exception(UNABLE_TO_DOWNLOAD_PROPERTIES_DB);
        }
    }

    private Map<String, String> getHeadersForPropertiesDbDownload(HttpHeaders responseHeaders) {
            List<String> cookies = responseHeaders.get("Set-Cookie");
        String cookiesHeader = "";
        if (cookies != null) {
            StringBuilder cookiesBuilder = new StringBuilder();
            for (String cookie : cookies)
                cookiesBuilder.append(cookie.replaceAll(COOKIE_EXCESS, ""));
            cookiesHeader = cookiesBuilder.toString();
        }
        Map<String, String> requestHeaders = getAuthorisationRequestHeader();
        requestHeaders.put("Cookie", cookiesHeader);
        return requestHeaders;
    }

    private ResponseEntity<Map> getDerivativeDownloadUrl(String urn, String derivativeUrn) throws Exception {
        urn = urn.substring(0, urn.length()-2);
        String getUrl = String.format(FETCH_DERIVATIVE_DOWNLOAD_URL, urn, derivativeUrn);
        Map<String, String> requestHeaders = getAuthorisationRequestHeader();
        return httpRequestHelper.get(getUrl, requestHeaders, Map.class);
    }

    private Map<String, String> getAuthorisationRequestHeader() {
        Map<String, String> requestHeaders = new HashMap<>();
        requestHeaders.put("Authorization", "Bearer " + oAuth2TwoLegged.getCredentials().getAccessToken());
        return requestHeaders;
    }
}
