/*
 * Copyright 2016 Bell.
 */
package co.cadshare.inventory.adapters.api.web;

import co.cadshare.inventory.core.BomUpload;
import co.cadshare.inventory.boundary.PartsLoadService;
import co.cadshare.inventory.core.BomUploadConfiguration;
import co.cadshare.shared.core.user.User;
import com.google.gson.JsonObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

@Slf4j
@Controller
@RequestMapping("/model")
public class PartsLoadController {

  private final PartsLoadService partsLoadService;
  private final BomCsvFileProcessor csvProcessor;

  public PartsLoadController(PartsLoadService partsLoadService,
                             BomCsvFileProcessor csvProcessor) {
    this.partsLoadService = partsLoadService;
    this.csvProcessor = csvProcessor;
  }

  @GetMapping(value = "/{modelId}/partDetailDownload",  produces = MediaType.APPLICATION_OCTET_STREAM_VALUE)
  @PreAuthorize("@permissionsDao.hasAccessToModel(#currentUser.getUserId(), #modelId)")
  public ResponseEntity<String> partDetailDownload(@AuthenticationPrincipal User currentUser, @PathVariable Integer modelId) {
    log.info("ACCESS: User [{}], partDetailDownload, model id [{}]", currentUser.accessDetails(), modelId);
    try {
      partsLoadService.downloadPartDetailsForModel(currentUser, modelId);
    } catch (Exception e) {
      JsonObject response = new JsonObject();
      response.addProperty("message", "Error processing file: " + e.getMessage());
      return new ResponseEntity<>(response.toString(), HttpStatus.BAD_REQUEST);
    }
    return new ResponseEntity<>(HttpStatus.ACCEPTED);
  }

  @PostMapping(value = "/uploadDetails/headers", consumes = "application/json")
  @PreAuthorize("hasRole('ROLE_MANUFACTURER')")
  public HttpEntity<List<String>> CSVHeaderDetails(@RequestBody String file) throws Exception {

    log.info("Extracting header for Details CSV upload");
    List<String> headers = partsLoadService.getHeaderMappings(file);

    return new ResponseEntity<>(headers, HttpStatus.OK);
  }

  @PostMapping(value = "/{modelId}/uploadDetailsFile")
  @PreAuthorize("hasRole('ROLE_MANUFACTURER')")
  public ResponseEntity<String> PartDetailUpload(@AuthenticationPrincipal User currentUser,
                                                 @PathVariable int modelId, @RequestParam("file") MultipartFile file,
                                             @RequestParam(value = "nameColumn", required = false) Integer nameColumn ,
                                             @RequestParam(value = "objectIdColumn", required = false) Integer objectIdColumn ,
                                             @RequestParam(value = "numberColumn", required = false) Integer numberColumn,
                                             @RequestParam(value = "updateNumber", required = false, defaultValue = "false") Boolean updateNumber,
                                             @RequestParam(value = "descriptionColumn", required = false) Integer descriptionColumn,
                                             @RequestParam(value = "updateDescription", required = false, defaultValue = "false") Boolean updateDescription) throws Exception {
    log.info("Uploading part details from CSV upload");
    log.info("Received file [{}]", file.getName());
    try {
      BomUpload bomUpload = csvProcessor.convert(file);
      BomUploadConfiguration bomUploadConfiguration = new BomUploadConfiguration(nameColumn,
              objectIdColumn,
              descriptionColumn,
              numberColumn,
              updateDescription,
              updateNumber);
      partsLoadService.uploadPartDetailsForModel(modelId, bomUploadConfiguration, bomUpload, currentUser);
    } catch (Exception e) {
      JsonObject response = new JsonObject();
      response.addProperty("message", "Error processing file: " + e.getMessage());
      return new ResponseEntity<>(response.toString(), HttpStatus.BAD_REQUEST);
    }
    return new ResponseEntity<>( HttpStatus.ACCEPTED);

  }

}
