package co.cadshare.models.boundary;

import co.cadshare.aspects.logging.ServiceLoggingAspect;
import co.cadshare.shared.core.user.User;
import co.cadshare.models.core.Model;
import com.flextrade.jfixture.annotations.Fixture;
import com.flextrade.jfixture.rules.FixtureRule;
import org.junit.Before;
import org.junit.Rule;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.MockitoAnnotations;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import static org.mockito.Mockito.*;

@EnableAspectJAutoProxy
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(classes = {UpdateModelService.class, ServiceLoggingAspect.class})
public class UpdateModelServiceTest {

    @MockBean
    private ModelCommandPort commandPort;
    @MockBean
    private ModelQueryPort queryPort;
    @Autowired
    private UpdateModelService out;
    private User user;
    @Fixture
    private Model model;
    @Fixture
    private Model errorModel;

    @Rule
    public FixtureRule fr = FixtureRule.initFixtures();

    @Before
    public void Before() {
        MockitoAnnotations.initMocks(this);
        user = new User() {{
            setUserId(1);
            setFirstName("firstName");
            setLastName("lastName");
        }};
    }

    @Test
    public void UpdateModelSuccess() throws Exception {
        doNothing().when(commandPort).update(user, model);
        out.update(user, model);
    }

    @Test(expected = RuntimeException.class)
    public void UpdateModelFailureException() throws Exception {
        doThrow(new RuntimeException("terrible")).when(commandPort).update(user, errorModel);
        out.update(user, errorModel);
    }
}
