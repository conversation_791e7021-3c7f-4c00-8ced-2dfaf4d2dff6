package co.cadshare.media.adapters.database;

import co.cadshare.media.core.Image;
import co.cadshare.shared.adapters.database.ImageEntity;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface ImageEntityMapper {
    ImageEntityMapper Instance = Mappers.getMapper(ImageEntityMapper.class);

    Image entityToCore(ImageEntity entity);

    ImageEntity coreToEntity(Image core);

    List<Image> entitiesToCores(List<ImageEntity> entities);

	List<ImageEntity> coresToEntities(List<Image> entities);
}
