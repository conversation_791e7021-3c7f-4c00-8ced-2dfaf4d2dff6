<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                   http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.0.xsd
">
    <!-- 1 - Caterpillar Dealer -->
    <!-- 2 - Caterpillar DealerPlus -->
    <!-- 3 - JCB Dealer -->
    <!-- 4 - Liebherr Dealer -->
    <changeSet author="AndyB" id="1.11-integration-test-data-create-purchaser-settings-1">
        <sql stripComments="true">
            INSERT INTO public.manufacturersubentitysettings (manufacturersubentityid, previewstocklevelenabled, previewpricingenabled, partsearchenabled, hideisolateenabled, childcurrencyid, defaultcurrencyid, taxpayments)
            VALUES (
            1,          --manufacturersubentityid,
            false,      --previewstocklevelenabled,
            false,      --previewpricingenabled,
            true,       --partsearchenabled,
            false,      --hideisolateenabled,
            null,       --childcurrencyid,
            1,          --defaultcurrencyid,
            false);     --taxpayments
        </sql>
    </changeSet>
    <changeSet author="AndyB" id="1.11-integration-test-data-create-purchaser-settings-2">
        <sql stripComments="true">
            INSERT INTO public.manufacturersubentitysettings (manufacturersubentityid, previewstocklevelenabled, previewpricingenabled, partsearchenabled, hideisolateenabled, childcurrencyid, defaultcurrencyid, taxpayments)
            VALUES (
            2,          --manufacturersubentityid,
            false,      --previewstocklevelenabled,
            false,      --previewpricingenabled,
            true,       --partsearchenabled,
            false,      --hideisolateenabled,
            null,       --childcurrencyid,
            1,          --defaultcurrencyid,
            false);       --taxpayments
        </sql>
    </changeSet>
    <changeSet author="AndyB" id="1.11-integration-test-data-create-purchaser-settings-3">
        <sql stripComments="true">
            INSERT INTO public.manufacturersubentitysettings (manufacturersubentityid, previewstocklevelenabled, previewpricingenabled, partsearchenabled, hideisolateenabled, childcurrencyid, defaultcurrencyid, taxpayments)
            VALUES (
            3,          --manufacturersubentityid,
            false,      --previewstocklevelenabled,
            false,      --previewpricingenabled,
            true,       --partsearchenabled,
            false,      --hideisolateenabled,
            null,       --childcurrencyid,
            1,          --defaultcurrencyid,
            false);     --taxpayments
        </sql>
    </changeSet>

    <changeSet author="AndyB" id="1.11-integration-test-data-create-purchaser-settings-4">
        <sql stripComments="true">
            INSERT INTO public.manufacturersubentitysettings (manufacturersubentityid, previewstocklevelenabled, previewpricingenabled, partsearchenabled, hideisolateenabled, childcurrencyid, defaultcurrencyid, taxpayments)
            VALUES (
            4,          --manufacturersubentityid,
            false,      --previewstocklevelenabled,
            false,      --previewpricingenabled,
            true,       --partsearchenabled,
            false,      --hideisolateenabled,
            null,       --childcurrencyid,
            1,          --defaultcurrencyid,
            false);     --taxpayments
        </sql>
    </changeSet>

    <changeSet author="AndyB" id="1.11-integration-test-data-create-purchaser-settings-5">
        <sql stripComments="true">
            INSERT INTO public.manufacturersubentitysettings (manufacturersubentityid, previewstocklevelenabled, previewpricingenabled, partsearchenabled, hideisolateenabled, childcurrencyid, defaultcurrencyid, taxpayments)
            VALUES (
            5,          --manufacturersubentityid,
            false,      --previewstocklevelenabled,
            false,      --previewpricingenabled,
            true,       --partsearchenabled,
            false,      --hideisolateenabled,
            null,       --childcurrencyid,
            1,          --defaultcurrencyid,
            false);     --taxpayments
        </sql>
    </changeSet>

    <changeSet author="AndyB" id="1.11-integration-test-data-create-purchaser-settings-6">
        <sql stripComments="true">
            INSERT INTO public.manufacturersubentitysettings (manufacturersubentityid, previewstocklevelenabled, previewpricingenabled, partsearchenabled, hideisolateenabled, childcurrencyid, defaultcurrencyid, taxpayments)
            VALUES (
            6,          --manufacturersubentityid,
            false,      --previewstocklevelenabled,
            false,      --previewpricingenabled,
            true,       --partsearchenabled,
            false,      --hideisolateenabled,
            null,       --childcurrencyid,
            1,          --defaultcurrencyid,
            false);     --taxpayments
        </sql>
    </changeSet>

    <changeSet author="AndyB" id="1.11-integration-test-data-create-purchaser-settings-7">
        <sql stripComments="true">
            INSERT INTO public.manufacturersubentitysettings (manufacturersubentityid, previewstocklevelenabled, previewpricingenabled, partsearchenabled, hideisolateenabled, childcurrencyid, defaultcurrencyid, taxpayments)
            VALUES (
            7,          --manufacturersubentityid,
            false,      --previewstocklevelenabled,
            false,      --previewpricingenabled,
            true,       --partsearchenabled,
            false,      --hideisolateenabled,
            null,       --childcurrencyid,
            1,          --defaultcurrencyid,
            false);     --taxpayments
        </sql>
    </changeSet>

    <changeSet author="AndyB" id="1.11-integration-test-data-create-purchaser-settings-8">
        <sql stripComments="true">
            INSERT INTO public.manufacturersubentitysettings (manufacturersubentityid, previewstocklevelenabled, previewpricingenabled, partsearchenabled, hideisolateenabled, childcurrencyid, defaultcurrencyid, taxpayments)
            VALUES (
            8,          --manufacturersubentityid,
            false,      --previewstocklevelenabled,
            false,      --previewpricingenabled,
            true,       --partsearchenabled,
            false,      --hideisolateenabled,
            null,       --childcurrencyid,
            1,          --defaultcurrencyid,
            false);     --taxpayments
        </sql>
    </changeSet>

    <changeSet author="AndyB" id="1.11-integration-test-data-create-purchaser-settings-9">
        <sql stripComments="true">
            INSERT INTO public.manufacturersubentitysettings (manufacturersubentityid, previewstocklevelenabled, previewpricingenabled, partsearchenabled, hideisolateenabled, childcurrencyid, defaultcurrencyid, taxpayments)
            VALUES (
            9,          --manufacturersubentityid,
            false,      --previewstocklevelenabled,
            false,      --previewpricingenabled,
            true,       --partsearchenabled,
            false,      --hideisolateenabled,
            null,       --childcurrencyid,
            1,          --defaultcurrencyid,
            false);     --taxpayments
        </sql>
    </changeSet>

    <changeSet author="AndyB" id="1.11-integration-test-data-create-purchaser-settings-10">
        <sql stripComments="true">
            INSERT INTO public.manufacturersubentitysettings (manufacturersubentityid, previewstocklevelenabled, previewpricingenabled, partsearchenabled, hideisolateenabled, childcurrencyid, defaultcurrencyid, taxpayments)
            VALUES (
            10,          --manufacturersubentityid,
            false,      --previewstocklevelenabled,
            false,      --previewpricingenabled,
            true,       --partsearchenabled,
            false,      --hideisolateenabled,
            null,       --childcurrencyid,
            1,          --defaultcurrencyid,
            false);     --taxpayments
        </sql>
    </changeSet>

    <changeSet author="AndyB" id="1.11-integration-test-data-create-purchaser-settings-11">
        <sql stripComments="true">
            INSERT INTO public.manufacturersubentitysettings (manufacturersubentityid, previewstocklevelenabled, previewpricingenabled, partsearchenabled, hideisolateenabled, childcurrencyid, defaultcurrencyid, taxpayments)
            VALUES (
            11,          --manufacturersubentityid,
            false,      --previewstocklevelenabled,
            false,      --previewpricingenabled,
            true,       --partsearchenabled,
            false,      --hideisolateenabled,
            null,       --childcurrencyid,
            1,          --defaultcurrencyid,
            false);     --taxpayments
        </sql>
    </changeSet>

    <changeSet author="AndyB" id="1.11-integration-test-data-create-purchaser-settings-12">
        <sql stripComments="true">
            INSERT INTO public.manufacturersubentitysettings (manufacturersubentityid, previewstocklevelenabled, previewpricingenabled, partsearchenabled, hideisolateenabled, childcurrencyid, defaultcurrencyid, taxpayments)
            VALUES (
            12,          --manufacturersubentityid,
            false,      --previewstocklevelenabled,
            false,      --previewpricingenabled,
            true,       --partsearchenabled,
            false,      --hideisolateenabled,
            null,       --childcurrencyid,
            1,          --defaultcurrencyid,
            false);     --taxpayments
        </sql>
    </changeSet>

</databaseChangeLog>
