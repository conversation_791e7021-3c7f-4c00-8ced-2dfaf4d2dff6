package co.cadshare.domainmodel.visibility;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;

@XmlRootElement(name = "PartAvailListDetail", namespace = "http://visibility.com/")
@XmlAccessorType(XmlAccessType.FIELD)
public class PartAvailListDetail {

    @XmlElement(name = "ENTITY_CODE")
    private String ENTITY_CODE;

    @XmlElement(name = "WAREHOUSE_X")
    private String WAREHOUSE_X;

    @XmlElement(name = "PART_X")
    private String PART_X;

    @XmlElement(name = "PART_DESCRIPTION_X")
    private String PART_DESCRIPTION_X;

    @XmlElement(name = "WEIGHT_X")
    private String WEIGHT_X;

    @XmlElement(name = "WEIGHT_UOM_X")
    private String WEIGHT_UOM_X;

    @XmlElement(name = "SELLING_UOM_X")
    private String SELLING_UOM_X;

    @XmlElement(name = "QtyAvaility")
    private String QtyAvaility;

    @XmlElement(name = "PST_TAXABLE_B")
    private String PST_TAXABLE_B;

    @XmlElement(name = "FST_TAXABLE_B")
    private String FST_TAXABLE_B;

    public String getENTITY_CODE() {
        return ENTITY_CODE;
    }

    public void setENTITY_CODE(String ENTITY_CODE) {
        this.ENTITY_CODE = ENTITY_CODE;
    }

    public String getWAREHOUSE_X() {
        return WAREHOUSE_X;
    }

    public void setWAREHOUSE_X(String WAREHOUSE_X) {
        this.WAREHOUSE_X = WAREHOUSE_X;
    }

    public String getPART_X() {
        return PART_X;
    }

    public void setPART_X(String PART_X) {
        this.PART_X = PART_X;
    }

    public String getPART_DESCRIPTION_X() {
        return PART_DESCRIPTION_X;
    }

    public void setPART_DESCRIPTION_X(String PART_DESCRIPTION_X) {
        this.PART_DESCRIPTION_X = PART_DESCRIPTION_X;
    }

    public String getWEIGHT_X() {
        return WEIGHT_X;
    }

    public void setWEIGHT_X(String WEIGHT_X) {
        this.WEIGHT_X = WEIGHT_X;
    }

    public String getWEIGHT_UOM_X() {
        return WEIGHT_UOM_X;
    }

    public void setWEIGHT_UOM_X(String WEIGHT_UOM_X) {
        this.WEIGHT_UOM_X = WEIGHT_UOM_X;
    }

    public String getSELLING_UOM_X() {
        return SELLING_UOM_X;
    }

    public void setSELLING_UOM_X(String SELLING_UOM_X) {
        this.SELLING_UOM_X = SELLING_UOM_X;
    }

    public String getQtyAvaility() {
        return QtyAvaility;
    }

    public void setQtyAvaility(String qtyAvaility) {
        QtyAvaility = qtyAvaility;
    }

    public String getPST_TAXABLE_B() {
        return PST_TAXABLE_B;
    }

    public void setPST_TAXABLE_B(String PST_TAXABLE_B) {
        this.PST_TAXABLE_B = PST_TAXABLE_B;
    }

    public String getFST_TAXABLE_B() {
        return FST_TAXABLE_B;
    }

    public void setFST_TAXABLE_B(String FST_TAXABLE_B) {
        this.FST_TAXABLE_B = FST_TAXABLE_B;
    }
}


