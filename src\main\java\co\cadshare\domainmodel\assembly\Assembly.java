/*
 * Copyright 2016 Bell.
 */
package co.cadshare.domainmodel.assembly;

import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
public class Assembly {

    private int id;
    private int partId;
    private int objectId;
    private String partNumber;
    private String partDescription;

    public static Assembly createFrom(Assembly assembly) {
        return Assembly.builder()
            .objectId(assembly.getObjectId())
            .partNumber(assembly.getPartNumber())
            .partDescription(assembly.getPartDescription())
            .build();
    }

    @Builder
    public Assembly(int id, int partId, int objectId, String partNumber, String partDescription) {
        this.id = id;
        this.partId = partId;
        this.objectId = objectId;
        this.partNumber = partNumber;
        this.partDescription = partDescription;
    }
}
