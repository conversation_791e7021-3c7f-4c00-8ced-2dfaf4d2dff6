package co.cadshare.addresses.boundary;

import co.cadshare.addresses.core.Address;
import co.cadshare.addresses.core.ExternalAddress;
import co.cadshare.addresses.core.ExternalContact;

public interface AddressCommandPort {
    Integer createUserAddress(int userId, Address address);

    int updateUserAddress(int userId, Address address);

    Boolean deleteUserAddress(int userId, int addressId);

    Integer createManufacturerAddress(int manufacturerId, Address address);

	void createAddress(ExternalAddress address);

    void updateAddress(ExternalAddress address);

	ExternalContact createContact(ExternalContact contact);

	void updateContact(ExternalContact contact);
}
