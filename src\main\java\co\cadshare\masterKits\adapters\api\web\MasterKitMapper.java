package co.cadshare.masterKits.adapters.api.web;

import co.cadshare.masterKits.boundary.CreateMasterKitCommand;
import co.cadshare.masterKits.boundary.UpdateMasterKitCommand;
import co.cadshare.masterKits.core.KitMasterPart;
import co.cadshare.masterKits.core.MasterKit;
import co.cadshare.masterKits.core.MasterPartDefaultPrice;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;
import org.mapstruct.factory.Mappers;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Mapper(uses = {
            MasterPartPriceListItemMapper.class,
            TranslationMapper.class,
            PartMasterPartMapper.class
        })
public interface MasterKitMapper {

    MasterKitMapper Instance = Mappers.getMapper(MasterKitMapper.class);

    @Mapping(source="postRequestDto.parts", target="parts", qualifiedByName="mapParts")
    @Mapping(source="manufacturerId", target="manufacturerId")
    CreateMasterKitCommand postRequestDtoToMasterKitCommand(PostMasterKitRequestDto postRequestDto, int manufacturerId);

    @Mapping(source="putRequestDto.titles", target="titles", qualifiedByName="mapTitles")
    @Mapping(source="putRequestDto.price", target="price", qualifiedByName="mapPrice")
    @Mapping(source="putRequestDto.prices", target="prices", qualifiedByName="mapPrices")
    @Mapping(source="kitMasterPartId", target="masterKitId")
    @Mapping(source="manufacturerId", target="manufacturerId")
    @Mapping(source="putRequestDto.masterParts", target="masterParts", qualifiedByName="mapParts")
    UpdateMasterKitCommand putRequestDtoToMasterKitCommand(PutMasterKitRequestDto putRequestDto, int manufacturerId, int kitMasterPartId);


    @Mapping(source="kitMasterPart.partNumber", target="partNumber")
    @Mapping(source="kitMasterPart.translations", target="titles")
    GetMasterKitListItemResponseDto masterKitToGetListResponseDto(MasterKit masterKit);

    List<GetMasterKitListItemResponseDto> masterKitToGetListResponseDto(List<MasterKit> masterKits);

    @Mapping(source="kitMasterPart.id", target="kitMasterPartId")
    @Mapping(source="kitMasterPart.partNumber", target="partNumber")
    @Mapping(source="kitMasterPart.prices", target="prices")
    @Mapping(source="kitMasterPart.price", target="price", qualifiedByName="priceMapping")
    @Mapping(source="kitMasterPart.translations", target="titles")
    @Mapping(source="partMasterParts", target="parts")
    GetMasterKitResponseDto masterKitToGetResponseDto(MasterKit masterKit);

    @Named("mapPrice")
    static Float mapPrice(Number price) {
        if(price==null) return null;
        return price.floatValue();
    }

    @Named("mapPrices")
    static Map<Integer, Float> mapPrices(List<PutMasterKitPriceRequestDto> prices) {
        if(prices==null) return null;
        Map<Integer, Float> mappedPrices = new HashMap<>();
        prices.forEach(price -> mappedPrices.put(price.getId(), price.getPrice().floatValue()));
        return mappedPrices;
    }

    @Named("mapParts")
    static Map<Integer, Integer> mapParts(List<MasterKitMasterPartsRequestDto> dtos){
        Map<Integer, Integer> mapParts = new HashMap<>();
        dtos.forEach(dto-> mapParts.put(dto.getMasterPartId(), dto.getQuantity()));
        return mapParts;
    }
    @Named("mapTitles")
    static Map<Integer, String> mapTitles(List<PutMasterKitTranslationRequestDto> dtos){
        Map<Integer, String> mapTitles = new HashMap<>();
        dtos.forEach(dto-> mapTitles.put(dto.getId(), dto.getTranslation()));
        return mapTitles;
    }

    @Named("priceMapping")
    static String priceMapping(MasterPartDefaultPrice price) {
        if(price == null) return null;
        return price.getDisplayPrice();
    }
    @Named("kitMasterPartId")
    static KitMasterPart kitMasterPartId(int partId){

        return new KitMasterPart() {{setId(partId);}};
    }
}

