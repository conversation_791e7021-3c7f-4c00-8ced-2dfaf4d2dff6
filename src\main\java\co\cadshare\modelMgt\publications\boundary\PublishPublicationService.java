package co.cadshare.modelMgt.publications.boundary;

import co.cadshare.exceptions.NotFoundException;
import co.cadshare.exceptions.UnprocessableEntityException;

import co.cadshare.modelMgt.publications.core.Manual;
import co.cadshare.modelMgt.shared.core.Publication;
import co.cadshare.modelMgt.shared.boundary.PublicationQueryPort;
import co.cadshare.modelMgt.shared.boundary.ViewableQueryPort;
import co.cadshare.modelMgt.shared.core.Viewable;
import co.cadshare.shared.core.user.User;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.EmptyResultDataAccessException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
public class PublishPublicationService {

    private final ManualCommandPort manualCommand;
    private final ViewableQueryPort viewableQuery;
    private final ProductCommandPort productCommand;
	private final PublicationCommandPort publicationCommandPort;
	private final PublicationQueryPort publicationQueryPort;

    @Autowired
    public PublishPublicationService(ManualCommandPort manualCommand,
                                     ViewableQueryPort modelQuery,
                                     ProductCommandPort productCommand,
                                     PublicationCommandPort publicationCommandPort,
                                     PublicationQueryPort publicationQueryPort) {
        this.manualCommand = manualCommand;
        this.viewableQuery = modelQuery;
        this.productCommand = productCommand;
	    this.publicationCommandPort = publicationCommandPort;
	    this.publicationQueryPort = publicationQueryPort;
    }

    @Transactional
    public int createAndAutoPublishPublication(User user, int viewableId) throws Exception {
        Viewable viewable = viewableQuery.getViewable(viewableId);
        if(viewable.isReadyForPublication(user)) {
            viewable.ensureProductHasThumbnail();
            productCommand.updateProduct(user, viewable.getProduct());
            Manual manual = Manual.buildForImmediatePublication(viewable, user);
            int manualId = manualCommand.createManual(manual);
            if (manual.shouldBeAssignedToPurchasers())
                manualCommand.assignManualToManufacturerSubEntities(manualId,
                        manual.getPurchasersAssignedToRanges());
            return manualId;
        } else
            throw new UnprocessableEntityException("Viewable not ready for publication");
    }

	public void publish(User user, Integer publicationId) throws Exception {
		try {
			Publication publication = publicationQueryPort.get(publicationId);
			publication.publish();
			this.publicationCommandPort.update(user, publication);
		} catch (EmptyResultDataAccessException e) {
			throw new NotFoundException(e.getMessage());
		}
	}

	public void unpublish(User user, Integer publicationId) throws Exception {
		try {
			Publication publication = publicationQueryPort.get(publicationId);
			publication.unpublish();
			this.publicationCommandPort.update(user, publication);
		} catch (EmptyResultDataAccessException e) {
			throw new NotFoundException(e.getMessage());
		}
	}
}
