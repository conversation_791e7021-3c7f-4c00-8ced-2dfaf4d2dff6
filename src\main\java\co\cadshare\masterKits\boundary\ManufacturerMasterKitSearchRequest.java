package co.cadshare.masterKits.boundary;

import co.cadshare.masterParts.boundary.MasterSearchRequest;
import co.cadshare.shared.core.Language;
import co.cadshare.shared.core.user.User;
import lombok.Data;

@Data
public class ManufacturerMasterKitSearchRequest extends MasterSearchRequest {

    private int manufacturerId;

    public static ManufacturerMasterKitSearchRequest build(User user, String partNumber, String partDescription, Language language) {
        ManufacturerMasterKitSearchRequest request = new ManufacturerMasterKitSearchRequest();
        request.manufacturerId = user.getManufacturerId();
        build(request, partNumber, partDescription, language);
        return request;
    }
}
