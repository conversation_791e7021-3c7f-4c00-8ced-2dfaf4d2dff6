package co.cadshare.modelMgt.publications.boundary;

import co.cadshare.exceptions.NoCadshareDataFoundException;
import co.cadshare.modelMgt.shared.core.Publication;
import co.cadshare.modelMgt.shared.boundary.PublicationQueryPort;
import co.cadshare.shared.core.user.User;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.EmptyResultDataAccessException;
import org.springframework.stereotype.Service;

@Service
public class UpdatePublicationService {

	private final PublicationQueryPort publicationQueryPort;
    private final PublicationCommandPort publicationCommandPort;
	private final PublicationHydrator publicationHydrator;

    @Autowired
    public UpdatePublicationService(PublicationQueryPort publicationQueryPort,
                                    PublicationCommandPort publicationCommandPort,
                                    PublicationHydrator publicationHydrator) {
	    this.publicationQueryPort = publicationQueryPort;
	    this.publicationCommandPort = publicationCommandPort;
	    this.publicationHydrator = publicationHydrator;
    }

    public void update(User user, UpdatePublicationCommand updateCommand) throws Exception {

	    try {
		    updateCommand.validate();
			Publication publication = publicationQueryPort.get(updateCommand.getId());
		    publication = updateCommand.build(publication);
		    publicationHydrator.hydrate(updateCommand, publication);
		    this.publicationCommandPort.update(user, publication);
	    } catch (EmptyResultDataAccessException e) {
		    throw new NoCadshareDataFoundException(e.getMessage());
	    }
    }
}
