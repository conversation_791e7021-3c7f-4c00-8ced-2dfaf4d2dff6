Feature: Manage Viewables

  Scenario Outline: Get All Viewables
    Given I am a Manufacturer with email address <emailAddress>
    Then I can view all Viewables belonging to my Manufacturer
    Examples:
      | emailAddress                 |
      | <EMAIL> |

  Scenario Outline: Search Viewables by name
    Given I am a Manufacturer with email address <emailAddress>
    When I search Viewables by name <viewableName>
    Then I can view <viewableCount> Viewables with matching name <viewableName> belonging to my Manufacturer
    Examples:
      | emailAddress                 | viewableName      | viewableCount |
      | <EMAIL> | Caterpillar Model | 1             |
      | <EMAIL> | Caterpillar       | 1             |
      | <EMAIL> | Made-Up Model     | 0             |

  Scenario Outline: Filter Viewables by product
    Given I am a Manufacturer with email address <emailAddress>
    When I filter Viewables by <productId>
    Then I can view <viewableCount> Viewables with matching product <productId> belonging to my Manufacturer
    Examples:
      | emailAddress                 | productId | viewableCount |
      | <EMAIL> | 1         | 1             |
      | <EMAIL> | 99        | 0             |

  @ignore
  Scenario Outline: Create new Viewable as Manufacturer
    Given I am a Manufacturer with email address <emailAddress>
    And a Viewable with name <viewableName> doesn't exist
    When I create a new Viewable named <viewableName>
    Then a Viewable with name <viewableName> exists
    Examples:
      | emailAddress                 | viewableName |
      | <EMAIL> | New Viewable |

  @ignore
  Scenario Outline: Update Viewable as Manufacturer
    Given I am a Manufacturer with email address <emailAddress>
    And a Viewable with name <oldViewableName> exists
    When I update the Viewable named <oldViewableName> to <newViewableName>
    Then a Viewable with name <newViewableName> exists
    And a Viewable with name <oldViewableName> doesn't exist
    Examples:
      | emailAddress                 | newViewableName | oldViewableName  |
      | <EMAIL> | New Viewable    | Publication for Caterpillar |

  @ignore
  Scenario Outline: Delete Viewable as Manufacturer
    Given I am a Manufacturer with email address <emailAddress>
    And I create a new Viewable named <viewableName>
    And a Viewable with name <viewableName> exists
    When I delete the Viewable named <viewableName>
    Then a Viewable with name <viewableName> doesn't exist
    Examples:
      | emailAddress                 | viewableName                 |
      | <EMAIL> | deleted Publication Category |

  @ignore
  Scenario Outline: Get Viewable as Manufacturer
    Given I am a Manufacturer with email address <emailAddress>
    And I create a new Viewable named <viewableName>
    And a Viewable with name <viewableName> exists
    Then I get the Viewable named <viewableN
    Examples:
      | emailAddress                 | viewableName                |
      | <EMAIL> | Single Publication Category |