package co.cadshare.modelMgt.models.boundary;

import co.cadshare.domainmodel.autodesk.AutodeskResource;
import co.cadshare.modelMgt.models.core.Model;
import co.cadshare.modelMgt.models.core.ModelManifestWrapper;
import co.cadshare.modelMgt.models.core.ParsedMetadata;
import co.cadshare.modelMgt.models.core.model.TranslateType;
import com.autodesk.client.auth.Credentials;

import java.util.List;

public interface AutodeskPort {
    Credentials getAutodeskToken() throws Exception;
    Credentials getAutodeskTokenForWebClient() throws Exception;
    List<AutodeskResource> getAllAutodeskModelResources(Model model);
    void translateModel(Model model, TranslateType translateType) throws Exception;
    ModelManifestWrapper getManifest(Model model) throws Exception;
    ParsedMetadata extractMetadata(Model model, ModelManifestWrapper manifest) throws Exception;
}
