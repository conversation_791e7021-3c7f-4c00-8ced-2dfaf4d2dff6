{"info": {"_postman_id": "d2ea660b-e238-413b-b863-820dda1a826e", "name": "API - apiservices", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "300252"}, "item": [{"name": "Health Check", "event": [{"listen": "test", "script": {"exec": ["var responseData = JSON.parse(responseBody);", "postman.setEnvironmentVariable(\"access-token\", responseData.access_token);"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Basic Y2xpZW50YXBwOnNlY3JldA=="}, {"key": "", "value": "", "type": "text", "disabled": true}], "url": {"raw": "localhost:8080/actuator/health", "host": ["localhost"], "port": "8080", "path": ["actuator", "health"]}}, "response": []}, {"name": "LogIn", "event": [{"listen": "test", "script": {"exec": ["var responseData = JSON.parse(responseBody);", "postman.setEnvironmentVariable(\"access-token\", responseData.access_token);"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Basic Y2xpZW50YXBwOnNlY3JldA=="}, {"key": "", "value": "", "type": "text", "disabled": true}], "body": {"mode": "formdata", "formdata": [{"key": "username", "value": "MmdApiManufacturer", "type": "text"}, {"key": "password", "value": "{{password}}", "type": "text"}, {"key": "grant_type", "value": "password", "type": "text"}]}, "url": {"raw": "localhost:8080/oauth/token", "host": ["localhost"], "port": "8080", "path": ["o<PERSON>h", "token"]}}, "response": []}, {"name": "Orders - getAll", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{access-token}} "}], "url": {"raw": "localhost:8080/order?status=SUBMITTED&status=PREPARING", "host": ["localhost"], "port": "8080", "path": ["order"], "query": [{"key": "status", "value": "SUBMITTED"}, {"key": "status", "value": "PREPARING"}]}}, "response": []}, {"name": "Orders - Update Status", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{access-token}} "}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "\"SHIPPED\""}, "url": {"raw": "cadshare-production-api.eu-west-1.elasticbeanstalk.com/order/{{orderId}}/status", "host": ["cadshare-production-api", "eu-west-1", "elasticbeanstalk", "com"], "path": ["order", "{{orderId}}", "status"]}}, "response": []}]}