
# @name cadshareAuthResponse

POST {{CADSHARE_URL}}/oauth/token
Content-Type: application/x-www-form-urlencoded
Authorization: Basic Y2xpZW50YXBwOnNlY3JldA==

username={{DEALER_USER_ID}}
&password={{STANDARD_PWD}}
&grant_type=password


###

@cadshareToken = Bearer {{cadshareAuthResponse.response.body.access_token}}

GET {{CADSHARE_URL}}/purchaser/451/master-part/C116961/supersession-history?language=EN
Content-Type: application/json
Authorization: {{cadshareToken}}
Site-Url: {{CADSHARE_URL}}
