package co.cadshare.modelMgt.publications.adapters.database;

import co.cadshare.modelMgt.publications.core.Snapshot;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface PublicationsSnapshotEntityViewMapper {

    PublicationsSnapshotEntityViewMapper Instance = Mappers.getMapper(PublicationsSnapshotEntityViewMapper.class);

    Snapshot entityToCore(PublicationsSnapshotEntityView entityView);
}
