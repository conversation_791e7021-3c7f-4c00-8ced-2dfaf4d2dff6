
# @name cadshareAuthResponse

POST {{CADSHARE_URL}}/oauth/token
Content-Type: application/x-www-form-urlencoded
Authorization: Basic Y2xpZW50YXBwOnNlY3JldA==

username={{MANUFACTURER_USER_ID}}
&password={{STANDARD_PWD}}
&grant_type=password


###

@cadshareToken = Bearer {{cadshareAuthResponse.response.body.access_token}}

POST {{CADSHARE_URL}}/manufacturers/{{MANUFACTURER_ID}}/publications/create-and-publish
Content-Type: application/json
Authorization: {{cadshareToken}}


{
    "viewableId": 560
}