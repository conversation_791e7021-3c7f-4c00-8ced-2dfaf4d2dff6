package co.cadshare.masterParts.adapters.api.web;

import co.cadshare.aspects.access.languages.CanUseLanguage;
import co.cadshare.shared.core.Language;
import co.cadshare.shared.core.user.User;
import co.cadshare.masterParts.boundary.SearchMasterPartService;
import co.cadshare.masterParts.boundary.MasterPartSearchRequest;
import co.cadshare.masterParts.boundary.MasterPartSearchResult;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.*;

@RestController
public class PurchaserMasterPartSearchController {

	private final SearchMasterPartService searchMasterPartService;

	@Autowired
	public PurchaserMasterPartSearchController(SearchMasterPartService searchMasterPartService) {
		this.searchMasterPartService = searchMasterPartService;
	}

	private static final Logger logger = LoggerFactory.getLogger(PurchaserMasterPartSearchController.class);

	//CALLED BY BOTH CUSTOMER AND MANUFACTURER USERS
	@PreAuthorize("(hasRole('ROLE_MANUFACTURER') and hasRole('Parts')) or "+
			" (hasRole('ROLE_MANUFACTURER_SUB_ENTITY_DEALER') or " +
			" hasRole('ROLE_MANUFACTURER_SUB_ENTITY_CUSTOMER') or " +
			" hasRole('ROLE_MANUFACTURER_SUB_ENTITY_DEALER_PLUS') and hasRole('PartSearch'))")
	@PostMapping("/manufacturer-sub-entities/{manufacturer-sub-entity-id}/master-parts/search")
	@CanUseLanguage
	public ResponseEntity<MasterPartSearchResult> searchMasterPartsForManufacturerSubEntity(@AuthenticationPrincipal User currentUser,
	                                                                                        @PathVariable("manufacturer-sub-entity-id") int subEntityId,
	                                                                                        @RequestBody PostPurchaserSearchMasterPartsDto dto,
	                                                                                        @RequestParam(value = "language") Language language) {

		if (dto.isSearchStringEmptyOrNull()) {
			logger.error("Part search failed no PartNumber or Description to search");
			return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
		}

		MasterPartSearchRequest searchRequest = MasterPartSearchRequest.build(dto.getPartNumber(),
				dto.getPartDescription(),
				dto.isExactMatch(),
				currentUser.findLanguage(language));

		MasterPartSearchResult resultPage = this.searchMasterPartService.findForPurchaser(currentUser,
				searchRequest,
				subEntityId,
				dto.getOnBehalfOfUserId());
		return new ResponseEntity<>(resultPage, HttpStatus.OK);
	}

	@PreAuthorize("hasRole('ROLE_MANUFACTURER_SUB_ENTITY_DEALER_PLUS') and hasRole('PartSearch')")
	@PostMapping("/purchasers/{purchaser-id}/master-parts-for-dealer-plus-prices/search")
	@CanUseLanguage
	public ResponseEntity<MasterPartSearchResult> searchMasterPartsForDealerPlusPrices(@AuthenticationPrincipal User currentUser,
	                                                                                   @PathVariable("purchaser-id") int purchaserId,
	                                                                                   @RequestBody PostPurchaserSearchMasterPartsDto dto,
	                                                                                   @RequestParam(value = "language") Language language) {

		if (dto.isSearchStringEmptyOrNull()) {
			logger.error("Part search failed no PartNumber or Description to search");
			return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
		}

		MasterPartSearchRequest searchRequest = MasterPartSearchRequest.build(dto.getPartNumber(),
				dto.getPartDescription(),
				dto.isExactMatch(),
				currentUser.findLanguage(language));

		MasterPartSearchResult resultPage = this.searchMasterPartService.findForDealerPlusPrices(currentUser,
				searchRequest,
				purchaserId);
		return new ResponseEntity<>(resultPage, HttpStatus.OK);
	}
}
