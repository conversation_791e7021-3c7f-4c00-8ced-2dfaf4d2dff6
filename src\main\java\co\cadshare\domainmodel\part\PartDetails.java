/*
 * Copyright 2016 Bell.
 */
package co.cadshare.domainmodel.part;

import co.cadshare.inventory.core.BomUploadConfiguration;
import lombok.Builder;
import lombok.Data;

import java.sql.Timestamp;
import java.util.Calendar;

@Data
public class PartDetails {
  private final Integer objectId;
  private String fileName;
  private String partDescription;
  private String partNumber;
  private Timestamp modifiedDate;
  private int modelId;
  private BomUploadConfiguration updatePartDetailsUpload;
  private int fileNameParamIndex;
  private int objectIdParamIndex;
  private int partDescriptionParamIndex;
  private int partNumberParamIndex;
  private int modifiedDateParamIndex;
  private int modelIdParamIndex;
  private boolean overwritePartNumber;
  private boolean overwritePartDescription;

  @Builder
  public PartDetails(String fileName,
                     Integer objectId,
                     String partDescription,
                     String partNumber,
                     BomUploadConfiguration updatePartDetailsUpload,
                     int modelId) {
    this.fileName = fileName;
    this.objectId = objectId;
    this.partDescription = partDescription;
    this.partNumber = partNumber;
    this.modifiedDate = new Timestamp(Calendar.getInstance().getTime().getTime());
    this.modelId = modelId;
    this.configure(updatePartDetailsUpload);
  }

  private void configure(BomUploadConfiguration updatePartDetailsUpload) {
    this.partDescriptionParamIndex = 1;
    this.setOverwritePartDescription(updatePartDetailsUpload.isUpdateDescription());
    this.setOverwritePartNumber(updatePartDetailsUpload.isUpdateNumber() && this.partNumber != null);
    if(this.overwritePartNumber) {
      this.partNumberParamIndex = 2;
      this.modifiedDateParamIndex = 3;
      this.modelIdParamIndex = 4;
      this.fileNameParamIndex = 5;
      this.objectIdParamIndex = 6;
    } else {
      this.modifiedDateParamIndex = 2;
      this.modelIdParamIndex = 3;
      this.fileNameParamIndex = 4;
      this.objectIdParamIndex = 5;
    }
  }
}
