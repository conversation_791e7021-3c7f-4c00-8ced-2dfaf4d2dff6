package co.cadshare.masterKits.adapters.database;

import co.cadshare.masterParts.core.MasterPartType;
import com.querydsl.jpa.impl.JPAQueryFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import javax.persistence.EntityManager;
import java.util.List;

@Repository
public class MkMasterPartComplexQueryRepo {

    private JPAQueryFactory queryFactory;

    @Autowired
    public MkMasterPartComplexQueryRepo(EntityManager entityManager) {
        this.queryFactory = new JPAQueryFactory(entityManager);
    }

    public List<MkMasterPartEntity> getMasterPartsForKit(List<Integer> ids) {
        QMkMasterPartEntity masterPart = QMkMasterPartEntity.mkMasterPartEntity;
        return queryFactory.selectFrom(masterPart)
                .where(masterPart.id.in(ids).and(masterPart.type.eq(MasterPartType.PART)))
                .fetch();
    }

}