package co.cadshare.modelMgt.publicationCategories.adapters.api.web;

import co.cadshare.aspects.access.roles.CanAccessManufacturer;
import co.cadshare.aspects.access.roles.IsManufacturer;
import co.cadshare.modelMgt.publicationCategories.boundary.*;
import co.cadshare.modelMgt.publicationCategories.core.PublicationCategory;
import co.cadshare.shared.core.user.User;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Description;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * REST API for managing Publication Categories
 */
@RestController
@RequestMapping("/manufacturers/{manufacturer-id}/publication-categories")
public class ManufacturerPublicationCategoriesController {

    private final CreatePublicationCategoryService createService;
    private final UpdatePublicationCategoryService updateService;
    private final DeletePublicationCategoryService deleteService;
    private final GetPublicationCategoryService getService;
	private final AssignPublicationCategoryService assignService;

    @Autowired
    public ManufacturerPublicationCategoriesController(CreatePublicationCategoryService createService,
                                                       UpdatePublicationCategoryService updateService,
                                                       DeletePublicationCategoryService deleteService,
                                                       GetPublicationCategoryService getService,
                                                       AssignPublicationCategoryService assignService){
        this.createService = createService;
        this.updateService = updateService;
        this.deleteService = deleteService;
        this.getService = getService;
	    this.assignService = assignService;
    }

    @PostMapping(consumes = "application/json", produces = "application/json")
    @CanAccessManufacturer
    @ResponseStatus(HttpStatus.OK)
    @Operation(summary = "Create a PublicationCategory")
    public ResponseEntity<PostPublicationCategoryResponseDto> postPublicationCategory(@PathVariable("manufacturer-id") int manufacturerId,
                                                                @AuthenticationPrincipal User currentUser,
                                                                @RequestBody PostPublicationCategoryRequestDto postPublicationCategory) {
        PublicationCategory publicationCategory = PublicationCategoryMapper.Instance.toPublicationCategory(postPublicationCategory);
        publicationCategory.setManufacturerId(manufacturerId);
        Integer createdId = this.createService.create(currentUser, publicationCategory);
        PostPublicationCategoryResponseDto response = new PostPublicationCategoryResponseDto() {{setId(createdId);}};
        return new ResponseEntity<>(response, HttpStatus.OK);
    }

    @PutMapping(path = "/{publication-category-id}", consumes = "application/json")
    @CanAccessManufacturer
    @ResponseStatus(HttpStatus.OK)
    @Operation(summary = "Maintain the details of a PublicationCategory")
    public ResponseEntity<Void> putPublicationCategory(@PathVariable("manufacturer-id") int manufacturerId,
                                         @PathVariable("publication-category-id") Integer publicationCategoryId,
                                         @AuthenticationPrincipal User currentUser,
                                         @RequestBody PutPublicationCategoryRequestDto putPublicationCategory) throws Exception {

        PublicationCategory publicationCategory = PublicationCategoryMapper.Instance.toPublicationCategory(putPublicationCategory);
        publicationCategory.setManufacturerId(manufacturerId);
        publicationCategory.setId(publicationCategoryId);
        this.updateService.update(currentUser, publicationCategory);
        return new ResponseEntity<>(HttpStatus.OK);
    }

    @DeleteMapping(path = "/{publication-category-id}")
    @CanAccessManufacturer
    @ResponseStatus(HttpStatus.OK)
    @Operation(summary = "Delete a PublicationCategory")
    public ResponseEntity<Void> deletePublicationCategory(@PathVariable("manufacturer-id") int manufacturerId,
                                            @PathVariable("publication-category-id") Integer publicationCategoryId,
                                            @AuthenticationPrincipal User currentUser) throws Exception {

        this.deleteService.delete(currentUser, publicationCategoryId);
        return new ResponseEntity<>(HttpStatus.OK);
    }

    @GetMapping(path = "/{publication-category-id}", produces = "application/json")
    @CanAccessManufacturer
    @ResponseStatus(HttpStatus.OK)
    @Operation(summary = "Get the details of a specific PublicationCategory")
    public ResponseEntity<GetPublicationCategoryResponseDto> getPublicationCategory(@PathVariable("manufacturer-id") int manufacturerId,
                                            @PathVariable("publication-category-id") Integer publicationCategoryId,
                                            @AuthenticationPrincipal User currentUser) {

        PublicationCategory publicationCategory = this.getService.get(publicationCategoryId);
        GetPublicationCategoryResponseDto getResponseDto = PublicationCategoryMapper.Instance.toGetResponseDto(publicationCategory);
        return new ResponseEntity<>(getResponseDto, HttpStatus.OK);
    }

    @GetMapping(produces = "application/json")
    @CanAccessManufacturer
    @ResponseStatus(HttpStatus.OK)
    @Operation(summary = "Get the summary details of the list of all PublicationCategories belonging to the Manufacturer")
    public ResponseEntity<GetPublicationCategoryListResponseDto> getPublicationCategories(@PathVariable("manufacturer-id") int manufacturerId,
                                                                          @AuthenticationPrincipal User currentUser) {

        List<PublicationCategory> publicationCategories = this.getService.getPublicationCategoriesForManufacturer(manufacturerId);
        List<GetPublicationCategoryListItemResponseDto> publicationCategoryResponses =
                PublicationCategoryMapper.Instance.toGetListResponseDto(publicationCategories);
        GetPublicationCategoryListResponseDto getListResponseDto = new GetPublicationCategoryListResponseDto(){{
            setPublicationCategories(publicationCategoryResponses);
        }};
        return new ResponseEntity<>(getListResponseDto, HttpStatus.OK);
    }

	@IsManufacturer
	@CanAccessManufacturer
	@PostMapping("/assign-to-purchaser")
	@Description("Assign publication categories to a purchaser")
	public HttpEntity<Void> assignPublicationCategories(@AuthenticationPrincipal User currentUser,
	                                                    @PathVariable("manufacturer-id") int manufacturerId,
	                                                    @RequestBody PostPublicationCategoriesDto dto) throws Exception {

		AssignPublicationCategoriesCommand command = AssignPublicationCategoriesMapper.Instance.dtoToCommand(dto);
		assignService.assignPublicationCategoriesToPurchaser(currentUser, command);
		return new ResponseEntity<>(HttpStatus.OK);
	}
}
