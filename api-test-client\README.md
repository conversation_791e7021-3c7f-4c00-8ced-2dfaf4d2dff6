# API Specs + Clients



## Getting started

1. Install [Rest Client VS Code Extension](https://github.com/Huachao/vscode-restclient)

1. Create a .env file in the same folder as the http files, and include the secret/sensitive env variables required to run the requests. This means there should be (at least 3 .env folders for secrets for Autodesk, Visibility & CADshare)

1. Open the http file, and set the correct environment (`ctrl + alt + e`)

1. If required, create separate .env files (e.g. env.local or env.prod) to hold sensitive variables for each environment. Then rename the relevant file to .env when running against that environment, as well as switching the environment (see step above for setting correct env)