/*
 * Copyright 2016 Bell.
 */
package co.cadshare.productRanges.adapters.database;

import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import co.cadshare.shared.core.manufacturer.subentity.ManufacturerSubEntity;
import co.cadshare.shared.core.user.User;
import co.cadshare.persistence.ManufacturerSubEntityDao;
import co.cadshare.persistence.dealer.DealerManualDao;
import co.cadshare.publications.boundary.ManualQueryPort;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.jdbc.core.BatchPreparedStatementSetter;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.namedparam.BeanPropertySqlParameterSource;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.jdbc.support.GeneratedKeyHolder;
import org.springframework.jdbc.support.KeyHolder;
import org.springframework.stereotype.Repository;

import co.cadshare.domainmodel.range.Range;

/**
 * TODO(dallanmc) Description of class.
 */
@Repository
public class RangeDao {

    private static final Logger logger = LoggerFactory.getLogger(RangeDao.class);
    private static final String ASSIGN_RANGES_TO_MANUFACTURER_SUB_ENTITY = "INSERT INTO ManufacturerSubEntityRangeMap (manufacturersubentityid, rangeid, createddate, createdbyuserid) VALUES (?, ?, ?, ?)";

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Autowired
    private ManualQueryPort manualDao;

    @Autowired
    private DealerManualDao dealerManualDao;

    @Autowired
    private ManufacturerSubEntityDao manufacturerSubEntityDao;

    @Autowired
    private NamedParameterJdbcTemplate namedParamJdbcTemplate;

    private final static String CREATE_RANGE = "INSERT INTO range (name, description, manufacturerId, createdDate) "
            + "VALUES( :name, :description, :manufacturerId, :createdDate)";

    private final static String GET_RANGES_FOR_MANUFACTURER_ID = "SELECT * FROM range WHERE manufacturerId = :manufacturerId";

    public int createRange(Range range) {

        Timestamp now = new Timestamp(Calendar.getInstance().getTime().getTime());
        range.setCreatedDate(now);

        KeyHolder keyHolder = new GeneratedKeyHolder();

        BeanPropertySqlParameterSource namedParameters = new BeanPropertySqlParameterSource(range);
        int result = namedParamJdbcTemplate.update(CREATE_RANGE, namedParameters, keyHolder, new String[]{"rangeid"});
        return keyHolder.getKey().intValue();
    }

    public List<Range> getRangesForManufacturer(int manufacturerId) {
        Map<String, Object> parameters = new HashMap<String, Object>();
        parameters.put("manufacturerId", manufacturerId);

        List<Range> rangeList = (List<Range>) namedParamJdbcTemplate.query(GET_RANGES_FOR_MANUFACTURER_ID,
                parameters, new BeanPropertyRowMapper<Range>(Range.class));

        return rangeList;
    }

    private final static String GET_RANGE_FOR_MACHINE_ID = "SELECT r.* FROM range r INNER JOIN machine mac ON mac.rangeid = r.rangeid WHERE mac.machineid = :machineId";

    public Range getRangeForMachineId(int machineId) {
        Map<String, Object> parameters = new HashMap<String, Object>();
        parameters.put("machineId", machineId);

        Range range = namedParamJdbcTemplate.queryForObject(GET_RANGE_FOR_MACHINE_ID,
                parameters, new BeanPropertyRowMapper<Range>(Range.class));

        return range;
    }

    public void assignRangesManufacturerSubEntity(ArrayList<Integer> rangeList, int manufacturerSubEntityId, int currentUserId) throws Exception {

        Timestamp now = new Timestamp(Calendar.getInstance().getTime().getTime());
        try {
            int[] results = jdbcTemplate.batchUpdate(ASSIGN_RANGES_TO_MANUFACTURER_SUB_ENTITY, new BatchPreparedStatementSetter() {

                @Override
                public void setValues(PreparedStatement ps, int i) throws SQLException {
                    int rangeId = rangeList.get(i);
                    ps.setInt(1, manufacturerSubEntityId);
                    ps.setInt(2, rangeId);
                    ps.setTimestamp(3, now);
                    ps.setInt(4, currentUserId);
                }

                @Override
                public int getBatchSize() {
                    return rangeList.size();
                }
            });

            logger.info("Result of db call for assignRangesManufacturerSubEntity: [{}]", results);
        } catch (DuplicateKeyException e) {
            throw new Exception("Range already assigned");
        }

    }

    private final static String CHECK_IF_RANGE_NAME_IN_USE = "SELECT COUNT(*) FROM range WHERE manufacturerId = :manufacturerId AND LOWER(name) = LOWER(:name)";

    public boolean rangeNameAlreadyExistsForManufacturer(String name, int manufacturerId) {

        Map<String, Object> parameters = new HashMap<String, Object>();
        parameters.put("manufacturerId", manufacturerId);
        parameters.put("name", name);

        Integer count = namedParamJdbcTemplate.queryForObject(CHECK_IF_RANGE_NAME_IN_USE, parameters, Integer.class);

        boolean exists = false;
        if (count > 0) {
            exists = true;
        }

        return exists;
    }

    private static final String GET_ASSIGNED_RANGE_IDS_FROM_MANUFACTURER_SUB_ENTITY_RANGE_MAP = "SELECT DISTINCT(rangeid) "
            + "FROM manufacturersubentityrangemap  "
            + "WHERE manufacturersubentityid = :manufacturerSubEntityId ";

    public List<Integer> getAssignedRangeIdsForManufacturerSubEntity(int manufacturerSubEntityId) {
        Map<String, Object> parameters = new HashMap<String, Object>();
        parameters.put("manufacturerSubEntityId", manufacturerSubEntityId);

        return namedParamJdbcTemplate.queryForList(GET_ASSIGNED_RANGE_IDS_FROM_MANUFACTURER_SUB_ENTITY_RANGE_MAP,
                parameters, Integer.class);
    }
    public boolean updateAssignedRangesToManufacturerSubEntity(int manufacturerSubEntityId, ArrayList<Integer> assignedRangeIds, User currentUser) throws Exception {
        //If dealer plus unassign manuals to their customers also
        ManufacturerSubEntity subEntity = manufacturerSubEntityDao.getManufacturerSubEntity(manufacturerSubEntityId);
        if (subEntity.getManufacturerSubEntityType() == ManufacturerSubEntity.ManufacturerSubEntityType.DEALER_PLUS) {
            List<Integer> currentRanges = getAssignedRangeIdsForManufacturerSubEntity(manufacturerSubEntityId);
            List<Integer> deletedRangeIds = new ArrayList<>();
            currentRanges.forEach(range -> {
                if (!assignedRangeIds.contains(range)) {
                    deletedRangeIds.add(range);
                }
            });

            //If ranges have been removed check which manuals are to be unassigned
            if (deletedRangeIds != null && deletedRangeIds.size() > 0) {
                //Get all manuals linked to deleted ranges
                List<Integer> rangeManualIds = dealerManualDao.getManualIdsforRangeIds(deletedRangeIds);
                List<Integer> manualIdsToBeUnassigned;
                if  (rangeManualIds != null && rangeManualIds.size() > 0) {
                    //Get all manuals assigned through publication to remove from range list prior to deletion
                    List<Integer> assignedManualIds = manualDao.getAssignedManualIdsForManufacturerSubEntity(manufacturerSubEntityId);

                    List<Integer> toBeUnassigned = new ArrayList<>();
                    rangeManualIds.forEach(manualId -> {
                        if (!assignedManualIds.contains(manualId)) {
                            //If manual is not also assigned via publication add to list to be removed
                            toBeUnassigned.add(manualId);
                        }
                    });
                    manualIdsToBeUnassigned = toBeUnassigned;
                } else {
                    manualIdsToBeUnassigned = rangeManualIds;
                }
                //Remove manual list from customers of current dealer subentity
                if (manualIdsToBeUnassigned.size() > 0) {
                    dealerManualDao.unassignManualIdsFromDealerCustomers(manufacturerSubEntityId, manualIdsToBeUnassigned);
                }
            }
        }
        boolean removedExisting = clearRangeMappingsForSubEntity(manufacturerSubEntityId);
        if (removedExisting && assignedRangeIds.size() > 0) {
            logger.info("Assigning Ranges to subentity");
            assignRangesManufacturerSubEntity(assignedRangeIds, manufacturerSubEntityId, currentUser.getUserId());
        }
        return true;
    }

    private static final String DELETE_MANUFACTURER_SUB_ENTITIES_TO_RANGE_MAPS_BY_SUBENTITY_ID =
            "DELETE FROM manufacturersubentityrangemap WHERE manufacturersubentityid = :manufacturerSubEntityId";
    public boolean clearRangeMappingsForSubEntity(int manufacturerSubEntityId) {
        Map<String, Integer> namedParameters = new HashMap<String, Integer>();
        namedParameters.put("manufacturerSubEntityId", manufacturerSubEntityId);

        namedParamJdbcTemplate.update(DELETE_MANUFACTURER_SUB_ENTITIES_TO_RANGE_MAPS_BY_SUBENTITY_ID, namedParameters);

        logger.info("Deleted SubEntity Range Mappings For manufacturerSubEntityId [{}]", manufacturerSubEntityId);
        return true;
    }
}
