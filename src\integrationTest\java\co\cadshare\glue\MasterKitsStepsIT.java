package co.cadshare.glue;

import co.cadshare.masterKits.adapters.api.web.MasterKitsSearchResponseItemDto;
import co.cadshare.masterKits.adapters.api.web.PostMasterKitsSearchResponseDto;
import io.cucumber.java.en.Then;
import io.cucumber.java.en.When;
import org.springframework.beans.factory.annotation.Autowired;

import static org.junit.Assert.*;
import static org.junit.Assert.assertTrue;

public class MasterKitsStepsIT {

	private PostMasterKitsSearchResponseDto masterKitSearchResult;
	protected UserIT loggedInUser;
	private final CadshareIT cadshare;

	@Autowired
	public MasterKitsStepsIT(CadshareIT cadshare) {
		this.cadshare = cadshare;
	}

	@When("I search MasterKits for part number by {}")
	public void iSearchMasterKitsForPartNumberBy(String masterKitNumberSearch) {
		masterKitSearchResult = cadshare.loggedInUser().searchForMasterKitByNumber(masterKitNumberSearch);
		assertNotNull(masterKitSearchResult);
	}

	@Then("I am presented with a list of {} MasterKits that match part number on {}")
	public void iAmPresentedWithAListOfMasterPartsThatMatchPartNumberOn(String count, String masterKitNumberSearch) {
		assertFalse(masterKitSearchResult.getKits().isEmpty());
		assertEquals(Integer.parseInt(count), masterKitSearchResult.getKits().size());
		for (MasterKitsSearchResponseItemDto masterKit : masterKitSearchResult.getKits()) {
			assertNotNull(masterKit);
			assertTrue(masterKit.getPartNumber().contains(masterKitNumberSearch));
		}
	}

	@When("I search MasterKits for part description by {} using {}")
	public void iSearchMasterPartsForKitDescriptionByUsing(String masterKitDescSearch, String languageCode) {
		masterKitSearchResult = cadshare.loggedInUser().searchForMasterKitByDescriptionUsingLanguage(masterKitDescSearch, languageCode);
		assertNotNull(masterKitSearchResult);
	}

	@Then("I am presented with a list of {} MasterKits that match part description on {}")
	public void iAmPresentedWithAListOfMasterKitsThatMatchPartDescriptionOn(String count, String masterPartDescSearch) {
		assertEquals(Integer.parseInt(count), masterKitSearchResult.getKits().size());
		for (MasterKitsSearchResponseItemDto masterKit : masterKitSearchResult.getKits()) {
			assertNotNull(masterKit);
			assertTrue(masterKit.getPartDescription().contains(masterPartDescSearch));
		}
	}

}