package co.cadshare.masterKits.adapters.database;

import lombok.Data;

import javax.persistence.*;
import java.util.List;

@Data
@Entity
@Table(name="manufacturer")
public class MkManufacturerEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name="manufacturerid")
    private Integer id;

    @OneToMany(fetch = FetchType.LAZY, cascade=CascadeType.ALL)
    @JoinColumn(name="manufacturerid")
    private List<MkManufacturerLanguageEntity> manufacturerLanguages;

    @OneToOne(fetch = FetchType.LAZY, cascade = CascadeType.ALL)
    @JoinColumn(name="manufacturerid")
    private MkManufacturerSettingsEntity settings;
}
