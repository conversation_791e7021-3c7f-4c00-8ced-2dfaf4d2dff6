package co.cadshare.masterKits.core;

import co.cadshare.exceptions.NotFoundException;
import co.cadshare.exceptions.UnprocessableEntityException;
import co.cadshare.masterKits.boundary.CreateMasterKitCommand;
import co.cadshare.masterKits.boundary.UpdateMasterKitCommand;
import co.cadshare.utils.ObjectUtilsExtension;
import lombok.*;
import lombok.experimental.ExtensionMethod;

import java.sql.Timestamp;
import java.util.*;

@Data
@Builder
@ExtensionMethod({ObjectUtilsExtension.class})
public class MasterKit {
    private final String KIT_MASTER_PART_NOT_FOUND = "There is no valid Master Part candidate for this Kit Master Part.";
    private final String KIT_MASTER_PART_NO_MATCH = "There is no matching Master Part candidate for this Kit Master Part, for this Manufacturer.";
    private final String PART_MASTER_PART_NO_MATCH = "There is no matching Master Part candidate for one or more Master Parts, for this Manufacturer.";
    private final String NO_MASTER_PARTS = "No Master Parts have been assigned to this kit.";
    private final String MISSING_MASTER_PARTS = "Some suggested Master Parts updates may not be valid candidates";
    private int id;
    private String description;
    private KitMasterPart kitMasterPart;
    private List<PartMasterPart> partMasterParts;
    private Integer manufacturerId;
    private boolean deleted;
    private Integer createdByUserId;
    private Integer modifiedByUserId;
    private Timestamp createdDate;
    private Timestamp modifiedDate;
    @Setter(AccessLevel.NONE)
    private PartMasterPart formerKitMasterPart;

    public void validate() {
        if(kitMasterPart.isNull())
            throw new NotFoundException(KIT_MASTER_PART_NOT_FOUND);
        if(partMasterParts.isNull() || partMasterParts.size() == 0)
            throw new UnprocessableEntityException(NO_MASTER_PARTS);
        if(kitMasterPart.getManufacturerId() != manufacturerId)
            throw new UnprocessableEntityException(KIT_MASTER_PART_NO_MATCH);
        if(partMasterParts.stream().anyMatch(pmp -> pmp.getManufacturerId() != manufacturerId))
            throw new UnprocessableEntityException(PART_MASTER_PART_NO_MATCH);
    }

    public boolean kitMasterPartIsChanging(Integer id) {
        return (this.kitMasterPart.isNull() || !this.kitMasterPart.getId().equals(id));
    }

    public void create(CreateMasterKitCommand create) {
        manufacturerId = create.getManufacturerId();
        applyMasterPartQuantities(create.getParts());
    }

    public void update(UpdateMasterKitCommand update,
                       KitMasterPart changingKitMasterPart,
                       List<PartMasterPart> hydratedMasterParts) {

        manufacturerId = update.getManufacturerId();
        if (changingKitMasterPart.isNotNull())
            setFormerKitMasterPart();
        changeKitMasterPart(changingKitMasterPart, update);
        setDescription(update.getDescription());
        updateMasterParts(hydratedMasterParts, update.getMasterParts());
        updateTranslations(update.getTitles());
        updatePrices(update);
    }

    public void Delete() {
        deleted = true;
        setFormerKitMasterPart();
    }

    public boolean hasKitMasterPart() {
        return kitMasterPart.isNotNull();
    }

    public boolean isNotLegacy() {
        return formerKitMasterPart.isNotNull();
    }

    private void updateMasterParts(List<PartMasterPart> hydratedMasterParts, Map<Integer, Integer> updates) {
        if(hydratedMasterParts.size() < updates.size())
            throw new UnprocessableEntityException(MISSING_MASTER_PARTS);
        List<PartMasterPart> updatedMasterParts = new ArrayList<>();
        hydratedMasterParts.forEach(hmp -> {
            Optional<PartMasterPart> masterPartSearch = partMasterParts.stream()
                    .filter(pmp -> pmp.getId().equals(hmp.getId()))
                    .findAny();
            PartMasterPart partToAdd = masterPartSearch.isPresent() ? masterPartSearch.get() : hmp ;
            if(updates.containsKey(partToAdd.getId()))
                partToAdd.setQuantity(updates.get(partToAdd.getId()));
            updatedMasterParts.add(partToAdd);
        });
        this.partMasterParts = updatedMasterParts;
    }

    private void changeKitMasterPart(KitMasterPart newKitMasterPart, UpdateMasterKitCommand update) {
        if(kitMasterPartIsChanging(update.getKitMasterPartId())) {
            if (newKitMasterPart.isNull())
                throw new NotFoundException(KIT_MASTER_PART_NOT_FOUND);
            if (newKitMasterPart.getManufacturerId() != update.getManufacturerId())
                throw new UnprocessableEntityException(KIT_MASTER_PART_NO_MATCH);
            else
                this.kitMasterPart = newKitMasterPart;
        }
    }

    private void updateTranslations(Map<Integer, String> updates) {
        if(kitMasterPart.isNotNull() && kitMasterPart.getTranslations().isNotNull())
            kitMasterPart.getTranslations().forEach(mp -> {
                if(updates.containsKey(mp.getId()))
                    mp.setDescription(updates.get(mp.getId()));
            });
    }

    private void updatePrices(UpdateMasterKitCommand update) {
        Float price = update.getPrice();
        Map<Integer, Float> priceUpdates = update.getPrices();
        if(price.isNotNull() && kitMasterPart.getPrice().isNotNull())
            kitMasterPart.getPrice().setPrice(price);
        if(priceUpdates.isNotNull() && kitMasterPart.getPrices().isNotNull()) {
            kitMasterPart.getPrices().forEach(p -> {
                if(priceUpdates.containsKey(p.getId()))
                    p.setPrice(priceUpdates.get(p.getId()));
            });
        }
    }

    private void applyMasterPartQuantities(Map<Integer, Integer> quantities) {
        if(this.partMasterParts.isNotNull())
            this.partMasterParts.forEach(mp -> {
                if(quantities.containsKey(mp.getId()))
                    mp.setQuantity(quantities.get(mp.getId()));
            });
    }

    private void setFormerKitMasterPart() {
        formerKitMasterPart = MasterPartMapper.Instance.kitMasterPartToPartMasterPart(kitMasterPart);
    }
}