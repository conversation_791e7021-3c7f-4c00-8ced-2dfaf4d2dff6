package co.cadshare.api.user;

import co.cadshare.shared.core.user.User;
import co.cadshare.shared.core.user.UserType;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class APIUser {

    private int userId;
    private String firstName;
    private String lastName;
    private String emailAddress;
    private String userType;
    //Manufacturer SubEntityId
    private Integer companyId;

    public String accessDetails() {
        return getUserId() + "," + getFirstName() + " " + getLastName();
    }

    public static APIUser createFrom(User user) {
        APIUser apiUser = APIUser.builder()
            .userId(user.getUserId())
            .firstName(user.getFirstName())
            .lastName(user.getLastName())
            .emailAddress(user.getEmailAddress())
            .userType(user.getUserType().name())
            .companyId(user.getManufacturerSubEntityId())
            .build();
        return apiUser;
    }

    public User toUser() {
        User user = new User();
        user.setFirstName(this.firstName);
        user.setLastName(this.lastName);
        user.setEmailAddress((this.emailAddress));
        user.setUserType(UserType.valueOf(this.userType));
        user.setManufacturerSubEntityId(this.companyId);
        user.setUserStatus(User.UserStatus.ACTIVE);
        return user;
    }
}
