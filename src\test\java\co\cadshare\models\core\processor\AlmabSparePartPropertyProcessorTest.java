package co.cadshare.models.core.processor;

import co.cadshare.models.core.MetadataObjectExtended;
import co.cadshare.models.core.processor.fileproperties.AlmabSparePartPropertyProcessor;
import org.junit.Before;
import org.junit.Test;

import java.util.Collections;
import java.util.LinkedHashMap;

import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;

public class AlmabSparePartPropertyProcessorTest {

    private AlmabSparePartPropertyProcessor out;
    private LinkedHashMap properties;
    private MetadataObjectExtended objectExtended;

    @Before
    public void Before() {
        out = new AlmabSparePartPropertyProcessor();
        out.setSynonyms(Collections.singletonList("DB_SPARE_PART_CHAR"));
        setProperties();
        objectExtended = new MetadataObjectExtended();
    }

    @Test
    public void isSellableAndSparePartForYes() {
        properties.put("DB_SPARE_PART_CHAR", "Yes");
        out.setProperties(properties, objectExtended);

        assertTrue(objectExtended.isSellablePart());
        assertTrue(objectExtended.isSparePart());
    }

    @Test
    public void isNotSellableOrSparePartForNo() {
        properties.put("DB_SPARE_PART_CHAR", "No");
        out.setProperties(properties, objectExtended);

        assertFalse(objectExtended.isSellablePart());
        assertFalse(objectExtended.isSparePart());
    }

    @Test
    public void isNotSellableButSparePartForNull() {

        out.setProperties(properties, objectExtended);

        assertFalse(objectExtended.isSellablePart());
        assertTrue(objectExtended.isSparePart());
    }

    private void setProperties() {
        properties = new LinkedHashMap<String, String>();
        properties.put("Original System", "NX");
        properties.put("CAD_PARTNAME", "16005-0011353-01");
    }
}
