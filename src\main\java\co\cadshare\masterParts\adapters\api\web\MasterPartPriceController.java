/*
 * Copyright 2016 Bell.
 */
package co.cadshare.masterParts.adapters.api.web;

import co.cadshare.domainmodel.part.Part;
import co.cadshare.masterParts.boundary.MasterPartPriceService;
import co.cadshare.masterParts.core.Price;
import co.cadshare.shared.core.user.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Slf4j
@Controller
@RequestMapping("/masterPart")
public class MasterPartPriceController {

  private final MasterPartPriceService priceService;

  @Autowired
  public MasterPartPriceController(MasterPartPriceService priceService) {
    this.priceService = priceService;
  }

  @GetMapping(value = "/{masterpartId}/price")
  public HttpEntity<Float> getMasterPartPrice(@AuthenticationPrincipal User currentUser, @PathVariable int masterpartId) {

    log.info("ACCESS: User [{}], getMasterPartPrice, masterpartid [{}]", currentUser.accessDetails(), masterpartId);

    Float price = priceService.getMasterPartPrice(masterpartId);
    return new ResponseEntity<>(price, HttpStatus.OK);
  }

  @PutMapping(value = "/{masterpartId}/price")
  public HttpEntity<Boolean> updateMasterPartPrice(@AuthenticationPrincipal User currentUser, @PathVariable int masterpartId, @RequestBody Part partInventory) {

    log.info("ACCESS: User [{}], getMasterPartInventory, masterpartid [{}]", currentUser.accessDetails(), masterpartId);

    partInventory.setMasterPartId(masterpartId);
    Boolean updated = priceService.updateMasterPartPrice(partInventory);
    return new ResponseEntity<>(updated, HttpStatus.OK);
  }

  //PriceList Services
  @GetMapping(value = "/{masterpartId}/priceList")
  public HttpEntity<List<MasterPartPrice>> getMasterPartPriceLists(@AuthenticationPrincipal User currentUser, @PathVariable int masterpartId) {

    log.info("ACCESS: User [{}], getMasterPartPriceLists, masterpartid [{}]", currentUser.accessDetails(), masterpartId);

    List<MasterPartPrice> priceList = priceService.getPartPriceList(masterpartId);
    return new ResponseEntity<>(priceList, HttpStatus.OK);
  }

  @PostMapping(value = "/{masterpartId}/priceList/identifier/{identifierId}")
  public HttpEntity<Boolean> createMasterPartPrice(@AuthenticationPrincipal User currentUser, @PathVariable int masterpartId, @PathVariable int identifierId, @RequestBody Price price) {

    log.info("ACCESS: User [{}], createMasterPartPrice, masterpartid [{}]", currentUser.accessDetails(), masterpartId);
    price.setMasterPartId(masterpartId);
    price.setPriceId(identifierId);
    Boolean result = priceService.createPartPrice(price);
    return new ResponseEntity<>(result, HttpStatus.OK);
  }

  @PutMapping(value = "/{masterpartId}/priceList/identifier/{identifierId}")
  public HttpEntity<Boolean> updateMasterPartPrice(@AuthenticationPrincipal User currentUser, @PathVariable int masterpartId, @PathVariable int identifierId, @RequestBody Price price) {

    log.info("ACCESS: User [{}], updateMasterPartPrice, masterpartid [{}]", currentUser.accessDetails(), masterpartId);
    price.setMasterPartId(masterpartId);
    price.setPriceId(identifierId);
    Boolean result = priceService.updatePartPrice(price);
    return new ResponseEntity<>(result, HttpStatus.OK);
  }

  @DeleteMapping(value = "/{masterpartId}/priceList/identifier/{identifierId}")
  public HttpEntity<Boolean> deleteMasterPartPrice(@AuthenticationPrincipal User currentUser, @PathVariable int masterpartId, @PathVariable int identifierId) {

    log.info("ACCESS: User [{}], deleteMasterPartPrice, masterpartid [{}]", currentUser.accessDetails(), masterpartId);

    boolean response =  priceService.deletePartPrice(masterpartId, identifierId);
    return new ResponseEntity<>(response, HttpStatus.OK);
  }
}
