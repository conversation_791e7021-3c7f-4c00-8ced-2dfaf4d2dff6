/*
 * Copyright 2016 Bell.
 */
package co.cadshare.masterParts.adapters.api.web;

import co.cadshare.domainmodel.techDoc.TechDoc;
import co.cadshare.masterParts.boundary.MasterPartLinkedPartService;
import co.cadshare.masterParts.core.extensions.partModelLink.PartModelLink;
import co.cadshare.shared.core.user.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Slf4j
@Controller
@RequestMapping("/masterPart")
public class MasterPartLinkedPartController {

  private final MasterPartLinkedPartService linkedPartService;

  public MasterPartLinkedPartController(MasterPartLinkedPartService linkedPartService) {
    this.linkedPartService = linkedPartService;
  }

  //Linked Parts Services
  @RequestMapping(value = "/{masterpartId}/linkedPart", method = RequestMethod.GET)
  public HttpEntity<PartModelLink> getLinkedPartsForPart(@AuthenticationPrincipal User currentUser, @PathVariable int masterpartId) throws Exception {

    log.info("ACCESS: User [{}], getlinkedPartsForPart, partId [{}]", currentUser.accessDetails(), masterpartId);

    PartModelLink linkedViewable = linkedPartService.getLinkedPartsForPart(masterpartId);
    return new ResponseEntity<>(linkedViewable, HttpStatus.OK);
  }

  @PostMapping(value = "/{masterpartId}/linkedPart", consumes = "application/json")
  public HttpEntity<Boolean> createLinkedPart(@AuthenticationPrincipal User currentUser, @PathVariable int masterpartId, @RequestBody PartModelLink modelLink) {

    log.info("ACCESS: User [{}], createlinkedPart, for masterpart Id [{}]", currentUser.accessDetails(), masterpartId);

    modelLink.setMasterPartId(masterpartId);
    Boolean success = linkedPartService.createLinkedPart(modelLink);

    return new ResponseEntity<>(success, HttpStatus.OK);
  }

  @PutMapping(value = "/{masterpartId}/linkedPart", consumes = "application/json")
  public HttpEntity<Boolean> updateLinkedPart(@AuthenticationPrincipal User currentUser, @PathVariable int masterpartId, @RequestBody PartModelLink modelLink) {

    log.info("ACCESS: User [{}], updateLinkedPart, linkedPartId [{}]", currentUser.accessDetails(), masterpartId);
    modelLink.setMasterPartId(masterpartId);

    boolean response = linkedPartService.updateLinkedPart(modelLink);
    return new ResponseEntity<>(response, HttpStatus.OK);
  }

  @DeleteMapping(value = "/{masterpartId}/linkedPart")
  public HttpEntity<Boolean> deleteLinkedPart(@AuthenticationPrincipal User currentUser, @PathVariable int masterpartId) {

    log.info("ACCESS: User [{}], deleteLinkedPart, id [{}]", currentUser.accessDetails(), masterpartId);
    boolean isDeleted = linkedPartService.deleteLinkedPart(masterpartId);

    return new ResponseEntity<>(isDeleted, HttpStatus.OK);
  }

  //Link Tech docs
  @RequestMapping(value = "/{masterpartId}/techDoc", method = RequestMethod.GET)
  public HttpEntity<List<TechDoc>> getLinkedTechDocsForPart(@AuthenticationPrincipal User currentUser, @PathVariable int masterpartId) throws Exception {

    log.info("ACCESS: User [{}], getLinkedTechDocsForPart, partId [{}]", currentUser.accessDetails(), masterpartId);

    List<TechDoc> techDocs = linkedPartService.getLinkedTechDocsForPart(masterpartId);
    return new ResponseEntity<>(techDocs, HttpStatus.OK);
  }

  @PutMapping(value = "/{masterpartId}/techDoc", consumes = "application/json")
  public HttpEntity<Boolean> updateLinkedTechDocsForPart(@AuthenticationPrincipal User currentUser, @PathVariable int masterpartId, @RequestBody List<TechDoc> techDocLinks) {

    log.info("ACCESS: User [{}], updateLinkedTechDocsForPart", currentUser.accessDetails());

    boolean response = linkedPartService.updateLinkedTechDocsForPart(masterpartId, techDocLinks);
    return new ResponseEntity<>(response, HttpStatus.OK);
  }

}
