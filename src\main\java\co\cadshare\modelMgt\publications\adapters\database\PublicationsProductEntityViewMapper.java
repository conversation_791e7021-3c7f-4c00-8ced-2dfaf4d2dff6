package co.cadshare.modelMgt.publications.adapters.database;

import co.cadshare.modelMgt.shared.core.Product;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface PublicationsProductEntityViewMapper {

    PublicationsProductEntityViewMapper Instance = Mappers.getMapper(PublicationsProductEntityViewMapper.class);

    Product entityToCore(PublicationsProductEntityView entityView);
}
