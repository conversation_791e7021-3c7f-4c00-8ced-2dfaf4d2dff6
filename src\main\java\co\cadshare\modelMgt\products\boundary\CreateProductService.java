package co.cadshare.modelMgt.products.boundary;

import co.cadshare.modelMgt.models.boundary.UserQueryPort;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import co.cadshare.shared.core.user.User;
import co.cadshare.aspects.logging.Log;
import co.cadshare.modelMgt.products.core.Product;

@Service
public class CreateProductService {

    private final ProductCommandPort productCommandPort;
    private final UserQueryPort userQueryPort;

    @Autowired
    public CreateProductService(ProductCommandPort productCommandPort, UserQueryPort userQueryPort) {
        this.productCommandPort = productCommandPort;
        this.userQueryPort = userQueryPort;
    }

    @Log
    public Integer create(User user, Product product) {
        return this.productCommandPort.create(user, product);
    }

    public int create(Product product, int manufacturerId) throws Exception {
        User apiUser = this.userQueryPort.getApiUserForManufacturer(manufacturerId);
        return  create(apiUser, product);
    }
}
