<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.0.xsd
        http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd">

  <changeSet author="AndyB" id="57.2-rename-add-manual-table-columns">
    <renameColumn tableName="manual" oldColumnName="mediaid" newColumnName="coverimageid"/>
      <addColumn tableName="manual">
          <column name="featuredviewableimageid" type="bigint">
            <constraints nullable="true" foreignKeyName="fk_manual_media_2" references="media(mediaid)"/>
          </column>
      </addColumn>
      <sql>
          INSERT INTO public.media(locationurl, mediatype)
          VALUES('images/placeholder.jpg', 'Image');

          INSERT INTO public.media(locationurl, mediatype)
          SELECT featuredmodelurl, 'Image' FROM manual WHERE featuredmodelurl IS NOT NULL and featuredmodelurl != 'images/placeholder.jpg';

          INSERT INTO public.media(locationurl, mediatype)
          SELECT distinct(thumbnailUrl), 'Image'
          FROM public.machine mac,
          manualmodelmap mmm,
          model mo,
          manual man
          WHERE mac.machineid = mo.machineid
          AND mmm.modelid = mo.modelid
          AND mmm.manualid = man.manualid
          AND man.useviewableimage = FALSE
          AND thumbnailurl != 'images/placeholder.jpg';

          UPDATE public.manual man
          SET featuredviewableimageid = (SELECT min(mediaid) FROM public.media WHERE locationurl = man.featuredmodelurl);

          UPDATE public.manual man
          SET coverimageid = (SELECT min(mediaid) FROM public.media WHERE locationurl = man.featuredmodelurl and man.useviewableimage = TRUE);

          UPDATE public.manual man3
          SET coverimageid = (
          select min(media.mediaid)
          from machine mac,
          model mo,
          manualmodelmap mmm2,
          manual man2,
          media
          where mac.machineid = mo.machineid
          AND mmm2.modelid = mo.modelid
          AND mmm2.manualid = man2.manualid
          AND mac.thumbnailurl = media.locationurl
          AND man3.manualid = man2.manualid
          and mo.modelid = (
          SELECT min(mmm.modelid)
          FROM manualmodelmap mmm
          where mmm.manualid = man2.manualid
          )
          group by man2.manualid, mo.modelid
          )
          WHERE man3.useviewableimage = FALSE;
      </sql>
  </changeSet>

</databaseChangeLog>
