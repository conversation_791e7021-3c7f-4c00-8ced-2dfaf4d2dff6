package co.cadshare.publications.adapters.database;

import co.cadshare.publications.core.Viewable;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper(uses = {
        PublicationsPurchaserEntityViewMapper.class,
        PublicationsSnapshotEntityViewMapper.class})
public interface PublicationsViewableEntityViewMapper {

    PublicationsViewableEntityViewMapper Instance = Mappers.getMapper(PublicationsViewableEntityViewMapper.class);

    Viewable entityToCore(PublicationsViewableEntityView entityView);
}
