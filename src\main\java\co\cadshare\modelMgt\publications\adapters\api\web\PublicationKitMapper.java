package co.cadshare.modelMgt.publications.adapters.api.web;

import co.cadshare.modelMgt.publications.core.Kit;
import co.cadshare.modelMgt.publications.core.ManualStatus;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

@Mapper
public interface PublicationKitMapper {

	PublicationKitMapper Instance = Mappers.getMapper(PublicationKitMapper.class);

	@Mapping(source="description", target="name")
	PublicationKitDto toDto(Kit core);

}
