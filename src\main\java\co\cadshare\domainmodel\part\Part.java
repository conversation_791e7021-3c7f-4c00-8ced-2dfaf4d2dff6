/*
 * Copyright 2016 Bell.
 */
package co.cadshare.domainmodel.part;

import java.sql.Timestamp;

import co.cadshare.masterParts.core.MasterPart;
import co.cadshare.shared.core.PartElementWithDescription;
import lombok.Data;

@Data
public class Part implements PartElementWithDescription {
  private Integer partId;
  private Integer masterPartId;
  private int modelId;
  private String fileName;
  private String partDescription;
  private String partNumber;
  private String weight;
  private String massUnit;
  private Timestamp createdDate;
  private Integer createdByUserId;
  private Timestamp modifiedDate;
  private Integer modifiedByUserId;
  private Integer objectId;
  private Integer parentObjectId;
  private String itemNumber;
  private Float price;
  private Double stock;
  private boolean masterPartFound;
  private boolean masterPartDescriptionFound;
  private boolean previewPricingEnabled;
  private boolean previewStockLevelEnabled;
  private String alternatePartNumber;
  private String note;
  private boolean sparePart;
  private boolean criticalSparePart;
  private boolean superseded;
  private boolean inSupersession;
  private Integer defaultWarehouseId;
  private String supersessionPartNumber;

  public Part() {
    sparePart = true;
    criticalSparePart = true;
  }

  //TODO - this is not correct and needs to be reworked. A Part doesn't always have a MasterPart
  //and even when it does, it shouldn't be shoe-horned into the Part. They are separate concepts.
  public void applyMasterPart(MasterPart masterPart) {
    this.masterPartId = masterPart.getMasterPartId();
    this.partNumber = masterPart.getPartNumber();
    this.price = masterPart.getPrice();
    this.stock = masterPart.getStock();
    this.weight = masterPart.getWeight();
    this.massUnit = masterPart.getMassUnit();
    this.partDescription = masterPart.getDescription();
    this.masterPartFound = true;
    this.masterPartDescriptionFound = true;
    this.note = masterPart.getNote();
  }
}
