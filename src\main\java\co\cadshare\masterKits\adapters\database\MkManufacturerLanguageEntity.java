package co.cadshare.masterKits.adapters.database;

import lombok.Data;

import javax.persistence.*;

@Data
@Entity
@Table(name="manufacturerlanguage")
public class MkManufacturerLanguageEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name="id")
    private Integer id;

    @Column(name="manufacturerid")
    private Integer manufacturerId;
    @Column(name="languageid")
    private Integer languageId;
    @Column(name="default")
    private boolean defaultLanguage;

}