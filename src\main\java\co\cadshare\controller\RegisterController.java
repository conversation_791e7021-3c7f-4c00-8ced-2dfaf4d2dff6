package co.cadshare.controller;

import co.cadshare.publications.core.Manual;
import co.cadshare.shared.core.manufacturer.Manufacturer;
import co.cadshare.domainmodel.range.Range;
import co.cadshare.shared.core.user.User;
import co.cadshare.publications.boundary.ManualService;
import co.cadshare.request.RegistrationUser;
import co.cadshare.response.ApiResponseEntity;
import co.cadshare.services.*;
import co.cadshare.users.boundary.UsersService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.net.MalformedURLException;
import java.net.URL;
import java.util.List;

@Controller
@RequestMapping("/register")
public class RegisterController {

    private static final Logger log = LoggerFactory.getLogger(RegisterController.class);

    @Autowired
    private ManufacturerService manufacturerService;

    @Autowired
    private ManualService manualService;

    @Autowired
    private UsersService usersService;

    @Autowired
    private ManufacturerSubEntityService manufacturerSubEntityService;

    @PostMapping(consumes = "application/json")
    public HttpEntity<ApiResponseEntity> registerUser(@RequestHeader(value = "Site-Url") String siteUrl, @RequestBody RegistrationUser registrationUser, @RequestParam(value = "theme", required = false) String theme, HttpServletRequest request) {
        log.info("ACCESS: RegisterUser with registrationUser [{}]", registrationUser);
        int userId = 0;
        try {
            URL url = new URL(request.getHeader(HttpHeaders.ORIGIN));
            Manufacturer manufacturer = manufacturerService.getManufacturerByDomain(url.getHost());
            if (!manufacturer.getManufacturerSettings().isUserRegistrationEnabled())
                return new ResponseEntity<>(new ApiResponseEntity(404, userId, "NOT_FOUND", false), HttpStatus.NOT_FOUND);
            int manufacturerId = manufacturer.getManufacturerId();

            User userToBeCreated = registrationUser.getUser();
            userToBeCreated.setManufacturerId(manufacturerId);
            if (!manufacturerSubEntityService.getManufacturerSubEntitiesForManufacturerByName(manufacturerId, userToBeCreated.getOrganisationName()).isEmpty()) {
                log.error("Company [{}] already exists.", userToBeCreated.getOrganisationName());
                return new ResponseEntity<>(new ApiResponseEntity(409, userId, "Company " + userToBeCreated.getOrganisationName() + " already exists.", false), HttpStatus.CONFLICT);
            }
            if (usersService.isEmailAlreadyInUse(userToBeCreated.getEmailAddress(), manufacturerId)) {
                log.error("User email [{}] already associated with an account.", userToBeCreated.getEmailAddress());
                return new ResponseEntity<>(new ApiResponseEntity(409, userId, "User's email " + userToBeCreated.getEmailAddress() + " already associated with an account.", false), HttpStatus.CONFLICT);
            }
            StringBuilder error = new StringBuilder();
            userId = usersService.createRegistrationUser(registrationUser, siteUrl, theme, error);
            registrationUser.getUser().setUserId(userId);
            if (error.length()>0) {
                log.error("Error : [{}]", error);
                return new ResponseEntity<>(new ApiResponseEntity(409, userId, "An error occurred while creating a customer in the Visibility system ( " + error + " ).", false, error.toString()), HttpStatus.CONFLICT);
            }
        } catch (MalformedURLException e) {
            return new ResponseEntity<>(new ApiResponseEntity(404, userId, "NOT_FOUND", false), HttpStatus.NOT_FOUND);
        } catch (Exception error) {
            log.error(error.getMessage());
            return new ResponseEntity<>(new ApiResponseEntity(500, userId, error.getMessage(), false), HttpStatus.INTERNAL_SERVER_ERROR);
        }
        return new ResponseEntity<>(new ApiResponseEntity(200, userId, null, true), HttpStatus.OK);
    }

    @GetMapping(value = "manufacturer/{manufacturerId}/ranges")
    public HttpEntity<List<Range>> getRangesForManufacturer(@PathVariable int manufacturerId, HttpServletRequest request) {
        log.info("ACCESS: getRangesForManufacturer [{}]", manufacturerId);
        if (!checkUserRegistrationEnabled(request))
            return new ResponseEntity<>(HttpStatus.NOT_FOUND);
        List<Range> rangeList = manufacturerService.getRangesForManufacturer(manufacturerId);
        return new ResponseEntity<>(rangeList, HttpStatus.OK);
    }

    @GetMapping(value = "/range/{rangeId}/manuals")
    public HttpEntity<List<Manual>> getManualsForRange(@PathVariable int rangeId, HttpServletRequest request) {
        log.info("ACCESS: getManualsForRange [{}]", rangeId);
        if (!checkUserRegistrationEnabled(request))
            return new ResponseEntity<>(HttpStatus.NOT_FOUND);
        List<Manual> manualList = manualService.getManualsForRange(rangeId);
        return new ResponseEntity<>(manualList, HttpStatus.OK);
    }

    private Boolean checkUserRegistrationEnabled(HttpServletRequest request) {
        try {
            URL url = new URL(request.getHeader(HttpHeaders.ORIGIN));
            return manufacturerService.getManufacturerByDomain(url.getHost())
                    .getManufacturerSettings().isUserRegistrationEnabled();
        } catch (MalformedURLException e) {
            return false;
        }
    }
}
