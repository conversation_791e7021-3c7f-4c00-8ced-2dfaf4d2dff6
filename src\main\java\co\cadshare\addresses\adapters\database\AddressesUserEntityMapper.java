package co.cadshare.addresses.adapters.database;

import co.cadshare.addresses.core.User;
import co.cadshare.addresses.core.UserAddressMap;
import co.cadshare.utils.CycleAvoidingMappingContext;
import org.mapstruct.Context;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper(uses = UserAddressMapEntityMapper.class)
public interface AddressesUserEntityMapper {

    AddressesUserEntityMapper Instance = Mappers.getMapper(AddressesUserEntityMapper.class);

    @Mapping(source="userAddressMaps", target="addressMaps", qualifiedByName="mapsEntityToCore")
    User entityToCore(AddressesUserEntity entity, @Context CycleAvoidingMappingContext context);

    @Mapping(source="addressMaps", target="userAddressMaps", qualifiedByName="mapsCoreToEntity")
    AddressesUserEntity coreToEntity(User core, @Context CycleAvoidingMappingContext context);

    List<User> entitiesToCores(List<AddressesUserEntity> entities, @Context CycleAvoidingMappingContext context);

    List<AddressesUserEntity> coresToEntities(List<User> cores, @Context CycleAvoidingMappingContext context);

    @Named("mapsEntityToCore")
    public static List<UserAddressMap> mapsEntityToCore(List<UserAddressMapEntity> entities, @Context CycleAvoidingMappingContext context) {
        return UserAddressMapEntityMapper.Instance.entitiesToCores(entities, context);
    }

    @Named("mapsCoreToEntity")
    public static List<UserAddressMapEntity> mapsCoreToEntity(List<UserAddressMap> entities, @Context CycleAvoidingMappingContext context) {
        return UserAddressMapEntityMapper.Instance.coresToEntities(entities, context);
    }
}
