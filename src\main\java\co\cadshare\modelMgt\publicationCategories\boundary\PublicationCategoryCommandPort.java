package co.cadshare.modelMgt.publicationCategories.boundary;

import co.cadshare.modelMgt.publicationCategories.core.PublicationCategory;
import co.cadshare.shared.boundary.CommandPort;
import co.cadshare.shared.core.user.User;

import java.util.List;

public interface PublicationCategoryCommandPort extends CommandPort<PublicationCategory, Integer> {
	void assignPublicationCategoriesToPurchaser(Integer purchaserId, List<Integer> publicationCategoryIds, User user) throws Exception;

	void clearPublicationCategoriesForPurchaser(Integer purchaserId);
}
