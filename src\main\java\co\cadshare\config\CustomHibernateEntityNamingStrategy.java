package co.cadshare.config;

import org.hibernate.boot.model.naming.Identifier;
import org.hibernate.boot.model.naming.PhysicalNamingStrategy;
import org.hibernate.boot.model.naming.PhysicalNamingStrategyStandardImpl;
import org.hibernate.engine.jdbc.env.spi.JdbcEnvironment;

public class CustomHibernateEntityNamingStrategy extends PhysicalNamingStrategyStandardImpl {

    @Override
    public Identifier toPhysicalColumnName(final Identifier identifier, final JdbcEnvironment jdbcEnv) {
        return convertToLowerCase(identifier);
    }

    private Identifier convertToLowerCase(final Identifier identifier) {
        final String newName = identifier.getText().toLowerCase();
        return Identifier.toIdentifier(newName);
    }
}
