
# @name cadshareAuthResponse

POST {{CADSHARE_URL}}/oauth/token
Content-Type: application/x-www-form-urlencoded
Authorization: Basic Y2xpZW50YXBwOnNlY3JldA==

username=1364
&password=Sellm0reparts
&grant_type=password


###

// create an order and then duplicate

@cadshareToken = Bearer {{cadshareAuthResponse.response.body.access_token}}

# @name modelSearchResponse
POST {{CADSHARE_URL}}/dealers/350/model-search
Content-Type: application/json
Authorization: {{cadshareToken}}
Site-Url: {{CADSHARE_URL}}

{
    "partNumber": "OSM17X"
}

###

POST {{CADSHARE_URL}}/manufacturer-sub-entities/350/master-part-search?language=EN
Content-Type: application/json
Authorization: {{cadshareToken}}
Site-Url: {{CADSHARE_URL}}

{
    "partNumber": "OSM17X"
}
