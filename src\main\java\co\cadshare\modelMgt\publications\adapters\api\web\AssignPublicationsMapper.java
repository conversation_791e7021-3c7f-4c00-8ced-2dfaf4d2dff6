package co.cadshare.modelMgt.publications.adapters.api.web;

import co.cadshare.modelMgt.publications.boundary.AssignPublicationsCommand;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface AssignPublicationsMapper {

	AssignPublicationsMapper Instance = Mappers.getMapper(AssignPublicationsMapper.class);


	AssignPublicationsCommand dtoToCommand(PostAssignPublicationsDto dto);
}
