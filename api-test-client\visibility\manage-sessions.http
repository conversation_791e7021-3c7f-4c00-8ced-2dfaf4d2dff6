// Successful request

# @name session
POST {{VIS_SESSION_URL}}
Content-Type: text/xml

<?xml version="1.0" encoding="utf-8"?>
<soap:Envelope xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">
    <soap:Body>
        <GetSession xmlns="http://visibility.com/">
            <strUser>{{VIS_SESSION_USER}}</strUser>
            <strPassword>{{$dotenv VIS_SESSION_PASSWORD}}</strPassword>
        </GetSession>
    </soap:Body>
</soap:Envelope>

###

//Set Entity
@secureId = {{session.response.body./*[local-name()='Envelope']/*[local-name()='Body']/*[local-name()='GetSessionResponse']/*[local-name()='GetSessionResult']/*[local-name()='sReturnMessage']}}

POST {{VIS_SESSION_URL}}
Content-Type: text/xml

<?xml version="1.0" encoding="utf-8"?>
<soap:Envelope xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">
    <soap:Body>
        <SetEntity xmlns="http://visibility.com/">
            <sessionId>{{secureId}}</sessionId>
            <strEntity_CODE>SIL</strEntity_CODE>
        </SetEntity>
    </soap:Body>
</soap:Envelope>

