package co.cadshare.ranges.core;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import java.sql.Timestamp;

@Data
public class Range {

    private Integer id;
    private String name;
    private Integer manufacturerId;
    private boolean deleted;
    private Integer createdByUserId;
    private Integer modifiedByUserId;
    private Timestamp createdDate;
    private Timestamp modifiedDate;

}