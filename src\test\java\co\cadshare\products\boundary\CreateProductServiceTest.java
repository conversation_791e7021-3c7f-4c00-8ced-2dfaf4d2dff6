package co.cadshare.products.boundary;

import co.cadshare.shared.core.user.User;
import co.cadshare.aspects.logging.ServiceLoggingAspect;
import co.cadshare.models.boundary.UserQueryPort;
import co.cadshare.products.core.Product;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.MockitoAnnotations;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.when;

@EnableAspectJAutoProxy
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(classes = {CreateProductService.class, ServiceLoggingAspect.class})
public class CreateProductServiceTest {

    @MockBean
    private ProductCommandPort commandPort;
    @Autowired
    private CreateProductService out;
    @MockBean
    private UserQueryPort userQueryPort;
    private User user;
    private Product product;
    private Product errorProduct = new Product();

    @Before
    public void Before() {
        MockitoAnnotations.initMocks(this);
        product = buildProduct();
        user = new User() {{
            setUserId(1);
            setFirstName("firstName");
            setLastName("lastName");
        }};
    }

    @Test
    public void CreateProductSuccess() throws Exception {
        Integer returnVal = Integer.valueOf(1);
        when(userQueryPort.getApiUserForManufacturer(1)).thenReturn(user);
        when(commandPort.create(user, product)).thenReturn(returnVal);
        Integer result = out.create(product, 1);
        assertEquals(returnVal, result);
    }

    @Test(expected = RuntimeException.class)
    public void CreateProductFailureException() throws Exception {
        when(userQueryPort.getApiUserForManufacturer(1)).thenReturn(user);
        when(commandPort.create(user, errorProduct)).thenThrow(new RuntimeException("terrible"));
        out.create(errorProduct, 1);
    }

    private Product buildProduct() {
        Product product = new Product();
        return product;
    }
}
