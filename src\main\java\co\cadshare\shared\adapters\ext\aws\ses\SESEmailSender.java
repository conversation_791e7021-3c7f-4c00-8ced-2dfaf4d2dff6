package co.cadshare.shared.adapters.ext.aws.ses;

import com.amazonaws.services.simpleemail.AmazonSimpleEmailServiceClient;
import com.amazonaws.services.simpleemail.model.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class SESEmailSender {

    @Autowired
    AmazonSimpleEmailServiceClient emailClient;

    private final Logger log = LoggerFactory.getLogger(getClass());

    public boolean sendEmail(String emailAddress, String emailContents, String subjectHeader, String sentFromEmailAddress) {
        String SUBJECT = subjectHeader;
        String BODY = emailContents;
        String FROM = sentFromEmailAddress != null ? sentFromEmailAddress : "<EMAIL>";

        // Construct an object to contain the recipient address.
        Destination destination = new Destination().withToAddresses(new String[]{emailAddress});

        // Create the subject and body of the message.
        Content subject = new Content().withData(SUBJECT);
        Content textBody = new Content().withData(BODY);
        Body body = new Body().withHtml(textBody);

        // Create a message with the specified subject and body.
        Message message = new Message().withSubject(subject).withBody(body);

        // Assemble the email.
        SendEmailRequest request = new SendEmailRequest().withSource(FROM).withDestination(destination).withMessage(message);

        SendEmailResult result = emailClient.sendEmail(request);

        log.info(result.toString());

        if (result.getMessageId() != null && !result.getMessageId().isEmpty()) {
            return true;
        }
        return false;
    }


}
