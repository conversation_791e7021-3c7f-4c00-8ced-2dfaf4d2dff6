<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                   http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.0.xsd
">

    <changeSet author="AndyB" id="1.5-integration-test-data-create-user-roles-1a">
        <sql>
            INSERT INTO public.userroles(userid, roleid)
            VALUES (1, 2);
            INSERT INTO public.userroles(userid, roleid)
            VALUES (1, 3);
            INSERT INTO public.userroles(userid, roleid)
            VALUES (1, 4);
            INSERT INTO public.userroles(userid, roleid)
            VALUES (1, 5);
            INSERT INTO public.userroles(userid, roleid)
            VALUES (1, 6);
            INSERT INTO public.userroles(userid, roleid)
            VALUES (1, 9);
            INSERT INTO public.userroles(userid, roleid)
            VALUES (1, 10);
        </sql>
    </changeSet>
    <changeSet author="AndyB" id="1.4-integration-test-data-create-user-roles-1b">
        <sql>
            INSERT INTO public.userroles(userid, roleid)
            VALUES (2, 2);
            INSERT INTO public.userroles(userid, roleid)
            VALUES (2, 7);
        </sql>
    </changeSet>
    <changeSet author="AndyB" id="1.4-integration-test-data-create-user-roles-1c">
        <sql>
            INSERT INTO public.userroles(userid, roleid)
            VALUES (3, 2);
            INSERT INTO public.userroles(userid, roleid)
            VALUES (3, 3);
            INSERT INTO public.userroles(userid, roleid)
            VALUES (3, 4);
            INSERT INTO public.userroles(userid, roleid)
            VALUES (3, 5);
            INSERT INTO public.userroles(userid, roleid)
            VALUES (3, 6);
            INSERT INTO public.userroles(userid, roleid)
            VALUES (3, 10);
            INSERT INTO public.userroles(userid, roleid)
            VALUES (3, 11);
        </sql>
    </changeSet>

    <changeSet author="AndyB" id="1.4-integration-test-data-create-user-roles-14">
        <sql>
            INSERT INTO public.userroles(userid, roleid)
            VALUES (14, 2);
            INSERT INTO public.userroles(userid, roleid)
            VALUES (14, 3);
            INSERT INTO public.userroles(userid, roleid)
            VALUES (14, 4);
            INSERT INTO public.userroles(userid, roleid)
            VALUES (14, 5);
            INSERT INTO public.userroles(userid, roleid)
            VALUES (14, 6);
            INSERT INTO public.userroles(userid, roleid)
            VALUES (14, 10);
            INSERT INTO public.userroles(userid, roleid)
            VALUES (14, 11);
        </sql>
    </changeSet>

    <changeSet author="AndyB" id="1.4-integration-test-data-create-user-roles-15">
        <sql>
            INSERT INTO public.userroles(userid, roleid)
            VALUES (15, 2);
            INSERT INTO public.userroles(userid, roleid)
            VALUES (15, 3);
            INSERT INTO public.userroles(userid, roleid)
            VALUES (15, 4);
            INSERT INTO public.userroles(userid, roleid)
            VALUES (15, 5);
            INSERT INTO public.userroles(userid, roleid)
            VALUES (15, 6);
            INSERT INTO public.userroles(userid, roleid)
            VALUES (15, 10);
            INSERT INTO public.userroles(userid, roleid)
            VALUES (15, 11);
        </sql>
    </changeSet>

    <changeSet author="AndyB" id="1.4-integration-test-data-create-user-roles-16">
        <sql>
            INSERT INTO public.userroles(userid, roleid)
            VALUES (16, 2);
            INSERT INTO public.userroles(userid, roleid)
            VALUES (16, 3);
            INSERT INTO public.userroles(userid, roleid)
            VALUES (16, 4);
            INSERT INTO public.userroles(userid, roleid)
            VALUES (16, 5);
            INSERT INTO public.userroles(userid, roleid)
            VALUES (16, 6);
            INSERT INTO public.userroles(userid, roleid)
            VALUES (16, 10);
            INSERT INTO public.userroles(userid, roleid)
            VALUES (16, 11);
        </sql>
    </changeSet>

    <changeSet author="AndyB" id="1.4-integration-test-data-create-user-roles-17">
        <sql>
            INSERT INTO public.userroles(userid, roleid)
            VALUES ((select userid
            FROM users
            WHERE emailaddress = '<EMAIL>'), 2);
            INSERT INTO public.userroles(userid, roleid)
            VALUES ((select userid
            FROM users
            WHERE emailaddress = '<EMAIL>'), 3);
            INSERT INTO public.userroles(userid, roleid)
            VALUES ((select userid
            FROM users
            WHERE emailaddress = '<EMAIL>'), 4);
            INSERT INTO public.userroles(userid, roleid)
            VALUES ((select userid
            FROM users
            WHERE emailaddress = '<EMAIL>'), 5);
            INSERT INTO public.userroles(userid, roleid)
            VALUES ((select userid
            FROM users
            WHERE emailaddress = '<EMAIL>'), 6);
            INSERT INTO public.userroles(userid, roleid)
            VALUES ((select userid
            FROM users
            WHERE emailaddress = '<EMAIL>'), 10);
        </sql>
    </changeSet>

    <changeSet author="AndyB" id="1.4-integration-test-data-create-user-roles-18">
        <sql>
            INSERT INTO public.userroles(userid, roleid)
            VALUES ((select userid
            FROM users
            WHERE emailaddress = '<EMAIL>'), 2);
            INSERT INTO public.userroles(userid, roleid)
            VALUES ((select userid
            FROM users
            WHERE emailaddress = '<EMAIL>'), 3);
            INSERT INTO public.userroles(userid, roleid)
            VALUES ((select userid
            FROM users
            WHERE emailaddress = '<EMAIL>'), 4);
            INSERT INTO public.userroles(userid, roleid)
            VALUES ((select userid
            FROM users
            WHERE emailaddress = '<EMAIL>'), 5);
            INSERT INTO public.userroles(userid, roleid)
            VALUES ((select userid
            FROM users
            WHERE emailaddress = '<EMAIL>'), 6);
            INSERT INTO public.userroles(userid, roleid)
            VALUES ((select userid
            FROM users
            WHERE emailaddress = '<EMAIL>'), 10);
        </sql>
    </changeSet>

    <changeSet author="AndyB" id="1.4-integration-test-data-create-user-roles-19">
        <sql>
            INSERT INTO public.userroles(userid, roleid)
            VALUES ((select userid
            FROM users
            WHERE emailaddress = '<EMAIL>'), 2);
            INSERT INTO public.userroles(userid, roleid)
            VALUES ((select userid
            FROM users
            WHERE emailaddress = '<EMAIL>'), 3);
            INSERT INTO public.userroles(userid, roleid)
            VALUES ((select userid
            FROM users
            WHERE emailaddress = '<EMAIL>'), 4);
            INSERT INTO public.userroles(userid, roleid)
            VALUES ((select userid
            FROM users
            WHERE emailaddress = '<EMAIL>'), 5);
            INSERT INTO public.userroles(userid, roleid)
            VALUES ((select userid
            FROM users
            WHERE emailaddress = '<EMAIL>'), 6);
            INSERT INTO public.userroles(userid, roleid)
            VALUES ((select userid
            FROM users
            WHERE emailaddress = '<EMAIL>'), 10);
        </sql>
    </changeSet>

</databaseChangeLog>
