package co.cadshare.modelMgt.models.core.model.viewable.softCopyDetail;

import co.cadshare.domainmodel.part.PartViewerDetails;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import lombok.Data;

@Data
public class SoftCopyDetailParts {

    private Integer stateDetailId;
    private String stateName;
    private String highResImgUrl;
    @JsonProperty("parts")
    private List<PartViewerDetails> partViewerDetails;
}
