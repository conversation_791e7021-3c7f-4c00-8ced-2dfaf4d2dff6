{"info": {"_postman_id": "e06d3365-ddcd-433c-88a2-7b1225952382", "name": "SSO", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "300252"}, "item": [{"name": "Local", "item": [{"name": "API PreAuth", "event": [{"listen": "test", "script": {"exec": ["tests[\"Status code is 200\"] = responseCode.code === 200;", "var responseData = JSON.parse(responseBody);", "postman.setEnvironmentVariable(\"access-token\", responseData.access_token);", "postman.setEnvironmentVariable(\"refresh-token\", responseData.refresh_token);"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\"client\":\"CDE\", \"token\":\"eyJraWQiOiJrclh1Rnl0TGlDQnp4U1M1b3NNbTlwbWV0YitcL2s2Qk5qVVpteFc2bkpVUT0iLCJhbGciOiJSUzI1NiJ9.************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.eAc2b3vMnB4-mm-nB560VC21NBnLooHa90ecrnS76eZsuAQ0AKbr3trT-p2wusbUzKpf25VvFi4L7_EYQ1TWGy2S1utR0C-cqsMrHVnQUvYJVVJLQqcKztzUKgKhCZKe52jjFcqT7CYL5r_U5tg7XvA1mezpfGo2nARtFoGWliQ8b2OduMSfdzXBQXco9qTQbg7Ut363BQdhKECJNVBPxGOXAG2wr3bFe2sv8o9fSJ1NWATPzBzLftJ-SqLxhgC_uXlPeXp4tVpbKfe2mY-CRZpmE32ebZ7VM7ITXuvBcPDq-IhlTYeQnAuCWh7Fb7g4WSfvNOLIquKC0rT5AEzh0Q\"}"}, "url": {"raw": "http://localhost:8080/api/preAuth", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["api", "preAuth"]}}, "response": []}, {"name": "API Orders Metadata", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************************************************************************************************************************************************************************************.q2UXPKKHFZboOGwaw7qmQ5sin_809A1HKBKDK0tSLW8", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "http://localhost:8080/api/orders/metadata?fromDate=2019-06-01&toDate=2019-07-02", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["api", "orders", "metadata"], "query": [{"key": "fromDate", "value": "2019-06-01"}, {"key": "toDate", "value": "2019-07-02"}]}}, "response": []}, {"name": "API Orders Unread Counts", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************************************************************************************************************************************************************************************.gJ-sibUDD41UEkKw5HD5PNx1cxPfvGVCuqusisPkFJg", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "http://localhost:8080/api/orders/unreadCounts", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["api", "orders", "unreadCounts"]}}, "response": []}, {"name": "API Products", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************************************************************************************************************************************************************************************.q2UXPKKHFZboOGwaw7qmQ5sin_809A1HKBKDK0tSLW8", "type": "string"}]}, "method": "GET", "header": [{"warning": "This is a duplicate header and will be overridden by the Authorization header generated by Postman.", "key": "Authorization", "value": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************************************************************************************************************************************************************************************.NgJ9erM1jwlMqEWiE9ML4r_QgW87tgJSV3ySjOpeaZk", "type": "text"}], "url": {"raw": "http://localhost:8080/api/products", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["api", "products"]}}, "response": []}]}, {"name": "Integration", "item": [{"name": "API PreAuth", "event": [{"listen": "test", "script": {"exec": ["tests[\"Status code is 200\"] = responseCode.code === 200;", "var responseData = JSON.parse(responseBody);", "postman.setEnvironmentVariable(\"access-token\", responseData.access_token);", "postman.setEnvironmentVariable(\"refresh-token\", responseData.refresh_token);"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\"client\": \"CDE\", \"token\": \"****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\"}"}, "url": {"raw": "https://api.integration.cadshare.com/preAuth", "protocol": "https", "host": ["api", "integration", "cadshare", "com"], "path": ["preAuth"]}}, "response": []}, {"name": "API Orders Metadata", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************************************************************************************************************************************************************************************.q2UXPKKHFZboOGwaw7qmQ5sin_809A1HKBKDK0tSLW8", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "https://api.integration.cadshare.com/orders/metadata?fromDate=2019-06-01&toDate=2019-07-02", "protocol": "https", "host": ["api", "integration", "cadshare", "com"], "path": ["orders", "metadata"], "query": [{"key": "fromDate", "value": "2019-06-01"}, {"key": "toDate", "value": "2019-07-02"}]}}, "response": []}, {"name": "API Orders Unread Counts", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************************************************************************************************************************************************************************************.gJ-sibUDD41UEkKw5HD5PNx1cxPfvGVCuqusisPkFJg", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "https://api.integration.cadshare.com/orders/unreadCounts", "protocol": "https", "host": ["api", "integration", "cadshare", "com"], "path": ["orders", "unreadCounts"]}}, "response": []}, {"name": "API Products", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************************************************************************************************************************************************************************************.q2UXPKKHFZboOGwaw7qmQ5sin_809A1HKBKDK0tSLW8", "type": "string"}]}, "method": "GET", "header": [{"warning": "This is a duplicate header and will be overridden by the Authorization header generated by Postman.", "key": "Authorization", "value": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************************************************************************************************************************************************************************************.NgJ9erM1jwlMqEWiE9ML4r_QgW87tgJSV3ySjOpeaZk", "type": "text"}], "url": {"raw": "https://api.integration.cadshare.com/products", "protocol": "https", "host": ["api", "integration", "cadshare", "com"], "path": ["products"]}}, "response": []}]}, {"name": "CDE Cognito Auth", "request": {"method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "value": "application/x-www-form-urlencoded", "type": "text"}], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "refresh_token", "value": "eyJjdHkiOiJKV1QiLCJlbmMiOiJBMjU2R0NNIiwiYWxnIjoiUlNBLU9BRVAifQ.Jh_hjA4QZT4FthGiCci_YYL8wGhxW-8_awiV48VBVuH17ohT8uetJCldcfuOzsiSd1mqGm8VXLrZoZhLQisOmlF8sYt0AfBZnkDDyOGD8BFsZNVfwMEhd-23ZXPR5bEe0q7opbMWlhcahArh5lOI_3ETdMqu_Zt_odAF8jpPQ4r6z9BAz_UbtqZH2EuwXivviyKUJf4OyPt-RVClRuMlycpS1973jWnNOd3H95Ok9gqWSJdm3sM4libC2vXHgcKllL6nbXPMKBTjSQjo-XD3ui5Rg9lTHrdKxiHDYktOdo_v9SSwM8Nq1_CFhi-qas6Du4kCWND7_7p5kcnEtyMDoQ.EmKNuGPpPzQzqCL7.w-m_8BTy4mNz_ZlCckCT7ylSRpO3tomAEXbDGAsgQNq04ZtpbkkxskmcmaAKaCS97sa4rHWR8PQIOQJdBi616DxEtb7LCVeir-POaet9EyJg7UldHE_Xdb8fRbP-rlvDGCB7uzfxRdsrGFJNKH6r51Wbh7mS0xE3ZTTQseQFRi5ncC1VVDFD3I4U6TfhSfl0wniTJOqMYoHzx5dz6pV-mLv6HcMfHBXncOO4QAHcRAYhsBAU5CgAfu8oOsVkKwcI0V0eO15MCJE0MjwvV_6BVeeAD3-i1ghecdtDVTMKP6HDsD0nIp9rRuFiuwmDE1Eb39yKrEF32d06AavrmEa6rmqC19X6l91yiXd738bGFAsnhsZyRhKdhEb1KwaEjZmGmwj5SRdxqwzNvPMqkynl5Z7JheESUjBKDf63yP5AHTeKlgK485oxrPVP7HEPIvKg5KTluM4e1EldK69SkeJ-ItMdVcBfu_46_CqaikuxJefrSt0OPVOKcaM7EAVY9e7hDoGpUkaMad5EileYegDSHNEiGR5x5OiqoFAlo-xQVM2-dwcRe79iOhMKW_oOWjfeKCl2p18FEKkXR53Uanw4ysdKuoO_jGTiqztIrfYJDcjlMXH338-NYtUytpZBI8fPMA7PGFGgKhKUZE-27Xmj85ZhZDXc3nCNW1gtr6VCdBVjSweQkvSRRD3rRagl23YMbJMcK-5cGu1IzRzjqgogR1SNtKycNZBxts9bXANz_p4u7-ymR7cqPZSQ36uzfqeB6NGqL8GAImUR3oFRw9M4O8sJoKBDsYUzZLBivLjGkxbVuGFbtajsJqm0O9Mh708p18Lse5YjSe-cGO6fGMIfo1eD8n27zpHtiNoOwXskmHW6lpr5qj_laJsbBQ2GJCFnjJm4rQG9Sxx7Cty96Y-Yldag9wuHonE18UbwDxbkSY4hZfQGKUbp8ylcbtdkhOG1dykaPDSYyw4NCXcyA8StI99osDFCcgPKudmTXfUDAncFFhycGAn9-RRXCdqscSJQJDeD6wg1CcCTQE9WHFI4EItN3QFzYsA6jFd31n3eaRP4vQF_wCH_8t2Od46_tEMlGPViauJXleBL0Ai5LWAb0n76TxcZewu1SjSxUQ8YqTk6lNOuGBWsY9ya8K74TSR2EJ08ASf3DFQ8gsarZCbNEB3E9qi8GZXxd3-U_ZcGoa56G7hO_8jfH2HMW3p9Wes2ANCMP-485By1yD52WHjmmaUICFWbp1IKDq5l-Bfu4ApbRuN8fuvY2ljOarPbaHEtcViD8tKvtMRXZyIzVlhHyBBI5hrQ8hFxkSVToJ6aHw-Sx6Uv61WaNjtE7A.TbJ4cQA6y_SDJI1411Tmkw", "type": "text"}, {"key": "grant_type", "value": "refresh_token", "type": "text"}, {"key": "client_id", "value": "3nt81rfh6mal6gvn9vjsr4i8an", "type": "text"}]}, "url": {"raw": "https://coreuserpool.auth.eu-west-1.amazoncognito.com/oauth2/token", "protocol": "https", "host": ["coreuserpool", "auth", "eu-west-1", "amazoncognito", "com"], "path": ["oauth2", "token"]}}, "response": []}]}