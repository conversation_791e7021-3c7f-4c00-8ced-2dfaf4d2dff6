package co.cadshare.glue;

import co.cadshare.shared.core.user.User;
import io.cucumber.java.en.Then;
import org.springframework.beans.factory.annotation.Autowired;

import static org.junit.Assert.assertNotNull;

public class UsersStepsIT {

	private final CadshareIT cadshare;

	private User user;

	@Autowired
	public UsersStepsIT(CadshareIT cadshare) {
		this.cadshare = cadshare;
	}

	@Then("I can view a customer for {}")
	public void iViewACustomerFor(String userId) {
		user = cadshare.loggedInUser().getUser(userId);
		assertNotNull(user);
	}

	@Then("I cannot view a customer for {}")
	public void iCannotViewACustomerFor(String userId) {
		cadshare.loggedInUser().notAuthorisedToGetUser(userId);
	}
}
