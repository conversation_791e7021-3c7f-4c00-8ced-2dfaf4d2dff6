package co.cadshare.glue;

import co.cadshare.shared.core.user.User;
import io.cucumber.java.en.Then;
import io.cucumber.java.en.When;
import org.springframework.beans.factory.annotation.Autowired;

import static org.junit.Assert.assertNotNull;

public class UsersStepsIT {

	private final UserIT loggedInUser;

	private User user;

	@Autowired
	public UsersStepsIT(AuthenticationIT auth) {
		loggedInUser = auth.getLoggedInUser();
	}



	@Then("I can view a customer for {}")
	public void iViewACustomerFor(String userId) {
		user = loggedInUser.getUser(userId);
		assertNotNull(user);
	}

	@Then("I cannot view a customer for {}")
	public void iCannotViewACustomerFor(String userId) {
		loggedInUser.notAuthorisedToGetUser(userId);
	}
}
